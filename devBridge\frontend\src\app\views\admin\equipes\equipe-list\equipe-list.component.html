<div
  class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden"
>
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto p-6 relative z-10">
    <!-- Header futuriste -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]"
      ></div>
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md"
      ></div>

      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20"
      >
        <div
          class="flex flex-col lg:flex-row lg:items-center lg:justify-between"
        >
          <div class="mb-4 lg:mb-0">
            <h1
              class="text-3xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 tracking-wide"
            >
              Équipes
            </h1>
            <p class="text-[#6d6870] dark:text-[#e0e0e0] text-sm">
              Gérez vos équipes et leurs membres avec style futuriste
            </p>
            <div class="mt-2 text-xs text-[#6d6870] dark:text-[#e0e0e0]">
              <span class="font-medium">{{ totalItems }}</span> équipe(s) au total
            </div>
          </div>

          <button
            (click)="navigateToAddEquipe()"
            class="relative overflow-hidden group bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]"
          >
            <i
              class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"
            ></i>
            Nouvelle Équipe
          </button>
        </div>
      </div>
    </div>

    <!-- Filtres et Recherche -->
    <div class="mb-6">
      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Recherche -->
          <div class="lg:col-span-2">
            <label class="block text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff] mb-2">
              <i class="fas fa-search mr-1"></i>
              Rechercher
            </label>
            <div class="relative">
              <input
                [formControl]="searchControl"
                type="text"
                placeholder="Nom d'équipe, description, tags..."
                class="w-full px-4 py-3 pl-10 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent text-[#2d3748] dark:text-[#e0e0e0] placeholder-[#6d6870] dark:placeholder-[#a0a0a0] transition-all"
              />
              <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-[#6d6870] dark:text-[#a0a0a0]"></i>
            </div>
          </div>

          <!-- Filtre Statut -->
          <div>
            <label class="block text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff] mb-2">
              <i class="fas fa-filter mr-1"></i>
              Statut
            </label>
            <select
              [formControl]="statusFilter"
              class="w-full px-4 py-3 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent text-[#2d3748] dark:text-[#e0e0e0] transition-all"
            >
              <option *ngFor="let option of statusOptions" [value]="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>

          <!-- Filtre Visibilité -->
          <div>
            <label class="block text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff] mb-2">
              <i class="fas fa-eye mr-1"></i>
              Visibilité
            </label>
            <select
              [formControl]="publicFilter"
              class="w-full px-4 py-3 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent text-[#2d3748] dark:text-[#e0e0e0] transition-all"
            >
              <option *ngFor="let option of publicOptions" [value]="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>

        <!-- Actions des filtres -->
        <div class="flex items-center justify-between mt-4 pt-4 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10">
          <div class="flex items-center space-x-2">
            <button
              (click)="resetFilters()"
              class="text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] text-sm font-medium transition-colors"
            >
              <i class="fas fa-undo mr-1"></i>
              Réinitialiser
            </button>
          </div>

          <div class="flex items-center space-x-4 text-sm text-[#6d6870] dark:text-[#a0a0a0]">
            <span>{{ equipes.length }} résultat(s)</span>
            <div class="flex items-center space-x-2">
              <label>Par page:</label>
              <select
                [value]="itemsPerPage"
                (change)="onItemsPerPageChange(+($any($event.target).value))"
                class="px-2 py-1 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded text-xs"
              >
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div
      *ngIf="loading"
      class="flex flex-col items-center justify-center py-16"
    >
      <div class="relative">
        <div
          class="w-12 h-12 border-3 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin"
        ></div>
        <div
          class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10"
        ></div>
      </div>
      <p
        class="mt-4 text-[#4f5fad] dark:text-[#00f7ff] text-sm font-medium tracking-wide"
      >
        Chargement des équipes...
      </p>
    </div>

    <!-- Error Alert -->
    <div *ngIf="error" class="mb-6">
      <div
        class="bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm"
      >
        <div class="flex items-start">
          <div class="text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="flex-1">
            <h3 class="font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1">
              Erreur de chargement des équipes
            </h3>
            <p class="text-sm text-[#6d6870] dark:text-[#e0e0e0]">
              {{ error }}
            </p>
            <button
              (click)="loadEquipes()"
              class="mt-3 bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30 transition-colors"
            >
              <i class="fas fa-sync-alt mr-1.5"></i> Réessayer
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- No Teams -->
    <div
      *ngIf="!loading && !error && equipes.length === 0"
      class="text-center py-16"
    >
      <div
        class="w-20 h-20 mx-auto mb-6 text-[#4f5fad] dark:text-[#00f7ff] opacity-70"
      >
        <i class="fas fa-users text-5xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-2">
        Aucune équipe trouvée
      </h3>
      <p class="text-[#6d6870] dark:text-[#e0e0e0] text-sm mb-6">
        Commencez par créer une nouvelle équipe pour organiser vos projets et
        membres
      </p>
      <button
        (click)="navigateToAddEquipe()"
        class="bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]"
      >
        <i class="fas fa-plus-circle mr-2"></i>
        Créer une équipe
      </button>
    </div>

    <!-- Teams Grid -->
    <div
      *ngIf="!loading && equipes.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
    >
      <div
        *ngFor="let equipe of equipes"
        class="group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm"
      >
        <!-- Header avec gradient -->
        <div class="relative">
          <div
            class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]"
          ></div>
          <div
            class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300"
          ></div>

          <div class="p-6">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <div class="flex items-center justify-between mb-2">
                  <h3
                    class="text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] group-hover:scale-[1.02] transition-transform duration-300 origin-left"
                  >
                    {{ equipe.name }}
                  </h3>
                  <!-- Badge de statut -->
                  <span
                    [class]="'px-2 py-1 rounded-full text-xs font-medium ' + getStatusBadgeClass(equipe.status)"
                  >
                    {{ getStatusText(equipe.status) }}
                  </span>
                </div>

                <div class="flex items-center space-x-2 text-xs mb-2">
                  <!-- Nombre de membres -->
                  <span
                    class="bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-1 rounded-full font-medium"
                  >
                    <i class="fas fa-users mr-1"></i>
                    {{ getMemberCount(equipe) }}/{{ equipe.maxMembers || 10 }}
                  </span>

                  <!-- Badge équipe pleine -->
                  <span
                    *ngIf="isTeamFull(equipe)"
                    class="bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded-full font-medium"
                  >
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    Complète
                  </span>

                  <!-- Badge visibilité -->
                  <span
                    [class]="equipe.isPublic ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' : 'bg-gray-100 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400'"
                    class="px-2 py-1 rounded-full font-medium"
                  >
                    <i [class]="equipe.isPublic ? 'fas fa-globe' : 'fas fa-lock'" class="mr-1"></i>
                    {{ equipe.isPublic ? 'Publique' : 'Privée' }}
                  </span>
                </div>

                <!-- Tags -->
                <div *ngIf="equipe.tags && equipe.tags.length > 0" class="flex flex-wrap gap-1 mb-2">
                  <span
                    *ngFor="let tag of equipe.tags.slice(0, 3)"
                    class="bg-[#4f5fad]/5 dark:bg-[#00f7ff]/5 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-0.5 rounded text-xs"
                  >
                    #{{ tag }}
                  </span>
                  <span
                    *ngIf="equipe.tags.length > 3"
                    class="text-[#6d6870] dark:text-[#a0a0a0] text-xs"
                  >
                    +{{ equipe.tags.length - 3 }}
                  </span>
                </div>

                <!-- Admin -->
                <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                  <i class="fas fa-crown mr-1"></i>
                  Admin: {{ getAdminDisplayName(equipe.admin) }}
                </div>
              </div>

              <!-- Avatar de l'équipe -->
              <div
                class="w-12 h-12 bg-gradient-to-br from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-full flex items-center justify-center shadow-lg"
              >
                <i class="fas fa-users text-white text-lg"></i>
              </div>
            </div>

            <!-- Description -->
            <p
              class="text-sm text-[#6d6870] dark:text-[#e0e0e0] line-clamp-2 mb-4"
            >
              {{
                equipe.description && equipe.description.length > 80
                  ? (equipe.description | slice : 0 : 80) + "..."
                  : equipe.description || "Aucune description disponible"
              }}
            </p>
          </div>
        </div>

        <!-- Actions -->
        <div class="px-6 pb-6">
          <div
            class="flex items-center justify-between pt-4 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10"
          >
            <!-- Bouton Détails -->
            <button
              (click)="equipe._id && navigateToEquipeDetail(equipe._id)"
              class="text-[#4f5fad] dark:text-[#00f7ff] hover:text-[#7826b5] dark:hover:text-[#4f5fad] text-sm font-medium transition-colors flex items-center group/details"
            >
              <i
                class="fas fa-eye mr-1 group-hover/details:scale-110 transition-transform"
              ></i>
              Détails
            </button>

            <!-- Actions rapides -->
            <div class="flex items-center space-x-2">
              <!-- Modifier (seulement si autorisé) -->
              <button
                *ngIf="canEditTeam(equipe)"
                (click)="equipe._id && navigateToEditEquipe(equipe._id)"
                class="p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all"
                title="Modifier l'équipe"
              >
                <i class="fas fa-edit"></i>
              </button>

              <!-- Gérer les tâches -->
              <button
                (click)="equipe._id && navigateToTasks(equipe._id)"
                class="p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#00ff9d] hover:bg-[#00ff9d]/10 rounded-lg transition-all"
                title="Gérer les tâches"
              >
                <i class="fas fa-tasks"></i>
              </button>

              <!-- Archiver/Activer -->
              <button
                *ngIf="canEditTeam(equipe) && equipe.status === 'active'"
                (click)="archiveTeam(equipe)"
                class="p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#f59e0b] hover:bg-[#f59e0b]/10 rounded-lg transition-all"
                title="Archiver l'équipe"
              >
                <i class="fas fa-archive"></i>
              </button>

              <button
                *ngIf="canEditTeam(equipe) && equipe.status === 'archived'"
                (click)="activateTeam(equipe)"
                class="p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#10b981] hover:bg-[#10b981]/10 rounded-lg transition-all"
                title="Activer l'équipe"
              >
                <i class="fas fa-play"></i>
              </button>

              <!-- Supprimer (seulement si autorisé) -->
              <button
                *ngIf="canDeleteTeam(equipe)"
                (click)="equipe._id && deleteEquipe(equipe._id)"
                class="p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all"
                title="Supprimer l'équipe"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div
      *ngIf="!loading && equipes.length > 0 && totalPages > 1"
      class="mt-8 flex items-center justify-center"
    >
      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-4 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20"
      >
        <div class="flex items-center space-x-2">
          <!-- Bouton Précédent -->
          <button
            [disabled]="currentPage === 1"
            (click)="onPageChange(currentPage - 1)"
            class="px-3 py-2 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <i class="fas fa-chevron-left mr-1"></i>
            Précédent
          </button>

          <!-- Numéros de pages -->
          <div class="flex items-center space-x-1">
            <button
              *ngFor="let page of [].constructor(totalPages); let i = index"
              [class.bg-gradient-to-r]="currentPage === i + 1"
              [class.from-[#4f5fad]]="currentPage === i + 1"
              [class.to-[#7826b5]]="currentPage === i + 1"
              [class.dark:from-[#00f7ff]]="currentPage === i + 1"
              [class.dark:to-[#4f5fad]]="currentPage === i + 1"
              [class.text-white]="currentPage === i + 1"
              [class.text-[#6d6870]]="currentPage !== i + 1"
              [class.dark:text-[#a0a0a0]]="currentPage !== i + 1"
              [class.hover:text-[#4f5fad]]="currentPage !== i + 1"
              [class.dark:hover:text-[#00f7ff]]="currentPage !== i + 1"
              (click)="onPageChange(i + 1)"
              class="w-10 h-10 rounded-lg font-medium text-sm transition-all hover:scale-105"
            >
              {{ i + 1 }}
            </button>
          </div>

          <!-- Bouton Suivant -->
          <button
            [disabled]="currentPage === totalPages"
            (click)="onPageChange(currentPage + 1)"
            class="px-3 py-2 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Suivant
            <i class="fas fa-chevron-right ml-1"></i>
          </button>
        </div>

        <!-- Informations de pagination -->
        <div class="mt-3 pt-3 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10 text-center">
          <span class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
            Page {{ currentPage }} sur {{ totalPages }}
            ({{ totalItems }} équipe(s) au total)
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
