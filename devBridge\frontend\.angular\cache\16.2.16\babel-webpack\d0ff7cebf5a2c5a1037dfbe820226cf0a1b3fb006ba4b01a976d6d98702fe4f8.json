{"ast": null, "code": "import { newInvariantError, invariant } from \"../../utilities/globals/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { validateOperation, createOperation, transformOperation } from \"../utils/index.js\";\nfunction passthrough(op, forward) {\n  return forward ? forward(op) : Observable.of();\n}\nfunction toLink(handler) {\n  return typeof handler === \"function\" ? new ApolloLink(handler) : handler;\n}\nfunction isTerminating(link) {\n  return link.request.length <= 1;\n}\nvar ApolloLink = /** @class */function () {\n  function ApolloLink(request) {\n    if (request) this.request = request;\n  }\n  ApolloLink.empty = function () {\n    return new ApolloLink(function () {\n      return Observable.of();\n    });\n  };\n  ApolloLink.from = function (links) {\n    if (links.length === 0) return ApolloLink.empty();\n    return links.map(toLink).reduce(function (x, y) {\n      return x.concat(y);\n    });\n  };\n  ApolloLink.split = function (test, left, right) {\n    var leftLink = toLink(left);\n    var rightLink = toLink(right || new ApolloLink(passthrough));\n    var ret;\n    if (isTerminating(leftLink) && isTerminating(rightLink)) {\n      ret = new ApolloLink(function (operation) {\n        return test(operation) ? leftLink.request(operation) || Observable.of() : rightLink.request(operation) || Observable.of();\n      });\n    } else {\n      ret = new ApolloLink(function (operation, forward) {\n        return test(operation) ? leftLink.request(operation, forward) || Observable.of() : rightLink.request(operation, forward) || Observable.of();\n      });\n    }\n    return Object.assign(ret, {\n      left: leftLink,\n      right: rightLink\n    });\n  };\n  ApolloLink.execute = function (link, operation) {\n    return link.request(createOperation(operation.context, transformOperation(validateOperation(operation)))) || Observable.of();\n  };\n  ApolloLink.concat = function (first, second) {\n    var firstLink = toLink(first);\n    if (isTerminating(firstLink)) {\n      globalThis.__DEV__ !== false && invariant.warn(38, firstLink);\n      return firstLink;\n    }\n    var nextLink = toLink(second);\n    var ret;\n    if (isTerminating(nextLink)) {\n      ret = new ApolloLink(function (operation) {\n        return firstLink.request(operation, function (op) {\n          return nextLink.request(op) || Observable.of();\n        }) || Observable.of();\n      });\n    } else {\n      ret = new ApolloLink(function (operation, forward) {\n        return firstLink.request(operation, function (op) {\n          return nextLink.request(op, forward) || Observable.of();\n        }) || Observable.of();\n      });\n    }\n    return Object.assign(ret, {\n      left: firstLink,\n      right: nextLink\n    });\n  };\n  ApolloLink.prototype.split = function (test, left, right) {\n    return this.concat(ApolloLink.split(test, left, right || new ApolloLink(passthrough)));\n  };\n  ApolloLink.prototype.concat = function (next) {\n    return ApolloLink.concat(this, next);\n  };\n  ApolloLink.prototype.request = function (operation, forward) {\n    throw newInvariantError(39);\n  };\n  ApolloLink.prototype.onError = function (error, observer) {\n    if (observer && observer.error) {\n      observer.error(error);\n      // Returning false indicates that observer.error does not need to be\n      // called again, since it was already called (on the previous line).\n      // Calling observer.error again would not cause any real problems,\n      // since only the first call matters, but custom onError functions\n      // might have other reasons for wanting to prevent the default\n      // behavior by returning false.\n      return false;\n    }\n    // Throw errors will be passed to observer.error.\n    throw error;\n  };\n  ApolloLink.prototype.setOnError = function (fn) {\n    this.onError = fn;\n    return this;\n  };\n  return ApolloLink;\n}();\nexport { ApolloLink };", "map": {"version": 3, "names": ["newInvariantError", "invariant", "Observable", "validateOperation", "createOperation", "transformOperation", "passthrough", "op", "forward", "of", "toLink", "handler", "ApolloLink", "isTerminating", "link", "request", "length", "empty", "from", "links", "map", "reduce", "x", "y", "concat", "split", "test", "left", "right", "leftLink", "rightLink", "ret", "operation", "Object", "assign", "execute", "context", "first", "second", "firstLink", "globalThis", "__DEV__", "warn", "nextLink", "prototype", "next", "onError", "error", "observer", "setOnError", "fn"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/core/ApolloLink.js"], "sourcesContent": ["import { newInvariantError, invariant } from \"../../utilities/globals/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { validateOperation, createOperation, transformOperation, } from \"../utils/index.js\";\nfunction passthrough(op, forward) {\n    return (forward ? forward(op) : Observable.of());\n}\nfunction toLink(handler) {\n    return typeof handler === \"function\" ? new ApolloLink(handler) : handler;\n}\nfunction isTerminating(link) {\n    return link.request.length <= 1;\n}\nvar ApolloLink = /** @class */ (function () {\n    function ApolloLink(request) {\n        if (request)\n            this.request = request;\n    }\n    ApolloLink.empty = function () {\n        return new ApolloLink(function () { return Observable.of(); });\n    };\n    ApolloLink.from = function (links) {\n        if (links.length === 0)\n            return ApolloLink.empty();\n        return links.map(toLink).reduce(function (x, y) { return x.concat(y); });\n    };\n    ApolloLink.split = function (test, left, right) {\n        var leftLink = toLink(left);\n        var rightLink = toLink(right || new ApolloLink(passthrough));\n        var ret;\n        if (isTerminating(leftLink) && isTerminating(rightLink)) {\n            ret = new ApolloLink(function (operation) {\n                return test(operation) ?\n                    leftLink.request(operation) || Observable.of()\n                    : rightLink.request(operation) || Observable.of();\n            });\n        }\n        else {\n            ret = new ApolloLink(function (operation, forward) {\n                return test(operation) ?\n                    leftLink.request(operation, forward) || Observable.of()\n                    : rightLink.request(operation, forward) || Observable.of();\n            });\n        }\n        return Object.assign(ret, { left: leftLink, right: rightLink });\n    };\n    ApolloLink.execute = function (link, operation) {\n        return (link.request(createOperation(operation.context, transformOperation(validateOperation(operation)))) || Observable.of());\n    };\n    ApolloLink.concat = function (first, second) {\n        var firstLink = toLink(first);\n        if (isTerminating(firstLink)) {\n            globalThis.__DEV__ !== false && invariant.warn(38, firstLink);\n            return firstLink;\n        }\n        var nextLink = toLink(second);\n        var ret;\n        if (isTerminating(nextLink)) {\n            ret = new ApolloLink(function (operation) {\n                return firstLink.request(operation, function (op) { return nextLink.request(op) || Observable.of(); }) || Observable.of();\n            });\n        }\n        else {\n            ret = new ApolloLink(function (operation, forward) {\n                return (firstLink.request(operation, function (op) {\n                    return nextLink.request(op, forward) || Observable.of();\n                }) || Observable.of());\n            });\n        }\n        return Object.assign(ret, { left: firstLink, right: nextLink });\n    };\n    ApolloLink.prototype.split = function (test, left, right) {\n        return this.concat(ApolloLink.split(test, left, right || new ApolloLink(passthrough)));\n    };\n    ApolloLink.prototype.concat = function (next) {\n        return ApolloLink.concat(this, next);\n    };\n    ApolloLink.prototype.request = function (operation, forward) {\n        throw newInvariantError(39);\n    };\n    ApolloLink.prototype.onError = function (error, observer) {\n        if (observer && observer.error) {\n            observer.error(error);\n            // Returning false indicates that observer.error does not need to be\n            // called again, since it was already called (on the previous line).\n            // Calling observer.error again would not cause any real problems,\n            // since only the first call matters, but custom onError functions\n            // might have other reasons for wanting to prevent the default\n            // behavior by returning false.\n            return false;\n        }\n        // Throw errors will be passed to observer.error.\n        throw error;\n    };\n    ApolloLink.prototype.setOnError = function (fn) {\n        this.onError = fn;\n        return this;\n    };\n    return ApolloLink;\n}());\nexport { ApolloLink };\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,SAAS,QAAQ,kCAAkC;AAC/E,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,iBAAiB,EAAEC,eAAe,EAAEC,kBAAkB,QAAS,mBAAmB;AAC3F,SAASC,WAAWA,CAACC,EAAE,EAAEC,OAAO,EAAE;EAC9B,OAAQA,OAAO,GAAGA,OAAO,CAACD,EAAE,CAAC,GAAGL,UAAU,CAACO,EAAE,CAAC,CAAC;AACnD;AACA,SAASC,MAAMA,CAACC,OAAO,EAAE;EACrB,OAAO,OAAOA,OAAO,KAAK,UAAU,GAAG,IAAIC,UAAU,CAACD,OAAO,CAAC,GAAGA,OAAO;AAC5E;AACA,SAASE,aAAaA,CAACC,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACC,OAAO,CAACC,MAAM,IAAI,CAAC;AACnC;AACA,IAAIJ,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACG,OAAO,EAAE;IACzB,IAAIA,OAAO,EACP,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC9B;EACAH,UAAU,CAACK,KAAK,GAAG,YAAY;IAC3B,OAAO,IAAIL,UAAU,CAAC,YAAY;MAAE,OAAOV,UAAU,CAACO,EAAE,CAAC,CAAC;IAAE,CAAC,CAAC;EAClE,CAAC;EACDG,UAAU,CAACM,IAAI,GAAG,UAAUC,KAAK,EAAE;IAC/B,IAAIA,KAAK,CAACH,MAAM,KAAK,CAAC,EAClB,OAAOJ,UAAU,CAACK,KAAK,CAAC,CAAC;IAC7B,OAAOE,KAAK,CAACC,GAAG,CAACV,MAAM,CAAC,CAACW,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAOD,CAAC,CAACE,MAAM,CAACD,CAAC,CAAC;IAAE,CAAC,CAAC;EAC5E,CAAC;EACDX,UAAU,CAACa,KAAK,GAAG,UAAUC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAE;IAC5C,IAAIC,QAAQ,GAAGnB,MAAM,CAACiB,IAAI,CAAC;IAC3B,IAAIG,SAAS,GAAGpB,MAAM,CAACkB,KAAK,IAAI,IAAIhB,UAAU,CAACN,WAAW,CAAC,CAAC;IAC5D,IAAIyB,GAAG;IACP,IAAIlB,aAAa,CAACgB,QAAQ,CAAC,IAAIhB,aAAa,CAACiB,SAAS,CAAC,EAAE;MACrDC,GAAG,GAAG,IAAInB,UAAU,CAAC,UAAUoB,SAAS,EAAE;QACtC,OAAON,IAAI,CAACM,SAAS,CAAC,GAClBH,QAAQ,CAACd,OAAO,CAACiB,SAAS,CAAC,IAAI9B,UAAU,CAACO,EAAE,CAAC,CAAC,GAC5CqB,SAAS,CAACf,OAAO,CAACiB,SAAS,CAAC,IAAI9B,UAAU,CAACO,EAAE,CAAC,CAAC;MACzD,CAAC,CAAC;IACN,CAAC,MACI;MACDsB,GAAG,GAAG,IAAInB,UAAU,CAAC,UAAUoB,SAAS,EAAExB,OAAO,EAAE;QAC/C,OAAOkB,IAAI,CAACM,SAAS,CAAC,GAClBH,QAAQ,CAACd,OAAO,CAACiB,SAAS,EAAExB,OAAO,CAAC,IAAIN,UAAU,CAACO,EAAE,CAAC,CAAC,GACrDqB,SAAS,CAACf,OAAO,CAACiB,SAAS,EAAExB,OAAO,CAAC,IAAIN,UAAU,CAACO,EAAE,CAAC,CAAC;MAClE,CAAC,CAAC;IACN;IACA,OAAOwB,MAAM,CAACC,MAAM,CAACH,GAAG,EAAE;MAAEJ,IAAI,EAAEE,QAAQ;MAAED,KAAK,EAAEE;IAAU,CAAC,CAAC;EACnE,CAAC;EACDlB,UAAU,CAACuB,OAAO,GAAG,UAAUrB,IAAI,EAAEkB,SAAS,EAAE;IAC5C,OAAQlB,IAAI,CAACC,OAAO,CAACX,eAAe,CAAC4B,SAAS,CAACI,OAAO,EAAE/B,kBAAkB,CAACF,iBAAiB,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI9B,UAAU,CAACO,EAAE,CAAC,CAAC;EACjI,CAAC;EACDG,UAAU,CAACY,MAAM,GAAG,UAAUa,KAAK,EAAEC,MAAM,EAAE;IACzC,IAAIC,SAAS,GAAG7B,MAAM,CAAC2B,KAAK,CAAC;IAC7B,IAAIxB,aAAa,CAAC0B,SAAS,CAAC,EAAE;MAC1BC,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIxC,SAAS,CAACyC,IAAI,CAAC,EAAE,EAAEH,SAAS,CAAC;MAC7D,OAAOA,SAAS;IACpB;IACA,IAAII,QAAQ,GAAGjC,MAAM,CAAC4B,MAAM,CAAC;IAC7B,IAAIP,GAAG;IACP,IAAIlB,aAAa,CAAC8B,QAAQ,CAAC,EAAE;MACzBZ,GAAG,GAAG,IAAInB,UAAU,CAAC,UAAUoB,SAAS,EAAE;QACtC,OAAOO,SAAS,CAACxB,OAAO,CAACiB,SAAS,EAAE,UAAUzB,EAAE,EAAE;UAAE,OAAOoC,QAAQ,CAAC5B,OAAO,CAACR,EAAE,CAAC,IAAIL,UAAU,CAACO,EAAE,CAAC,CAAC;QAAE,CAAC,CAAC,IAAIP,UAAU,CAACO,EAAE,CAAC,CAAC;MAC7H,CAAC,CAAC;IACN,CAAC,MACI;MACDsB,GAAG,GAAG,IAAInB,UAAU,CAAC,UAAUoB,SAAS,EAAExB,OAAO,EAAE;QAC/C,OAAQ+B,SAAS,CAACxB,OAAO,CAACiB,SAAS,EAAE,UAAUzB,EAAE,EAAE;UAC/C,OAAOoC,QAAQ,CAAC5B,OAAO,CAACR,EAAE,EAAEC,OAAO,CAAC,IAAIN,UAAU,CAACO,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,IAAIP,UAAU,CAACO,EAAE,CAAC,CAAC;MACzB,CAAC,CAAC;IACN;IACA,OAAOwB,MAAM,CAACC,MAAM,CAACH,GAAG,EAAE;MAAEJ,IAAI,EAAEY,SAAS;MAAEX,KAAK,EAAEe;IAAS,CAAC,CAAC;EACnE,CAAC;EACD/B,UAAU,CAACgC,SAAS,CAACnB,KAAK,GAAG,UAAUC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACtD,OAAO,IAAI,CAACJ,MAAM,CAACZ,UAAU,CAACa,KAAK,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,IAAI,IAAIhB,UAAU,CAACN,WAAW,CAAC,CAAC,CAAC;EAC1F,CAAC;EACDM,UAAU,CAACgC,SAAS,CAACpB,MAAM,GAAG,UAAUqB,IAAI,EAAE;IAC1C,OAAOjC,UAAU,CAACY,MAAM,CAAC,IAAI,EAAEqB,IAAI,CAAC;EACxC,CAAC;EACDjC,UAAU,CAACgC,SAAS,CAAC7B,OAAO,GAAG,UAAUiB,SAAS,EAAExB,OAAO,EAAE;IACzD,MAAMR,iBAAiB,CAAC,EAAE,CAAC;EAC/B,CAAC;EACDY,UAAU,CAACgC,SAAS,CAACE,OAAO,GAAG,UAAUC,KAAK,EAAEC,QAAQ,EAAE;IACtD,IAAIA,QAAQ,IAAIA,QAAQ,CAACD,KAAK,EAAE;MAC5BC,QAAQ,CAACD,KAAK,CAACA,KAAK,CAAC;MACrB;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA;IACA,MAAMA,KAAK;EACf,CAAC;EACDnC,UAAU,CAACgC,SAAS,CAACK,UAAU,GAAG,UAAUC,EAAE,EAAE;IAC5C,IAAI,CAACJ,OAAO,GAAGI,EAAE;IACjB,OAAO,IAAI;EACf,CAAC;EACD,OAAOtC,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}