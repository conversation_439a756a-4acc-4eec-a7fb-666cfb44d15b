{"ast": null, "code": "import { secondsInHour } from \"./constants.js\";\n\n/**\n * @name secondsToHours\n * @category Conversion Helpers\n * @summary Convert seconds to hours.\n *\n * @description\n * Convert a number of seconds to a full number of hours.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in hours\n *\n * @example\n * // Convert 7200 seconds into hours\n * const result = secondsToHours(7200)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToHours(7199)\n * //=> 1\n */\nexport function secondsToHours(seconds) {\n  const hours = seconds / secondsInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default secondsToHours;", "map": {"version": 3, "names": ["secondsInHour", "secondsToHours", "seconds", "hours", "Math", "trunc"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/secondsToHours.js"], "sourcesContent": ["import { secondsInHour } from \"./constants.js\";\n\n/**\n * @name secondsToHours\n * @category Conversion Helpers\n * @summary Convert seconds to hours.\n *\n * @description\n * Convert a number of seconds to a full number of hours.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in hours\n *\n * @example\n * // Convert 7200 seconds into hours\n * const result = secondsToHours(7200)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToHours(7199)\n * //=> 1\n */\nexport function secondsToHours(seconds) {\n  const hours = seconds / secondsInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default secondsToHours;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,gBAAgB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAE;EACtC,MAAMC,KAAK,GAAGD,OAAO,GAAGF,aAAa;EACrC,OAAOI,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B;;AAEA;AACA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}