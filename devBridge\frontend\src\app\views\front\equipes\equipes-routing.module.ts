import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { EquipeFormComponent } from './equipe-form/equipe-form.component';
import { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';
import { TaskListComponent } from './task-list/task-list.component';
import { EquipeComponent } from './equipe/equipe.component';
import { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';
import { EquipeSimpleComponent } from './equipe-simple/equipe-simple.component';

const routes: Routes = [
  {
    path: '',
    component: EquipeLayoutComponent,
    children: [
      // Liste des équipes (page principale)
      { path: '', component: EquipeComponent },
      { path: 'liste', redirectTo: '', pathMatch: 'full' },

      // Création d'équipe (formulaire)
      { path: 'creation', component: EquipeFormComponent },
      { path: 'ajouter', redirectTo: 'creation', pathMatch: 'full' },
      { path: 'nouveau', redirectTo: 'creation', pathMatch: 'full' },

      // Autres routes nécessaires
      { path: 'modifier/:id', component: EquipeFormComponent },
      { path: 'detail/:id', component: EquipeDetailComponent },
      { path: 'tasks/:id', component: TaskListComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EquipesRoutingModule {}
