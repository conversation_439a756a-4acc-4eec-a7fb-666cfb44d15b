{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { Kind } from \"graphql\";\nimport { wrap } from \"optimism\";\nimport { isField, resultKeyNameFromField, isReference, makeReference, shouldInclude, addTypenameToDocument, getDefaultValues, getMainDefinition, getQueryDefinition, getFragmentFromSelection, maybeDeepFreeze, mergeDeepArray, DeepMerger, isNonNullObject, canUseWeakMap, compact, canonicalStringify, cacheSizes } from \"../../utilities/index.js\";\nimport { maybeDependOnExistenceOfEntity, supportsResultCaching } from \"./entityStore.js\";\nimport { isArray, extractFragmentContext, getTypenameFromStoreObject, shouldCanonizeResults } from \"./helpers.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { ObjectCanon } from \"./object-canon.js\";\nfunction execSelectionSetKeyArgs(options) {\n  return [options.selectionSet, options.objectOrReference, options.context,\n  // We split out this property so we can pass different values\n  // independently without modifying options.context itself.\n  options.context.canonizeResults];\n}\nvar StoreReader = /** @class */function () {\n  function StoreReader(config) {\n    var _this = this;\n    this.knownResults = new (canUseWeakMap ? WeakMap : Map)();\n    this.config = compact(config, {\n      addTypename: config.addTypename !== false,\n      canonizeResults: shouldCanonizeResults(config)\n    });\n    this.canon = config.canon || new ObjectCanon();\n    // memoized functions in this class will be \"garbage-collected\"\n    // by recreating the whole `StoreReader` in\n    // `InMemoryCache.resetResultsCache`\n    // (triggered from `InMemoryCache.gc` with `resetResultCache: true`)\n    this.executeSelectionSet = wrap(function (options) {\n      var _a;\n      var canonizeResults = options.context.canonizeResults;\n      var peekArgs = execSelectionSetKeyArgs(options);\n      // Negate this boolean option so we can find out if we've already read\n      // this result using the other boolean value.\n      peekArgs[3] = !canonizeResults;\n      var other = (_a = _this.executeSelectionSet).peek.apply(_a, peekArgs);\n      if (other) {\n        if (canonizeResults) {\n          return __assign(__assign({}, other), {\n            // If we previously read this result without canonizing it, we can\n            // reuse that result simply by canonizing it now.\n            result: _this.canon.admit(other.result)\n          });\n        }\n        // If we previously read this result with canonization enabled, we can\n        // return that canonized result as-is.\n        return other;\n      }\n      maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n      // Finally, if we didn't find any useful previous results, run the real\n      // execSelectionSetImpl method with the given options.\n      return _this.execSelectionSetImpl(options);\n    }, {\n      max: this.config.resultCacheMaxSize || cacheSizes[\"inMemoryCache.executeSelectionSet\"] || 50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n      keyArgs: execSelectionSetKeyArgs,\n      // Note that the parameters of makeCacheKey are determined by the\n      // array returned by keyArgs.\n      makeCacheKey: function (selectionSet, parent, context, canonizeResults) {\n        if (supportsResultCaching(context.store)) {\n          return context.store.makeCacheKey(selectionSet, isReference(parent) ? parent.__ref : parent, context.varString, canonizeResults);\n        }\n      }\n    });\n    this.executeSubSelectedArray = wrap(function (options) {\n      maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n      return _this.execSubSelectedArrayImpl(options);\n    }, {\n      max: this.config.resultCacheMaxSize || cacheSizes[\"inMemoryCache.executeSubSelectedArray\"] || 10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n      makeCacheKey: function (_a) {\n        var field = _a.field,\n          array = _a.array,\n          context = _a.context;\n        if (supportsResultCaching(context.store)) {\n          return context.store.makeCacheKey(field, array, context.varString);\n        }\n      }\n    });\n  }\n  StoreReader.prototype.resetCanon = function () {\n    this.canon = new ObjectCanon();\n  };\n  /**\n   * Given a store and a query, return as much of the result as possible and\n   * identify if any data was missing from the store.\n   */\n  StoreReader.prototype.diffQueryAgainstStore = function (_a) {\n    var store = _a.store,\n      query = _a.query,\n      _b = _a.rootId,\n      rootId = _b === void 0 ? \"ROOT_QUERY\" : _b,\n      variables = _a.variables,\n      _c = _a.returnPartialData,\n      returnPartialData = _c === void 0 ? true : _c,\n      _d = _a.canonizeResults,\n      canonizeResults = _d === void 0 ? this.config.canonizeResults : _d;\n    var policies = this.config.cache.policies;\n    variables = __assign(__assign({}, getDefaultValues(getQueryDefinition(query))), variables);\n    var rootRef = makeReference(rootId);\n    var execResult = this.executeSelectionSet({\n      selectionSet: getMainDefinition(query).selectionSet,\n      objectOrReference: rootRef,\n      enclosingRef: rootRef,\n      context: __assign({\n        store: store,\n        query: query,\n        policies: policies,\n        variables: variables,\n        varString: canonicalStringify(variables),\n        canonizeResults: canonizeResults\n      }, extractFragmentContext(query, this.config.fragments))\n    });\n    var missing;\n    if (execResult.missing) {\n      // For backwards compatibility we still report an array of\n      // MissingFieldError objects, even though there will only ever be at most\n      // one of them, now that all missing field error messages are grouped\n      // together in the execResult.missing tree.\n      missing = [new MissingFieldError(firstMissing(execResult.missing), execResult.missing, query, variables)];\n      if (!returnPartialData) {\n        throw missing[0];\n      }\n    }\n    return {\n      result: execResult.result,\n      complete: !missing,\n      missing: missing\n    };\n  };\n  StoreReader.prototype.isFresh = function (result, parent, selectionSet, context) {\n    if (supportsResultCaching(context.store) && this.knownResults.get(result) === selectionSet) {\n      var latest = this.executeSelectionSet.peek(selectionSet, parent, context,\n      // If result is canonical, then it could only have been previously\n      // cached by the canonizing version of executeSelectionSet, so we can\n      // avoid checking both possibilities here.\n      this.canon.isKnown(result));\n      if (latest && result === latest.result) {\n        return true;\n      }\n    }\n    return false;\n  };\n  // Uncached version of executeSelectionSet.\n  StoreReader.prototype.execSelectionSetImpl = function (_a) {\n    var _this = this;\n    var selectionSet = _a.selectionSet,\n      objectOrReference = _a.objectOrReference,\n      enclosingRef = _a.enclosingRef,\n      context = _a.context;\n    if (isReference(objectOrReference) && !context.policies.rootTypenamesById[objectOrReference.__ref] && !context.store.has(objectOrReference.__ref)) {\n      return {\n        result: this.canon.empty,\n        missing: \"Dangling reference to missing \".concat(objectOrReference.__ref, \" object\")\n      };\n    }\n    var variables = context.variables,\n      policies = context.policies,\n      store = context.store;\n    var typename = store.getFieldValue(objectOrReference, \"__typename\");\n    var objectsToMerge = [];\n    var missing;\n    var missingMerger = new DeepMerger();\n    if (this.config.addTypename && typeof typename === \"string\" && !policies.rootIdsByTypename[typename]) {\n      // Ensure we always include a default value for the __typename\n      // field, if we have one, and this.config.addTypename is true. Note\n      // that this field can be overridden by other merged objects.\n      objectsToMerge.push({\n        __typename: typename\n      });\n    }\n    function handleMissing(result, resultName) {\n      var _a;\n      if (result.missing) {\n        missing = missingMerger.merge(missing, (_a = {}, _a[resultName] = result.missing, _a));\n      }\n      return result.result;\n    }\n    var workSet = new Set(selectionSet.selections);\n    workSet.forEach(function (selection) {\n      var _a, _b;\n      // Omit fields with directives @skip(if: <truthy value>) or\n      // @include(if: <falsy value>).\n      if (!shouldInclude(selection, variables)) return;\n      if (isField(selection)) {\n        var fieldValue = policies.readField({\n          fieldName: selection.name.value,\n          field: selection,\n          variables: context.variables,\n          from: objectOrReference\n        }, context);\n        var resultName = resultKeyNameFromField(selection);\n        if (fieldValue === void 0) {\n          if (!addTypenameToDocument.added(selection)) {\n            missing = missingMerger.merge(missing, (_a = {}, _a[resultName] = \"Can't find field '\".concat(selection.name.value, \"' on \").concat(isReference(objectOrReference) ? objectOrReference.__ref + \" object\" : \"object \" + JSON.stringify(objectOrReference, null, 2)), _a));\n          }\n        } else if (isArray(fieldValue)) {\n          if (fieldValue.length > 0) {\n            fieldValue = handleMissing(_this.executeSubSelectedArray({\n              field: selection,\n              array: fieldValue,\n              enclosingRef: enclosingRef,\n              context: context\n            }), resultName);\n          }\n        } else if (!selection.selectionSet) {\n          // If the field does not have a selection set, then we handle it\n          // as a scalar value. To keep this.canon from canonicalizing\n          // this value, we use this.canon.pass to wrap fieldValue in a\n          // Pass object that this.canon.admit will later unwrap as-is.\n          if (context.canonizeResults) {\n            fieldValue = _this.canon.pass(fieldValue);\n          }\n        } else if (fieldValue != null) {\n          // In this case, because we know the field has a selection set,\n          // it must be trying to query a GraphQLObjectType, which is why\n          // fieldValue must be != null.\n          fieldValue = handleMissing(_this.executeSelectionSet({\n            selectionSet: selection.selectionSet,\n            objectOrReference: fieldValue,\n            enclosingRef: isReference(fieldValue) ? fieldValue : enclosingRef,\n            context: context\n          }), resultName);\n        }\n        if (fieldValue !== void 0) {\n          objectsToMerge.push((_b = {}, _b[resultName] = fieldValue, _b));\n        }\n      } else {\n        var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n        if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n          throw newInvariantError(10, selection.name.value);\n        }\n        if (fragment && policies.fragmentMatches(fragment, typename)) {\n          fragment.selectionSet.selections.forEach(workSet.add, workSet);\n        }\n      }\n    });\n    var result = mergeDeepArray(objectsToMerge);\n    var finalResult = {\n      result: result,\n      missing: missing\n    };\n    var frozen = context.canonizeResults ? this.canon.admit(finalResult)\n    // Since this.canon is normally responsible for freezing results (only in\n    // development), freeze them manually if canonization is disabled.\n    : maybeDeepFreeze(finalResult);\n    // Store this result with its selection set so that we can quickly\n    // recognize it again in the StoreReader#isFresh method.\n    if (frozen.result) {\n      this.knownResults.set(frozen.result, selectionSet);\n    }\n    return frozen;\n  };\n  // Uncached version of executeSubSelectedArray.\n  StoreReader.prototype.execSubSelectedArrayImpl = function (_a) {\n    var _this = this;\n    var field = _a.field,\n      array = _a.array,\n      enclosingRef = _a.enclosingRef,\n      context = _a.context;\n    var missing;\n    var missingMerger = new DeepMerger();\n    function handleMissing(childResult, i) {\n      var _a;\n      if (childResult.missing) {\n        missing = missingMerger.merge(missing, (_a = {}, _a[i] = childResult.missing, _a));\n      }\n      return childResult.result;\n    }\n    if (field.selectionSet) {\n      array = array.filter(context.store.canRead);\n    }\n    array = array.map(function (item, i) {\n      // null value in array\n      if (item === null) {\n        return null;\n      }\n      // This is a nested array, recurse\n      if (isArray(item)) {\n        return handleMissing(_this.executeSubSelectedArray({\n          field: field,\n          array: item,\n          enclosingRef: enclosingRef,\n          context: context\n        }), i);\n      }\n      // This is an object, run the selection set on it\n      if (field.selectionSet) {\n        return handleMissing(_this.executeSelectionSet({\n          selectionSet: field.selectionSet,\n          objectOrReference: item,\n          enclosingRef: isReference(item) ? item : enclosingRef,\n          context: context\n        }), i);\n      }\n      if (globalThis.__DEV__ !== false) {\n        assertSelectionSetForIdValue(context.store, field, item);\n      }\n      return item;\n    });\n    return {\n      result: context.canonizeResults ? this.canon.admit(array) : array,\n      missing: missing\n    };\n  };\n  return StoreReader;\n}();\nexport { StoreReader };\nfunction firstMissing(tree) {\n  try {\n    JSON.stringify(tree, function (_, value) {\n      if (typeof value === \"string\") throw value;\n      return value;\n    });\n  } catch (result) {\n    return result;\n  }\n}\nfunction assertSelectionSetForIdValue(store, field, fieldValue) {\n  if (!field.selectionSet) {\n    var workSet_1 = new Set([fieldValue]);\n    workSet_1.forEach(function (value) {\n      if (isNonNullObject(value)) {\n        invariant(!isReference(value), 11, getTypenameFromStoreObject(store, value), field.name.value);\n        Object.values(value).forEach(workSet_1.add, workSet_1);\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["__assign", "invariant", "newInvariantError", "Kind", "wrap", "isField", "resultKeyNameFromField", "isReference", "makeReference", "shouldInclude", "addTypenameToDocument", "getDefaultValues", "getMainDefinition", "getQueryDefinition", "getFragmentFromSelection", "maybeDeepFreeze", "mergeDeepArray", "DeepMerger", "isNonNullObject", "canUseWeakMap", "compact", "canonicalStringify", "cacheSizes", "maybeDependOnExistenceOfEntity", "supportsResultCaching", "isArray", "extractFragmentContext", "getTypenameFromStoreObject", "shouldCanonizeResults", "Missing<PERSON>ieldE<PERSON>r", "ObjectCanon", "execSelectionSetKeyArgs", "options", "selectionSet", "objectOrReference", "context", "canon<PERSON><PERSON><PERSON><PERSON><PERSON>", "StoreReader", "config", "_this", "knownResults", "WeakMap", "Map", "addTypename", "canon", "executeSelectionSet", "_a", "peekArgs", "other", "peek", "apply", "result", "admit", "store", "enclosingRef", "__ref", "execSelectionSetImpl", "max", "resultCacheMaxSize", "keyArgs", "make<PERSON><PERSON><PERSON><PERSON>", "parent", "varString", "executeSubSelectedArray", "execSubSelectedArrayImpl", "field", "array", "prototype", "resetCanon", "diffQueryAgainstStore", "query", "_b", "rootId", "variables", "_c", "returnPartialData", "_d", "policies", "cache", "rootRef", "execResult", "fragments", "missing", "firstMissing", "complete", "isFresh", "get", "latest", "isKnown", "rootTypenamesById", "has", "empty", "concat", "typename", "getFieldValue", "objectsToMerge", "<PERSON><PERSON><PERSON><PERSON>", "rootIdsByTypename", "push", "__typename", "handleMissing", "resultName", "merge", "workSet", "Set", "selections", "for<PERSON>ach", "selection", "fieldValue", "readField", "fieldName", "name", "value", "from", "added", "JSON", "stringify", "length", "pass", "fragment", "lookupFragment", "kind", "FRAGMENT_SPREAD", "fragmentMatches", "add", "finalResult", "frozen", "set", "childResult", "i", "filter", "canRead", "map", "item", "globalThis", "__DEV__", "assertSelectionSetForIdValue", "tree", "_", "workSet_1", "Object", "values"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/readFromStore.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { Kind } from \"graphql\";\nimport { wrap } from \"optimism\";\nimport { isField, resultKeyNameFromField, isReference, makeReference, shouldInclude, addTypenameToDocument, getDefaultValues, getMainDefinition, getQueryDefinition, getFragmentFromSelection, maybeDeepFreeze, mergeDeepArray, DeepMerger, isNonNullObject, canUseWeakMap, compact, canonicalStringify, cacheSizes, } from \"../../utilities/index.js\";\nimport { maybeDependOnExistenceOfEntity, supportsResultCaching, } from \"./entityStore.js\";\nimport { isArray, extractFragmentContext, getTypenameFromStoreObject, shouldCanonizeResults, } from \"./helpers.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { ObjectCanon } from \"./object-canon.js\";\nfunction execSelectionSetKeyArgs(options) {\n    return [\n        options.selectionSet,\n        options.objectOrReference,\n        options.context,\n        // We split out this property so we can pass different values\n        // independently without modifying options.context itself.\n        options.context.canonizeResults,\n    ];\n}\nvar StoreReader = /** @class */ (function () {\n    function StoreReader(config) {\n        var _this = this;\n        this.knownResults = new (canUseWeakMap ? WeakMap : Map)();\n        this.config = compact(config, {\n            addTypename: config.addTypename !== false,\n            canonizeResults: shouldCanonizeResults(config),\n        });\n        this.canon = config.canon || new ObjectCanon();\n        // memoized functions in this class will be \"garbage-collected\"\n        // by recreating the whole `StoreReader` in\n        // `InMemoryCache.resetResultsCache`\n        // (triggered from `InMemoryCache.gc` with `resetResultCache: true`)\n        this.executeSelectionSet = wrap(function (options) {\n            var _a;\n            var canonizeResults = options.context.canonizeResults;\n            var peekArgs = execSelectionSetKeyArgs(options);\n            // Negate this boolean option so we can find out if we've already read\n            // this result using the other boolean value.\n            peekArgs[3] = !canonizeResults;\n            var other = (_a = _this.executeSelectionSet).peek.apply(_a, peekArgs);\n            if (other) {\n                if (canonizeResults) {\n                    return __assign(__assign({}, other), { \n                        // If we previously read this result without canonizing it, we can\n                        // reuse that result simply by canonizing it now.\n                        result: _this.canon.admit(other.result) });\n                }\n                // If we previously read this result with canonization enabled, we can\n                // return that canonized result as-is.\n                return other;\n            }\n            maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n            // Finally, if we didn't find any useful previous results, run the real\n            // execSelectionSetImpl method with the given options.\n            return _this.execSelectionSetImpl(options);\n        }, {\n            max: this.config.resultCacheMaxSize ||\n                cacheSizes[\"inMemoryCache.executeSelectionSet\"] ||\n                50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n            keyArgs: execSelectionSetKeyArgs,\n            // Note that the parameters of makeCacheKey are determined by the\n            // array returned by keyArgs.\n            makeCacheKey: function (selectionSet, parent, context, canonizeResults) {\n                if (supportsResultCaching(context.store)) {\n                    return context.store.makeCacheKey(selectionSet, isReference(parent) ? parent.__ref : parent, context.varString, canonizeResults);\n                }\n            },\n        });\n        this.executeSubSelectedArray = wrap(function (options) {\n            maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n            return _this.execSubSelectedArrayImpl(options);\n        }, {\n            max: this.config.resultCacheMaxSize ||\n                cacheSizes[\"inMemoryCache.executeSubSelectedArray\"] ||\n                10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n            makeCacheKey: function (_a) {\n                var field = _a.field, array = _a.array, context = _a.context;\n                if (supportsResultCaching(context.store)) {\n                    return context.store.makeCacheKey(field, array, context.varString);\n                }\n            },\n        });\n    }\n    StoreReader.prototype.resetCanon = function () {\n        this.canon = new ObjectCanon();\n    };\n    /**\n     * Given a store and a query, return as much of the result as possible and\n     * identify if any data was missing from the store.\n     */\n    StoreReader.prototype.diffQueryAgainstStore = function (_a) {\n        var store = _a.store, query = _a.query, _b = _a.rootId, rootId = _b === void 0 ? \"ROOT_QUERY\" : _b, variables = _a.variables, _c = _a.returnPartialData, returnPartialData = _c === void 0 ? true : _c, _d = _a.canonizeResults, canonizeResults = _d === void 0 ? this.config.canonizeResults : _d;\n        var policies = this.config.cache.policies;\n        variables = __assign(__assign({}, getDefaultValues(getQueryDefinition(query))), variables);\n        var rootRef = makeReference(rootId);\n        var execResult = this.executeSelectionSet({\n            selectionSet: getMainDefinition(query).selectionSet,\n            objectOrReference: rootRef,\n            enclosingRef: rootRef,\n            context: __assign({ store: store, query: query, policies: policies, variables: variables, varString: canonicalStringify(variables), canonizeResults: canonizeResults }, extractFragmentContext(query, this.config.fragments)),\n        });\n        var missing;\n        if (execResult.missing) {\n            // For backwards compatibility we still report an array of\n            // MissingFieldError objects, even though there will only ever be at most\n            // one of them, now that all missing field error messages are grouped\n            // together in the execResult.missing tree.\n            missing = [\n                new MissingFieldError(firstMissing(execResult.missing), execResult.missing, query, variables),\n            ];\n            if (!returnPartialData) {\n                throw missing[0];\n            }\n        }\n        return {\n            result: execResult.result,\n            complete: !missing,\n            missing: missing,\n        };\n    };\n    StoreReader.prototype.isFresh = function (result, parent, selectionSet, context) {\n        if (supportsResultCaching(context.store) &&\n            this.knownResults.get(result) === selectionSet) {\n            var latest = this.executeSelectionSet.peek(selectionSet, parent, context, \n            // If result is canonical, then it could only have been previously\n            // cached by the canonizing version of executeSelectionSet, so we can\n            // avoid checking both possibilities here.\n            this.canon.isKnown(result));\n            if (latest && result === latest.result) {\n                return true;\n            }\n        }\n        return false;\n    };\n    // Uncached version of executeSelectionSet.\n    StoreReader.prototype.execSelectionSetImpl = function (_a) {\n        var _this = this;\n        var selectionSet = _a.selectionSet, objectOrReference = _a.objectOrReference, enclosingRef = _a.enclosingRef, context = _a.context;\n        if (isReference(objectOrReference) &&\n            !context.policies.rootTypenamesById[objectOrReference.__ref] &&\n            !context.store.has(objectOrReference.__ref)) {\n            return {\n                result: this.canon.empty,\n                missing: \"Dangling reference to missing \".concat(objectOrReference.__ref, \" object\"),\n            };\n        }\n        var variables = context.variables, policies = context.policies, store = context.store;\n        var typename = store.getFieldValue(objectOrReference, \"__typename\");\n        var objectsToMerge = [];\n        var missing;\n        var missingMerger = new DeepMerger();\n        if (this.config.addTypename &&\n            typeof typename === \"string\" &&\n            !policies.rootIdsByTypename[typename]) {\n            // Ensure we always include a default value for the __typename\n            // field, if we have one, and this.config.addTypename is true. Note\n            // that this field can be overridden by other merged objects.\n            objectsToMerge.push({ __typename: typename });\n        }\n        function handleMissing(result, resultName) {\n            var _a;\n            if (result.missing) {\n                missing = missingMerger.merge(missing, (_a = {},\n                    _a[resultName] = result.missing,\n                    _a));\n            }\n            return result.result;\n        }\n        var workSet = new Set(selectionSet.selections);\n        workSet.forEach(function (selection) {\n            var _a, _b;\n            // Omit fields with directives @skip(if: <truthy value>) or\n            // @include(if: <falsy value>).\n            if (!shouldInclude(selection, variables))\n                return;\n            if (isField(selection)) {\n                var fieldValue = policies.readField({\n                    fieldName: selection.name.value,\n                    field: selection,\n                    variables: context.variables,\n                    from: objectOrReference,\n                }, context);\n                var resultName = resultKeyNameFromField(selection);\n                if (fieldValue === void 0) {\n                    if (!addTypenameToDocument.added(selection)) {\n                        missing = missingMerger.merge(missing, (_a = {},\n                            _a[resultName] = \"Can't find field '\".concat(selection.name.value, \"' on \").concat(isReference(objectOrReference) ?\n                                objectOrReference.__ref + \" object\"\n                                : \"object \" + JSON.stringify(objectOrReference, null, 2)),\n                            _a));\n                    }\n                }\n                else if (isArray(fieldValue)) {\n                    if (fieldValue.length > 0) {\n                        fieldValue = handleMissing(_this.executeSubSelectedArray({\n                            field: selection,\n                            array: fieldValue,\n                            enclosingRef: enclosingRef,\n                            context: context,\n                        }), resultName);\n                    }\n                }\n                else if (!selection.selectionSet) {\n                    // If the field does not have a selection set, then we handle it\n                    // as a scalar value. To keep this.canon from canonicalizing\n                    // this value, we use this.canon.pass to wrap fieldValue in a\n                    // Pass object that this.canon.admit will later unwrap as-is.\n                    if (context.canonizeResults) {\n                        fieldValue = _this.canon.pass(fieldValue);\n                    }\n                }\n                else if (fieldValue != null) {\n                    // In this case, because we know the field has a selection set,\n                    // it must be trying to query a GraphQLObjectType, which is why\n                    // fieldValue must be != null.\n                    fieldValue = handleMissing(_this.executeSelectionSet({\n                        selectionSet: selection.selectionSet,\n                        objectOrReference: fieldValue,\n                        enclosingRef: isReference(fieldValue) ? fieldValue : enclosingRef,\n                        context: context,\n                    }), resultName);\n                }\n                if (fieldValue !== void 0) {\n                    objectsToMerge.push((_b = {}, _b[resultName] = fieldValue, _b));\n                }\n            }\n            else {\n                var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n                if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n                    throw newInvariantError(10, selection.name.value);\n                }\n                if (fragment && policies.fragmentMatches(fragment, typename)) {\n                    fragment.selectionSet.selections.forEach(workSet.add, workSet);\n                }\n            }\n        });\n        var result = mergeDeepArray(objectsToMerge);\n        var finalResult = { result: result, missing: missing };\n        var frozen = context.canonizeResults ?\n            this.canon.admit(finalResult)\n            // Since this.canon is normally responsible for freezing results (only in\n            // development), freeze them manually if canonization is disabled.\n            : maybeDeepFreeze(finalResult);\n        // Store this result with its selection set so that we can quickly\n        // recognize it again in the StoreReader#isFresh method.\n        if (frozen.result) {\n            this.knownResults.set(frozen.result, selectionSet);\n        }\n        return frozen;\n    };\n    // Uncached version of executeSubSelectedArray.\n    StoreReader.prototype.execSubSelectedArrayImpl = function (_a) {\n        var _this = this;\n        var field = _a.field, array = _a.array, enclosingRef = _a.enclosingRef, context = _a.context;\n        var missing;\n        var missingMerger = new DeepMerger();\n        function handleMissing(childResult, i) {\n            var _a;\n            if (childResult.missing) {\n                missing = missingMerger.merge(missing, (_a = {}, _a[i] = childResult.missing, _a));\n            }\n            return childResult.result;\n        }\n        if (field.selectionSet) {\n            array = array.filter(context.store.canRead);\n        }\n        array = array.map(function (item, i) {\n            // null value in array\n            if (item === null) {\n                return null;\n            }\n            // This is a nested array, recurse\n            if (isArray(item)) {\n                return handleMissing(_this.executeSubSelectedArray({\n                    field: field,\n                    array: item,\n                    enclosingRef: enclosingRef,\n                    context: context,\n                }), i);\n            }\n            // This is an object, run the selection set on it\n            if (field.selectionSet) {\n                return handleMissing(_this.executeSelectionSet({\n                    selectionSet: field.selectionSet,\n                    objectOrReference: item,\n                    enclosingRef: isReference(item) ? item : enclosingRef,\n                    context: context,\n                }), i);\n            }\n            if (globalThis.__DEV__ !== false) {\n                assertSelectionSetForIdValue(context.store, field, item);\n            }\n            return item;\n        });\n        return {\n            result: context.canonizeResults ? this.canon.admit(array) : array,\n            missing: missing,\n        };\n    };\n    return StoreReader;\n}());\nexport { StoreReader };\nfunction firstMissing(tree) {\n    try {\n        JSON.stringify(tree, function (_, value) {\n            if (typeof value === \"string\")\n                throw value;\n            return value;\n        });\n    }\n    catch (result) {\n        return result;\n    }\n}\nfunction assertSelectionSetForIdValue(store, field, fieldValue) {\n    if (!field.selectionSet) {\n        var workSet_1 = new Set([fieldValue]);\n        workSet_1.forEach(function (value) {\n            if (isNonNullObject(value)) {\n                invariant(\n                    !isReference(value),\n                    11,\n                    getTypenameFromStoreObject(store, value),\n                    field.name.value\n                );\n                Object.values(value).forEach(workSet_1.add, workSet_1);\n            }\n        });\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,kCAAkC;AAC/E,SAASC,IAAI,QAAQ,SAAS;AAC9B,SAASC,IAAI,QAAQ,UAAU;AAC/B,SAASC,OAAO,EAAEC,sBAAsB,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,UAAU,EAAEC,eAAe,EAAEC,aAAa,EAAEC,OAAO,EAAEC,kBAAkB,EAAEC,UAAU,QAAS,0BAA0B;AACtV,SAASC,8BAA8B,EAAEC,qBAAqB,QAAS,kBAAkB;AACzF,SAASC,OAAO,EAAEC,sBAAsB,EAAEC,0BAA0B,EAAEC,qBAAqB,QAAS,cAAc;AAClH,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EACtC,OAAO,CACHA,OAAO,CAACC,YAAY,EACpBD,OAAO,CAACE,iBAAiB,EACzBF,OAAO,CAACG,OAAO;EACf;EACA;EACAH,OAAO,CAACG,OAAO,CAACC,eAAe,CAClC;AACL;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACC,MAAM,EAAE;IACzB,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,YAAY,GAAG,KAAKrB,aAAa,GAAGsB,OAAO,GAAGC,GAAG,EAAE,CAAC;IACzD,IAAI,CAACJ,MAAM,GAAGlB,OAAO,CAACkB,MAAM,EAAE;MAC1BK,WAAW,EAAEL,MAAM,CAACK,WAAW,KAAK,KAAK;MACzCP,eAAe,EAAER,qBAAqB,CAACU,MAAM;IACjD,CAAC,CAAC;IACF,IAAI,CAACM,KAAK,GAAGN,MAAM,CAACM,KAAK,IAAI,IAAId,WAAW,CAAC,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI,CAACe,mBAAmB,GAAGzC,IAAI,CAAC,UAAU4B,OAAO,EAAE;MAC/C,IAAIc,EAAE;MACN,IAAIV,eAAe,GAAGJ,OAAO,CAACG,OAAO,CAACC,eAAe;MACrD,IAAIW,QAAQ,GAAGhB,uBAAuB,CAACC,OAAO,CAAC;MAC/C;MACA;MACAe,QAAQ,CAAC,CAAC,CAAC,GAAG,CAACX,eAAe;MAC9B,IAAIY,KAAK,GAAG,CAACF,EAAE,GAAGP,KAAK,CAACM,mBAAmB,EAAEI,IAAI,CAACC,KAAK,CAACJ,EAAE,EAAEC,QAAQ,CAAC;MACrE,IAAIC,KAAK,EAAE;QACP,IAAIZ,eAAe,EAAE;UACjB,OAAOpC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,CAAC,EAAE;YACjC;YACA;YACAG,MAAM,EAAEZ,KAAK,CAACK,KAAK,CAACQ,KAAK,CAACJ,KAAK,CAACG,MAAM;UAAE,CAAC,CAAC;QAClD;QACA;QACA;QACA,OAAOH,KAAK;MAChB;MACAzB,8BAA8B,CAACS,OAAO,CAACG,OAAO,CAACkB,KAAK,EAAErB,OAAO,CAACsB,YAAY,CAACC,KAAK,CAAC;MACjF;MACA;MACA,OAAOhB,KAAK,CAACiB,oBAAoB,CAACxB,OAAO,CAAC;IAC9C,CAAC,EAAE;MACCyB,GAAG,EAAE,IAAI,CAACnB,MAAM,CAACoB,kBAAkB,IAC/BpC,UAAU,CAAC,mCAAmC,CAAC,IAC/C,KAAK,CAAC;MACVqC,OAAO,EAAE5B,uBAAuB;MAChC;MACA;MACA6B,YAAY,EAAE,SAAAA,CAAU3B,YAAY,EAAE4B,MAAM,EAAE1B,OAAO,EAAEC,eAAe,EAAE;QACpE,IAAIZ,qBAAqB,CAACW,OAAO,CAACkB,KAAK,CAAC,EAAE;UACtC,OAAOlB,OAAO,CAACkB,KAAK,CAACO,YAAY,CAAC3B,YAAY,EAAE1B,WAAW,CAACsD,MAAM,CAAC,GAAGA,MAAM,CAACN,KAAK,GAAGM,MAAM,EAAE1B,OAAO,CAAC2B,SAAS,EAAE1B,eAAe,CAAC;QACpI;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAAC2B,uBAAuB,GAAG3D,IAAI,CAAC,UAAU4B,OAAO,EAAE;MACnDT,8BAA8B,CAACS,OAAO,CAACG,OAAO,CAACkB,KAAK,EAAErB,OAAO,CAACsB,YAAY,CAACC,KAAK,CAAC;MACjF,OAAOhB,KAAK,CAACyB,wBAAwB,CAAChC,OAAO,CAAC;IAClD,CAAC,EAAE;MACCyB,GAAG,EAAE,IAAI,CAACnB,MAAM,CAACoB,kBAAkB,IAC/BpC,UAAU,CAAC,uCAAuC,CAAC,IACnD,KAAK,CAAC;MACVsC,YAAY,EAAE,SAAAA,CAAUd,EAAE,EAAE;QACxB,IAAImB,KAAK,GAAGnB,EAAE,CAACmB,KAAK;UAAEC,KAAK,GAAGpB,EAAE,CAACoB,KAAK;UAAE/B,OAAO,GAAGW,EAAE,CAACX,OAAO;QAC5D,IAAIX,qBAAqB,CAACW,OAAO,CAACkB,KAAK,CAAC,EAAE;UACtC,OAAOlB,OAAO,CAACkB,KAAK,CAACO,YAAY,CAACK,KAAK,EAAEC,KAAK,EAAE/B,OAAO,CAAC2B,SAAS,CAAC;QACtE;MACJ;IACJ,CAAC,CAAC;EACN;EACAzB,WAAW,CAAC8B,SAAS,CAACC,UAAU,GAAG,YAAY;IAC3C,IAAI,CAACxB,KAAK,GAAG,IAAId,WAAW,CAAC,CAAC;EAClC,CAAC;EACD;AACJ;AACA;AACA;EACIO,WAAW,CAAC8B,SAAS,CAACE,qBAAqB,GAAG,UAAUvB,EAAE,EAAE;IACxD,IAAIO,KAAK,GAAGP,EAAE,CAACO,KAAK;MAAEiB,KAAK,GAAGxB,EAAE,CAACwB,KAAK;MAAEC,EAAE,GAAGzB,EAAE,CAAC0B,MAAM;MAAEA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,EAAE;MAAEE,SAAS,GAAG3B,EAAE,CAAC2B,SAAS;MAAEC,EAAE,GAAG5B,EAAE,CAAC6B,iBAAiB;MAAEA,iBAAiB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEE,EAAE,GAAG9B,EAAE,CAACV,eAAe;MAAEA,eAAe,GAAGwC,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,CAACtC,MAAM,CAACF,eAAe,GAAGwC,EAAE;IACnS,IAAIC,QAAQ,GAAG,IAAI,CAACvC,MAAM,CAACwC,KAAK,CAACD,QAAQ;IACzCJ,SAAS,GAAGzE,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEW,gBAAgB,CAACE,kBAAkB,CAACyD,KAAK,CAAC,CAAC,CAAC,EAAEG,SAAS,CAAC;IAC1F,IAAIM,OAAO,GAAGvE,aAAa,CAACgE,MAAM,CAAC;IACnC,IAAIQ,UAAU,GAAG,IAAI,CAACnC,mBAAmB,CAAC;MACtCZ,YAAY,EAAErB,iBAAiB,CAAC0D,KAAK,CAAC,CAACrC,YAAY;MACnDC,iBAAiB,EAAE6C,OAAO;MAC1BzB,YAAY,EAAEyB,OAAO;MACrB5C,OAAO,EAAEnC,QAAQ,CAAC;QAAEqD,KAAK,EAAEA,KAAK;QAAEiB,KAAK,EAAEA,KAAK;QAAEO,QAAQ,EAAEA,QAAQ;QAAEJ,SAAS,EAAEA,SAAS;QAAEX,SAAS,EAAEzC,kBAAkB,CAACoD,SAAS,CAAC;QAAErC,eAAe,EAAEA;MAAgB,CAAC,EAAEV,sBAAsB,CAAC4C,KAAK,EAAE,IAAI,CAAChC,MAAM,CAAC2C,SAAS,CAAC;IAChO,CAAC,CAAC;IACF,IAAIC,OAAO;IACX,IAAIF,UAAU,CAACE,OAAO,EAAE;MACpB;MACA;MACA;MACA;MACAA,OAAO,GAAG,CACN,IAAIrD,iBAAiB,CAACsD,YAAY,CAACH,UAAU,CAACE,OAAO,CAAC,EAAEF,UAAU,CAACE,OAAO,EAAEZ,KAAK,EAAEG,SAAS,CAAC,CAChG;MACD,IAAI,CAACE,iBAAiB,EAAE;QACpB,MAAMO,OAAO,CAAC,CAAC,CAAC;MACpB;IACJ;IACA,OAAO;MACH/B,MAAM,EAAE6B,UAAU,CAAC7B,MAAM;MACzBiC,QAAQ,EAAE,CAACF,OAAO;MAClBA,OAAO,EAAEA;IACb,CAAC;EACL,CAAC;EACD7C,WAAW,CAAC8B,SAAS,CAACkB,OAAO,GAAG,UAAUlC,MAAM,EAAEU,MAAM,EAAE5B,YAAY,EAAEE,OAAO,EAAE;IAC7E,IAAIX,qBAAqB,CAACW,OAAO,CAACkB,KAAK,CAAC,IACpC,IAAI,CAACb,YAAY,CAAC8C,GAAG,CAACnC,MAAM,CAAC,KAAKlB,YAAY,EAAE;MAChD,IAAIsD,MAAM,GAAG,IAAI,CAAC1C,mBAAmB,CAACI,IAAI,CAAChB,YAAY,EAAE4B,MAAM,EAAE1B,OAAO;MACxE;MACA;MACA;MACA,IAAI,CAACS,KAAK,CAAC4C,OAAO,CAACrC,MAAM,CAAC,CAAC;MAC3B,IAAIoC,MAAM,IAAIpC,MAAM,KAAKoC,MAAM,CAACpC,MAAM,EAAE;QACpC,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACD;EACAd,WAAW,CAAC8B,SAAS,CAACX,oBAAoB,GAAG,UAAUV,EAAE,EAAE;IACvD,IAAIP,KAAK,GAAG,IAAI;IAChB,IAAIN,YAAY,GAAGa,EAAE,CAACb,YAAY;MAAEC,iBAAiB,GAAGY,EAAE,CAACZ,iBAAiB;MAAEoB,YAAY,GAAGR,EAAE,CAACQ,YAAY;MAAEnB,OAAO,GAAGW,EAAE,CAACX,OAAO;IAClI,IAAI5B,WAAW,CAAC2B,iBAAiB,CAAC,IAC9B,CAACC,OAAO,CAAC0C,QAAQ,CAACY,iBAAiB,CAACvD,iBAAiB,CAACqB,KAAK,CAAC,IAC5D,CAACpB,OAAO,CAACkB,KAAK,CAACqC,GAAG,CAACxD,iBAAiB,CAACqB,KAAK,CAAC,EAAE;MAC7C,OAAO;QACHJ,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC+C,KAAK;QACxBT,OAAO,EAAE,gCAAgC,CAACU,MAAM,CAAC1D,iBAAiB,CAACqB,KAAK,EAAE,SAAS;MACvF,CAAC;IACL;IACA,IAAIkB,SAAS,GAAGtC,OAAO,CAACsC,SAAS;MAAEI,QAAQ,GAAG1C,OAAO,CAAC0C,QAAQ;MAAExB,KAAK,GAAGlB,OAAO,CAACkB,KAAK;IACrF,IAAIwC,QAAQ,GAAGxC,KAAK,CAACyC,aAAa,CAAC5D,iBAAiB,EAAE,YAAY,CAAC;IACnE,IAAI6D,cAAc,GAAG,EAAE;IACvB,IAAIb,OAAO;IACX,IAAIc,aAAa,GAAG,IAAI/E,UAAU,CAAC,CAAC;IACpC,IAAI,IAAI,CAACqB,MAAM,CAACK,WAAW,IACvB,OAAOkD,QAAQ,KAAK,QAAQ,IAC5B,CAAChB,QAAQ,CAACoB,iBAAiB,CAACJ,QAAQ,CAAC,EAAE;MACvC;MACA;MACA;MACAE,cAAc,CAACG,IAAI,CAAC;QAAEC,UAAU,EAAEN;MAAS,CAAC,CAAC;IACjD;IACA,SAASO,aAAaA,CAACjD,MAAM,EAAEkD,UAAU,EAAE;MACvC,IAAIvD,EAAE;MACN,IAAIK,MAAM,CAAC+B,OAAO,EAAE;QAChBA,OAAO,GAAGc,aAAa,CAACM,KAAK,CAACpB,OAAO,GAAGpC,EAAE,GAAG,CAAC,CAAC,EAC3CA,EAAE,CAACuD,UAAU,CAAC,GAAGlD,MAAM,CAAC+B,OAAO,EAC/BpC,EAAE,CAAC,CAAC;MACZ;MACA,OAAOK,MAAM,CAACA,MAAM;IACxB;IACA,IAAIoD,OAAO,GAAG,IAAIC,GAAG,CAACvE,YAAY,CAACwE,UAAU,CAAC;IAC9CF,OAAO,CAACG,OAAO,CAAC,UAAUC,SAAS,EAAE;MACjC,IAAI7D,EAAE,EAAEyB,EAAE;MACV;MACA;MACA,IAAI,CAAC9D,aAAa,CAACkG,SAAS,EAAElC,SAAS,CAAC,EACpC;MACJ,IAAIpE,OAAO,CAACsG,SAAS,CAAC,EAAE;QACpB,IAAIC,UAAU,GAAG/B,QAAQ,CAACgC,SAAS,CAAC;UAChCC,SAAS,EAAEH,SAAS,CAACI,IAAI,CAACC,KAAK;UAC/B/C,KAAK,EAAE0C,SAAS;UAChBlC,SAAS,EAAEtC,OAAO,CAACsC,SAAS;UAC5BwC,IAAI,EAAE/E;QACV,CAAC,EAAEC,OAAO,CAAC;QACX,IAAIkE,UAAU,GAAG/F,sBAAsB,CAACqG,SAAS,CAAC;QAClD,IAAIC,UAAU,KAAK,KAAK,CAAC,EAAE;UACvB,IAAI,CAAClG,qBAAqB,CAACwG,KAAK,CAACP,SAAS,CAAC,EAAE;YACzCzB,OAAO,GAAGc,aAAa,CAACM,KAAK,CAACpB,OAAO,GAAGpC,EAAE,GAAG,CAAC,CAAC,EAC3CA,EAAE,CAACuD,UAAU,CAAC,GAAG,oBAAoB,CAACT,MAAM,CAACe,SAAS,CAACI,IAAI,CAACC,KAAK,EAAE,OAAO,CAAC,CAACpB,MAAM,CAACrF,WAAW,CAAC2B,iBAAiB,CAAC,GAC7GA,iBAAiB,CAACqB,KAAK,GAAG,SAAS,GACjC,SAAS,GAAG4D,IAAI,CAACC,SAAS,CAAClF,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAC7DY,EAAE,CAAC,CAAC;UACZ;QACJ,CAAC,MACI,IAAIrB,OAAO,CAACmF,UAAU,CAAC,EAAE;UAC1B,IAAIA,UAAU,CAACS,MAAM,GAAG,CAAC,EAAE;YACvBT,UAAU,GAAGR,aAAa,CAAC7D,KAAK,CAACwB,uBAAuB,CAAC;cACrDE,KAAK,EAAE0C,SAAS;cAChBzC,KAAK,EAAE0C,UAAU;cACjBtD,YAAY,EAAEA,YAAY;cAC1BnB,OAAO,EAAEA;YACb,CAAC,CAAC,EAAEkE,UAAU,CAAC;UACnB;QACJ,CAAC,MACI,IAAI,CAACM,SAAS,CAAC1E,YAAY,EAAE;UAC9B;UACA;UACA;UACA;UACA,IAAIE,OAAO,CAACC,eAAe,EAAE;YACzBwE,UAAU,GAAGrE,KAAK,CAACK,KAAK,CAAC0E,IAAI,CAACV,UAAU,CAAC;UAC7C;QACJ,CAAC,MACI,IAAIA,UAAU,IAAI,IAAI,EAAE;UACzB;UACA;UACA;UACAA,UAAU,GAAGR,aAAa,CAAC7D,KAAK,CAACM,mBAAmB,CAAC;YACjDZ,YAAY,EAAE0E,SAAS,CAAC1E,YAAY;YACpCC,iBAAiB,EAAE0E,UAAU;YAC7BtD,YAAY,EAAE/C,WAAW,CAACqG,UAAU,CAAC,GAAGA,UAAU,GAAGtD,YAAY;YACjEnB,OAAO,EAAEA;UACb,CAAC,CAAC,EAAEkE,UAAU,CAAC;QACnB;QACA,IAAIO,UAAU,KAAK,KAAK,CAAC,EAAE;UACvBb,cAAc,CAACG,IAAI,EAAE3B,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAAC8B,UAAU,CAAC,GAAGO,UAAU,EAAErC,EAAE,CAAC,CAAC;QACnE;MACJ,CAAC,MACI;QACD,IAAIgD,QAAQ,GAAGzG,wBAAwB,CAAC6F,SAAS,EAAExE,OAAO,CAACqF,cAAc,CAAC;QAC1E,IAAI,CAACD,QAAQ,IAAIZ,SAAS,CAACc,IAAI,KAAKtH,IAAI,CAACuH,eAAe,EAAE;UACtD,MAAMxH,iBAAiB,CAAC,EAAE,EAAEyG,SAAS,CAACI,IAAI,CAACC,KAAK,CAAC;QACrD;QACA,IAAIO,QAAQ,IAAI1C,QAAQ,CAAC8C,eAAe,CAACJ,QAAQ,EAAE1B,QAAQ,CAAC,EAAE;UAC1D0B,QAAQ,CAACtF,YAAY,CAACwE,UAAU,CAACC,OAAO,CAACH,OAAO,CAACqB,GAAG,EAAErB,OAAO,CAAC;QAClE;MACJ;IACJ,CAAC,CAAC;IACF,IAAIpD,MAAM,GAAGnC,cAAc,CAAC+E,cAAc,CAAC;IAC3C,IAAI8B,WAAW,GAAG;MAAE1E,MAAM,EAAEA,MAAM;MAAE+B,OAAO,EAAEA;IAAQ,CAAC;IACtD,IAAI4C,MAAM,GAAG3F,OAAO,CAACC,eAAe,GAChC,IAAI,CAACQ,KAAK,CAACQ,KAAK,CAACyE,WAAW;IAC5B;IACA;IAAA,EACE9G,eAAe,CAAC8G,WAAW,CAAC;IAClC;IACA;IACA,IAAIC,MAAM,CAAC3E,MAAM,EAAE;MACf,IAAI,CAACX,YAAY,CAACuF,GAAG,CAACD,MAAM,CAAC3E,MAAM,EAAElB,YAAY,CAAC;IACtD;IACA,OAAO6F,MAAM;EACjB,CAAC;EACD;EACAzF,WAAW,CAAC8B,SAAS,CAACH,wBAAwB,GAAG,UAAUlB,EAAE,EAAE;IAC3D,IAAIP,KAAK,GAAG,IAAI;IAChB,IAAI0B,KAAK,GAAGnB,EAAE,CAACmB,KAAK;MAAEC,KAAK,GAAGpB,EAAE,CAACoB,KAAK;MAAEZ,YAAY,GAAGR,EAAE,CAACQ,YAAY;MAAEnB,OAAO,GAAGW,EAAE,CAACX,OAAO;IAC5F,IAAI+C,OAAO;IACX,IAAIc,aAAa,GAAG,IAAI/E,UAAU,CAAC,CAAC;IACpC,SAASmF,aAAaA,CAAC4B,WAAW,EAAEC,CAAC,EAAE;MACnC,IAAInF,EAAE;MACN,IAAIkF,WAAW,CAAC9C,OAAO,EAAE;QACrBA,OAAO,GAAGc,aAAa,CAACM,KAAK,CAACpB,OAAO,GAAGpC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACmF,CAAC,CAAC,GAAGD,WAAW,CAAC9C,OAAO,EAAEpC,EAAE,CAAC,CAAC;MACtF;MACA,OAAOkF,WAAW,CAAC7E,MAAM;IAC7B;IACA,IAAIc,KAAK,CAAChC,YAAY,EAAE;MACpBiC,KAAK,GAAGA,KAAK,CAACgE,MAAM,CAAC/F,OAAO,CAACkB,KAAK,CAAC8E,OAAO,CAAC;IAC/C;IACAjE,KAAK,GAAGA,KAAK,CAACkE,GAAG,CAAC,UAAUC,IAAI,EAAEJ,CAAC,EAAE;MACjC;MACA,IAAII,IAAI,KAAK,IAAI,EAAE;QACf,OAAO,IAAI;MACf;MACA;MACA,IAAI5G,OAAO,CAAC4G,IAAI,CAAC,EAAE;QACf,OAAOjC,aAAa,CAAC7D,KAAK,CAACwB,uBAAuB,CAAC;UAC/CE,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEmE,IAAI;UACX/E,YAAY,EAAEA,YAAY;UAC1BnB,OAAO,EAAEA;QACb,CAAC,CAAC,EAAE8F,CAAC,CAAC;MACV;MACA;MACA,IAAIhE,KAAK,CAAChC,YAAY,EAAE;QACpB,OAAOmE,aAAa,CAAC7D,KAAK,CAACM,mBAAmB,CAAC;UAC3CZ,YAAY,EAAEgC,KAAK,CAAChC,YAAY;UAChCC,iBAAiB,EAAEmG,IAAI;UACvB/E,YAAY,EAAE/C,WAAW,CAAC8H,IAAI,CAAC,GAAGA,IAAI,GAAG/E,YAAY;UACrDnB,OAAO,EAAEA;QACb,CAAC,CAAC,EAAE8F,CAAC,CAAC;MACV;MACA,IAAIK,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;QAC9BC,4BAA4B,CAACrG,OAAO,CAACkB,KAAK,EAAEY,KAAK,EAAEoE,IAAI,CAAC;MAC5D;MACA,OAAOA,IAAI;IACf,CAAC,CAAC;IACF,OAAO;MACHlF,MAAM,EAAEhB,OAAO,CAACC,eAAe,GAAG,IAAI,CAACQ,KAAK,CAACQ,KAAK,CAACc,KAAK,CAAC,GAAGA,KAAK;MACjEgB,OAAO,EAAEA;IACb,CAAC;EACL,CAAC;EACD,OAAO7C,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,SAAS8C,YAAYA,CAACsD,IAAI,EAAE;EACxB,IAAI;IACAtB,IAAI,CAACC,SAAS,CAACqB,IAAI,EAAE,UAAUC,CAAC,EAAE1B,KAAK,EAAE;MACrC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACzB,MAAMA,KAAK;MACf,OAAOA,KAAK;IAChB,CAAC,CAAC;EACN,CAAC,CACD,OAAO7D,MAAM,EAAE;IACX,OAAOA,MAAM;EACjB;AACJ;AACA,SAASqF,4BAA4BA,CAACnF,KAAK,EAAEY,KAAK,EAAE2C,UAAU,EAAE;EAC5D,IAAI,CAAC3C,KAAK,CAAChC,YAAY,EAAE;IACrB,IAAI0G,SAAS,GAAG,IAAInC,GAAG,CAAC,CAACI,UAAU,CAAC,CAAC;IACrC+B,SAAS,CAACjC,OAAO,CAAC,UAAUM,KAAK,EAAE;MAC/B,IAAI9F,eAAe,CAAC8F,KAAK,CAAC,EAAE;QACxB/G,SAAS,CACL,CAACM,WAAW,CAACyG,KAAK,CAAC,EACnB,EAAE,EACFrF,0BAA0B,CAAC0B,KAAK,EAAE2D,KAAK,CAAC,EACxC/C,KAAK,CAAC8C,IAAI,CAACC,KACf,CAAC;QACD4B,MAAM,CAACC,MAAM,CAAC7B,KAAK,CAAC,CAACN,OAAO,CAACiC,SAAS,CAACf,GAAG,EAAEe,SAAS,CAAC;MAC1D;IACJ,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}