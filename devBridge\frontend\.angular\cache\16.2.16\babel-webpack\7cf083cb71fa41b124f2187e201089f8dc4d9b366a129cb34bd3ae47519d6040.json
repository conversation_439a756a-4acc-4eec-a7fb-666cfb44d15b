{"ast": null, "code": "import { syntaxError } from '../error/syntaxError.mjs';\nimport { Location, OperationTypeNode } from './ast.mjs';\nimport { DirectiveLocation } from './directiveLocation.mjs';\nimport { Kind } from './kinds.mjs';\nimport { isPunctuatorTokenKind, Lexer } from './lexer.mjs';\nimport { isSource, Source } from './source.mjs';\nimport { TokenKind } from './tokenKind.mjs';\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nexport function parse(source, options) {\n  const parser = new Parser(source, options);\n  return parser.parseDocument();\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nexport function parseValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const value = parser.parseValueLiteral(false);\n  parser.expectToken(TokenKind.EOF);\n  return value;\n}\n/**\n * Similar to parseValue(), but raises a parse error if it encounters a\n * variable. The return type will be a constant value.\n */\n\nexport function parseConstValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const value = parser.parseConstValueLiteral();\n  parser.expectToken(TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nexport function parseType(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const type = parser.parseTypeReference();\n  parser.expectToken(TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nexport class Parser {\n  constructor(source, options = {}) {\n    const sourceObj = isSource(source) ? source : new Source(source);\n    this._lexer = new Lexer(sourceObj);\n    this._options = options;\n    this._tokenCounter = 0;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n  parseName() {\n    const token = this.expectToken(TokenKind.NAME);\n    return this.node(token, {\n      kind: Kind.NAME,\n      value: token.value\n    });\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n\n  parseDocument() {\n    return this.node(this._lexer.token, {\n      kind: Kind.DOCUMENT,\n      definitions: this.many(TokenKind.SOF, this.parseDefinition, TokenKind.EOF)\n    });\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   *\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n\n  parseDefinition() {\n    if (this.peek(TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } // Many definitions begin with a description and require a lookahead.\n\n    const hasDescription = this.peekDescription();\n    const keywordToken = hasDescription ? this._lexer.lookahead() : this._lexer.token;\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n        case 'type':\n          return this.parseObjectTypeDefinition();\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n        case 'union':\n          return this.parseUnionTypeDefinition();\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n      if (hasDescription) {\n        throw syntaxError(this._lexer.source, this._lexer.token.start, 'Unexpected description, descriptions are supported only on type definitions.');\n      }\n      switch (keywordToken.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n        case 'fragment':\n          return this.parseFragmentDefinition();\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    }\n    throw this.unexpected(keywordToken);\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n\n  parseOperationDefinition() {\n    const start = this._lexer.token;\n    if (this.peek(TokenKind.BRACE_L)) {\n      return this.node(start, {\n        kind: Kind.OPERATION_DEFINITION,\n        operation: OperationTypeNode.QUERY,\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet()\n      });\n    }\n    const operation = this.parseOperationType();\n    let name;\n    if (this.peek(TokenKind.NAME)) {\n      name = this.parseName();\n    }\n    return this.node(start, {\n      kind: Kind.OPERATION_DEFINITION,\n      operation,\n      name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet()\n    });\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n\n  parseOperationType() {\n    const operationToken = this.expectToken(TokenKind.NAME);\n    switch (operationToken.value) {\n      case 'query':\n        return OperationTypeNode.QUERY;\n      case 'mutation':\n        return OperationTypeNode.MUTATION;\n      case 'subscription':\n        return OperationTypeNode.SUBSCRIPTION;\n    }\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n\n  parseVariableDefinitions() {\n    return this.optionalMany(TokenKind.PAREN_L, this.parseVariableDefinition, TokenKind.PAREN_R);\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n\n  parseVariableDefinition() {\n    return this.node(this._lexer.token, {\n      kind: Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(TokenKind.EQUALS) ? this.parseConstValueLiteral() : undefined,\n      directives: this.parseConstDirectives()\n    });\n  }\n  /**\n   * Variable : $ Name\n   */\n\n  parseVariable() {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.DOLLAR);\n    return this.node(start, {\n      kind: Kind.VARIABLE,\n      name: this.parseName()\n    });\n  }\n  /**\n   * ```\n   * SelectionSet : { Selection+ }\n   * ```\n   */\n\n  parseSelectionSet() {\n    return this.node(this._lexer.token, {\n      kind: Kind.SELECTION_SET,\n      selections: this.many(TokenKind.BRACE_L, this.parseSelection, TokenKind.BRACE_R)\n    });\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n\n  parseSelection() {\n    return this.peek(TokenKind.SPREAD) ? this.parseFragment() : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n\n  parseField() {\n    const start = this._lexer.token;\n    const nameOrAlias = this.parseName();\n    let alias;\n    let name;\n    if (this.expectOptionalToken(TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n    return this.node(start, {\n      kind: Kind.FIELD,\n      alias,\n      name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(TokenKind.BRACE_L) ? this.parseSelectionSet() : undefined\n    });\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n\n  parseArguments(isConst) {\n    const item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(TokenKind.PAREN_L, item, TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n\n  parseArgument(isConst = false) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return this.node(start, {\n      kind: Kind.ARGUMENT,\n      name,\n      value: this.parseValueLiteral(isConst)\n    });\n  }\n  parseConstArgument() {\n    return this.parseArgument(true);\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n\n  parseFragment() {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.SPREAD);\n    const hasTypeCondition = this.expectOptionalKeyword('on');\n    if (!hasTypeCondition && this.peek(TokenKind.NAME)) {\n      return this.node(start, {\n        kind: Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false)\n      });\n    }\n    return this.node(start, {\n      kind: Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet()\n    });\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n\n  parseFragmentDefinition() {\n    const start = this._lexer.token;\n    this.expectKeyword('fragment'); // Legacy support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (this._options.allowLegacyFragmentVariables === true) {\n      return this.node(start, {\n        kind: Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet()\n      });\n    }\n    return this.node(start, {\n      kind: Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet()\n    });\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n\n  parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseValueLiteral(isConst) {\n    const token = this._lexer.token;\n    switch (token.kind) {\n      case TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n      case TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n      case TokenKind.INT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: Kind.INT,\n          value: token.value\n        });\n      case TokenKind.FLOAT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: Kind.FLOAT,\n          value: token.value\n        });\n      case TokenKind.STRING:\n      case TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n      case TokenKind.NAME:\n        this.advanceLexer();\n        switch (token.value) {\n          case 'true':\n            return this.node(token, {\n              kind: Kind.BOOLEAN,\n              value: true\n            });\n          case 'false':\n            return this.node(token, {\n              kind: Kind.BOOLEAN,\n              value: false\n            });\n          case 'null':\n            return this.node(token, {\n              kind: Kind.NULL\n            });\n          default:\n            return this.node(token, {\n              kind: Kind.ENUM,\n              value: token.value\n            });\n        }\n      case TokenKind.DOLLAR:\n        if (isConst) {\n          this.expectToken(TokenKind.DOLLAR);\n          if (this._lexer.token.kind === TokenKind.NAME) {\n            const varName = this._lexer.token.value;\n            throw syntaxError(this._lexer.source, token.start, `Unexpected variable \"$${varName}\" in constant value.`);\n          } else {\n            throw this.unexpected(token);\n          }\n        }\n        return this.parseVariable();\n      default:\n        throw this.unexpected();\n    }\n  }\n  parseConstValueLiteral() {\n    return this.parseValueLiteral(true);\n  }\n  parseStringLiteral() {\n    const token = this._lexer.token;\n    this.advanceLexer();\n    return this.node(token, {\n      kind: Kind.STRING,\n      value: token.value,\n      block: token.kind === TokenKind.BLOCK_STRING\n    });\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n\n  parseList(isConst) {\n    const item = () => this.parseValueLiteral(isConst);\n    return this.node(this._lexer.token, {\n      kind: Kind.LIST,\n      values: this.any(TokenKind.BRACKET_L, item, TokenKind.BRACKET_R)\n    });\n  }\n  /**\n   * ```\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   * ```\n   */\n\n  parseObject(isConst) {\n    const item = () => this.parseObjectField(isConst);\n    return this.node(this._lexer.token, {\n      kind: Kind.OBJECT,\n      fields: this.any(TokenKind.BRACE_L, item, TokenKind.BRACE_R)\n    });\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n\n  parseObjectField(isConst) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return this.node(start, {\n      kind: Kind.OBJECT_FIELD,\n      name,\n      value: this.parseValueLiteral(isConst)\n    });\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n\n  parseDirectives(isConst) {\n    const directives = [];\n    while (this.peek(TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n    return directives;\n  }\n  parseConstDirectives() {\n    return this.parseDirectives(true);\n  }\n  /**\n   * ```\n   * Directive[Const] : @ Name Arguments[?Const]?\n   * ```\n   */\n\n  parseDirective(isConst) {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.AT);\n    return this.node(start, {\n      kind: Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst)\n    });\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n\n  parseTypeReference() {\n    const start = this._lexer.token;\n    let type;\n    if (this.expectOptionalToken(TokenKind.BRACKET_L)) {\n      const innerType = this.parseTypeReference();\n      this.expectToken(TokenKind.BRACKET_R);\n      type = this.node(start, {\n        kind: Kind.LIST_TYPE,\n        type: innerType\n      });\n    } else {\n      type = this.parseNamedType();\n    }\n    if (this.expectOptionalToken(TokenKind.BANG)) {\n      return this.node(start, {\n        kind: Kind.NON_NULL_TYPE,\n        type\n      });\n    }\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n\n  parseNamedType() {\n    return this.node(this._lexer.token, {\n      kind: Kind.NAMED_TYPE,\n      name: this.parseName()\n    });\n  } // Implements the parsing rules in the Type Definition section.\n\n  peekDescription() {\n    return this.peek(TokenKind.STRING) || this.peek(TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n\n  parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * ```\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   * ```\n   */\n\n  parseSchemaDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.many(TokenKind.BRACE_L, this.parseOperationTypeDefinition, TokenKind.BRACE_R);\n    return this.node(start, {\n      kind: Kind.SCHEMA_DEFINITION,\n      description,\n      directives,\n      operationTypes\n    });\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n\n  parseOperationTypeDefinition() {\n    const start = this._lexer.token;\n    const operation = this.parseOperationType();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseNamedType();\n    return this.node(start, {\n      kind: Kind.OPERATION_TYPE_DEFINITION,\n      operation,\n      type\n    });\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n\n  parseScalarTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.SCALAR_TYPE_DEFINITION,\n      description,\n      name,\n      directives\n    });\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n\n  parseObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n\n  parseImplementsInterfaces() {\n    return this.expectOptionalKeyword('implements') ? this.delimitedMany(TokenKind.AMP, this.parseNamedType) : [];\n  }\n  /**\n   * ```\n   * FieldsDefinition : { FieldDefinition+ }\n   * ```\n   */\n\n  parseFieldsDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseFieldDefinition, TokenKind.BRACE_R);\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n\n  parseFieldDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseTypeReference();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.FIELD_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      type,\n      directives\n    });\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n\n  parseArgumentDefs() {\n    return this.optionalMany(TokenKind.PAREN_L, this.parseInputValueDef, TokenKind.PAREN_R);\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n\n  parseInputValueDef() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseTypeReference();\n    let defaultValue;\n    if (this.expectOptionalToken(TokenKind.EQUALS)) {\n      defaultValue = this.parseConstValueLiteral();\n    }\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.INPUT_VALUE_DEFINITION,\n      description,\n      name,\n      type,\n      defaultValue,\n      directives\n    });\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n\n  parseInterfaceTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.INTERFACE_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n\n  parseUnionTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    return this.node(start, {\n      kind: Kind.UNION_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      types\n    });\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n\n  parseUnionMemberTypes() {\n    return this.expectOptionalToken(TokenKind.EQUALS) ? this.delimitedMany(TokenKind.PIPE, this.parseNamedType) : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n\n  parseEnumTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    return this.node(start, {\n      kind: Kind.ENUM_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      values\n    });\n  }\n  /**\n   * ```\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   * ```\n   */\n\n  parseEnumValuesDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseEnumValueDefinition, TokenKind.BRACE_R);\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   */\n\n  parseEnumValueDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseEnumValueName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.ENUM_VALUE_DEFINITION,\n      description,\n      name,\n      directives\n    });\n  }\n  /**\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseEnumValueName() {\n    if (this._lexer.token.value === 'true' || this._lexer.token.value === 'false' || this._lexer.token.value === 'null') {\n      throw syntaxError(this._lexer.source, this._lexer.token.start, `${getTokenDesc(this._lexer.token)} is reserved and cannot be used for an enum value.`);\n    }\n    return this.parseName();\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n\n  parseInputObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      fields\n    });\n  }\n  /**\n   * ```\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   * ```\n   */\n\n  parseInputFieldsDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseInputValueDef, TokenKind.BRACE_R);\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n\n  parseTypeSystemExtension() {\n    const keywordToken = this._lexer.lookahead();\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n        case 'type':\n          return this.parseObjectTypeExtension();\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n        case 'union':\n          return this.parseUnionTypeExtension();\n        case 'enum':\n          return this.parseEnumTypeExtension();\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * ```\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   * ```\n   */\n\n  parseSchemaExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.optionalMany(TokenKind.BRACE_L, this.parseOperationTypeDefinition, TokenKind.BRACE_R);\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.SCHEMA_EXTENSION,\n      directives,\n      operationTypes\n    });\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n\n  parseScalarTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.SCALAR_TYPE_EXTENSION,\n      name,\n      directives\n    });\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n\n  parseObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    if (interfaces.length === 0 && directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.OBJECT_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n\n  parseInterfaceTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    if (interfaces.length === 0 && directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.INTERFACE_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n\n  parseUnionTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.UNION_TYPE_EXTENSION,\n      name,\n      directives,\n      types\n    });\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n\n  parseEnumTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.ENUM_TYPE_EXTENSION,\n      name,\n      directives,\n      values\n    });\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n\n  parseInputObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name,\n      directives,\n      fields\n    });\n  }\n  /**\n   * ```\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   * ```\n   */\n\n  parseDirectiveDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(TokenKind.AT);\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    const repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    const locations = this.parseDirectiveLocations();\n    return this.node(start, {\n      kind: Kind.DIRECTIVE_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations\n    });\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n\n  parseDirectiveLocations() {\n    return this.delimitedMany(TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n\n  parseDirectiveLocation() {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    if (Object.prototype.hasOwnProperty.call(DirectiveLocation, name.value)) {\n      return name;\n    }\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a node that, if configured to do so, sets a \"loc\" field as a\n   * location object, used to identify the place in the source that created a\n   * given parsed object.\n   */\n\n  node(startToken, node) {\n    if (this._options.noLocation !== true) {\n      node.loc = new Location(startToken, this._lexer.lastToken, this._lexer.source);\n    }\n    return node;\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n\n  peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectToken(kind) {\n    const token = this._lexer.token;\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return token;\n    }\n    throw syntaxError(this._lexer.source, token.start, `Expected ${getTokenKindDesc(kind)}, found ${getTokenDesc(token)}.`);\n  }\n  /**\n   * If the next token is of the given kind, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalToken(kind) {\n    const token = this._lexer.token;\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return true;\n    }\n    return false;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectKeyword(value) {\n    const token = this._lexer.token;\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n    } else {\n      throw syntaxError(this._lexer.source, token.start, `Expected \"${value}\", found ${getTokenDesc(token)}.`);\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalKeyword(value) {\n    const token = this._lexer.token;\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n\n  unexpected(atToken) {\n    const token = atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return syntaxError(this._lexer.source, token.start, `Unexpected ${getTokenDesc(token)}.`);\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      const nodes = [];\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n      return nodes;\n    }\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n\n  delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    const nodes = [];\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n    return nodes;\n  }\n  advanceLexer() {\n    const {\n      maxTokens\n    } = this._options;\n    const token = this._lexer.advance();\n    if (maxTokens !== undefined && token.kind !== TokenKind.EOF) {\n      ++this._tokenCounter;\n      if (this._tokenCounter > maxTokens) {\n        throw syntaxError(this._lexer.source, token.start, `Document contains more that ${maxTokens} tokens. Parsing aborted.`);\n      }\n    }\n  }\n}\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  const value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? ` \"${value}\"` : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\nfunction getTokenKindDesc(kind) {\n  return isPunctuatorTokenKind(kind) ? `\"${kind}\"` : kind;\n}", "map": {"version": 3, "names": ["syntaxError", "Location", "OperationTypeNode", "DirectiveLocation", "Kind", "isPunctuatorTokenKind", "<PERSON><PERSON>", "isSource", "Source", "TokenKind", "parse", "source", "options", "parser", "<PERSON><PERSON><PERSON>", "parseDocument", "parseValue", "expectToken", "SOF", "value", "parseValueLiteral", "EOF", "parseConstValue", "parseConstValueLiteral", "parseType", "type", "parseTypeReference", "constructor", "sourceObj", "_lexer", "_options", "_tokenCounter", "parseName", "token", "NAME", "node", "kind", "DOCUMENT", "definitions", "many", "parseDefinition", "peek", "BRACE_L", "parseOperationDefinition", "hasDescription", "peekDescription", "keywordToken", "<PERSON><PERSON><PERSON>", "parseSchemaDefinition", "parseScalarTypeDefinition", "parseObjectTypeDefinition", "parseInterfaceTypeDefinition", "parseUnionTypeDefinition", "parseEnumTypeDefinition", "parseInputObjectTypeDefinition", "parseDirectiveDefinition", "start", "parseFragmentDefinition", "parseTypeSystemExtension", "unexpected", "OPERATION_DEFINITION", "operation", "QUERY", "name", "undefined", "variableDefinitions", "directives", "selectionSet", "parseSelectionSet", "parseOperationType", "parseVariableDefinitions", "parseDirectives", "operationToken", "MUTATION", "SUBSCRIPTION", "optional<PERSON><PERSON>", "PAREN_L", "parseVariableDefinition", "PAREN_R", "VARIABLE_DEFINITION", "variable", "parseVariable", "COLON", "defaultValue", "expectOptionalToken", "EQUALS", "parseConstDirectives", "DOLLAR", "VARIABLE", "SELECTION_SET", "selections", "parseSelection", "BRACE_R", "SPREAD", "parseFragment", "parseField", "nameOr<PERSON><PERSON><PERSON>", "alias", "FIELD", "arguments", "parseArguments", "isConst", "item", "parseConstArgument", "parseArgument", "ARGUMENT", "hasTypeCondition", "expectOptionalKeyword", "FRAGMENT_SPREAD", "parseFragmentName", "INLINE_FRAGMENT", "typeCondition", "parseNamedType", "expectKeyword", "allowLegacyFragmentVariables", "FRAGMENT_DEFINITION", "BRACKET_L", "parseList", "parseObject", "INT", "advanceLexer", "FLOAT", "STRING", "BLOCK_STRING", "parseStringLiteral", "BOOLEAN", "NULL", "ENUM", "varName", "block", "LIST", "values", "any", "BRACKET_R", "parseObjectField", "OBJECT", "fields", "OBJECT_FIELD", "AT", "push", "parseDirective", "DIRECTIVE", "innerType", "LIST_TYPE", "BANG", "NON_NULL_TYPE", "NAMED_TYPE", "parseDescription", "description", "operationTypes", "parseOperationTypeDefinition", "SCHEMA_DEFINITION", "OPERATION_TYPE_DEFINITION", "SCALAR_TYPE_DEFINITION", "interfaces", "parseImplementsInterfaces", "parseFieldsDefinition", "OBJECT_TYPE_DEFINITION", "delimitedMany", "AMP", "parseFieldDefinition", "args", "parseArgumentDefs", "FIELD_DEFINITION", "parseInputValueDef", "INPUT_VALUE_DEFINITION", "INTERFACE_TYPE_DEFINITION", "types", "parseUnionMemberTypes", "UNION_TYPE_DEFINITION", "PIPE", "parseEnumValuesDefinition", "ENUM_TYPE_DEFINITION", "parseEnumValueDefinition", "parseEnumValueName", "ENUM_VALUE_DEFINITION", "getTokenDesc", "parseInputFieldsDefinition", "INPUT_OBJECT_TYPE_DEFINITION", "parseSchemaExtension", "parseScalarTypeExtension", "parseObjectTypeExtension", "parseInterfaceTypeExtension", "parseUnionTypeExtension", "parseEnumTypeExtension", "parseInputObjectTypeExtension", "length", "SCHEMA_EXTENSION", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_EXTENSION", "repeatable", "locations", "parseDirectiveLocations", "DIRECTIVE_DEFINITION", "parseDirectiveLocation", "Object", "prototype", "hasOwnProperty", "call", "startToken", "noLocation", "loc", "lastToken", "getTokenKindDesc", "atToken", "openKind", "parseFn", "<PERSON><PERSON><PERSON>", "nodes", "delimiterKind", "maxTokens", "advance"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/language/parser.mjs"], "sourcesContent": ["import { syntaxError } from '../error/syntaxError.mjs';\nimport { Location, OperationTypeNode } from './ast.mjs';\nimport { DirectiveLocation } from './directiveLocation.mjs';\nimport { Kind } from './kinds.mjs';\nimport { isPunctuatorTokenKind, Lexer } from './lexer.mjs';\nimport { isSource, Source } from './source.mjs';\nimport { TokenKind } from './tokenKind.mjs';\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nexport function parse(source, options) {\n  const parser = new Parser(source, options);\n  return parser.parseDocument();\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nexport function parseValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const value = parser.parseValueLiteral(false);\n  parser.expectToken(TokenKind.EOF);\n  return value;\n}\n/**\n * Similar to parseValue(), but raises a parse error if it encounters a\n * variable. The return type will be a constant value.\n */\n\nexport function parseConstValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const value = parser.parseConstValueLiteral();\n  parser.expectToken(TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nexport function parseType(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const type = parser.parseTypeReference();\n  parser.expectToken(TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nexport class Parser {\n  constructor(source, options = {}) {\n    const sourceObj = isSource(source) ? source : new Source(source);\n    this._lexer = new Lexer(sourceObj);\n    this._options = options;\n    this._tokenCounter = 0;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n  parseName() {\n    const token = this.expectToken(TokenKind.NAME);\n    return this.node(token, {\n      kind: Kind.NAME,\n      value: token.value,\n    });\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n\n  parseDocument() {\n    return this.node(this._lexer.token, {\n      kind: Kind.DOCUMENT,\n      definitions: this.many(\n        TokenKind.SOF,\n        this.parseDefinition,\n        TokenKind.EOF,\n      ),\n    });\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   *\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n\n  parseDefinition() {\n    if (this.peek(TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } // Many definitions begin with a description and require a lookahead.\n\n    const hasDescription = this.peekDescription();\n    const keywordToken = hasDescription\n      ? this._lexer.lookahead()\n      : this._lexer.token;\n\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n\n        case 'type':\n          return this.parseObjectTypeDefinition();\n\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n\n        case 'union':\n          return this.parseUnionTypeDefinition();\n\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n\n      if (hasDescription) {\n        throw syntaxError(\n          this._lexer.source,\n          this._lexer.token.start,\n          'Unexpected description, descriptions are supported only on type definitions.',\n        );\n      }\n\n      switch (keywordToken.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n\n        case 'fragment':\n          return this.parseFragmentDefinition();\n\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n\n  parseOperationDefinition() {\n    const start = this._lexer.token;\n\n    if (this.peek(TokenKind.BRACE_L)) {\n      return this.node(start, {\n        kind: Kind.OPERATION_DEFINITION,\n        operation: OperationTypeNode.QUERY,\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    const operation = this.parseOperationType();\n    let name;\n\n    if (this.peek(TokenKind.NAME)) {\n      name = this.parseName();\n    }\n\n    return this.node(start, {\n      kind: Kind.OPERATION_DEFINITION,\n      operation,\n      name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n\n  parseOperationType() {\n    const operationToken = this.expectToken(TokenKind.NAME);\n\n    switch (operationToken.value) {\n      case 'query':\n        return OperationTypeNode.QUERY;\n\n      case 'mutation':\n        return OperationTypeNode.MUTATION;\n\n      case 'subscription':\n        return OperationTypeNode.SUBSCRIPTION;\n    }\n\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n\n  parseVariableDefinitions() {\n    return this.optionalMany(\n      TokenKind.PAREN_L,\n      this.parseVariableDefinition,\n      TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n\n  parseVariableDefinition() {\n    return this.node(this._lexer.token, {\n      kind: Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(TokenKind.EQUALS)\n        ? this.parseConstValueLiteral()\n        : undefined,\n      directives: this.parseConstDirectives(),\n    });\n  }\n  /**\n   * Variable : $ Name\n   */\n\n  parseVariable() {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.DOLLAR);\n    return this.node(start, {\n      kind: Kind.VARIABLE,\n      name: this.parseName(),\n    });\n  }\n  /**\n   * ```\n   * SelectionSet : { Selection+ }\n   * ```\n   */\n\n  parseSelectionSet() {\n    return this.node(this._lexer.token, {\n      kind: Kind.SELECTION_SET,\n      selections: this.many(\n        TokenKind.BRACE_L,\n        this.parseSelection,\n        TokenKind.BRACE_R,\n      ),\n    });\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n\n  parseSelection() {\n    return this.peek(TokenKind.SPREAD)\n      ? this.parseFragment()\n      : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n\n  parseField() {\n    const start = this._lexer.token;\n    const nameOrAlias = this.parseName();\n    let alias;\n    let name;\n\n    if (this.expectOptionalToken(TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n\n    return this.node(start, {\n      kind: Kind.FIELD,\n      alias,\n      name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(TokenKind.BRACE_L)\n        ? this.parseSelectionSet()\n        : undefined,\n    });\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n\n  parseArguments(isConst) {\n    const item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(TokenKind.PAREN_L, item, TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n\n  parseArgument(isConst = false) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return this.node(start, {\n      kind: Kind.ARGUMENT,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  }\n\n  parseConstArgument() {\n    return this.parseArgument(true);\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n\n  parseFragment() {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.SPREAD);\n    const hasTypeCondition = this.expectOptionalKeyword('on');\n\n    if (!hasTypeCondition && this.peek(TokenKind.NAME)) {\n      return this.node(start, {\n        kind: Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false),\n      });\n    }\n\n    return this.node(start, {\n      kind: Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n\n  parseFragmentDefinition() {\n    const start = this._lexer.token;\n    this.expectKeyword('fragment'); // Legacy support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (this._options.allowLegacyFragmentVariables === true) {\n      return this.node(start, {\n        kind: Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    return this.node(start, {\n      kind: Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n\n  parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseValueLiteral(isConst) {\n    const token = this._lexer.token;\n\n    switch (token.kind) {\n      case TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n\n      case TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n\n      case TokenKind.INT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: Kind.INT,\n          value: token.value,\n        });\n\n      case TokenKind.FLOAT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: Kind.FLOAT,\n          value: token.value,\n        });\n\n      case TokenKind.STRING:\n      case TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n\n      case TokenKind.NAME:\n        this.advanceLexer();\n\n        switch (token.value) {\n          case 'true':\n            return this.node(token, {\n              kind: Kind.BOOLEAN,\n              value: true,\n            });\n\n          case 'false':\n            return this.node(token, {\n              kind: Kind.BOOLEAN,\n              value: false,\n            });\n\n          case 'null':\n            return this.node(token, {\n              kind: Kind.NULL,\n            });\n\n          default:\n            return this.node(token, {\n              kind: Kind.ENUM,\n              value: token.value,\n            });\n        }\n\n      case TokenKind.DOLLAR:\n        if (isConst) {\n          this.expectToken(TokenKind.DOLLAR);\n\n          if (this._lexer.token.kind === TokenKind.NAME) {\n            const varName = this._lexer.token.value;\n            throw syntaxError(\n              this._lexer.source,\n              token.start,\n              `Unexpected variable \"$${varName}\" in constant value.`,\n            );\n          } else {\n            throw this.unexpected(token);\n          }\n        }\n\n        return this.parseVariable();\n\n      default:\n        throw this.unexpected();\n    }\n  }\n\n  parseConstValueLiteral() {\n    return this.parseValueLiteral(true);\n  }\n\n  parseStringLiteral() {\n    const token = this._lexer.token;\n    this.advanceLexer();\n    return this.node(token, {\n      kind: Kind.STRING,\n      value: token.value,\n      block: token.kind === TokenKind.BLOCK_STRING,\n    });\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n\n  parseList(isConst) {\n    const item = () => this.parseValueLiteral(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: Kind.LIST,\n      values: this.any(TokenKind.BRACKET_L, item, TokenKind.BRACKET_R),\n    });\n  }\n  /**\n   * ```\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   * ```\n   */\n\n  parseObject(isConst) {\n    const item = () => this.parseObjectField(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: Kind.OBJECT,\n      fields: this.any(TokenKind.BRACE_L, item, TokenKind.BRACE_R),\n    });\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n\n  parseObjectField(isConst) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return this.node(start, {\n      kind: Kind.OBJECT_FIELD,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n\n  parseDirectives(isConst) {\n    const directives = [];\n\n    while (this.peek(TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n\n    return directives;\n  }\n\n  parseConstDirectives() {\n    return this.parseDirectives(true);\n  }\n  /**\n   * ```\n   * Directive[Const] : @ Name Arguments[?Const]?\n   * ```\n   */\n\n  parseDirective(isConst) {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.AT);\n    return this.node(start, {\n      kind: Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst),\n    });\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n\n  parseTypeReference() {\n    const start = this._lexer.token;\n    let type;\n\n    if (this.expectOptionalToken(TokenKind.BRACKET_L)) {\n      const innerType = this.parseTypeReference();\n      this.expectToken(TokenKind.BRACKET_R);\n      type = this.node(start, {\n        kind: Kind.LIST_TYPE,\n        type: innerType,\n      });\n    } else {\n      type = this.parseNamedType();\n    }\n\n    if (this.expectOptionalToken(TokenKind.BANG)) {\n      return this.node(start, {\n        kind: Kind.NON_NULL_TYPE,\n        type,\n      });\n    }\n\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n\n  parseNamedType() {\n    return this.node(this._lexer.token, {\n      kind: Kind.NAMED_TYPE,\n      name: this.parseName(),\n    });\n  } // Implements the parsing rules in the Type Definition section.\n\n  peekDescription() {\n    return this.peek(TokenKind.STRING) || this.peek(TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n\n  parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * ```\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   * ```\n   */\n\n  parseSchemaDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.many(\n      TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      TokenKind.BRACE_R,\n    );\n    return this.node(start, {\n      kind: Kind.SCHEMA_DEFINITION,\n      description,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n\n  parseOperationTypeDefinition() {\n    const start = this._lexer.token;\n    const operation = this.parseOperationType();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseNamedType();\n    return this.node(start, {\n      kind: Kind.OPERATION_TYPE_DEFINITION,\n      operation,\n      type,\n    });\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n\n  parseScalarTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.SCALAR_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n\n  parseObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n\n  parseImplementsInterfaces() {\n    return this.expectOptionalKeyword('implements')\n      ? this.delimitedMany(TokenKind.AMP, this.parseNamedType)\n      : [];\n  }\n  /**\n   * ```\n   * FieldsDefinition : { FieldDefinition+ }\n   * ```\n   */\n\n  parseFieldsDefinition() {\n    return this.optionalMany(\n      TokenKind.BRACE_L,\n      this.parseFieldDefinition,\n      TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n\n  parseFieldDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseTypeReference();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.FIELD_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      type,\n      directives,\n    });\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n\n  parseArgumentDefs() {\n    return this.optionalMany(\n      TokenKind.PAREN_L,\n      this.parseInputValueDef,\n      TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n\n  parseInputValueDef() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseTypeReference();\n    let defaultValue;\n\n    if (this.expectOptionalToken(TokenKind.EQUALS)) {\n      defaultValue = this.parseConstValueLiteral();\n    }\n\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.INPUT_VALUE_DEFINITION,\n      description,\n      name,\n      type,\n      defaultValue,\n      directives,\n    });\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n\n  parseInterfaceTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.INTERFACE_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n\n  parseUnionTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    return this.node(start, {\n      kind: Kind.UNION_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n\n  parseUnionMemberTypes() {\n    return this.expectOptionalToken(TokenKind.EQUALS)\n      ? this.delimitedMany(TokenKind.PIPE, this.parseNamedType)\n      : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n\n  parseEnumTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    return this.node(start, {\n      kind: Kind.ENUM_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * ```\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   * ```\n   */\n\n  parseEnumValuesDefinition() {\n    return this.optionalMany(\n      TokenKind.BRACE_L,\n      this.parseEnumValueDefinition,\n      TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   */\n\n  parseEnumValueDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseEnumValueName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.ENUM_VALUE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseEnumValueName() {\n    if (\n      this._lexer.token.value === 'true' ||\n      this._lexer.token.value === 'false' ||\n      this._lexer.token.value === 'null'\n    ) {\n      throw syntaxError(\n        this._lexer.source,\n        this._lexer.token.start,\n        `${getTokenDesc(\n          this._lexer.token,\n        )} is reserved and cannot be used for an enum value.`,\n      );\n    }\n\n    return this.parseName();\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n\n  parseInputObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   * ```\n   */\n\n  parseInputFieldsDefinition() {\n    return this.optionalMany(\n      TokenKind.BRACE_L,\n      this.parseInputValueDef,\n      TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n\n  parseTypeSystemExtension() {\n    const keywordToken = this._lexer.lookahead();\n\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n\n        case 'type':\n          return this.parseObjectTypeExtension();\n\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n\n        case 'union':\n          return this.parseUnionTypeExtension();\n\n        case 'enum':\n          return this.parseEnumTypeExtension();\n\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * ```\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   * ```\n   */\n\n  parseSchemaExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.optionalMany(\n      TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      TokenKind.BRACE_R,\n    );\n\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: Kind.SCHEMA_EXTENSION,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n\n  parseScalarTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: Kind.SCALAR_TYPE_EXTENSION,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n\n  parseObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: Kind.OBJECT_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n\n  parseInterfaceTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: Kind.INTERFACE_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n\n  parseUnionTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: Kind.UNION_TYPE_EXTENSION,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n\n  parseEnumTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: Kind.ENUM_TYPE_EXTENSION,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n\n  parseInputObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   * ```\n   */\n\n  parseDirectiveDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(TokenKind.AT);\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    const repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    const locations = this.parseDirectiveLocations();\n    return this.node(start, {\n      kind: Kind.DIRECTIVE_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations,\n    });\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n\n  parseDirectiveLocations() {\n    return this.delimitedMany(TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n\n  parseDirectiveLocation() {\n    const start = this._lexer.token;\n    const name = this.parseName();\n\n    if (Object.prototype.hasOwnProperty.call(DirectiveLocation, name.value)) {\n      return name;\n    }\n\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a node that, if configured to do so, sets a \"loc\" field as a\n   * location object, used to identify the place in the source that created a\n   * given parsed object.\n   */\n\n  node(startToken, node) {\n    if (this._options.noLocation !== true) {\n      node.loc = new Location(\n        startToken,\n        this._lexer.lastToken,\n        this._lexer.source,\n      );\n    }\n\n    return node;\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n\n  peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return token;\n    }\n\n    throw syntaxError(\n      this._lexer.source,\n      token.start,\n      `Expected ${getTokenKindDesc(kind)}, found ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * If the next token is of the given kind, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n    } else {\n      throw syntaxError(\n        this._lexer.source,\n        token.start,\n        `Expected \"${value}\", found ${getTokenDesc(token)}.`,\n      );\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n\n  unexpected(atToken) {\n    const token =\n      atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return syntaxError(\n      this._lexer.source,\n      token.start,\n      `Unexpected ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      const nodes = [];\n\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n\n      return nodes;\n    }\n\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n\n  delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n\n    return nodes;\n  }\n\n  advanceLexer() {\n    const { maxTokens } = this._options;\n\n    const token = this._lexer.advance();\n\n    if (maxTokens !== undefined && token.kind !== TokenKind.EOF) {\n      ++this._tokenCounter;\n\n      if (this._tokenCounter > maxTokens) {\n        throw syntaxError(\n          this._lexer.source,\n          token.start,\n          `Document contains more that ${maxTokens} tokens. Parsing aborted.`,\n        );\n      }\n    }\n  }\n}\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  const value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? ` \"${value}\"` : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\nfunction getTokenKindDesc(kind) {\n  return isPunctuatorTokenKind(kind) ? `\"${kind}\"` : kind;\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,WAAW;AACvD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,qBAAqB,EAAEC,KAAK,QAAQ,aAAa;AAC1D,SAASC,QAAQ,EAAEC,MAAM,QAAQ,cAAc;AAC/C,SAASC,SAAS,QAAQ,iBAAiB;AAC3C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACrC,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACH,MAAM,EAAEC,OAAO,CAAC;EAC1C,OAAOC,MAAM,CAACE,aAAa,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,UAAUA,CAACL,MAAM,EAAEC,OAAO,EAAE;EAC1C,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACH,MAAM,EAAEC,OAAO,CAAC;EAC1CC,MAAM,CAACI,WAAW,CAACR,SAAS,CAACS,GAAG,CAAC;EACjC,MAAMC,KAAK,GAAGN,MAAM,CAACO,iBAAiB,CAAC,KAAK,CAAC;EAC7CP,MAAM,CAACI,WAAW,CAACR,SAAS,CAACY,GAAG,CAAC;EACjC,OAAOF,KAAK;AACd;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASG,eAAeA,CAACX,MAAM,EAAEC,OAAO,EAAE;EAC/C,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACH,MAAM,EAAEC,OAAO,CAAC;EAC1CC,MAAM,CAACI,WAAW,CAACR,SAAS,CAACS,GAAG,CAAC;EACjC,MAAMC,KAAK,GAAGN,MAAM,CAACU,sBAAsB,CAAC,CAAC;EAC7CV,MAAM,CAACI,WAAW,CAACR,SAAS,CAACY,GAAG,CAAC;EACjC,OAAOF,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASK,SAASA,CAACb,MAAM,EAAEC,OAAO,EAAE;EACzC,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACH,MAAM,EAAEC,OAAO,CAAC;EAC1CC,MAAM,CAACI,WAAW,CAACR,SAAS,CAACS,GAAG,CAAC;EACjC,MAAMO,IAAI,GAAGZ,MAAM,CAACa,kBAAkB,CAAC,CAAC;EACxCb,MAAM,CAACI,WAAW,CAACR,SAAS,CAACY,GAAG,CAAC;EACjC,OAAOI,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMX,MAAM,CAAC;EAClBa,WAAWA,CAAChB,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAChC,MAAMgB,SAAS,GAAGrB,QAAQ,CAACI,MAAM,CAAC,GAAGA,MAAM,GAAG,IAAIH,MAAM,CAACG,MAAM,CAAC;IAChE,IAAI,CAACkB,MAAM,GAAG,IAAIvB,KAAK,CAACsB,SAAS,CAAC;IAClC,IAAI,CAACE,QAAQ,GAAGlB,OAAO;IACvB,IAAI,CAACmB,aAAa,GAAG,CAAC;EACxB;EACA;AACF;AACA;;EAEEC,SAASA,CAAA,EAAG;IACV,MAAMC,KAAK,GAAG,IAAI,CAAChB,WAAW,CAACR,SAAS,CAACyB,IAAI,CAAC;IAC9C,OAAO,IAAI,CAACC,IAAI,CAACF,KAAK,EAAE;MACtBG,IAAI,EAAEhC,IAAI,CAAC8B,IAAI;MACff,KAAK,EAAEc,KAAK,CAACd;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;AACF;AACA;;EAEEJ,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACoB,IAAI,CAAC,IAAI,CAACN,MAAM,CAACI,KAAK,EAAE;MAClCG,IAAI,EAAEhC,IAAI,CAACiC,QAAQ;MACnBC,WAAW,EAAE,IAAI,CAACC,IAAI,CACpB9B,SAAS,CAACS,GAAG,EACb,IAAI,CAACsB,eAAe,EACpB/B,SAAS,CAACY,GACZ;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEEmB,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACC,IAAI,CAAChC,SAAS,CAACiC,OAAO,CAAC,EAAE;MAChC,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACxC,CAAC,CAAC;;IAEF,MAAMC,cAAc,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAC7C,MAAMC,YAAY,GAAGF,cAAc,GAC/B,IAAI,CAACf,MAAM,CAACkB,SAAS,CAAC,CAAC,GACvB,IAAI,CAAClB,MAAM,CAACI,KAAK;IAErB,IAAIa,YAAY,CAACV,IAAI,KAAK3B,SAAS,CAACyB,IAAI,EAAE;MACxC,QAAQY,YAAY,CAAC3B,KAAK;QACxB,KAAK,QAAQ;UACX,OAAO,IAAI,CAAC6B,qBAAqB,CAAC,CAAC;QAErC,KAAK,QAAQ;UACX,OAAO,IAAI,CAACC,yBAAyB,CAAC,CAAC;QAEzC,KAAK,MAAM;UACT,OAAO,IAAI,CAACC,yBAAyB,CAAC,CAAC;QAEzC,KAAK,WAAW;UACd,OAAO,IAAI,CAACC,4BAA4B,CAAC,CAAC;QAE5C,KAAK,OAAO;UACV,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;QAExC,KAAK,MAAM;UACT,OAAO,IAAI,CAACC,uBAAuB,CAAC,CAAC;QAEvC,KAAK,OAAO;UACV,OAAO,IAAI,CAACC,8BAA8B,CAAC,CAAC;QAE9C,KAAK,WAAW;UACd,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC1C;MAEA,IAAIX,cAAc,EAAE;QAClB,MAAM5C,WAAW,CACf,IAAI,CAAC6B,MAAM,CAAClB,MAAM,EAClB,IAAI,CAACkB,MAAM,CAACI,KAAK,CAACuB,KAAK,EACvB,8EACF,CAAC;MACH;MAEA,QAAQV,YAAY,CAAC3B,KAAK;QACxB,KAAK,OAAO;QACZ,KAAK,UAAU;QACf,KAAK,cAAc;UACjB,OAAO,IAAI,CAACwB,wBAAwB,CAAC,CAAC;QAExC,KAAK,UAAU;UACb,OAAO,IAAI,CAACc,uBAAuB,CAAC,CAAC;QAEvC,KAAK,QAAQ;UACX,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC1C;IACF;IAEA,MAAM,IAAI,CAACC,UAAU,CAACb,YAAY,CAAC;EACrC,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;;EAEEH,wBAAwBA,CAAA,EAAG;IACzB,MAAMa,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAE/B,IAAI,IAAI,CAACQ,IAAI,CAAChC,SAAS,CAACiC,OAAO,CAAC,EAAE;MAChC,OAAO,IAAI,CAACP,IAAI,CAACqB,KAAK,EAAE;QACtBpB,IAAI,EAAEhC,IAAI,CAACwD,oBAAoB;QAC/BC,SAAS,EAAE3D,iBAAiB,CAAC4D,KAAK;QAClCC,IAAI,EAAEC,SAAS;QACfC,mBAAmB,EAAE,EAAE;QACvBC,UAAU,EAAE,EAAE;QACdC,YAAY,EAAE,IAAI,CAACC,iBAAiB,CAAC;MACvC,CAAC,CAAC;IACJ;IAEA,MAAMP,SAAS,GAAG,IAAI,CAACQ,kBAAkB,CAAC,CAAC;IAC3C,IAAIN,IAAI;IAER,IAAI,IAAI,CAACtB,IAAI,CAAChC,SAAS,CAACyB,IAAI,CAAC,EAAE;MAC7B6B,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACG,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACwD,oBAAoB;MAC/BC,SAAS;MACTE,IAAI;MACJE,mBAAmB,EAAE,IAAI,CAACK,wBAAwB,CAAC,CAAC;MACpDJ,UAAU,EAAE,IAAI,CAACK,eAAe,CAAC,KAAK,CAAC;MACvCJ,YAAY,EAAE,IAAI,CAACC,iBAAiB,CAAC;IACvC,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEEC,kBAAkBA,CAAA,EAAG;IACnB,MAAMG,cAAc,GAAG,IAAI,CAACvD,WAAW,CAACR,SAAS,CAACyB,IAAI,CAAC;IAEvD,QAAQsC,cAAc,CAACrD,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOjB,iBAAiB,CAAC4D,KAAK;MAEhC,KAAK,UAAU;QACb,OAAO5D,iBAAiB,CAACuE,QAAQ;MAEnC,KAAK,cAAc;QACjB,OAAOvE,iBAAiB,CAACwE,YAAY;IACzC;IAEA,MAAM,IAAI,CAACf,UAAU,CAACa,cAAc,CAAC;EACvC;EACA;AACF;AACA;;EAEEF,wBAAwBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACK,YAAY,CACtBlE,SAAS,CAACmE,OAAO,EACjB,IAAI,CAACC,uBAAuB,EAC5BpE,SAAS,CAACqE,OACZ,CAAC;EACH;EACA;AACF;AACA;;EAEED,uBAAuBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAC1C,IAAI,CAAC,IAAI,CAACN,MAAM,CAACI,KAAK,EAAE;MAClCG,IAAI,EAAEhC,IAAI,CAAC2E,mBAAmB;MAC9BC,QAAQ,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC;MAC9BxD,IAAI,GAAG,IAAI,CAACR,WAAW,CAACR,SAAS,CAACyE,KAAK,CAAC,EAAE,IAAI,CAACxD,kBAAkB,CAAC,CAAC,CAAC;MACpEyD,YAAY,EAAE,IAAI,CAACC,mBAAmB,CAAC3E,SAAS,CAAC4E,MAAM,CAAC,GACpD,IAAI,CAAC9D,sBAAsB,CAAC,CAAC,GAC7ByC,SAAS;MACbE,UAAU,EAAE,IAAI,CAACoB,oBAAoB,CAAC;IACxC,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEEL,aAAaA,CAAA,EAAG;IACd,MAAMzB,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAAChB,WAAW,CAACR,SAAS,CAAC8E,MAAM,CAAC;IAClC,OAAO,IAAI,CAACpD,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACoF,QAAQ;MACnBzB,IAAI,EAAE,IAAI,CAAC/B,SAAS,CAAC;IACvB,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEoC,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACjC,IAAI,CAAC,IAAI,CAACN,MAAM,CAACI,KAAK,EAAE;MAClCG,IAAI,EAAEhC,IAAI,CAACqF,aAAa;MACxBC,UAAU,EAAE,IAAI,CAACnD,IAAI,CACnB9B,SAAS,CAACiC,OAAO,EACjB,IAAI,CAACiD,cAAc,EACnBlF,SAAS,CAACmF,OACZ;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;;EAEED,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAAClD,IAAI,CAAChC,SAAS,CAACoF,MAAM,CAAC,GAC9B,IAAI,CAACC,aAAa,CAAC,CAAC,GACpB,IAAI,CAACC,UAAU,CAAC,CAAC;EACvB;EACA;AACF;AACA;AACA;AACA;;EAEEA,UAAUA,CAAA,EAAG;IACX,MAAMvC,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAM+D,WAAW,GAAG,IAAI,CAAChE,SAAS,CAAC,CAAC;IACpC,IAAIiE,KAAK;IACT,IAAIlC,IAAI;IAER,IAAI,IAAI,CAACqB,mBAAmB,CAAC3E,SAAS,CAACyE,KAAK,CAAC,EAAE;MAC7Ce,KAAK,GAAGD,WAAW;MACnBjC,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IACzB,CAAC,MAAM;MACL+B,IAAI,GAAGiC,WAAW;IACpB;IAEA,OAAO,IAAI,CAAC7D,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC8F,KAAK;MAChBD,KAAK;MACLlC,IAAI;MACJoC,SAAS,EAAE,IAAI,CAACC,cAAc,CAAC,KAAK,CAAC;MACrClC,UAAU,EAAE,IAAI,CAACK,eAAe,CAAC,KAAK,CAAC;MACvCJ,YAAY,EAAE,IAAI,CAAC1B,IAAI,CAAChC,SAAS,CAACiC,OAAO,CAAC,GACtC,IAAI,CAAC0B,iBAAiB,CAAC,CAAC,GACxBJ;IACN,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEEoC,cAAcA,CAACC,OAAO,EAAE;IACtB,MAAMC,IAAI,GAAGD,OAAO,GAAG,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACC,aAAa;IACnE,OAAO,IAAI,CAAC7B,YAAY,CAAClE,SAAS,CAACmE,OAAO,EAAE0B,IAAI,EAAE7F,SAAS,CAACqE,OAAO,CAAC;EACtE;EACA;AACF;AACA;;EAEE0B,aAAaA,CAACH,OAAO,GAAG,KAAK,EAAE;IAC7B,MAAM7C,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAM8B,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACf,WAAW,CAACR,SAAS,CAACyE,KAAK,CAAC;IACjC,OAAO,IAAI,CAAC/C,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACqG,QAAQ;MACnB1C,IAAI;MACJ5C,KAAK,EAAE,IAAI,CAACC,iBAAiB,CAACiF,OAAO;IACvC,CAAC,CAAC;EACJ;EAEAE,kBAAkBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EACjC,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;;EAEEV,aAAaA,CAAA,EAAG;IACd,MAAMtC,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAAChB,WAAW,CAACR,SAAS,CAACoF,MAAM,CAAC;IAClC,MAAMa,gBAAgB,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC;IAEzD,IAAI,CAACD,gBAAgB,IAAI,IAAI,CAACjE,IAAI,CAAChC,SAAS,CAACyB,IAAI,CAAC,EAAE;MAClD,OAAO,IAAI,CAACC,IAAI,CAACqB,KAAK,EAAE;QACtBpB,IAAI,EAAEhC,IAAI,CAACwG,eAAe;QAC1B7C,IAAI,EAAE,IAAI,CAAC8C,iBAAiB,CAAC,CAAC;QAC9B3C,UAAU,EAAE,IAAI,CAACK,eAAe,CAAC,KAAK;MACxC,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI,CAACpC,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC0G,eAAe;MAC1BC,aAAa,EAAEL,gBAAgB,GAAG,IAAI,CAACM,cAAc,CAAC,CAAC,GAAGhD,SAAS;MACnEE,UAAU,EAAE,IAAI,CAACK,eAAe,CAAC,KAAK,CAAC;MACvCJ,YAAY,EAAE,IAAI,CAACC,iBAAiB,CAAC;IACvC,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;;EAEEX,uBAAuBA,CAAA,EAAG;IACxB,MAAMD,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;IAChC;IACA;;IAEA,IAAI,IAAI,CAACnF,QAAQ,CAACoF,4BAA4B,KAAK,IAAI,EAAE;MACvD,OAAO,IAAI,CAAC/E,IAAI,CAACqB,KAAK,EAAE;QACtBpB,IAAI,EAAEhC,IAAI,CAAC+G,mBAAmB;QAC9BpD,IAAI,EAAE,IAAI,CAAC8C,iBAAiB,CAAC,CAAC;QAC9B5C,mBAAmB,EAAE,IAAI,CAACK,wBAAwB,CAAC,CAAC;QACpDyC,aAAa,GAAG,IAAI,CAACE,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,CAAC;QAChE9C,UAAU,EAAE,IAAI,CAACK,eAAe,CAAC,KAAK,CAAC;QACvCJ,YAAY,EAAE,IAAI,CAACC,iBAAiB,CAAC;MACvC,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI,CAACjC,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC+G,mBAAmB;MAC9BpD,IAAI,EAAE,IAAI,CAAC8C,iBAAiB,CAAC,CAAC;MAC9BE,aAAa,GAAG,IAAI,CAACE,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,CAAC;MAChE9C,UAAU,EAAE,IAAI,CAACK,eAAe,CAAC,KAAK,CAAC;MACvCJ,YAAY,EAAE,IAAI,CAACC,iBAAiB,CAAC;IACvC,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEEyC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAChF,MAAM,CAACI,KAAK,CAACd,KAAK,KAAK,IAAI,EAAE;MACpC,MAAM,IAAI,CAACwC,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAAC3B,SAAS,CAAC,CAAC;EACzB,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEEZ,iBAAiBA,CAACiF,OAAO,EAAE;IACzB,MAAMpE,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;IAE/B,QAAQA,KAAK,CAACG,IAAI;MAChB,KAAK3B,SAAS,CAAC2G,SAAS;QACtB,OAAO,IAAI,CAACC,SAAS,CAAChB,OAAO,CAAC;MAEhC,KAAK5F,SAAS,CAACiC,OAAO;QACpB,OAAO,IAAI,CAAC4E,WAAW,CAACjB,OAAO,CAAC;MAElC,KAAK5F,SAAS,CAAC8G,GAAG;QAChB,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB,OAAO,IAAI,CAACrF,IAAI,CAACF,KAAK,EAAE;UACtBG,IAAI,EAAEhC,IAAI,CAACmH,GAAG;UACdpG,KAAK,EAAEc,KAAK,CAACd;QACf,CAAC,CAAC;MAEJ,KAAKV,SAAS,CAACgH,KAAK;QAClB,IAAI,CAACD,YAAY,CAAC,CAAC;QACnB,OAAO,IAAI,CAACrF,IAAI,CAACF,KAAK,EAAE;UACtBG,IAAI,EAAEhC,IAAI,CAACqH,KAAK;UAChBtG,KAAK,EAAEc,KAAK,CAACd;QACf,CAAC,CAAC;MAEJ,KAAKV,SAAS,CAACiH,MAAM;MACrB,KAAKjH,SAAS,CAACkH,YAAY;QACzB,OAAO,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAElC,KAAKnH,SAAS,CAACyB,IAAI;QACjB,IAAI,CAACsF,YAAY,CAAC,CAAC;QAEnB,QAAQvF,KAAK,CAACd,KAAK;UACjB,KAAK,MAAM;YACT,OAAO,IAAI,CAACgB,IAAI,CAACF,KAAK,EAAE;cACtBG,IAAI,EAAEhC,IAAI,CAACyH,OAAO;cAClB1G,KAAK,EAAE;YACT,CAAC,CAAC;UAEJ,KAAK,OAAO;YACV,OAAO,IAAI,CAACgB,IAAI,CAACF,KAAK,EAAE;cACtBG,IAAI,EAAEhC,IAAI,CAACyH,OAAO;cAClB1G,KAAK,EAAE;YACT,CAAC,CAAC;UAEJ,KAAK,MAAM;YACT,OAAO,IAAI,CAACgB,IAAI,CAACF,KAAK,EAAE;cACtBG,IAAI,EAAEhC,IAAI,CAAC0H;YACb,CAAC,CAAC;UAEJ;YACE,OAAO,IAAI,CAAC3F,IAAI,CAACF,KAAK,EAAE;cACtBG,IAAI,EAAEhC,IAAI,CAAC2H,IAAI;cACf5G,KAAK,EAAEc,KAAK,CAACd;YACf,CAAC,CAAC;QACN;MAEF,KAAKV,SAAS,CAAC8E,MAAM;QACnB,IAAIc,OAAO,EAAE;UACX,IAAI,CAACpF,WAAW,CAACR,SAAS,CAAC8E,MAAM,CAAC;UAElC,IAAI,IAAI,CAAC1D,MAAM,CAACI,KAAK,CAACG,IAAI,KAAK3B,SAAS,CAACyB,IAAI,EAAE;YAC7C,MAAM8F,OAAO,GAAG,IAAI,CAACnG,MAAM,CAACI,KAAK,CAACd,KAAK;YACvC,MAAMnB,WAAW,CACf,IAAI,CAAC6B,MAAM,CAAClB,MAAM,EAClBsB,KAAK,CAACuB,KAAK,EACV,yBAAwBwE,OAAQ,sBACnC,CAAC;UACH,CAAC,MAAM;YACL,MAAM,IAAI,CAACrE,UAAU,CAAC1B,KAAK,CAAC;UAC9B;QACF;QAEA,OAAO,IAAI,CAACgD,aAAa,CAAC,CAAC;MAE7B;QACE,MAAM,IAAI,CAACtB,UAAU,CAAC,CAAC;IAC3B;EACF;EAEApC,sBAAsBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACH,iBAAiB,CAAC,IAAI,CAAC;EACrC;EAEAwG,kBAAkBA,CAAA,EAAG;IACnB,MAAM3F,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACuF,YAAY,CAAC,CAAC;IACnB,OAAO,IAAI,CAACrF,IAAI,CAACF,KAAK,EAAE;MACtBG,IAAI,EAAEhC,IAAI,CAACsH,MAAM;MACjBvG,KAAK,EAAEc,KAAK,CAACd,KAAK;MAClB8G,KAAK,EAAEhG,KAAK,CAACG,IAAI,KAAK3B,SAAS,CAACkH;IAClC,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEN,SAASA,CAAChB,OAAO,EAAE;IACjB,MAAMC,IAAI,GAAGA,CAAA,KAAM,IAAI,CAAClF,iBAAiB,CAACiF,OAAO,CAAC;IAElD,OAAO,IAAI,CAAClE,IAAI,CAAC,IAAI,CAACN,MAAM,CAACI,KAAK,EAAE;MAClCG,IAAI,EAAEhC,IAAI,CAAC8H,IAAI;MACfC,MAAM,EAAE,IAAI,CAACC,GAAG,CAAC3H,SAAS,CAAC2G,SAAS,EAAEd,IAAI,EAAE7F,SAAS,CAAC4H,SAAS;IACjE,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAEEf,WAAWA,CAACjB,OAAO,EAAE;IACnB,MAAMC,IAAI,GAAGA,CAAA,KAAM,IAAI,CAACgC,gBAAgB,CAACjC,OAAO,CAAC;IAEjD,OAAO,IAAI,CAAClE,IAAI,CAAC,IAAI,CAACN,MAAM,CAACI,KAAK,EAAE;MAClCG,IAAI,EAAEhC,IAAI,CAACmI,MAAM;MACjBC,MAAM,EAAE,IAAI,CAACJ,GAAG,CAAC3H,SAAS,CAACiC,OAAO,EAAE4D,IAAI,EAAE7F,SAAS,CAACmF,OAAO;IAC7D,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEE0C,gBAAgBA,CAACjC,OAAO,EAAE;IACxB,MAAM7C,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAM8B,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACf,WAAW,CAACR,SAAS,CAACyE,KAAK,CAAC;IACjC,OAAO,IAAI,CAAC/C,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACqI,YAAY;MACvB1E,IAAI;MACJ5C,KAAK,EAAE,IAAI,CAACC,iBAAiB,CAACiF,OAAO;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;AACF;AACA;;EAEE9B,eAAeA,CAAC8B,OAAO,EAAE;IACvB,MAAMnC,UAAU,GAAG,EAAE;IAErB,OAAO,IAAI,CAACzB,IAAI,CAAChC,SAAS,CAACiI,EAAE,CAAC,EAAE;MAC9BxE,UAAU,CAACyE,IAAI,CAAC,IAAI,CAACC,cAAc,CAACvC,OAAO,CAAC,CAAC;IAC/C;IAEA,OAAOnC,UAAU;EACnB;EAEAoB,oBAAoBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACf,eAAe,CAAC,IAAI,CAAC;EACnC;EACA;AACF;AACA;AACA;AACA;;EAEEqE,cAAcA,CAACvC,OAAO,EAAE;IACtB,MAAM7C,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAAChB,WAAW,CAACR,SAAS,CAACiI,EAAE,CAAC;IAC9B,OAAO,IAAI,CAACvG,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACyI,SAAS;MACpB9E,IAAI,EAAE,IAAI,CAAC/B,SAAS,CAAC,CAAC;MACtBmE,SAAS,EAAE,IAAI,CAACC,cAAc,CAACC,OAAO;IACxC,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;;EAEE3E,kBAAkBA,CAAA,EAAG;IACnB,MAAM8B,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAIR,IAAI;IAER,IAAI,IAAI,CAAC2D,mBAAmB,CAAC3E,SAAS,CAAC2G,SAAS,CAAC,EAAE;MACjD,MAAM0B,SAAS,GAAG,IAAI,CAACpH,kBAAkB,CAAC,CAAC;MAC3C,IAAI,CAACT,WAAW,CAACR,SAAS,CAAC4H,SAAS,CAAC;MACrC5G,IAAI,GAAG,IAAI,CAACU,IAAI,CAACqB,KAAK,EAAE;QACtBpB,IAAI,EAAEhC,IAAI,CAAC2I,SAAS;QACpBtH,IAAI,EAAEqH;MACR,CAAC,CAAC;IACJ,CAAC,MAAM;MACLrH,IAAI,GAAG,IAAI,CAACuF,cAAc,CAAC,CAAC;IAC9B;IAEA,IAAI,IAAI,CAAC5B,mBAAmB,CAAC3E,SAAS,CAACuI,IAAI,CAAC,EAAE;MAC5C,OAAO,IAAI,CAAC7G,IAAI,CAACqB,KAAK,EAAE;QACtBpB,IAAI,EAAEhC,IAAI,CAAC6I,aAAa;QACxBxH;MACF,CAAC,CAAC;IACJ;IAEA,OAAOA,IAAI;EACb;EACA;AACF;AACA;;EAEEuF,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC7E,IAAI,CAAC,IAAI,CAACN,MAAM,CAACI,KAAK,EAAE;MAClCG,IAAI,EAAEhC,IAAI,CAAC8I,UAAU;MACrBnF,IAAI,EAAE,IAAI,CAAC/B,SAAS,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEFa,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACJ,IAAI,CAAChC,SAAS,CAACiH,MAAM,CAAC,IAAI,IAAI,CAACjF,IAAI,CAAChC,SAAS,CAACkH,YAAY,CAAC;EACzE;EACA;AACF;AACA;;EAEEwB,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACtG,eAAe,CAAC,CAAC,EAAE;MAC1B,OAAO,IAAI,CAAC+E,kBAAkB,CAAC,CAAC;IAClC;EACF;EACA;AACF;AACA;AACA;AACA;;EAEE5E,qBAAqBA,CAAA,EAAG;IACtB,MAAMQ,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,QAAQ,CAAC;IAC5B,MAAM/C,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAM+D,cAAc,GAAG,IAAI,CAAC9G,IAAI,CAC9B9B,SAAS,CAACiC,OAAO,EACjB,IAAI,CAAC4G,4BAA4B,EACjC7I,SAAS,CAACmF,OACZ,CAAC;IACD,OAAO,IAAI,CAACzD,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACmJ,iBAAiB;MAC5BH,WAAW;MACXlF,UAAU;MACVmF;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEEC,4BAA4BA,CAAA,EAAG;IAC7B,MAAM9F,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAM4B,SAAS,GAAG,IAAI,CAACQ,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACpD,WAAW,CAACR,SAAS,CAACyE,KAAK,CAAC;IACjC,MAAMzD,IAAI,GAAG,IAAI,CAACuF,cAAc,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC7E,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACoJ,yBAAyB;MACpC3F,SAAS;MACTpC;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEEwB,yBAAyBA,CAAA,EAAG;IAC1B,MAAMO,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,QAAQ,CAAC;IAC5B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACnD,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACqJ,sBAAsB;MACjCL,WAAW;MACXrF,IAAI;MACJG;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEhB,yBAAyBA,CAAA,EAAG;IAC1B,MAAMM,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,MAAM,CAAC;IAC1B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAM0H,UAAU,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACnD,MAAMzF,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMkD,MAAM,GAAG,IAAI,CAACoB,qBAAqB,CAAC,CAAC;IAC3C,OAAO,IAAI,CAACzH,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACyJ,sBAAsB;MACjCT,WAAW;MACXrF,IAAI;MACJ2F,UAAU;MACVxF,UAAU;MACVsE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEmB,yBAAyBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAAChD,qBAAqB,CAAC,YAAY,CAAC,GAC3C,IAAI,CAACmD,aAAa,CAACrJ,SAAS,CAACsJ,GAAG,EAAE,IAAI,CAAC/C,cAAc,CAAC,GACtD,EAAE;EACR;EACA;AACF;AACA;AACA;AACA;;EAEE4C,qBAAqBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACjF,YAAY,CACtBlE,SAAS,CAACiC,OAAO,EACjB,IAAI,CAACsH,oBAAoB,EACzBvJ,SAAS,CAACmF,OACZ,CAAC;EACH;EACA;AACF;AACA;AACA;;EAEEoE,oBAAoBA,CAAA,EAAG;IACrB,MAAMxG,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,MAAMpF,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMiI,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACjJ,WAAW,CAACR,SAAS,CAACyE,KAAK,CAAC;IACjC,MAAMzD,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,MAAMwC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACnD,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC+J,gBAAgB;MAC3Bf,WAAW;MACXrF,IAAI;MACJoC,SAAS,EAAE8D,IAAI;MACfxI,IAAI;MACJyC;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEEgG,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACvF,YAAY,CACtBlE,SAAS,CAACmE,OAAO,EACjB,IAAI,CAACwF,kBAAkB,EACvB3J,SAAS,CAACqE,OACZ,CAAC;EACH;EACA;AACF;AACA;AACA;;EAEEsF,kBAAkBA,CAAA,EAAG;IACnB,MAAM5G,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,MAAMpF,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACf,WAAW,CAACR,SAAS,CAACyE,KAAK,CAAC;IACjC,MAAMzD,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,IAAIyD,YAAY;IAEhB,IAAI,IAAI,CAACC,mBAAmB,CAAC3E,SAAS,CAAC4E,MAAM,CAAC,EAAE;MAC9CF,YAAY,GAAG,IAAI,CAAC5D,sBAAsB,CAAC,CAAC;IAC9C;IAEA,MAAM2C,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACnD,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACiK,sBAAsB;MACjCjB,WAAW;MACXrF,IAAI;MACJtC,IAAI;MACJ0D,YAAY;MACZjB;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;;EAEEf,4BAA4BA,CAAA,EAAG;IAC7B,MAAMK,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,WAAW,CAAC;IAC/B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAM0H,UAAU,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACnD,MAAMzF,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMkD,MAAM,GAAG,IAAI,CAACoB,qBAAqB,CAAC,CAAC;IAC3C,OAAO,IAAI,CAACzH,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACkK,yBAAyB;MACpClB,WAAW;MACXrF,IAAI;MACJ2F,UAAU;MACVxF,UAAU;MACVsE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;;EAEEpF,wBAAwBA,CAAA,EAAG;IACzB,MAAMI,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,OAAO,CAAC;IAC3B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMiF,KAAK,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC1C,OAAO,IAAI,CAACrI,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACqK,qBAAqB;MAChCrB,WAAW;MACXrF,IAAI;MACJG,UAAU;MACVqG;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEC,qBAAqBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACpF,mBAAmB,CAAC3E,SAAS,CAAC4E,MAAM,CAAC,GAC7C,IAAI,CAACyE,aAAa,CAACrJ,SAAS,CAACiK,IAAI,EAAE,IAAI,CAAC1D,cAAc,CAAC,GACvD,EAAE;EACR;EACA;AACF;AACA;AACA;;EAEE3D,uBAAuBA,CAAA,EAAG;IACxB,MAAMG,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,MAAM,CAAC;IAC1B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAM6C,MAAM,GAAG,IAAI,CAACwC,yBAAyB,CAAC,CAAC;IAC/C,OAAO,IAAI,CAACxI,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACwK,oBAAoB;MAC/BxB,WAAW;MACXrF,IAAI;MACJG,UAAU;MACViE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEwC,yBAAyBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAAChG,YAAY,CACtBlE,SAAS,CAACiC,OAAO,EACjB,IAAI,CAACmI,wBAAwB,EAC7BpK,SAAS,CAACmF,OACZ,CAAC;EACH;EACA;AACF;AACA;;EAEEiF,wBAAwBA,CAAA,EAAG;IACzB,MAAMrH,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,MAAMpF,IAAI,GAAG,IAAI,CAAC+G,kBAAkB,CAAC,CAAC;IACtC,MAAM5G,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACnD,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC2K,qBAAqB;MAChC3B,WAAW;MACXrF,IAAI;MACJG;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;;EAEE4G,kBAAkBA,CAAA,EAAG;IACnB,IACE,IAAI,CAACjJ,MAAM,CAACI,KAAK,CAACd,KAAK,KAAK,MAAM,IAClC,IAAI,CAACU,MAAM,CAACI,KAAK,CAACd,KAAK,KAAK,OAAO,IACnC,IAAI,CAACU,MAAM,CAACI,KAAK,CAACd,KAAK,KAAK,MAAM,EAClC;MACA,MAAMnB,WAAW,CACf,IAAI,CAAC6B,MAAM,CAAClB,MAAM,EAClB,IAAI,CAACkB,MAAM,CAACI,KAAK,CAACuB,KAAK,EACtB,GAAEwH,YAAY,CACb,IAAI,CAACnJ,MAAM,CAACI,KACd,CAAE,oDACJ,CAAC;IACH;IAEA,OAAO,IAAI,CAACD,SAAS,CAAC,CAAC;EACzB;EACA;AACF;AACA;AACA;;EAEEsB,8BAA8BA,CAAA,EAAG;IAC/B,MAAME,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,OAAO,CAAC;IAC3B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMkD,MAAM,GAAG,IAAI,CAACyC,0BAA0B,CAAC,CAAC;IAChD,OAAO,IAAI,CAAC9I,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC8K,4BAA4B;MACvC9B,WAAW;MACXrF,IAAI;MACJG,UAAU;MACVsE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEyC,0BAA0BA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACtG,YAAY,CACtBlE,SAAS,CAACiC,OAAO,EACjB,IAAI,CAAC0H,kBAAkB,EACvB3J,SAAS,CAACmF,OACZ,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEElC,wBAAwBA,CAAA,EAAG;IACzB,MAAMZ,YAAY,GAAG,IAAI,CAACjB,MAAM,CAACkB,SAAS,CAAC,CAAC;IAE5C,IAAID,YAAY,CAACV,IAAI,KAAK3B,SAAS,CAACyB,IAAI,EAAE;MACxC,QAAQY,YAAY,CAAC3B,KAAK;QACxB,KAAK,QAAQ;UACX,OAAO,IAAI,CAACgK,oBAAoB,CAAC,CAAC;QAEpC,KAAK,QAAQ;UACX,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;QAExC,KAAK,MAAM;UACT,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;QAExC,KAAK,WAAW;UACd,OAAO,IAAI,CAACC,2BAA2B,CAAC,CAAC;QAE3C,KAAK,OAAO;UACV,OAAO,IAAI,CAACC,uBAAuB,CAAC,CAAC;QAEvC,KAAK,MAAM;UACT,OAAO,IAAI,CAACC,sBAAsB,CAAC,CAAC;QAEtC,KAAK,OAAO;UACV,OAAO,IAAI,CAACC,6BAA6B,CAAC,CAAC;MAC/C;IACF;IAEA,MAAM,IAAI,CAAC9H,UAAU,CAACb,YAAY,CAAC;EACrC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAEEqI,oBAAoBA,CAAA,EAAG;IACrB,MAAM3H,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACA,aAAa,CAAC,QAAQ,CAAC;IAC5B,MAAM/C,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAM+D,cAAc,GAAG,IAAI,CAAC1E,YAAY,CACtClE,SAAS,CAACiC,OAAO,EACjB,IAAI,CAAC4G,4BAA4B,EACjC7I,SAAS,CAACmF,OACZ,CAAC;IAED,IAAI1B,UAAU,CAACwH,MAAM,KAAK,CAAC,IAAIrC,cAAc,CAACqC,MAAM,KAAK,CAAC,EAAE;MAC1D,MAAM,IAAI,CAAC/H,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACxB,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACuL,gBAAgB;MAC3BzH,UAAU;MACVmF;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;;EAEE+B,wBAAwBA,CAAA,EAAG;IACzB,MAAM5H,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACA,aAAa,CAAC,QAAQ,CAAC;IAC5B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAE9C,IAAIpB,UAAU,CAACwH,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAI,CAAC/H,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACxB,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACwL,qBAAqB;MAChC7H,IAAI;MACJG;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;;EAEEmH,wBAAwBA,CAAA,EAAG;IACzB,MAAM7H,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACA,aAAa,CAAC,MAAM,CAAC;IAC1B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAM0H,UAAU,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACnD,MAAMzF,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMkD,MAAM,GAAG,IAAI,CAACoB,qBAAqB,CAAC,CAAC;IAE3C,IACEF,UAAU,CAACgC,MAAM,KAAK,CAAC,IACvBxH,UAAU,CAACwH,MAAM,KAAK,CAAC,IACvBlD,MAAM,CAACkD,MAAM,KAAK,CAAC,EACnB;MACA,MAAM,IAAI,CAAC/H,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACxB,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACyL,qBAAqB;MAChC9H,IAAI;MACJ2F,UAAU;MACVxF,UAAU;MACVsE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;;EAEE8C,2BAA2BA,CAAA,EAAG;IAC5B,MAAM9H,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACA,aAAa,CAAC,WAAW,CAAC;IAC/B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAM0H,UAAU,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACnD,MAAMzF,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMkD,MAAM,GAAG,IAAI,CAACoB,qBAAqB,CAAC,CAAC;IAE3C,IACEF,UAAU,CAACgC,MAAM,KAAK,CAAC,IACvBxH,UAAU,CAACwH,MAAM,KAAK,CAAC,IACvBlD,MAAM,CAACkD,MAAM,KAAK,CAAC,EACnB;MACA,MAAM,IAAI,CAAC/H,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACxB,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC0L,wBAAwB;MACnC/H,IAAI;MACJ2F,UAAU;MACVxF,UAAU;MACVsE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEE+C,uBAAuBA,CAAA,EAAG;IACxB,MAAM/H,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACA,aAAa,CAAC,OAAO,CAAC;IAC3B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMiF,KAAK,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAE1C,IAAItG,UAAU,CAACwH,MAAM,KAAK,CAAC,IAAInB,KAAK,CAACmB,MAAM,KAAK,CAAC,EAAE;MACjD,MAAM,IAAI,CAAC/H,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACxB,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC2L,oBAAoB;MAC/BhI,IAAI;MACJG,UAAU;MACVqG;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEiB,sBAAsBA,CAAA,EAAG;IACvB,MAAMhI,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACA,aAAa,CAAC,MAAM,CAAC;IAC1B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAM6C,MAAM,GAAG,IAAI,CAACwC,yBAAyB,CAAC,CAAC;IAE/C,IAAIzG,UAAU,CAACwH,MAAM,KAAK,CAAC,IAAIvD,MAAM,CAACuD,MAAM,KAAK,CAAC,EAAE;MAClD,MAAM,IAAI,CAAC/H,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACxB,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC4L,mBAAmB;MAC9BjI,IAAI;MACJG,UAAU;MACViE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEsD,6BAA6BA,CAAA,EAAG;IAC9B,MAAMjI,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,IAAI,CAACgF,aAAa,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACA,aAAa,CAAC,OAAO,CAAC;IAC3B,MAAMlD,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMkC,UAAU,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC9C,MAAMkD,MAAM,GAAG,IAAI,CAACyC,0BAA0B,CAAC,CAAC;IAEhD,IAAI/G,UAAU,CAACwH,MAAM,KAAK,CAAC,IAAIlD,MAAM,CAACkD,MAAM,KAAK,CAAC,EAAE;MAClD,MAAM,IAAI,CAAC/H,UAAU,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI,CAACxB,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAAC6L,2BAA2B;MACtClI,IAAI;MACJG,UAAU;MACVsE;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;;EAEEjF,wBAAwBA,CAAA,EAAG;IACzB,MAAMC,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAMmH,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC;IAC3C,IAAI,CAAClC,aAAa,CAAC,WAAW,CAAC;IAC/B,IAAI,CAAChG,WAAW,CAACR,SAAS,CAACiI,EAAE,CAAC;IAC9B,MAAM3E,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAC7B,MAAMiI,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,MAAMgC,UAAU,GAAG,IAAI,CAACvF,qBAAqB,CAAC,YAAY,CAAC;IAC3D,IAAI,CAACM,aAAa,CAAC,IAAI,CAAC;IACxB,MAAMkF,SAAS,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAChD,OAAO,IAAI,CAACjK,IAAI,CAACqB,KAAK,EAAE;MACtBpB,IAAI,EAAEhC,IAAI,CAACiM,oBAAoB;MAC/BjD,WAAW;MACXrF,IAAI;MACJoC,SAAS,EAAE8D,IAAI;MACfiC,UAAU;MACVC;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;;EAEEC,uBAAuBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACtC,aAAa,CAACrJ,SAAS,CAACiK,IAAI,EAAE,IAAI,CAAC4B,sBAAsB,CAAC;EACxE;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEEA,sBAAsBA,CAAA,EAAG;IACvB,MAAM9I,KAAK,GAAG,IAAI,CAAC3B,MAAM,CAACI,KAAK;IAC/B,MAAM8B,IAAI,GAAG,IAAI,CAAC/B,SAAS,CAAC,CAAC;IAE7B,IAAIuK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACvM,iBAAiB,EAAE4D,IAAI,CAAC5C,KAAK,CAAC,EAAE;MACvE,OAAO4C,IAAI;IACb;IAEA,MAAM,IAAI,CAACJ,UAAU,CAACH,KAAK,CAAC;EAC9B,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;;EAEErB,IAAIA,CAACwK,UAAU,EAAExK,IAAI,EAAE;IACrB,IAAI,IAAI,CAACL,QAAQ,CAAC8K,UAAU,KAAK,IAAI,EAAE;MACrCzK,IAAI,CAAC0K,GAAG,GAAG,IAAI5M,QAAQ,CACrB0M,UAAU,EACV,IAAI,CAAC9K,MAAM,CAACiL,SAAS,EACrB,IAAI,CAACjL,MAAM,CAAClB,MACd,CAAC;IACH;IAEA,OAAOwB,IAAI;EACb;EACA;AACF;AACA;;EAEEM,IAAIA,CAACL,IAAI,EAAE;IACT,OAAO,IAAI,CAACP,MAAM,CAACI,KAAK,CAACG,IAAI,KAAKA,IAAI;EACxC;EACA;AACF;AACA;AACA;;EAEEnB,WAAWA,CAACmB,IAAI,EAAE;IAChB,MAAMH,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;IAE/B,IAAIA,KAAK,CAACG,IAAI,KAAKA,IAAI,EAAE;MACvB,IAAI,CAACoF,YAAY,CAAC,CAAC;MACnB,OAAOvF,KAAK;IACd;IAEA,MAAMjC,WAAW,CACf,IAAI,CAAC6B,MAAM,CAAClB,MAAM,EAClBsB,KAAK,CAACuB,KAAK,EACV,YAAWuJ,gBAAgB,CAAC3K,IAAI,CAAE,WAAU4I,YAAY,CAAC/I,KAAK,CAAE,GACnE,CAAC;EACH;EACA;AACF;AACA;AACA;;EAEEmD,mBAAmBA,CAAChD,IAAI,EAAE;IACxB,MAAMH,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;IAE/B,IAAIA,KAAK,CAACG,IAAI,KAAKA,IAAI,EAAE;MACvB,IAAI,CAACoF,YAAY,CAAC,CAAC;MACnB,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EACA;AACF;AACA;AACA;;EAEEP,aAAaA,CAAC9F,KAAK,EAAE;IACnB,MAAMc,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;IAE/B,IAAIA,KAAK,CAACG,IAAI,KAAK3B,SAAS,CAACyB,IAAI,IAAID,KAAK,CAACd,KAAK,KAAKA,KAAK,EAAE;MAC1D,IAAI,CAACqG,YAAY,CAAC,CAAC;IACrB,CAAC,MAAM;MACL,MAAMxH,WAAW,CACf,IAAI,CAAC6B,MAAM,CAAClB,MAAM,EAClBsB,KAAK,CAACuB,KAAK,EACV,aAAYrC,KAAM,YAAW6J,YAAY,CAAC/I,KAAK,CAAE,GACpD,CAAC;IACH;EACF;EACA;AACF;AACA;AACA;;EAEE0E,qBAAqBA,CAACxF,KAAK,EAAE;IAC3B,MAAMc,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;IAE/B,IAAIA,KAAK,CAACG,IAAI,KAAK3B,SAAS,CAACyB,IAAI,IAAID,KAAK,CAACd,KAAK,KAAKA,KAAK,EAAE;MAC1D,IAAI,CAACqG,YAAY,CAAC,CAAC;MACnB,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EACA;AACF;AACA;;EAEE7D,UAAUA,CAACqJ,OAAO,EAAE;IAClB,MAAM/K,KAAK,GACT+K,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,IAAI,CAACnL,MAAM,CAACI,KAAK;IACtE,OAAOjC,WAAW,CAChB,IAAI,CAAC6B,MAAM,CAAClB,MAAM,EAClBsB,KAAK,CAACuB,KAAK,EACV,cAAawH,YAAY,CAAC/I,KAAK,CAAE,GACpC,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;;EAEEmG,GAAGA,CAAC6E,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;IAChC,IAAI,CAAClM,WAAW,CAACgM,QAAQ,CAAC;IAC1B,MAAMG,KAAK,GAAG,EAAE;IAEhB,OAAO,CAAC,IAAI,CAAChI,mBAAmB,CAAC+H,SAAS,CAAC,EAAE;MAC3CC,KAAK,CAACzE,IAAI,CAACuE,OAAO,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC;IAEA,OAAOU,KAAK;EACd;EACA;AACF;AACA;AACA;AACA;AACA;;EAEEzI,YAAYA,CAACsI,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;IACzC,IAAI,IAAI,CAAC/H,mBAAmB,CAAC6H,QAAQ,CAAC,EAAE;MACtC,MAAMG,KAAK,GAAG,EAAE;MAEhB,GAAG;QACDA,KAAK,CAACzE,IAAI,CAACuE,OAAO,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;MAChC,CAAC,QAAQ,CAAC,IAAI,CAACtH,mBAAmB,CAAC+H,SAAS,CAAC;MAE7C,OAAOC,KAAK;IACd;IAEA,OAAO,EAAE;EACX;EACA;AACF;AACA;AACA;AACA;;EAEE7K,IAAIA,CAAC0K,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;IACjC,IAAI,CAAClM,WAAW,CAACgM,QAAQ,CAAC;IAC1B,MAAMG,KAAK,GAAG,EAAE;IAEhB,GAAG;MACDA,KAAK,CAACzE,IAAI,CAACuE,OAAO,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,QAAQ,CAAC,IAAI,CAACtH,mBAAmB,CAAC+H,SAAS,CAAC;IAE7C,OAAOC,KAAK;EACd;EACA;AACF;AACA;AACA;AACA;;EAEEtD,aAAaA,CAACuD,aAAa,EAAEH,OAAO,EAAE;IACpC,IAAI,CAAC9H,mBAAmB,CAACiI,aAAa,CAAC;IACvC,MAAMD,KAAK,GAAG,EAAE;IAEhB,GAAG;MACDA,KAAK,CAACzE,IAAI,CAACuE,OAAO,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,QAAQ,IAAI,CAACtH,mBAAmB,CAACiI,aAAa,CAAC;IAEhD,OAAOD,KAAK;EACd;EAEA5F,YAAYA,CAAA,EAAG;IACb,MAAM;MAAE8F;IAAU,CAAC,GAAG,IAAI,CAACxL,QAAQ;IAEnC,MAAMG,KAAK,GAAG,IAAI,CAACJ,MAAM,CAAC0L,OAAO,CAAC,CAAC;IAEnC,IAAID,SAAS,KAAKtJ,SAAS,IAAI/B,KAAK,CAACG,IAAI,KAAK3B,SAAS,CAACY,GAAG,EAAE;MAC3D,EAAE,IAAI,CAACU,aAAa;MAEpB,IAAI,IAAI,CAACA,aAAa,GAAGuL,SAAS,EAAE;QAClC,MAAMtN,WAAW,CACf,IAAI,CAAC6B,MAAM,CAAClB,MAAM,EAClBsB,KAAK,CAACuB,KAAK,EACV,+BAA8B8J,SAAU,2BAC3C,CAAC;MACH;IACF;EACF;AACF;AACA;AACA;AACA;;AAEA,SAAStC,YAAYA,CAAC/I,KAAK,EAAE;EAC3B,MAAMd,KAAK,GAAGc,KAAK,CAACd,KAAK;EACzB,OAAO4L,gBAAgB,CAAC9K,KAAK,CAACG,IAAI,CAAC,IAAIjB,KAAK,IAAI,IAAI,GAAI,KAAIA,KAAM,GAAE,GAAG,EAAE,CAAC;AAC5E;AACA;AACA;AACA;;AAEA,SAAS4L,gBAAgBA,CAAC3K,IAAI,EAAE;EAC9B,OAAO/B,qBAAqB,CAAC+B,IAAI,CAAC,GAAI,IAAGA,IAAK,GAAE,GAAGA,IAAI;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}