{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\n/**\n * Returns an operation AST given a document AST and optionally an operation\n * name. If a name is not provided, an operation is only returned if only one is\n * provided in the document.\n */\n\nexport function getOperationAST(documentAST, operationName) {\n  let operation = null;\n  for (const definition of documentAST.definitions) {\n    if (definition.kind === Kind.OPERATION_DEFINITION) {\n      var _definition$name;\n      if (operationName == null) {\n        // If no operation name was provided, only return an Operation if there\n        // is one defined in the document. Upon encountering the second, return\n        // null.\n        if (operation) {\n          return null;\n        }\n        operation = definition;\n      } else if (((_definition$name = definition.name) === null || _definition$name === void 0 ? void 0 : _definition$name.value) === operationName) {\n        return definition;\n      }\n    }\n  }\n  return operation;\n}", "map": {"version": 3, "names": ["Kind", "getOperationAST", "documentAST", "operationName", "operation", "definition", "definitions", "kind", "OPERATION_DEFINITION", "_definition$name", "name", "value"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/utilities/getOperationAST.mjs"], "sourcesContent": ["import { Kind } from '../language/kinds.mjs';\n/**\n * Returns an operation AST given a document AST and optionally an operation\n * name. If a name is not provided, an operation is only returned if only one is\n * provided in the document.\n */\n\nexport function getOperationAST(documentAST, operationName) {\n  let operation = null;\n\n  for (const definition of documentAST.definitions) {\n    if (definition.kind === Kind.OPERATION_DEFINITION) {\n      var _definition$name;\n\n      if (operationName == null) {\n        // If no operation name was provided, only return an Operation if there\n        // is one defined in the document. Upon encountering the second, return\n        // null.\n        if (operation) {\n          return null;\n        }\n\n        operation = definition;\n      } else if (\n        ((_definition$name = definition.name) === null ||\n        _definition$name === void 0\n          ? void 0\n          : _definition$name.value) === operationName\n      ) {\n        return definition;\n      }\n    }\n  }\n\n  return operation;\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,WAAW,EAAEC,aAAa,EAAE;EAC1D,IAAIC,SAAS,GAAG,IAAI;EAEpB,KAAK,MAAMC,UAAU,IAAIH,WAAW,CAACI,WAAW,EAAE;IAChD,IAAID,UAAU,CAACE,IAAI,KAAKP,IAAI,CAACQ,oBAAoB,EAAE;MACjD,IAAIC,gBAAgB;MAEpB,IAAIN,aAAa,IAAI,IAAI,EAAE;QACzB;QACA;QACA;QACA,IAAIC,SAAS,EAAE;UACb,OAAO,IAAI;QACb;QAEAA,SAAS,GAAGC,UAAU;MACxB,CAAC,MAAM,IACL,CAAC,CAACI,gBAAgB,GAAGJ,UAAU,CAACK,IAAI,MAAM,IAAI,IAC9CD,gBAAgB,KAAK,KAAK,CAAC,GACvB,KAAK,CAAC,GACNA,gBAAgB,CAACE,KAAK,MAAMR,aAAa,EAC7C;QACA,OAAOE,UAAU;MACnB;IACF;EACF;EAEA,OAAOD,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}