{"ast": null, "code": "import { ConnectableObservable, isObservable, of, Subject } from 'rxjs';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken } from '@angular/core';\nclass DataSource {}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n  // Check if the value is a DataSource by observing if it has a connect function. Cannot\n  // be checked as an `instanceof DataSource` since people could create their own sources\n  // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n  // here, because of some internal apps.\n  return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n  constructor(_data) {\n    super();\n    this._data = _data;\n  }\n  connect() {\n    return isObservable(this._data) ? this._data : of(this._data);\n  }\n  disconnect() {}\n}\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n        operation = 1 /* _ViewRepeaterOperation.INSERTED */;\n      } else if (currentIndex == null) {\n        viewContainerRef.remove(adjustedPreviousIndex);\n        operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n      } else {\n        view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        operation = 2 /* _ViewRepeaterOperation.MOVED */;\n      }\n\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {}\n}\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n  constructor() {\n    /**\n     * The size of the cache used to store unused views.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n     */\n    this.viewCacheSize = 20;\n    /**\n     * View cache that stores embedded view instances that have been previously stamped out,\n     * but don't are not currently rendered. The view repeater will reuse these views rather than\n     * creating brand new ones.\n     *\n     * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n     */\n    this._viewCache = [];\n  }\n  /** Apply changes to the DOM. */\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        // Item added.\n        const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = view ? 1 /* _ViewRepeaterOperation.INSERTED */ : 0 /* _ViewRepeaterOperation.REPLACED */;\n      } else if (currentIndex == null) {\n        // Item removed.\n        this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n        operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n      } else {\n        // Item moved.\n        view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = 2 /* _ViewRepeaterOperation.MOVED */;\n      }\n\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n    this._viewCache = [];\n  }\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n  _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n    const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n  }\n  /** Detaches the view at the given index and inserts into the view cache. */\n  _detachAndCacheView(index, viewContainerRef) {\n    const detachedView = viewContainerRef.detach(index);\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n  /** Moves view at the previous index to the current index. */\n  _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n    const view = viewContainerRef.get(adjustedPreviousIndex);\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n  _maybeCacheView(view, viewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view);\n      // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n  /** Inserts a recycled view from the cache at the given index. */\n  _insertViewFromCache(index, viewContainerRef) {\n    const cachedView = this._viewCache.pop();\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n    return cachedView || null;\n  }\n}\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n  /** Selected values. */\n  get selected() {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n    return this._selected;\n  }\n  constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n    this._multiple = _multiple;\n    this._emitChanges = _emitChanges;\n    this.compareWith = compareWith;\n    /** Currently-selected values. */\n    this._selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    this._deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    this._selectedToEmit = [];\n    /** Event emitted when the value has changed. */\n    this.changed = new Subject();\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n  /**\n   * Selects a value or an array of values.\n   * @param values The values to select\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  select(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Deselects a value or an array of values.\n   * @param values The values to deselect\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  deselect(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Sets the selected values\n   * @param values The new selected values\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  setSelection(...values) {\n    this._verifyValueAssignment(values);\n    const oldValues = this.selected;\n    const newSelectedSet = new Set(values);\n    values.forEach(value => this._markSelected(value));\n    oldValues.filter(value => !newSelectedSet.has(value)).forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Toggles a value between selected and deselected.\n   * @param value The value to toggle\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  toggle(value) {\n    return this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n  /**\n   * Clears all of the selected values.\n   * @param flushEvent Whether to flush the changes in an event.\n   *   If false, the changes to the selection will be flushed along with the next event.\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  clear(flushEvent = true) {\n    this._unmarkAll();\n    const changed = this._hasQueuedChanges();\n    if (flushEvent) {\n      this._emitChangeEvent();\n    }\n    return changed;\n  }\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value) {\n    return this._selection.has(this._getConcreteValue(value));\n  }\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty() {\n    return this._selection.size === 0;\n  }\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue() {\n    return !this.isEmpty();\n  }\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate) {\n    if (this._multiple && this.selected) {\n      this._selected.sort(predicate);\n    }\n  }\n  /**\n   * Gets whether multiple values can be selected.\n   */\n  isMultipleSelection() {\n    return this._multiple;\n  }\n  /** Emits a change event and clears the records of selected and deselected values. */\n  _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit\n      });\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n  /** Selects a value. */\n  _markSelected(value) {\n    value = this._getConcreteValue(value);\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n      if (!this.isSelected(value)) {\n        this._selection.add(value);\n      }\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n  /** Deselects a value. */\n  _unmarkSelected(value) {\n    value = this._getConcreteValue(value);\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n  /** Clears out the selected values. */\n  _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  _verifyValueAssignment(values) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n  /** Whether there are queued up change to be emitted. */\n  _hasQueuedChanges() {\n    return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n  }\n  /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n  _getConcreteValue(inputValue) {\n    if (!this.compareWith) {\n      return inputValue;\n    } else {\n      for (let selectedValue of this._selection) {\n        if (this.compareWith(inputValue, selectedValue)) {\n          return selectedValue;\n        }\n      }\n      return inputValue;\n    }\n  }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  constructor() {\n    this._listeners = [];\n  }\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n  static {\n    this.ɵfac = function UniqueSelectionDispatcher_Factory(t) {\n      return new (t || UniqueSelectionDispatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UniqueSelectionDispatcher,\n      factory: UniqueSelectionDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrayDataSource, DataSource, SelectionModel, UniqueSelectionDispatcher, _DisposeViewRepeaterStrategy, _RecycleViewRepeaterStrategy, _VIEW_REPEATER_STRATEGY, getMultipleValuesInSingleSelectionError, isDataSource };", "map": {"version": 3, "names": ["ConnectableObservable", "isObservable", "of", "Subject", "i0", "Injectable", "InjectionToken", "DataSource", "isDataSource", "value", "connect", "ArrayDataSource", "constructor", "_data", "disconnect", "_DisposeViewRepeaterStrategy", "applyChanges", "changes", "viewContainerRef", "itemContextFactory", "itemValueResolver", "itemViewChanged", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "view", "operation", "previousIndex", "insertContext", "createEmbeddedView", "templateRef", "context", "index", "remove", "get", "move", "detach", "_RecycleViewRepeaterStrategy", "viewCacheSize", "_viewCache", "viewArgsFactory", "_insertView", "_detachAndCacheView", "_moveView", "destroy", "cachedView", "_insertViewFromCache", "$implicit", "undefined", "viewArgs", "detached<PERSON>iew", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "push", "indexOf", "pop", "insert", "SelectionModel", "selected", "_selected", "Array", "from", "_selection", "values", "_multiple", "initiallySelectedValues", "_emitChanges", "compareWith", "Set", "_deselectedToEmit", "_selectedToEmit", "changed", "for<PERSON>ach", "_markSelected", "select", "_verifyValueAssignment", "_hasQueuedChanges", "_emitChangeEvent", "deselect", "_unmarkSelected", "setSelection", "oldValues", "newSelectedSet", "filter", "has", "toggle", "isSelected", "clear", "flushEvent", "_unmarkAll", "_getConcreteValue", "isEmpty", "size", "hasValue", "sort", "predicate", "isMultipleSelection", "next", "source", "added", "removed", "add", "delete", "ngDevMode", "getMultipleValuesInSingleSelectionError", "inputValue", "selected<PERSON><PERSON><PERSON>", "Error", "UniqueSelectionDispatcher", "_listeners", "notify", "id", "name", "listener", "listen", "registered", "ngOnDestroy", "ɵfac", "UniqueSelectionDispatcher_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args", "_VIEW_REPEATER_STRATEGY"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@angular/cdk/fesm2022/collections.mjs"], "sourcesContent": ["import { ConnectableObservable, isObservable, of, Subject } from 'rxjs';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken } from '@angular/core';\n\nclass DataSource {\n}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n    // Check if the value is a DataSource by observing if it has a connect function. Cannot\n    // be checked as an `instanceof DataSource` since people could create their own sources\n    // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n    // here, because of some internal apps.\n    return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n    constructor(_data) {\n        super();\n        this._data = _data;\n    }\n    connect() {\n        return isObservable(this._data) ? this._data : of(this._data);\n    }\n    disconnect() { }\n}\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n                operation = 1 /* _ViewRepeaterOperation.INSERTED */;\n            }\n            else if (currentIndex == null) {\n                viewContainerRef.remove(adjustedPreviousIndex);\n                operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n            }\n            else {\n                view = viewContainerRef.get(adjustedPreviousIndex);\n                viewContainerRef.move(view, currentIndex);\n                operation = 2 /* _ViewRepeaterOperation.MOVED */;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() { }\n}\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n    constructor() {\n        /**\n         * The size of the cache used to store unused views.\n         * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n         */\n        this.viewCacheSize = 20;\n        /**\n         * View cache that stores embedded view instances that have been previously stamped out,\n         * but don't are not currently rendered. The view repeater will reuse these views rather than\n         * creating brand new ones.\n         *\n         * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n         */\n        this._viewCache = [];\n    }\n    /** Apply changes to the DOM. */\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        // Rearrange the views to put them in the right location.\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                // Item added.\n                const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = view ? 1 /* _ViewRepeaterOperation.INSERTED */ : 0 /* _ViewRepeaterOperation.REPLACED */;\n            }\n            else if (currentIndex == null) {\n                // Item removed.\n                this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n                operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n            }\n            else {\n                // Item moved.\n                view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = 2 /* _ViewRepeaterOperation.MOVED */;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() {\n        for (const view of this._viewCache) {\n            view.destroy();\n        }\n        this._viewCache = [];\n    }\n    /**\n     * Inserts a view for a new item, either from the cache or by creating a new\n     * one. Returns `undefined` if the item was inserted into a cached view.\n     */\n    _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n        const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n        if (cachedView) {\n            cachedView.context.$implicit = value;\n            return undefined;\n        }\n        const viewArgs = viewArgsFactory();\n        return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n    }\n    /** Detaches the view at the given index and inserts into the view cache. */\n    _detachAndCacheView(index, viewContainerRef) {\n        const detachedView = viewContainerRef.detach(index);\n        this._maybeCacheView(detachedView, viewContainerRef);\n    }\n    /** Moves view at the previous index to the current index. */\n    _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n        const view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        view.context.$implicit = value;\n        return view;\n    }\n    /**\n     * Cache the given detached view. If the cache is full, the view will be\n     * destroyed.\n     */\n    _maybeCacheView(view, viewContainerRef) {\n        if (this._viewCache.length < this.viewCacheSize) {\n            this._viewCache.push(view);\n        }\n        else {\n            const index = viewContainerRef.indexOf(view);\n            // The host component could remove views from the container outside of\n            // the view repeater. It's unlikely this will occur, but just in case,\n            // destroy the view on its own, otherwise destroy it through the\n            // container to ensure that all the references are removed.\n            if (index === -1) {\n                view.destroy();\n            }\n            else {\n                viewContainerRef.remove(index);\n            }\n        }\n    }\n    /** Inserts a recycled view from the cache at the given index. */\n    _insertViewFromCache(index, viewContainerRef) {\n        const cachedView = this._viewCache.pop();\n        if (cachedView) {\n            viewContainerRef.insert(cachedView, index);\n        }\n        return cachedView || null;\n    }\n}\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        /** Currently-selected values. */\n        this._selection = new Set();\n        /** Keeps track of the deselected options that haven't been emitted by the change event. */\n        this._deselectedToEmit = [];\n        /** Keeps track of the selected options that haven't been emitted by the change event. */\n        this._selectedToEmit = [];\n        /** Event emitted when the value has changed. */\n        this.changed = new Subject();\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values);\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(value))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            for (let selectedValue of this._selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n    constructor() {\n        this._listeners = [];\n    }\n    /**\n     * Notify other items that selection for the given name has been set.\n     * @param id ID of the item.\n     * @param name Name of the item.\n     */\n    notify(id, name) {\n        for (let listener of this._listeners) {\n            listener(id, name);\n        }\n    }\n    /**\n     * Listen for future changes to item selection.\n     * @return Function used to deregister listener\n     */\n    listen(listener) {\n        this._listeners.push(listener);\n        return () => {\n            this._listeners = this._listeners.filter((registered) => {\n                return listener !== registered;\n            });\n        };\n    }\n    ngOnDestroy() {\n        this._listeners = [];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: UniqueSelectionDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: UniqueSelectionDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: UniqueSelectionDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrayDataSource, DataSource, SelectionModel, UniqueSelectionDispatcher, _DisposeViewRepeaterStrategy, _RecycleViewRepeaterStrategy, _VIEW_REPEATER_STRATEGY, getMultipleValuesInSingleSelectionError, isDataSource };\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,YAAY,EAAEC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AACvE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,QAAQ,eAAe;AAE1D,MAAMC,UAAU,CAAC;AAEjB;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB;EACA;EACA;EACA;EACA,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACC,OAAO,KAAK,UAAU,IAAI,EAAED,KAAK,YAAYT,qBAAqB,CAAC;AACpG;;AAEA;AACA,MAAMW,eAAe,SAASJ,UAAU,CAAC;EACrCK,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAH,OAAOA,CAAA,EAAG;IACN,OAAOT,YAAY,CAAC,IAAI,CAACY,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,GAAGX,EAAE,CAAC,IAAI,CAACW,KAAK,CAAC;EACjE;EACAC,UAAUA,CAAA,EAAG,CAAE;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/BC,YAAYA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5FJ,OAAO,CAACK,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B,MAAMC,aAAa,GAAGV,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QACrFC,IAAI,GAAGR,gBAAgB,CAACY,kBAAkB,CAACD,aAAa,CAACE,WAAW,EAAEF,aAAa,CAACG,OAAO,EAAEH,aAAa,CAACI,KAAK,CAAC;QACjHN,SAAS,GAAG,CAAC,CAAC;MAClB,CAAC,MACI,IAAIF,YAAY,IAAI,IAAI,EAAE;QAC3BP,gBAAgB,CAACgB,MAAM,CAACV,qBAAqB,CAAC;QAC9CG,SAAS,GAAG,CAAC,CAAC;MAClB,CAAC,MACI;QACDD,IAAI,GAAGR,gBAAgB,CAACiB,GAAG,CAACX,qBAAqB,CAAC;QAClDN,gBAAgB,CAACkB,IAAI,CAACV,IAAI,EAAED,YAAY,CAAC;QACzCE,SAAS,GAAG,CAAC,CAAC;MAClB;;MACA,IAAIN,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,EAAEM,OAAO;UACtBL,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAc,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/B1B,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAAC2B,aAAa,GAAG,EAAE;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,EAAE;EACxB;EACA;EACAxB,YAAYA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5F;IACAJ,OAAO,CAACK,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B;QACA,MAAMa,eAAe,GAAGA,CAAA,KAAMtB,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QAC7FC,IAAI,GAAG,IAAI,CAACgB,WAAW,CAACD,eAAe,EAAEhB,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACnGI,SAAS,GAAGD,IAAI,GAAG,CAAC,CAAC,wCAAwC,CAAC,CAAC;MACnE,CAAC,MACI,IAAID,YAAY,IAAI,IAAI,EAAE;QAC3B;QACA,IAAI,CAACkB,mBAAmB,CAACnB,qBAAqB,EAAEN,gBAAgB,CAAC;QACjES,SAAS,GAAG,CAAC,CAAC;MAClB,CAAC,MACI;QACD;QACAD,IAAI,GAAG,IAAI,CAACkB,SAAS,CAACpB,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACvGI,SAAS,GAAG,CAAC,CAAC;MAClB;;MACA,IAAIN,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,EAAEM,OAAO;UACtBL,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAc,MAAMA,CAAA,EAAG;IACL,KAAK,MAAMX,IAAI,IAAI,IAAI,CAACc,UAAU,EAAE;MAChCd,IAAI,CAACmB,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,CAACL,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIE,WAAWA,CAACD,eAAe,EAAEhB,YAAY,EAAEP,gBAAgB,EAAET,KAAK,EAAE;IAChE,MAAMqC,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAACtB,YAAY,EAAEP,gBAAgB,CAAC;IAC5E,IAAI4B,UAAU,EAAE;MACZA,UAAU,CAACd,OAAO,CAACgB,SAAS,GAAGvC,KAAK;MACpC,OAAOwC,SAAS;IACpB;IACA,MAAMC,QAAQ,GAAGT,eAAe,CAAC,CAAC;IAClC,OAAOvB,gBAAgB,CAACY,kBAAkB,CAACoB,QAAQ,CAACnB,WAAW,EAAEmB,QAAQ,CAAClB,OAAO,EAAEkB,QAAQ,CAACjB,KAAK,CAAC;EACtG;EACA;EACAU,mBAAmBA,CAACV,KAAK,EAAEf,gBAAgB,EAAE;IACzC,MAAMiC,YAAY,GAAGjC,gBAAgB,CAACmB,MAAM,CAACJ,KAAK,CAAC;IACnD,IAAI,CAACmB,eAAe,CAACD,YAAY,EAAEjC,gBAAgB,CAAC;EACxD;EACA;EACA0B,SAASA,CAACpB,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAET,KAAK,EAAE;IACpE,MAAMiB,IAAI,GAAGR,gBAAgB,CAACiB,GAAG,CAACX,qBAAqB,CAAC;IACxDN,gBAAgB,CAACkB,IAAI,CAACV,IAAI,EAAED,YAAY,CAAC;IACzCC,IAAI,CAACM,OAAO,CAACgB,SAAS,GAAGvC,KAAK;IAC9B,OAAOiB,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI0B,eAAeA,CAAC1B,IAAI,EAAER,gBAAgB,EAAE;IACpC,IAAI,IAAI,CAACsB,UAAU,CAACa,MAAM,GAAG,IAAI,CAACd,aAAa,EAAE;MAC7C,IAAI,CAACC,UAAU,CAACc,IAAI,CAAC5B,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,MAAMO,KAAK,GAAGf,gBAAgB,CAACqC,OAAO,CAAC7B,IAAI,CAAC;MAC5C;MACA;MACA;MACA;MACA,IAAIO,KAAK,KAAK,CAAC,CAAC,EAAE;QACdP,IAAI,CAACmB,OAAO,CAAC,CAAC;MAClB,CAAC,MACI;QACD3B,gBAAgB,CAACgB,MAAM,CAACD,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;EACAc,oBAAoBA,CAACd,KAAK,EAAEf,gBAAgB,EAAE;IAC1C,MAAM4B,UAAU,GAAG,IAAI,CAACN,UAAU,CAACgB,GAAG,CAAC,CAAC;IACxC,IAAIV,UAAU,EAAE;MACZ5B,gBAAgB,CAACuC,MAAM,CAACX,UAAU,EAAEb,KAAK,CAAC;IAC9C;IACA,OAAOa,UAAU,IAAI,IAAI;EAC7B;AACJ;;AAEA;AACA;AACA;AACA,MAAMY,cAAc,CAAC;EACjB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC;IACzD;IACA,OAAO,IAAI,CAACJ,SAAS;EACzB;EACAhD,WAAWA,CAACqD,SAAS,GAAG,KAAK,EAAEC,uBAAuB,EAAEC,YAAY,GAAG,IAAI,EAAEC,WAAW,EAAE;IACtF,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACL,UAAU,GAAG,IAAIM,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;IACA,IAAI,CAACC,OAAO,GAAG,IAAIrE,OAAO,CAAC,CAAC;IAC5B,IAAI+D,uBAAuB,IAAIA,uBAAuB,CAACb,MAAM,EAAE;MAC3D,IAAIY,SAAS,EAAE;QACXC,uBAAuB,CAACO,OAAO,CAAChE,KAAK,IAAI,IAAI,CAACiE,aAAa,CAACjE,KAAK,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAACiE,aAAa,CAACR,uBAAuB,CAAC,CAAC,CAAC,CAAC;MAClD;MACA;MACA,IAAI,CAACK,eAAe,CAAClB,MAAM,GAAG,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsB,MAAMA,CAAC,GAAGX,MAAM,EAAE;IACd,IAAI,CAACY,sBAAsB,CAACZ,MAAM,CAAC;IACnCA,MAAM,CAACS,OAAO,CAAChE,KAAK,IAAI,IAAI,CAACiE,aAAa,CAACjE,KAAK,CAAC,CAAC;IAClD,MAAM+D,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAON,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIO,QAAQA,CAAC,GAAGf,MAAM,EAAE;IAChB,IAAI,CAACY,sBAAsB,CAACZ,MAAM,CAAC;IACnCA,MAAM,CAACS,OAAO,CAAChE,KAAK,IAAI,IAAI,CAACuE,eAAe,CAACvE,KAAK,CAAC,CAAC;IACpD,MAAM+D,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAON,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIS,YAAYA,CAAC,GAAGjB,MAAM,EAAE;IACpB,IAAI,CAACY,sBAAsB,CAACZ,MAAM,CAAC;IACnC,MAAMkB,SAAS,GAAG,IAAI,CAACvB,QAAQ;IAC/B,MAAMwB,cAAc,GAAG,IAAId,GAAG,CAACL,MAAM,CAAC;IACtCA,MAAM,CAACS,OAAO,CAAChE,KAAK,IAAI,IAAI,CAACiE,aAAa,CAACjE,KAAK,CAAC,CAAC;IAClDyE,SAAS,CACJE,MAAM,CAAC3E,KAAK,IAAI,CAAC0E,cAAc,CAACE,GAAG,CAAC5E,KAAK,CAAC,CAAC,CAC3CgE,OAAO,CAAChE,KAAK,IAAI,IAAI,CAACuE,eAAe,CAACvE,KAAK,CAAC,CAAC;IAClD,MAAM+D,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAON,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIc,MAAMA,CAAC7E,KAAK,EAAE;IACV,OAAO,IAAI,CAAC8E,UAAU,CAAC9E,KAAK,CAAC,GAAG,IAAI,CAACsE,QAAQ,CAACtE,KAAK,CAAC,GAAG,IAAI,CAACkE,MAAM,CAAClE,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+E,KAAKA,CAACC,UAAU,GAAG,IAAI,EAAE;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,MAAMlB,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAIY,UAAU,EAAE;MACZ,IAAI,CAACX,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAON,OAAO;EAClB;EACA;AACJ;AACA;EACIe,UAAUA,CAAC9E,KAAK,EAAE;IACd,OAAO,IAAI,CAACsD,UAAU,CAACsB,GAAG,CAAC,IAAI,CAACM,iBAAiB,CAAClF,KAAK,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;EACImF,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC7B,UAAU,CAAC8B,IAAI,KAAK,CAAC;EACrC;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;EACIG,IAAIA,CAACC,SAAS,EAAE;IACZ,IAAI,IAAI,CAAC/B,SAAS,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjC,IAAI,CAACC,SAAS,CAACmC,IAAI,CAACC,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;EACIC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAChC,SAAS;EACzB;EACA;EACAa,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAAClB,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACW,eAAe,CAAClB,MAAM,IAAI,IAAI,CAACiB,iBAAiB,CAACjB,MAAM,EAAE;MAC9D,IAAI,CAACmB,OAAO,CAAC0B,IAAI,CAAC;QACdC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,IAAI,CAAC7B,eAAe;QAC3B8B,OAAO,EAAE,IAAI,CAAC/B;MAClB,CAAC,CAAC;MACF,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,eAAe,GAAG,EAAE;IAC7B;EACJ;EACA;EACAG,aAAaA,CAACjE,KAAK,EAAE;IACjBA,KAAK,GAAG,IAAI,CAACkF,iBAAiB,CAAClF,KAAK,CAAC;IACrC,IAAI,CAAC,IAAI,CAAC8E,UAAU,CAAC9E,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC,IAAI,CAACwD,SAAS,EAAE;QACjB,IAAI,CAACyB,UAAU,CAAC,CAAC;MACrB;MACA,IAAI,CAAC,IAAI,CAACH,UAAU,CAAC9E,KAAK,CAAC,EAAE;QACzB,IAAI,CAACsD,UAAU,CAACuC,GAAG,CAAC7F,KAAK,CAAC;MAC9B;MACA,IAAI,IAAI,CAAC0D,YAAY,EAAE;QACnB,IAAI,CAACI,eAAe,CAACjB,IAAI,CAAC7C,KAAK,CAAC;MACpC;IACJ;EACJ;EACA;EACAuE,eAAeA,CAACvE,KAAK,EAAE;IACnBA,KAAK,GAAG,IAAI,CAACkF,iBAAiB,CAAClF,KAAK,CAAC;IACrC,IAAI,IAAI,CAAC8E,UAAU,CAAC9E,KAAK,CAAC,EAAE;MACxB,IAAI,CAACsD,UAAU,CAACwC,MAAM,CAAC9F,KAAK,CAAC;MAC7B,IAAI,IAAI,CAAC0D,YAAY,EAAE;QACnB,IAAI,CAACG,iBAAiB,CAAChB,IAAI,CAAC7C,KAAK,CAAC;MACtC;IACJ;EACJ;EACA;EACAiF,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE;MACjB,IAAI,CAAC7B,UAAU,CAACU,OAAO,CAAChE,KAAK,IAAI,IAAI,CAACuE,eAAe,CAACvE,KAAK,CAAC,CAAC;IACjE;EACJ;EACA;AACJ;AACA;AACA;EACImE,sBAAsBA,CAACZ,MAAM,EAAE;IAC3B,IAAIA,MAAM,CAACX,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACY,SAAS,KAAK,OAAOuC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzF,MAAMC,uCAAuC,CAAC,CAAC;IACnD;EACJ;EACA;EACA5B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAACP,iBAAiB,CAACjB,MAAM,IAAI,IAAI,CAACkB,eAAe,CAAClB,MAAM,CAAC;EAC3E;EACA;EACAsC,iBAAiBA,CAACe,UAAU,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACtC,WAAW,EAAE;MACnB,OAAOsC,UAAU;IACrB,CAAC,MACI;MACD,KAAK,IAAIC,aAAa,IAAI,IAAI,CAAC5C,UAAU,EAAE;QACvC,IAAI,IAAI,CAACK,WAAW,CAACsC,UAAU,EAAEC,aAAa,CAAC,EAAE;UAC7C,OAAOA,aAAa;QACxB;MACJ;MACA,OAAOD,UAAU;IACrB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,uCAAuCA,CAAA,EAAG;EAC/C,OAAOG,KAAK,CAAC,yEAAyE,CAAC;AAC3F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BjG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkG,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACb,KAAK,IAAIC,QAAQ,IAAI,IAAI,CAACJ,UAAU,EAAE;MAClCI,QAAQ,CAACF,EAAE,EAAEC,IAAI,CAAC;IACtB;EACJ;EACA;AACJ;AACA;AACA;EACIE,MAAMA,CAACD,QAAQ,EAAE;IACb,IAAI,CAACJ,UAAU,CAACxD,IAAI,CAAC4D,QAAQ,CAAC;IAC9B,OAAO,MAAM;MACT,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC1B,MAAM,CAAEgC,UAAU,IAAK;QACrD,OAAOF,QAAQ,KAAKE,UAAU;MAClC,CAAC,CAAC;IACN,CAAC;EACL;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACP,UAAU,GAAG,EAAE;EACxB;EACA;IAAS,IAAI,CAACQ,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFX,yBAAyB;IAAA,CAAoD;EAAE;EACzL;IAAS,IAAI,CAACY,KAAK,kBAD6ErH,EAAE,CAAAsH,kBAAA;MAAAC,KAAA,EACYd,yBAAyB;MAAAe,OAAA,EAAzBf,yBAAyB,CAAAS,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AACpK;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAHoGpG,EAAE,CAAA0H,iBAAA,CAGXjB,yBAAyB,EAAc,CAAC;IACvHkB,IAAI,EAAE1H,UAAU;IAChB2H,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMI,uBAAuB,GAAG,IAAI3H,cAAc,CAAC,eAAe,CAAC;;AAEnE;AACA;AACA;;AAEA,SAASK,eAAe,EAAEJ,UAAU,EAAEmD,cAAc,EAAEmD,yBAAyB,EAAE9F,4BAA4B,EAAEuB,4BAA4B,EAAE2F,uBAAuB,EAAExB,uCAAuC,EAAEjG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}