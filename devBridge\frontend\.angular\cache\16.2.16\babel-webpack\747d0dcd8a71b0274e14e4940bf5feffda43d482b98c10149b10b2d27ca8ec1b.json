{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction EquipeFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 23)(3, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 25);\n    i0.ɵɵtext(5, \" Chargement des donn\\u00E9es... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelement(4, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"h3\", 32);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeFormComponent_div_33_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵtext(2, \" Ce nom d'\\u00E9quipe existe d\\u00E9j\\u00E0. Veuillez en choisir un autre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_button_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_button_45_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.deleteEquipe());\n    });\n    i0.ɵɵelement(1, \"i\", 67);\n    i0.ɵɵtext(2, \" Supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 68);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"fa-save\": a0,\n    \"fa-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_i_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, ctx_r8.isEditMode, !ctx_r8.isEditMode));\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-edit\": a0,\n    \"fa-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"div\", 35)(5, \"h3\", 36);\n    i0.ɵɵelement(6, \"i\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 38);\n    i0.ɵɵtext(9, \" Remplissez les informations de base de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"form\", 40);\n    i0.ɵɵlistener(\"ngSubmit\", function EquipeFormComponent_div_33_Template_form_ngSubmit_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onSubmit());\n    });\n    i0.ɵɵelementStart(12, \"div\")(13, \"label\", 41);\n    i0.ɵɵtext(14, \" Nom de l'\\u00E9quipe \");\n    i0.ɵɵelementStart(15, \"span\", 42);\n    i0.ɵɵtext(16, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 22)(18, \"div\", 43);\n    i0.ɵɵelement(19, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 45, 46);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_input_input_20_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const _r3 = i0.ɵɵreference(21);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.updateName(_r3.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, EquipeFormComponent_div_33_div_22_Template, 3, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\")(24, \"label\", 41);\n    i0.ɵɵtext(25, \" Description \");\n    i0.ɵɵelementStart(26, \"span\", 42);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 22)(29, \"div\", 48);\n    i0.ɵɵelement(30, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"textarea\", 50, 51);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_textarea_input_31_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const _r5 = i0.ɵɵreference(32);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.updateDescription(_r5.value));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(33, \"input\", 52);\n    i0.ɵɵelementStart(34, \"div\", 53)(35, \"div\", 54)(36, \"div\", 55);\n    i0.ɵɵelement(37, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 33);\n    i0.ɵɵtext(39, \" Un administrateur par d\\u00E9faut sera assign\\u00E9 \\u00E0 cette \\u00E9quipe. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 57)(41, \"div\", 58)(42, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.cancel());\n    });\n    i0.ɵɵelement(43, \"i\", 17);\n    i0.ɵɵtext(44, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, EquipeFormComponent_div_33_button_45_Template, 3, 0, \"button\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 61);\n    i0.ɵɵtemplate(47, EquipeFormComponent_div_33_span_47_Template, 1, 0, \"span\", 62);\n    i0.ɵɵtemplate(48, EquipeFormComponent_div_33_i_48_Template, 1, 4, \"i\", 63);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c1, ctx_r2.isEditMode, !ctx_r2.isEditMode));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Informations de l'\\u00E9quipe\" : \"D\\u00E9tails de la nouvelle \\u00E9quipe\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.name || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameExists);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.description || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.admin);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipeId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.submitting || !ctx_r2.equipe.name || !ctx_r2.equipe.description || ctx_r2.nameExists || ctx_r2.nameError || ctx_r2.descriptionError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er l'\\u00E9quipe\", \" \");\n  }\n}\nexport class EquipeFormComponent {\n  constructor(equipeService, membreService, userService, route, router, notificationService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipe = {\n      name: '',\n      description: '',\n      admin: '' // Sera défini avec l'ID de l'utilisateur connecté\n    };\n\n    this.isEditMode = false;\n    this.loading = false;\n    this.submitting = false;\n    this.error = null;\n    this.equipeId = null;\n    this.nameExists = false;\n    this.nameError = false;\n    this.descriptionError = false;\n    this.checkingName = false;\n    this.existingEquipes = [];\n    this.availableMembers = []; // Liste des membres disponibles\n    this.availableUsers = []; // Liste des utilisateurs disponibles\n    this.currentUserId = null; // ID de l'utilisateur connecté\n  }\n\n  ngOnInit() {\n    console.log('EquipeFormComponent initialized');\n    // Récupérer l'ID de l'utilisateur connecté\n    this.getCurrentUser();\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n  getCurrentUser() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getProfile(token).subscribe({\n        next: user => {\n          console.log('Utilisateur connecté:', user);\n          this.currentUserId = user._id || user.id;\n          // Définir l'admin de l'équipe avec l'ID de l'utilisateur connecté\n          if (!this.isEditMode && this.currentUserId) {\n            this.equipe.admin = this.currentUserId;\n            console.log('Admin défini pour nouvelle équipe:', this.equipe.admin);\n          }\n        },\n        error: error => {\n          console.error('Erreur lors de la récupération du profil utilisateur:', error);\n          this.error = \"Impossible de récupérer les informations de l'utilisateur connecté.\";\n        }\n      });\n    } else {\n      this.error = \"Aucun token d'authentification trouvé. Veuillez vous reconnecter.\";\n    }\n  }\n  loadAllMembers() {\n    this.membreService.getMembres().subscribe({\n      next: membres => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      }\n    });\n  }\n  loadAllUsers() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getAllUsers(token).subscribe({\n        next: users => {\n          this.availableUsers = users;\n          console.log('Utilisateurs disponibles chargés:', users);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n        }\n      });\n    }\n  }\n  loadAllEquipes() {\n    this.equipeService.getEquipes().subscribe({\n      next: equipes => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      }\n    });\n  }\n  loadEquipe(id) {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails() {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach(membre => {\n      const membreId = this.getMemberId(membre);\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || !user.profession && !user.role) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          const token = localStorage.getItem('token');\n          if (token && membreId) {\n            this.userService.getUserById(membreId, token).subscribe({\n              next: userData => {\n                console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);\n                if (index !== -1) {\n                  this.availableUsers[index] = {\n                    ...this.availableUsers[index],\n                    ...userData\n                  };\n                }\n              },\n              error: error => {\n                console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);\n              }\n            });\n          }\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        const token = localStorage.getItem('token');\n        if (token && membreId) {\n          this.userService.getUserById(membreId, token).subscribe({\n            next: userData => {\n              console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n              if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {\n                this.availableUsers.push(userData);\n              }\n            },\n            error: error => {\n              console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);\n            }\n          });\n        }\n      }\n    });\n  }\n  checkNameExists(name) {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some(e => e.name === name);\n  }\n  updateName(value) {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n  updateDescription(value) {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n  onSubmit() {\n    console.log('Form submitted with:', this.equipe);\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error = \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n    this.submitting = true;\n    this.error = null;\n    // S'assurer que l'admin est défini\n    if (!this.equipe.admin && this.currentUserId) {\n      this.equipe.admin = this.currentUserId;\n    }\n    if (!this.equipe.admin) {\n      this.error = \"Impossible de déterminer l'administrateur de l'équipe. Veuillez vous reconnecter.\";\n      return;\n    }\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin\n    };\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n    console.log('Données à envoyer:', equipeToSave);\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été mise à jour avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    }\n  }\n  cancel() {\n    console.log('Form cancelled');\n    this.router.navigate(['/admin/equipes']);\n  }\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId, role = 'membre') {\n    console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    console.log('equipeId calculé:', equipeId);\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\"Ce membre fait déjà partie de l'équipe\");\n      return;\n    }\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: membreId,\n      role: role\n    };\n    this.loading = true;\n    console.log(`Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`);\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: response => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error = \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\"Erreur lors de l'ajout du membre: \" + error.message);\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId) {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);\n    if (membre && membre.name) {\n      return membre.name;\n    }\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(_membreId) {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de retirer le membre.\");\n      return;\n    }\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');\n      return;\n    }\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n    console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({\n            next: response => {\n              console.log(`Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`, response);\n              this.loading = false;\n              this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);\n              // Recharger l'équipe pour mettre à jour la liste des membres\n              this.loadEquipe(equipeId);\n            },\n            error: error => {\n              console.error(`Erreur lors du retrait de l'utilisateur \"${membreName}\":`, error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  // Méthode pour supprimer l'équipe\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de supprimer l'équipe.\");\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: response => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/admin/equipes']);\n              }, 500);\n            },\n            error: error => {\n              console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  static {\n    this.ɵfac = function EquipeFormComponent_Factory(t) {\n      return new (t || EquipeFormComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeFormComponent,\n      selectors: [[\"app-equipe-form\"]],\n      decls: 34,\n      vars: 5,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-4xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\"], [1, \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#dac4ea]/30\", \"dark:hover:bg-[#00f7ff]/30\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-2\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"mb-8 relative\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#dac4ea]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-6\"], [1, \"text-xl\", \"font-bold\", \"text-white\", \"mb-1\", \"flex\", \"items-center\"], [1, \"fas\", \"mr-2\", 3, \"ngClass\"], [1, \"text-white/80\", \"text-sm\"], [1, \"p-6\"], [1, \"space-y-6\", 3, \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-users\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [\"type\", \"text\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", \"required\", \"\", \"minlength\", \"3\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [\"class\", \"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30] flex items-center\", 4, \"ngIf\"], [1, \"absolute\", \"top-3\", \"left-3\", \"pointer-events-none\"], [1, \"fas\", \"fa-file-alt\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [\"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez l'objectif et les activit\\u00E9s de cette \\u00E9quipe\", \"required\", \"\", \"minlength\", \"10\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", \"resize-none\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [\"type\", \"hidden\", 3, \"value\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"border-l-4\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\", \"rounded-lg\", \"p-4\"], [1, \"flex\", \"items-center\"], [1, \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mr-3\", \"text-lg\"], [1, \"fas\", \"fa-info-circle\"], [1, \"flex\", \"items-center\", \"justify-between\", \"pt-4\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [\"type\", \"button\", 1, \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#6d6870]/30\", \"dark:hover:bg-[#a0a0a0]/30\", 3, \"click\"], [\"type\", \"button\", \"class\", \"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(218,196,234,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\"], [\"class\", \"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas mr-2\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"mt-1\", \"text-sm\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [\"type\", \"button\", 1, \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#ff6b69]/30\", \"dark:hover:bg-[#ff3b30]/30\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-2\"], [1, \"inline-block\", \"w-4\", \"h-4\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\", \"mr-2\"]],\n      template: function EquipeFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 15);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function EquipeFormComponent_Template_button_click_28_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(29, \"i\", 17);\n          i0.ɵɵtext(30, \" Retour \\u00E0 la liste \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(31, EquipeFormComponent_div_31_Template, 6, 0, \"div\", 18);\n          i0.ɵɵtemplate(32, EquipeFormComponent_div_32_Template, 10, 1, \"div\", 19);\n          i0.ɵɵtemplate(33, EquipeFormComponent_div_33_Template, 50, 14, \"div\", 20);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier l'\\u00E9quipe\" : \"Nouvelle \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations et les membres de votre \\u00E9quipe\" : \"Cr\\u00E9ez une nouvelle \\u00E9quipe pour organiser vos projets et membres\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i7.ɵNgNoValidate, i7.NgControlStatusGroup, i7.NgForm],\n      styles: [\".cursor-pointer[_ngcontent-%COMP%] {\\n      cursor: pointer;\\n    }\\n    summary[_ngcontent-%COMP%]:hover {\\n      text-decoration: underline;\\n    }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1mb3JtLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkdBQUc7TUFDRyxlQUFlO0lBQ2pCO0lBQ0E7TUFDRSwwQkFBMEI7SUFDNUIiLCJmaWxlIjoiZXF1aXBlLWZvcm0uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIiAgIC5jdXJzb3ItcG9pbnRlciB7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIH1cclxuICAgIHN1bW1hcnk6aG92ZXIge1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICAgIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9lcXVpcGUtZm9ybS9lcXVpcGUtZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJHQUFHO01BQ0csZUFBZTtJQUNqQjtJQUNBO01BQ0UsMEJBQTBCO0lBQzVCO0FBQ0osd2NBQXdjIiwic291cmNlc0NvbnRlbnQiOlsiICAgLmN1cnNvci1wb2ludGVyIHtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgfVxyXG4gICAgc3VtbWFyeTpob3ZlciB7XHJcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵlistener", "EquipeFormComponent_div_33_button_45_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "deleteEquipe", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ctx_r8", "isEditMode", "EquipeFormComponent_div_33_Template_form_ngSubmit_11_listener", "_r12", "ctx_r11", "onSubmit", "EquipeFormComponent_div_33_Template_input_input_20_listener", "_r3", "ɵɵreference", "ctx_r13", "updateName", "value", "ɵɵtemplate", "EquipeFormComponent_div_33_div_22_Template", "EquipeFormComponent_div_33_Template_textarea_input_31_listener", "_r5", "ctx_r14", "updateDescription", "EquipeFormComponent_div_33_Template_button_click_42_listener", "ctx_r15", "cancel", "EquipeFormComponent_div_33_button_45_Template", "EquipeFormComponent_div_33_span_47_Template", "EquipeFormComponent_div_33_i_48_Template", "_c1", "ctx_r2", "equipe", "name", "nameExists", "description", "admin", "equipeId", "submitting", "nameError", "descriptionError", "EquipeFormComponent", "constructor", "equipeService", "membreService", "userService", "route", "router", "notificationService", "loading", "checkingName", "existingEquipes", "availableMembers", "availableUsers", "currentUserId", "ngOnInit", "console", "log", "getCurrentUser", "loadAllEquipes", "loadAllMembers", "loadAllUsers", "snapshot", "paramMap", "get", "loadEquipe", "setTimeout", "addButton", "document", "getElementById", "addEventListener", "token", "localStorage", "getItem", "getProfile", "subscribe", "next", "user", "_id", "id", "getMembres", "membres", "getAllUsers", "users", "getEquipes", "equipes", "getEquipe", "data", "members", "length", "loadMembersDetails", "for<PERSON>ach", "membre", "membreId", "getMemberId", "find", "u", "email", "profession", "role", "getUserById", "userData", "index", "findIndex", "some", "push", "checkNameExists", "e", "warn", "equipeToSave", "updateEquipe", "response", "showSuccess", "navigate", "message", "showError", "addEquipe", "addMembreToEquipe", "includes", "userName", "firstName", "lastName", "getMembreName", "m", "getMembreEmail", "getMembreProfession", "getMembreRole", "_membreId", "removeMembreFromEquipe", "membreName", "confirm", "status", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "i3", "AuthService", "i4", "ActivatedRoute", "Router", "i5", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeFormComponent_Template", "rf", "ctx", "EquipeFormComponent_Template_button_click_28_listener", "EquipeFormComponent_div_31_Template", "EquipeFormComponent_div_32_Template", "EquipeFormComponent_div_33_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-form\\equipe-form.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { EquipeService } from 'src/app/services/equipe.service';\r\nimport { MembreService } from 'src/app/services/membre.service';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { NotificationService } from 'src/app/services/notification.service';\r\nimport { Equipe } from 'src/app/models/equipe.model';\r\nimport { Membre } from 'src/app/models/membre.model';\r\nimport { User } from 'src/app/models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-equipe-form',\r\n  template: `\r\n    <div\r\n      class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\r\n    >\r\n      <!-- Background decorative elements -->\r\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        <div\r\n          class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n        ></div>\r\n\r\n        <!-- Grid pattern -->\r\n        <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n          <div class=\"h-full grid grid-cols-12\">\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"max-w-4xl mx-auto p-6 relative z-10\">\r\n        <!-- Header -->\r\n        <div class=\"mb-8 relative\">\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\r\n          ></div>\r\n\r\n          <div\r\n            class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\r\n          >\r\n            <div\r\n              class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\r\n            >\r\n              <div class=\"mb-4 lg:mb-0\">\r\n                <h1\r\n                  class=\"text-3xl font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-2 tracking-wide\"\r\n                >\r\n                  {{ isEditMode ? \"Modifier l'équipe\" : 'Nouvelle équipe' }}\r\n                </h1>\r\n                <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm\">\r\n                  {{\r\n                    isEditMode\r\n                      ? 'Modifiez les informations et les membres de votre équipe'\r\n                      : 'Créez une nouvelle équipe pour organiser vos projets et membres'\r\n                  }}\r\n                </p>\r\n              </div>\r\n\r\n              <button\r\n                (click)=\"cancel()\"\r\n                class=\"bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#dac4ea]/30 dark:hover:bg-[#00f7ff]/30\"\r\n              >\r\n                <i class=\"fas fa-arrow-left mr-2\"></i>\r\n                Retour à la liste\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Loading Indicator -->\r\n        <div\r\n          *ngIf=\"loading\"\r\n          class=\"flex flex-col items-center justify-center py-16\"\r\n        >\r\n          <div class=\"relative\">\r\n            <div\r\n              class=\"w-12 h-12 border-3 border-[#dac4ea]/20 dark:border-[#00f7ff]/20 border-t-[#dac4ea] dark:border-t-[#00f7ff] rounded-full animate-spin\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n            ></div>\r\n          </div>\r\n          <p\r\n            class=\"mt-4 text-[#dac4ea] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\r\n          >\r\n            Chargement des données...\r\n          </p>\r\n        </div>\r\n\r\n        <!-- Error Alert -->\r\n        <div *ngIf=\"error\" class=\"mb-6\">\r\n          <div\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\r\n                <i class=\"fas fa-exclamation-triangle\"></i>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <h3\r\n                  class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\"\r\n                >\r\n                  Erreur\r\n                </h3>\r\n                <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\r\n                  {{ error }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Formulaire principal -->\r\n        <div *ngIf=\"!loading\" class=\"mb-8 relative\">\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\r\n          ></div>\r\n\r\n          <div\r\n            class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\r\n          >\r\n            <!-- Header -->\r\n            <div\r\n              class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6\"\r\n            >\r\n              <h3 class=\"text-xl font-bold text-white mb-1 flex items-center\">\r\n                <i\r\n                  class=\"fas mr-2\"\r\n                  [ngClass]=\"{\r\n                    'fa-edit': isEditMode,\r\n                    'fa-plus-circle': !isEditMode\r\n                  }\"\r\n                ></i>\r\n                {{\r\n                  isEditMode\r\n                    ? \"Informations de l'équipe\"\r\n                    : 'Détails de la nouvelle équipe'\r\n                }}\r\n              </h3>\r\n              <p class=\"text-white/80 text-sm\">\r\n                Remplissez les informations de base de l'équipe\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Form -->\r\n            <div class=\"p-6\">\r\n              <form (ngSubmit)=\"onSubmit()\" class=\"space-y-6\">\r\n                <!-- Nom de l'équipe -->\r\n                <div>\r\n                  <label\r\n                    class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\r\n                  >\r\n                    Nom de l'équipe\r\n                    <span class=\"text-[#ff6b69] dark:text-[#ff3b30]\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <div\r\n                      class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\r\n                    >\r\n                      <i\r\n                        class=\"fas fa-users text-[#dac4ea] dark:text-[#00f7ff]\"\r\n                      ></i>\r\n                    </div>\r\n                    <input\r\n                      #nameInput\r\n                      type=\"text\"\r\n                      [value]=\"equipe.name || ''\"\r\n                      (input)=\"updateName(nameInput.value)\"\r\n                      class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\r\n                      placeholder=\"Entrez le nom de l'équipe\"\r\n                      required\r\n                      minlength=\"3\"\r\n                    />\r\n                  </div>\r\n                  <div\r\n                    *ngIf=\"nameExists\"\r\n                    class=\"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30] flex items-center\"\r\n                  >\r\n                    <i class=\"fas fa-exclamation-triangle mr-1\"></i>\r\n                    Ce nom d'équipe existe déjà. Veuillez en choisir un autre.\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Description -->\r\n                <div>\r\n                  <label\r\n                    class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\r\n                  >\r\n                    Description\r\n                    <span class=\"text-[#ff6b69] dark:text-[#ff3b30]\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <div class=\"absolute top-3 left-3 pointer-events-none\">\r\n                      <i\r\n                        class=\"fas fa-file-alt text-[#dac4ea] dark:text-[#00f7ff]\"\r\n                      ></i>\r\n                    </div>\r\n                    <textarea\r\n                      #descInput\r\n                      [value]=\"equipe.description || ''\"\r\n                      (input)=\"updateDescription(descInput.value)\"\r\n                      rows=\"4\"\r\n                      class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all resize-none\"\r\n                      placeholder=\"Décrivez l'objectif et les activités de cette équipe\"\r\n                      required\r\n                      minlength=\"10\"\r\n                    ></textarea>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Admin info -->\r\n                <input type=\"hidden\" [value]=\"equipe.admin\" />\r\n                <div\r\n                  class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4\"\r\n                >\r\n                  <div class=\"flex items-center\">\r\n                    <div\r\n                      class=\"text-[#dac4ea] dark:text-[#00f7ff] mr-3 text-lg\"\r\n                    >\r\n                      <i class=\"fas fa-info-circle\"></i>\r\n                    </div>\r\n                    <div class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\r\n                      Un administrateur par défaut sera assigné à cette équipe.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Buttons -->\r\n                <div class=\"flex items-center justify-between pt-4\">\r\n                  <div class=\"flex items-center space-x-4\">\r\n                    <button\r\n                      type=\"button\"\r\n                      (click)=\"cancel()\"\r\n                      class=\"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30\"\r\n                    >\r\n                      <i class=\"fas fa-arrow-left mr-2\"></i>\r\n                      Retour\r\n                    </button>\r\n\r\n                    <button\r\n                      *ngIf=\"isEditMode && equipeId\"\r\n                      type=\"button\"\r\n                      (click)=\"deleteEquipe()\"\r\n                      class=\"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30\"\r\n                    >\r\n                      <i class=\"fas fa-trash mr-2\"></i>\r\n                      Supprimer\r\n                    </button>\r\n                  </div>\r\n\r\n                  <button\r\n                    type=\"submit\"\r\n                    [disabled]=\"\r\n                      submitting ||\r\n                      !equipe.name ||\r\n                      !equipe.description ||\r\n                      nameExists ||\r\n                      nameError ||\r\n                      descriptionError\r\n                    \"\r\n                    class=\"relative overflow-hidden group bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(218,196,234,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\r\n                  >\r\n                    <span\r\n                      *ngIf=\"submitting\"\r\n                      class=\"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\"\r\n                    ></span>\r\n                    <i\r\n                      *ngIf=\"!submitting\"\r\n                      class=\"fas mr-2\"\r\n                      [ngClass]=\"{\r\n                        'fa-save': isEditMode,\r\n                        'fa-plus-circle': !isEditMode\r\n                      }\"\r\n                    ></i>\r\n                    {{ isEditMode ? 'Mettre à jour' : \"Créer l'équipe\" }}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styleUrls: ['./equipe-form.component.css'],\r\n})\r\nexport class EquipeFormComponent implements OnInit {\r\n  equipe: Equipe = {\r\n    name: '',\r\n    description: '',\r\n    admin: '', // Sera défini avec l'ID de l'utilisateur connecté\r\n  };\r\n  isEditMode = false;\r\n  loading = false;\r\n  submitting = false;\r\n  error: string | null = null;\r\n  equipeId: string | null = null;\r\n  nameExists = false;\r\n  nameError = false;\r\n  descriptionError = false;\r\n  checkingName = false;\r\n  existingEquipes: Equipe[] = [];\r\n  availableMembers: Membre[] = []; // Liste des membres disponibles\r\n  availableUsers: User[] = []; // Liste des utilisateurs disponibles\r\n  currentUserId: string | null = null; // ID de l'utilisateur connecté\r\n\r\n  constructor(\r\n    private equipeService: EquipeService,\r\n    private membreService: MembreService,\r\n    private userService: AuthService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private notificationService: NotificationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    console.log('EquipeFormComponent initialized');\r\n\r\n    // Récupérer l'ID de l'utilisateur connecté\r\n    this.getCurrentUser();\r\n\r\n    // Charger toutes les équipes pour vérifier les noms existants\r\n    this.loadAllEquipes();\r\n\r\n    // Charger tous les membres disponibles\r\n    this.loadAllMembers();\r\n\r\n    // Charger tous les utilisateurs disponibles\r\n    this.loadAllUsers();\r\n\r\n    try {\r\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\r\n      this.equipeId = this.route.snapshot.paramMap.get('id');\r\n      this.isEditMode = !!this.equipeId;\r\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\r\n\r\n      if (this.isEditMode && this.equipeId) {\r\n        this.loadEquipe(this.equipeId);\r\n\r\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\r\n        setTimeout(() => {\r\n          console.log('Après délai - this.equipeId:', this.equipeId);\r\n          console.log('Après délai - this.equipe:', this.equipe);\r\n        }, 1000);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error in ngOnInit:', error);\r\n      this.error = \"Erreur d'initialisation\";\r\n    }\r\n\r\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\r\n    setTimeout(() => {\r\n      const addButton = document.getElementById('addMembreButton');\r\n      if (addButton) {\r\n        console.log(\"Bouton d'ajout de membre trouvé\");\r\n        addButton.addEventListener('click', () => {\r\n          console.log(\"Bouton d'ajout de membre cliqué\");\r\n        });\r\n      } else {\r\n        console.log(\"Bouton d'ajout de membre non trouvé\");\r\n      }\r\n    }, 2000);\r\n  }\r\n\r\n  getCurrentUser(): void {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      this.userService.getProfile(token).subscribe({\r\n        next: (user: any) => {\r\n          console.log('Utilisateur connecté:', user);\r\n          this.currentUserId = user._id || user.id;\r\n\r\n          // Définir l'admin de l'équipe avec l'ID de l'utilisateur connecté\r\n          if (!this.isEditMode && this.currentUserId) {\r\n            this.equipe.admin = this.currentUserId;\r\n            console.log(\r\n              'Admin défini pour nouvelle équipe:',\r\n              this.equipe.admin\r\n            );\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error(\r\n            'Erreur lors de la récupération du profil utilisateur:',\r\n            error\r\n          );\r\n          this.error =\r\n            \"Impossible de récupérer les informations de l'utilisateur connecté.\";\r\n        },\r\n      });\r\n    } else {\r\n      this.error =\r\n        \"Aucun token d'authentification trouvé. Veuillez vous reconnecter.\";\r\n    }\r\n  }\r\n\r\n  loadAllMembers(): void {\r\n    this.membreService.getMembres().subscribe({\r\n      next: (membres) => {\r\n        this.availableMembers = membres;\r\n        console.log('Membres disponibles chargés:', membres);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des membres:', error);\r\n        this.error =\r\n          'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\r\n      },\r\n    });\r\n  }\r\n\r\n  loadAllUsers(): void {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      this.userService.getAllUsers(token).subscribe({\r\n        next: (users: any) => {\r\n          this.availableUsers = users;\r\n          console.log('Utilisateurs disponibles chargés:', users);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors du chargement des utilisateurs:', error);\r\n          this.error =\r\n            'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  loadAllEquipes(): void {\r\n    this.equipeService.getEquipes().subscribe({\r\n      next: (equipes) => {\r\n        this.existingEquipes = equipes;\r\n        console.log('Équipes existantes chargées:', equipes);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des équipes:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  loadEquipe(id: string): void {\r\n    console.log('Loading equipe with ID:', id);\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.equipeService.getEquipe(id).subscribe({\r\n      next: (data) => {\r\n        console.log('Équipe chargée:', data);\r\n        this.equipe = data;\r\n\r\n        // Vérifier que l'ID est correctement défini\r\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\r\n        console.log('this.equipeId:', this.equipeId);\r\n\r\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\r\n        if (this.equipe.members && this.equipe.members.length > 0) {\r\n          this.loadMembersDetails();\r\n        }\r\n\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\r\n        this.error =\r\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  // Méthode pour récupérer les détails des membres de l'équipe\r\n  loadMembersDetails(): void {\r\n    if (!this.equipe.members || this.equipe.members.length === 0) {\r\n      return;\r\n    }\r\n\r\n    console.log(\"Chargement des détails des membres de l'équipe...\");\r\n\r\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\r\n    this.equipe.members.forEach((membre) => {\r\n      const membreId = this.getMemberId(membre);\r\n\r\n      // Chercher d'abord dans la liste des utilisateurs\r\n      const user = this.availableUsers.find(\r\n        (u) => u._id === membreId || u.id === membreId\r\n      );\r\n      if (user) {\r\n        console.log(\r\n          `Membre ${membreId} trouvé dans la liste des utilisateurs:`,\r\n          user\r\n        );\r\n\r\n        // Vérifier si toutes les informations nécessaires sont présentes\r\n        if (!user.email || (!user.profession && !user.role)) {\r\n          // Si des informations manquent, essayer de les récupérer depuis l'API\r\n          const token = localStorage.getItem('token');\r\n          if (token && membreId) {\r\n            this.userService.getUserById(membreId, token).subscribe({\r\n              next: (userData: any) => {\r\n                console.log(\r\n                  `Détails supplémentaires de l'utilisateur ${membreId} récupérés:`,\r\n                  userData\r\n                );\r\n\r\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\r\n                const index = this.availableUsers.findIndex(\r\n                  (u) => u._id === membreId || u.id === membreId\r\n                );\r\n                if (index !== -1) {\r\n                  this.availableUsers[index] = {\r\n                    ...this.availableUsers[index],\r\n                    ...userData,\r\n                  };\r\n                }\r\n              },\r\n              error: (error) => {\r\n                console.error(\r\n                  `Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`,\r\n                  error\r\n                );\r\n              },\r\n            });\r\n          }\r\n        }\r\n      } else {\r\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\r\n        const token = localStorage.getItem('token');\r\n        if (token && membreId) {\r\n          this.userService.getUserById(membreId, token).subscribe({\r\n            next: (userData: any) => {\r\n              console.log(\r\n                `Détails de l'utilisateur ${membreId} récupérés:`,\r\n                userData\r\n              );\r\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\r\n              if (\r\n                !this.availableUsers.some(\r\n                  (u) => u._id === userData._id || u.id === userData.id\r\n                )\r\n              ) {\r\n                this.availableUsers.push(userData);\r\n              }\r\n            },\r\n            error: (error) => {\r\n              console.error(\r\n                `Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`,\r\n                error\r\n              );\r\n            },\r\n          });\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  checkNameExists(name: string): boolean {\r\n    // En mode édition, ignorer l'équipe actuelle\r\n    if (this.isEditMode && this.equipeId) {\r\n      return this.existingEquipes.some(\r\n        (e) => e.name === name && e._id !== this.equipeId\r\n      );\r\n    }\r\n    // En mode création, vérifier tous les noms\r\n    return this.existingEquipes.some((e) => e.name === name);\r\n  }\r\n\r\n  updateName(value: string): void {\r\n    console.log('Name updated:', value);\r\n    this.equipe.name = value;\r\n\r\n    // Vérifier si le nom existe déjà\r\n    this.nameExists = this.checkNameExists(value);\r\n    if (this.nameExists) {\r\n      console.warn(\"Ce nom d'équipe existe déjà\");\r\n    }\r\n\r\n    // Vérifier si le nom a au moins 3 caractères\r\n    this.nameError = value.length > 0 && value.length < 3;\r\n    if (this.nameError) {\r\n      console.warn('Le nom doit contenir au moins 3 caractères');\r\n    }\r\n  }\r\n\r\n  updateDescription(value: string): void {\r\n    console.log('Description updated:', value);\r\n    this.equipe.description = value;\r\n\r\n    // Vérifier si la description a au moins 10 caractères\r\n    this.descriptionError = value.length > 0 && value.length < 10;\r\n    if (this.descriptionError) {\r\n      console.warn('La description doit contenir au moins 10 caractères');\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Form submitted with:', this.equipe);\r\n\r\n    // Vérifier si le nom est présent et valide\r\n    if (!this.equipe.name) {\r\n      this.error = \"Le nom de l'équipe est requis.\";\r\n      return;\r\n    }\r\n\r\n    if (this.equipe.name.length < 3) {\r\n      this.nameError = true;\r\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\r\n      return;\r\n    }\r\n\r\n    // Vérifier si la description est présente et valide\r\n    if (!this.equipe.description) {\r\n      this.error = \"La description de l'équipe est requise.\";\r\n      return;\r\n    }\r\n\r\n    if (this.equipe.description.length < 10) {\r\n      this.descriptionError = true;\r\n      this.error =\r\n        \"La description de l'équipe doit contenir au moins 10 caractères.\";\r\n      return;\r\n    }\r\n\r\n    // Vérifier si le nom existe déjà avant de soumettre\r\n    if (this.checkNameExists(this.equipe.name)) {\r\n      this.nameExists = true;\r\n      this.error =\r\n        'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\r\n      return;\r\n    }\r\n\r\n    this.submitting = true;\r\n    this.error = null;\r\n\r\n    // S'assurer que l'admin est défini\r\n    if (!this.equipe.admin && this.currentUserId) {\r\n      this.equipe.admin = this.currentUserId;\r\n    }\r\n\r\n    if (!this.equipe.admin) {\r\n      this.error =\r\n        \"Impossible de déterminer l'administrateur de l'équipe. Veuillez vous reconnecter.\";\r\n      return;\r\n    }\r\n\r\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\r\n    const equipeToSave: Equipe = {\r\n      name: this.equipe.name,\r\n      description: this.equipe.description || '',\r\n      admin: this.equipe.admin,\r\n    };\r\n\r\n    // Ajouter l'ID si nous sommes en mode édition\r\n    if (this.isEditMode && this.equipeId) {\r\n      equipeToSave._id = this.equipeId;\r\n    }\r\n\r\n    console.log('Données à envoyer:', equipeToSave);\r\n\r\n    if (this.isEditMode && this.equipeId) {\r\n      // Mode édition\r\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\r\n        next: (response) => {\r\n          console.log('Équipe mise à jour avec succès:', response);\r\n          this.submitting = false;\r\n          this.notificationService.showSuccess(\r\n            `L'équipe \"${response.name}\" a été mise à jour avec succès.`\r\n          );\r\n          this.router.navigate(['/equipes/liste']);\r\n        },\r\n        error: (error) => {\r\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\r\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\r\n          this.submitting = false;\r\n          this.notificationService.showError(`Erreur: ${error.message}`);\r\n        },\r\n      });\r\n    } else {\r\n      // Mode ajout\r\n      this.equipeService.addEquipe(equipeToSave).subscribe({\r\n        next: (response) => {\r\n          console.log('Équipe ajoutée avec succès:', response);\r\n          this.submitting = false;\r\n          this.notificationService.showSuccess(\r\n            `L'équipe \"${response.name}\" a été créée avec succès.`\r\n          );\r\n          this.router.navigate(['/equipes/liste']);\r\n        },\r\n        error: (error) => {\r\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\r\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\r\n          this.submitting = false;\r\n          this.notificationService.showError(`Erreur: ${error.message}`);\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  cancel(): void {\r\n    console.log('Form cancelled');\r\n    this.router.navigate(['/admin/equipes']);\r\n  }\r\n\r\n  // Méthodes pour gérer les membres\r\n  addMembreToEquipe(membreId: string, role: string = 'membre'): void {\r\n    console.log(\r\n      'Début de addMembreToEquipe avec membreId:',\r\n      membreId,\r\n      'et rôle:',\r\n      role\r\n    );\r\n    console.log('État actuel - this.equipeId:', this.equipeId);\r\n    console.log('État actuel - this.equipe:', this.equipe);\r\n\r\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\r\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\r\n\r\n    console.log('equipeId calculé:', equipeId);\r\n\r\n    if (!equipeId || !membreId) {\r\n      console.error(\"ID d'équipe ou ID de membre manquant\");\r\n      this.error = \"ID d'équipe ou ID de membre manquant\";\r\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\r\n\r\n      // Afficher un message à l'utilisateur\r\n      this.notificationService.showError(\r\n        \"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Vérifier si le membre est déjà dans l'équipe\r\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\r\n      this.notificationService.showError(\r\n        \"Ce membre fait déjà partie de l'équipe\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\r\n    const user = this.availableUsers.find(\r\n      (u) => u._id === membreId || u.id === membreId\r\n    );\r\n    const userName = user\r\n      ? user.firstName && user.lastName\r\n        ? `${user.firstName} ${user.lastName}`\r\n        : user.name || membreId\r\n      : membreId;\r\n\r\n    // Créer l'objet membre avec le rôle spécifié\r\n    const membre: Membre = {\r\n      id: membreId,\r\n      role: role,\r\n    };\r\n\r\n    this.loading = true;\r\n\r\n    console.log(\r\n      `Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`\r\n    );\r\n\r\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\r\n      next: (response) => {\r\n        console.log('Membre ajouté avec succès:', response);\r\n        this.notificationService.showSuccess(\r\n          `${userName} a été ajouté comme ${\r\n            role === 'admin' ? 'administrateur' : 'membre'\r\n          } à l'équipe`\r\n        );\r\n        // Recharger l'équipe pour mettre à jour la liste des membres\r\n        this.loadEquipe(equipeId);\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\r\n        this.error =\r\n          \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\r\n        this.notificationService.showError(\r\n          \"Erreur lors de l'ajout du membre: \" + error.message\r\n        );\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\r\n  getMembreName(membreId: string): string {\r\n    // Chercher d'abord dans la liste des utilisateurs\r\n    const user = this.availableUsers.find(\r\n      (u) => u._id === membreId || u.id === membreId\r\n    );\r\n    if (user) {\r\n      if (user.firstName && user.lastName) {\r\n        return `${user.firstName} ${user.lastName}`;\r\n      } else if (user.name) {\r\n        return user.name;\r\n      }\r\n    }\r\n\r\n    // Chercher ensuite dans la liste des membres\r\n    const membre = this.availableMembers.find(\r\n      (m) => m._id === membreId || m.id === membreId\r\n    );\r\n    if (membre && membre.name) {\r\n      return membre.name;\r\n    }\r\n\r\n    // Si aucun nom n'est trouvé, retourner l'ID\r\n    return membreId;\r\n  }\r\n\r\n  // Méthode pour obtenir l'email d'un membre\r\n  getMembreEmail(membreId: string): string {\r\n    const user = this.availableUsers.find(\r\n      (u) => u._id === membreId || u.id === membreId\r\n    );\r\n    if (user && user.email) {\r\n      return user.email;\r\n    }\r\n    return 'Non renseigné';\r\n  }\r\n\r\n  // Méthode pour obtenir la profession d'un membre\r\n  getMembreProfession(membreId: string): string {\r\n    const user = this.availableUsers.find(\r\n      (u) => u._id === membreId || u.id === membreId\r\n    );\r\n    if (user) {\r\n      if (user.profession) {\r\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\r\n      } else if (user.role) {\r\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\r\n      }\r\n    }\r\n    return 'Non spécifié';\r\n  }\r\n\r\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\r\n  getMembreRole(_membreId: string): string {\r\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\r\n    // Pour l'instant, nous retournons une valeur par défaut\r\n    return 'Membre';\r\n  }\r\n\r\n  removeMembreFromEquipe(membreId: string): void {\r\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\r\n    console.log('État actuel - this.equipeId:', this.equipeId);\r\n    console.log('État actuel - this.equipe:', this.equipe);\r\n\r\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\r\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\r\n\r\n    if (!equipeId) {\r\n      console.error(\"ID d'équipe manquant\");\r\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\r\n      this.notificationService.showError(\r\n        \"ID d'équipe manquant. Impossible de retirer le membre.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    if (!membreId) {\r\n      console.error('ID de membre manquant');\r\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\r\n      this.notificationService.showError(\r\n        'ID de membre manquant. Impossible de retirer le membre.'\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\r\n    const membreName = this.getMembreName(membreId);\r\n\r\n    console.log(\r\n      `Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`\r\n    );\r\n\r\n    try {\r\n      if (\r\n        confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)\r\n      ) {\r\n        console.log('Confirmation acceptée, suppression en cours...');\r\n\r\n        this.loading = true;\r\n        this.error = null;\r\n\r\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\r\n        setTimeout(() => {\r\n          this.equipeService\r\n            .removeMembreFromEquipe(equipeId, membreId)\r\n            .subscribe({\r\n              next: (response) => {\r\n                console.log(\r\n                  `Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`,\r\n                  response\r\n                );\r\n                this.loading = false;\r\n                this.notificationService.showSuccess(\r\n                  `${membreName} a été retiré avec succès de l'équipe`\r\n                );\r\n\r\n                // Recharger l'équipe pour mettre à jour la liste des membres\r\n                this.loadEquipe(equipeId);\r\n              },\r\n              error: (error) => {\r\n                console.error(\r\n                  `Erreur lors du retrait de l'utilisateur \"${membreName}\":`,\r\n                  error\r\n                );\r\n                console.error(\"Détails de l'erreur:\", {\r\n                  status: error.status,\r\n                  message: error.message,\r\n                  error: error,\r\n                });\r\n\r\n                this.loading = false;\r\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${\r\n                  error.message || 'Erreur inconnue'\r\n                }`;\r\n                this.notificationService.showError(\r\n                  `Erreur lors du retrait du membre: ${this.error}`\r\n                );\r\n              },\r\n            });\r\n        }, 500);\r\n      } else {\r\n        console.log(\"Suppression annulée par l'utilisateur\");\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Exception lors du retrait du membre:', error);\r\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\r\n      this.notificationService.showError(`Exception: ${this.error}`);\r\n    }\r\n  }\r\n\r\n  // Méthode pour supprimer l'équipe\r\n  deleteEquipe(): void {\r\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\r\n    console.log('État actuel - this.equipeId:', this.equipeId);\r\n    console.log('État actuel - this.equipe:', this.equipe);\r\n\r\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\r\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\r\n\r\n    if (!equipeId) {\r\n      console.error(\"ID d'équipe manquant\");\r\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\r\n      this.notificationService.showError(\r\n        \"ID d'équipe manquant. Impossible de supprimer l'équipe.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\r\n\r\n    try {\r\n      if (\r\n        confirm(\r\n          `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`\r\n        )\r\n      ) {\r\n        console.log('Confirmation acceptée, suppression en cours...');\r\n\r\n        this.loading = true;\r\n        this.error = null;\r\n\r\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\r\n        setTimeout(() => {\r\n          this.equipeService.deleteEquipe(equipeId).subscribe({\r\n            next: (response) => {\r\n              console.log('Équipe supprimée avec succès, réponse:', response);\r\n              this.loading = false;\r\n              this.notificationService.showSuccess(\r\n                `L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`\r\n              );\r\n\r\n              // Ajouter un délai avant la redirection\r\n              setTimeout(() => {\r\n                this.router.navigate(['/admin/equipes']);\r\n              }, 500);\r\n            },\r\n            error: (error) => {\r\n              console.error(\r\n                \"Erreur lors de la suppression de l'équipe:\",\r\n                error\r\n              );\r\n              console.error(\"Détails de l'erreur:\", {\r\n                status: error.status,\r\n                message: error.message,\r\n                error: error,\r\n              });\r\n\r\n              this.loading = false;\r\n              this.error = `Impossible de supprimer l'équipe: ${\r\n                error.message || 'Erreur inconnue'\r\n              }`;\r\n              this.notificationService.showError(\r\n                `Erreur lors de la suppression: ${this.error}`\r\n              );\r\n            },\r\n          });\r\n        }, 500);\r\n      } else {\r\n        console.log(\"Suppression annulée par l'utilisateur\");\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Exception lors de la suppression:', error);\r\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\r\n      this.notificationService.showError(`Exception: ${this.error}`);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;IAsFQA,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAI,MAAA,uCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAINH,EAAA,CAAAC,cAAA,cAAgC;IAMxBD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAIhBD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAuEER,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAI,MAAA,kFACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;;IA2DJH,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAS,UAAA,mBAAAC,sEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAGxBhB,EAAA,CAAAE,SAAA,YAAiC;IACjCF,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAeTH,EAAA,CAAAE,SAAA,eAGQ;;;;;;;;;;;IACRF,EAAA,CAAAE,SAAA,YAOK;;;;IAJHF,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,UAAA,GAAAD,MAAA,CAAAC,UAAA,EAGE;;;;;;;;;;;;IAlKhBrB,EAAA,CAAAC,cAAA,aAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAEO;IAKPF,EAAA,CAAAC,cAAA,cAEC;IAMKD,EAAA,CAAAE,SAAA,YAMK;IACLF,EAAA,CAAAI,MAAA,GAKF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAI,MAAA,6DACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAAiB;IACTD,EAAA,CAAAS,UAAA,sBAAAa,8DAAA;MAAAtB,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAc,aAAA;MAAA,OAAYd,EAAA,CAAAe,WAAA,CAAAS,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAE3BzB,EAAA,CAAAC,cAAA,WAAK;IAIDD,EAAA,CAAAI,MAAA,8BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAC,cAAA,eAAsB;IAIlBD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,qBASE;IALAD,EAAA,CAAAS,UAAA,mBAAAiB,4DAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAI,GAAA,GAAA3B,EAAA,CAAA4B,WAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAc,OAAA,CAAAC,UAAA,CAAAH,GAAA,CAAAI,KAAA,CAA2B;IAAA,EAAC;IAJvC/B,EAAA,CAAAG,YAAA,EASE;IAEJH,EAAA,CAAAgC,UAAA,KAAAC,0CAAA,kBAMM;IACRjC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAIDD,EAAA,CAAAI,MAAA,qBACA;IAAAJ,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAC,cAAA,eAAsB;IAElBD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,wBASC;IANCD,EAAA,CAAAS,UAAA,mBAAAyB,+DAAA;MAAAlC,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAY,GAAA,GAAAnC,EAAA,CAAA4B,WAAA;MAAA,MAAAQ,OAAA,GAAApC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAqB,OAAA,CAAAC,iBAAA,CAAAF,GAAA,CAAAJ,KAAA,CAAkC;IAAA,EAAC;IAM7C/B,EAAA,CAAAG,YAAA,EAAW;IAKhBH,EAAA,CAAAE,SAAA,iBAA8C;IAC9CF,EAAA,CAAAC,cAAA,eAEC;IAKKD,EAAA,CAAAE,SAAA,aAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAI,MAAA,uFACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAoD;IAI9CD,EAAA,CAAAS,UAAA,mBAAA6B,6DAAA;MAAAtC,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAgB,OAAA,GAAAvC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAwB,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlBxC,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAgC,UAAA,KAAAS,6CAAA,qBAQS;IACXzC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAWC;IACCD,EAAA,CAAAgC,UAAA,KAAAU,2CAAA,mBAGQ;IACR1C,EAAA,CAAAgC,UAAA,KAAAW,wCAAA,gBAOK;IACL3C,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAnJTH,EAAA,CAAAK,SAAA,GAGE;IAHFL,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAA0B,GAAA,EAAAC,MAAA,CAAAxB,UAAA,GAAAwB,MAAA,CAAAxB,UAAA,EAGE;IAEJrB,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,MAAAuC,MAAA,CAAAxB,UAAA,oFAKF;IA4BQrB,EAAA,CAAAK,SAAA,IAA2B;IAA3BL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAC,MAAA,CAAAC,IAAA,OAA2B;IAS5B/C,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAiB,UAAA,SAAA4B,MAAA,CAAAG,UAAA,CAAgB;IAwBfhD,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAC,MAAA,CAAAG,WAAA,OAAkC;IAYnBjD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAC,MAAA,CAAAI,KAAA,CAAsB;IA6BpClD,EAAA,CAAAK,SAAA,IAA4B;IAA5BL,EAAA,CAAAiB,UAAA,SAAA4B,MAAA,CAAAxB,UAAA,IAAAwB,MAAA,CAAAM,QAAA,CAA4B;IAY/BnD,EAAA,CAAAK,SAAA,GAOC;IAPDL,EAAA,CAAAiB,UAAA,aAAA4B,MAAA,CAAAO,UAAA,KAAAP,MAAA,CAAAC,MAAA,CAAAC,IAAA,KAAAF,MAAA,CAAAC,MAAA,CAAAG,WAAA,IAAAJ,MAAA,CAAAG,UAAA,IAAAH,MAAA,CAAAQ,SAAA,IAAAR,MAAA,CAAAS,gBAAA,CAOC;IAIEtD,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAiB,UAAA,SAAA4B,MAAA,CAAAO,UAAA,CAAgB;IAIhBpD,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAO,UAAA,CAAiB;IAOpBpD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAuC,MAAA,CAAAxB,UAAA,0DACF;;;AAWlB,OAAM,MAAOkC,mBAAmB;EAoB9BC,YACUC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,mBAAwC;IALxC,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAzB7B,KAAAhB,MAAM,GAAW;MACfC,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE,CAAE;KACZ;;IACD,KAAA7B,UAAU,GAAG,KAAK;IAClB,KAAA0C,OAAO,GAAG,KAAK;IACf,KAAAX,UAAU,GAAG,KAAK;IAClB,KAAA5C,KAAK,GAAkB,IAAI;IAC3B,KAAA2C,QAAQ,GAAkB,IAAI;IAC9B,KAAAH,UAAU,GAAG,KAAK;IAClB,KAAAK,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAU,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,cAAc,GAAW,EAAE,CAAC,CAAC;IAC7B,KAAAC,aAAa,GAAkB,IAAI,CAAC,CAAC;EASlC;;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI;MACF;MACA,IAAI,CAACxB,QAAQ,GAAG,IAAI,CAACS,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MACtD,IAAI,CAACzD,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC8B,QAAQ;MACjCmB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAClD,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC8B,QAAQ,CAAC;MAEhE,IAAI,IAAI,CAAC9B,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;QACpC,IAAI,CAAC4B,UAAU,CAAC,IAAI,CAAC5B,QAAQ,CAAC;QAE9B;QACA6B,UAAU,CAAC,MAAK;UACdV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACpB,QAAQ,CAAC;UAC1DmB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACzB,MAAM,CAAC;QACxD,CAAC,EAAE,IAAI,CAAC;;KAEX,CAAC,OAAOtC,KAAK,EAAE;MACd8D,OAAO,CAAC9D,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACA,KAAK,GAAG,yBAAyB;;IAGxC;IACAwE,UAAU,CAAC,MAAK;MACd,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC5D,IAAIF,SAAS,EAAE;QACbX,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CU,SAAS,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;UACvCd,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAChD,CAAC,CAAC;OACH,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAEtD,CAAC,EAAE,IAAI,CAAC;EACV;EAEAC,cAAcA,CAAA;IACZ,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAAC1B,WAAW,CAAC6B,UAAU,CAACH,KAAK,CAAC,CAACI,SAAS,CAAC;QAC3CC,IAAI,EAAGC,IAAS,IAAI;UAClBrB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoB,IAAI,CAAC;UAC1C,IAAI,CAACvB,aAAa,GAAGuB,IAAI,CAACC,GAAG,IAAID,IAAI,CAACE,EAAE;UAExC;UACA,IAAI,CAAC,IAAI,CAACxE,UAAU,IAAI,IAAI,CAAC+C,aAAa,EAAE;YAC1C,IAAI,CAACtB,MAAM,CAACI,KAAK,GAAG,IAAI,CAACkB,aAAa;YACtCE,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACzB,MAAM,CAACI,KAAK,CAClB;;QAEL,CAAC;QACD1C,KAAK,EAAGA,KAAK,IAAI;UACf8D,OAAO,CAAC9D,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;UACD,IAAI,CAACA,KAAK,GACR,qEAAqE;QACzE;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACA,KAAK,GACR,mEAAmE;;EAEzE;EAEAkE,cAAcA,CAAA;IACZ,IAAI,CAAChB,aAAa,CAACoC,UAAU,EAAE,CAACL,SAAS,CAAC;MACxCC,IAAI,EAAGK,OAAO,IAAI;QAChB,IAAI,CAAC7B,gBAAgB,GAAG6B,OAAO;QAC/BzB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEwB,OAAO,CAAC;MACtD,CAAC;MACDvF,KAAK,EAAGA,KAAK,IAAI;QACf8D,OAAO,CAAC9D,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,2EAA2E;MAC/E;KACD,CAAC;EACJ;EAEAmE,YAAYA,CAAA;IACV,MAAMU,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAAC1B,WAAW,CAACqC,WAAW,CAACX,KAAK,CAAC,CAACI,SAAS,CAAC;QAC5CC,IAAI,EAAGO,KAAU,IAAI;UACnB,IAAI,CAAC9B,cAAc,GAAG8B,KAAK;UAC3B3B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE0B,KAAK,CAAC;QACzD,CAAC;QACDzF,KAAK,EAAGA,KAAK,IAAI;UACf8D,OAAO,CAAC9D,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GACR,gFAAgF;QACpF;OACD,CAAC;;EAEN;EAEAiE,cAAcA,CAAA;IACZ,IAAI,CAAChB,aAAa,CAACyC,UAAU,EAAE,CAACT,SAAS,CAAC;MACxCC,IAAI,EAAGS,OAAO,IAAI;QAChB,IAAI,CAAClC,eAAe,GAAGkC,OAAO;QAC9B7B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4B,OAAO,CAAC;MACtD,CAAC;MACD3F,KAAK,EAAGA,KAAK,IAAI;QACf8D,OAAO,CAAC9D,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC;EACJ;EAEAuE,UAAUA,CAACc,EAAU;IACnBvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsB,EAAE,CAAC;IAC1C,IAAI,CAAC9B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACiD,aAAa,CAAC2C,SAAS,CAACP,EAAE,CAAC,CAACJ,SAAS,CAAC;MACzCC,IAAI,EAAGW,IAAI,IAAI;QACb/B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8B,IAAI,CAAC;QACpC,IAAI,CAACvD,MAAM,GAAGuD,IAAI;QAElB;QACA/B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACzB,MAAM,CAAC8C,GAAG,CAAC;QAChEtB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACpB,QAAQ,CAAC;QAE5C;QACA,IAAI,IAAI,CAACL,MAAM,CAACwD,OAAO,IAAI,IAAI,CAACxD,MAAM,CAACwD,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACzD,IAAI,CAACC,kBAAkB,EAAE;;QAG3B,IAAI,CAACzC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvD,KAAK,EAAGA,KAAK,IAAI;QACf8D,OAAO,CAAC9D,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACuD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAyC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC1D,MAAM,CAACwD,OAAO,IAAI,IAAI,CAACxD,MAAM,CAACwD,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5D;;IAGFjC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,IAAI,CAACzB,MAAM,CAACwD,OAAO,CAACG,OAAO,CAAEC,MAAM,IAAI;MACrC,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACF,MAAM,CAAC;MAEzC;MACA,MAAMf,IAAI,GAAG,IAAI,CAACxB,cAAc,CAAC0C,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKe,QAAQ,IAAIG,CAAC,CAACjB,EAAE,KAAKc,QAAQ,CAC/C;MACD,IAAIhB,IAAI,EAAE;QACRrB,OAAO,CAACC,GAAG,CACT,UAAUoC,QAAQ,yCAAyC,EAC3DhB,IAAI,CACL;QAED;QACA,IAAI,CAACA,IAAI,CAACoB,KAAK,IAAK,CAACpB,IAAI,CAACqB,UAAU,IAAI,CAACrB,IAAI,CAACsB,IAAK,EAAE;UACnD;UACA,MAAM5B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAIF,KAAK,IAAIsB,QAAQ,EAAE;YACrB,IAAI,CAAChD,WAAW,CAACuD,WAAW,CAACP,QAAQ,EAAEtB,KAAK,CAAC,CAACI,SAAS,CAAC;cACtDC,IAAI,EAAGyB,QAAa,IAAI;gBACtB7C,OAAO,CAACC,GAAG,CACT,4CAA4CoC,QAAQ,aAAa,EACjEQ,QAAQ,CACT;gBAED;gBACA,MAAMC,KAAK,GAAG,IAAI,CAACjD,cAAc,CAACkD,SAAS,CACxCP,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKe,QAAQ,IAAIG,CAAC,CAACjB,EAAE,KAAKc,QAAQ,CAC/C;gBACD,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;kBAChB,IAAI,CAACjD,cAAc,CAACiD,KAAK,CAAC,GAAG;oBAC3B,GAAG,IAAI,CAACjD,cAAc,CAACiD,KAAK,CAAC;oBAC7B,GAAGD;mBACJ;;cAEL,CAAC;cACD3G,KAAK,EAAGA,KAAK,IAAI;gBACf8D,OAAO,CAAC9D,KAAK,CACX,+EAA+EmG,QAAQ,GAAG,EAC1FnG,KAAK,CACN;cACH;aACD,CAAC;;;OAGP,MAAM;QACL;QACA,MAAM6E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAIF,KAAK,IAAIsB,QAAQ,EAAE;UACrB,IAAI,CAAChD,WAAW,CAACuD,WAAW,CAACP,QAAQ,EAAEtB,KAAK,CAAC,CAACI,SAAS,CAAC;YACtDC,IAAI,EAAGyB,QAAa,IAAI;cACtB7C,OAAO,CAACC,GAAG,CACT,4BAA4BoC,QAAQ,aAAa,EACjDQ,QAAQ,CACT;cACD;cACA,IACE,CAAC,IAAI,CAAChD,cAAc,CAACmD,IAAI,CACtBR,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKuB,QAAQ,CAACvB,GAAG,IAAIkB,CAAC,CAACjB,EAAE,KAAKsB,QAAQ,CAACtB,EAAE,CACtD,EACD;gBACA,IAAI,CAAC1B,cAAc,CAACoD,IAAI,CAACJ,QAAQ,CAAC;;YAEtC,CAAC;YACD3G,KAAK,EAAGA,KAAK,IAAI;cACf8D,OAAO,CAAC9D,KAAK,CACX,+DAA+DmG,QAAQ,GAAG,EAC1EnG,KAAK,CACN;YACH;WACD,CAAC;;;IAGR,CAAC,CAAC;EACJ;EAEAgH,eAAeA,CAACzE,IAAY;IAC1B;IACA,IAAI,IAAI,CAAC1B,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;MACpC,OAAO,IAAI,CAACc,eAAe,CAACqD,IAAI,CAC7BG,CAAC,IAAKA,CAAC,CAAC1E,IAAI,KAAKA,IAAI,IAAI0E,CAAC,CAAC7B,GAAG,KAAK,IAAI,CAACzC,QAAQ,CAClD;;IAEH;IACA,OAAO,IAAI,CAACc,eAAe,CAACqD,IAAI,CAAEG,CAAC,IAAKA,CAAC,CAAC1E,IAAI,KAAKA,IAAI,CAAC;EAC1D;EAEAjB,UAAUA,CAACC,KAAa;IACtBuC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAExC,KAAK,CAAC;IACnC,IAAI,CAACe,MAAM,CAACC,IAAI,GAAGhB,KAAK;IAExB;IACA,IAAI,CAACiB,UAAU,GAAG,IAAI,CAACwE,eAAe,CAACzF,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACiB,UAAU,EAAE;MACnBsB,OAAO,CAACoD,IAAI,CAAC,6BAA6B,CAAC;;IAG7C;IACA,IAAI,CAACrE,SAAS,GAAGtB,KAAK,CAACwE,MAAM,GAAG,CAAC,IAAIxE,KAAK,CAACwE,MAAM,GAAG,CAAC;IACrD,IAAI,IAAI,CAAClD,SAAS,EAAE;MAClBiB,OAAO,CAACoD,IAAI,CAAC,4CAA4C,CAAC;;EAE9D;EAEArF,iBAAiBA,CAACN,KAAa;IAC7BuC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExC,KAAK,CAAC;IAC1C,IAAI,CAACe,MAAM,CAACG,WAAW,GAAGlB,KAAK;IAE/B;IACA,IAAI,CAACuB,gBAAgB,GAAGvB,KAAK,CAACwE,MAAM,GAAG,CAAC,IAAIxE,KAAK,CAACwE,MAAM,GAAG,EAAE;IAC7D,IAAI,IAAI,CAACjD,gBAAgB,EAAE;MACzBgB,OAAO,CAACoD,IAAI,CAAC,qDAAqD,CAAC;;EAEvE;EAEAjG,QAAQA,CAAA;IACN6C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACzB,MAAM,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;MACrB,IAAI,CAACvC,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACsC,MAAM,CAACC,IAAI,CAACwD,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAClD,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC7C,KAAK,GAAG,yDAAyD;MACtE;;IAGF;IACA,IAAI,CAAC,IAAI,CAACsC,MAAM,CAACG,WAAW,EAAE;MAC5B,IAAI,CAACzC,KAAK,GAAG,yCAAyC;MACtD;;IAGF,IAAI,IAAI,CAACsC,MAAM,CAACG,WAAW,CAACsD,MAAM,GAAG,EAAE,EAAE;MACvC,IAAI,CAACjD,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC9C,KAAK,GACR,kEAAkE;MACpE;;IAGF;IACA,IAAI,IAAI,CAACgH,eAAe,CAAC,IAAI,CAAC1E,MAAM,CAACC,IAAI,CAAC,EAAE;MAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACxC,KAAK,GACR,oEAAoE;MACtE;;IAGF,IAAI,CAAC4C,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC5C,KAAK,GAAG,IAAI;IAEjB;IACA,IAAI,CAAC,IAAI,CAACsC,MAAM,CAACI,KAAK,IAAI,IAAI,CAACkB,aAAa,EAAE;MAC5C,IAAI,CAACtB,MAAM,CAACI,KAAK,GAAG,IAAI,CAACkB,aAAa;;IAGxC,IAAI,CAAC,IAAI,CAACtB,MAAM,CAACI,KAAK,EAAE;MACtB,IAAI,CAAC1C,KAAK,GACR,mFAAmF;MACrF;;IAGF;IACA,MAAMmH,YAAY,GAAW;MAC3B5E,IAAI,EAAE,IAAI,CAACD,MAAM,CAACC,IAAI;MACtBE,WAAW,EAAE,IAAI,CAACH,MAAM,CAACG,WAAW,IAAI,EAAE;MAC1CC,KAAK,EAAE,IAAI,CAACJ,MAAM,CAACI;KACpB;IAED;IACA,IAAI,IAAI,CAAC7B,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;MACpCwE,YAAY,CAAC/B,GAAG,GAAG,IAAI,CAACzC,QAAQ;;IAGlCmB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoD,YAAY,CAAC;IAE/C,IAAI,IAAI,CAACtG,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;MACpC;MACA,IAAI,CAACM,aAAa,CAACmE,YAAY,CAAC,IAAI,CAACzE,QAAQ,EAAEwE,YAAY,CAAC,CAAClC,SAAS,CAAC;QACrEC,IAAI,EAAGmC,QAAQ,IAAI;UACjBvD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEsD,QAAQ,CAAC;UACxD,IAAI,CAACzE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAACgE,WAAW,CAClC,aAAaD,QAAQ,CAAC9E,IAAI,kCAAkC,CAC7D;UACD,IAAI,CAACc,MAAM,CAACkE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDvH,KAAK,EAAGA,KAAK,IAAI;UACf8D,OAAO,CAAC9D,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACA,KAAK,GAAG,yCAAyCA,KAAK,CAACwH,OAAO,EAAE;UACrE,IAAI,CAAC5E,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAACmE,SAAS,CAAC,WAAWzH,KAAK,CAACwH,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACvE,aAAa,CAACyE,SAAS,CAACP,YAAY,CAAC,CAAClC,SAAS,CAAC;QACnDC,IAAI,EAAGmC,QAAQ,IAAI;UACjBvD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEsD,QAAQ,CAAC;UACpD,IAAI,CAACzE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAACgE,WAAW,CAClC,aAAaD,QAAQ,CAAC9E,IAAI,4BAA4B,CACvD;UACD,IAAI,CAACc,MAAM,CAACkE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDvH,KAAK,EAAGA,KAAK,IAAI;UACf8D,OAAO,CAAC9D,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,IAAI,CAACA,KAAK,GAAG,kCAAkCA,KAAK,CAACwH,OAAO,EAAE;UAC9D,IAAI,CAAC5E,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAACmE,SAAS,CAAC,WAAWzH,KAAK,CAACwH,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;;EAEN;EAEAxF,MAAMA,CAAA;IACJ8B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,IAAI,CAACV,MAAM,CAACkE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA;EACAI,iBAAiBA,CAACxB,QAAgB,EAAEM,IAAA,GAAe,QAAQ;IACzD3C,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CoC,QAAQ,EACR,UAAU,EACVM,IAAI,CACL;IACD3C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAC1DmB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACzB,MAAM,CAAC;IAEtD;IACA,MAAMK,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC8C,GAAI;IAElEtB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEpB,QAAQ,CAAC;IAE1C,IAAI,CAACA,QAAQ,IAAI,CAACwD,QAAQ,EAAE;MAC1BrC,OAAO,CAAC9D,KAAK,CAAC,sCAAsC,CAAC;MACrD,IAAI,CAACA,KAAK,GAAG,sCAAsC;MACnD8D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEpB,QAAQ,EAAE,WAAW,EAAEwD,QAAQ,CAAC;MAEzD;MACA,IAAI,CAAC7C,mBAAmB,CAACmE,SAAS,CAChC,sEAAsE,CACvE;MACD;;IAGF;IACA,IAAI,IAAI,CAACnF,MAAM,CAACwD,OAAO,IAAI,IAAI,CAACxD,MAAM,CAACwD,OAAO,CAAC8B,QAAQ,CAACzB,QAAQ,CAAC,EAAE;MACjE,IAAI,CAAC7C,mBAAmB,CAACmE,SAAS,CAChC,wCAAwC,CACzC;MACD;;IAGF;IACA,MAAMtC,IAAI,GAAG,IAAI,CAACxB,cAAc,CAAC0C,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKe,QAAQ,IAAIG,CAAC,CAACjB,EAAE,KAAKc,QAAQ,CAC/C;IACD,MAAM0B,QAAQ,GAAG1C,IAAI,GACjBA,IAAI,CAAC2C,SAAS,IAAI3C,IAAI,CAAC4C,QAAQ,GAC7B,GAAG5C,IAAI,CAAC2C,SAAS,IAAI3C,IAAI,CAAC4C,QAAQ,EAAE,GACpC5C,IAAI,CAAC5C,IAAI,IAAI4D,QAAQ,GACvBA,QAAQ;IAEZ;IACA,MAAMD,MAAM,GAAW;MACrBb,EAAE,EAAEc,QAAQ;MACZM,IAAI,EAAEA;KACP;IAED,IAAI,CAAClD,OAAO,GAAG,IAAI;IAEnBO,OAAO,CAACC,GAAG,CACT,2BAA2B8D,QAAQ,WAAWpB,IAAI,eAAe9D,QAAQ,EAAE,CAC5E;IAED,IAAI,CAACM,aAAa,CAAC0E,iBAAiB,CAAChF,QAAQ,EAAEuD,MAAM,CAAC,CAACjB,SAAS,CAAC;MAC/DC,IAAI,EAAGmC,QAAQ,IAAI;QACjBvD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEsD,QAAQ,CAAC;QACnD,IAAI,CAAC/D,mBAAmB,CAACgE,WAAW,CAClC,GAAGO,QAAQ,uBACTpB,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QACxC,aAAa,CACd;QACD;QACA,IAAI,CAAClC,UAAU,CAAC5B,QAAQ,CAAC;QACzB,IAAI,CAACY,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvD,KAAK,EAAGA,KAAK,IAAI;QACf8D,OAAO,CAAC9D,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACA,KAAK,GACR,+DAA+D;QACjE,IAAI,CAACsD,mBAAmB,CAACmE,SAAS,CAChC,oCAAoC,GAAGzH,KAAK,CAACwH,OAAO,CACrD;QACD,IAAI,CAACjE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAyE,aAAaA,CAAC7B,QAAgB;IAC5B;IACA,MAAMhB,IAAI,GAAG,IAAI,CAACxB,cAAc,CAAC0C,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKe,QAAQ,IAAIG,CAAC,CAACjB,EAAE,KAAKc,QAAQ,CAC/C;IACD,IAAIhB,IAAI,EAAE;MACR,IAAIA,IAAI,CAAC2C,SAAS,IAAI3C,IAAI,CAAC4C,QAAQ,EAAE;QACnC,OAAO,GAAG5C,IAAI,CAAC2C,SAAS,IAAI3C,IAAI,CAAC4C,QAAQ,EAAE;OAC5C,MAAM,IAAI5C,IAAI,CAAC5C,IAAI,EAAE;QACpB,OAAO4C,IAAI,CAAC5C,IAAI;;;IAIpB;IACA,MAAM2D,MAAM,GAAG,IAAI,CAACxC,gBAAgB,CAAC2C,IAAI,CACtC4B,CAAC,IAAKA,CAAC,CAAC7C,GAAG,KAAKe,QAAQ,IAAI8B,CAAC,CAAC5C,EAAE,KAAKc,QAAQ,CAC/C;IACD,IAAID,MAAM,IAAIA,MAAM,CAAC3D,IAAI,EAAE;MACzB,OAAO2D,MAAM,CAAC3D,IAAI;;IAGpB;IACA,OAAO4D,QAAQ;EACjB;EAEA;EACA+B,cAAcA,CAAC/B,QAAgB;IAC7B,MAAMhB,IAAI,GAAG,IAAI,CAACxB,cAAc,CAAC0C,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKe,QAAQ,IAAIG,CAAC,CAACjB,EAAE,KAAKc,QAAQ,CAC/C;IACD,IAAIhB,IAAI,IAAIA,IAAI,CAACoB,KAAK,EAAE;MACtB,OAAOpB,IAAI,CAACoB,KAAK;;IAEnB,OAAO,eAAe;EACxB;EAEA;EACA4B,mBAAmBA,CAAChC,QAAgB;IAClC,MAAMhB,IAAI,GAAG,IAAI,CAACxB,cAAc,CAAC0C,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKe,QAAQ,IAAIG,CAAC,CAACjB,EAAE,KAAKc,QAAQ,CAC/C;IACD,IAAIhB,IAAI,EAAE;MACR,IAAIA,IAAI,CAACqB,UAAU,EAAE;QACnB,OAAOrB,IAAI,CAACqB,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;OAClE,MAAM,IAAIrB,IAAI,CAACsB,IAAI,EAAE;QACpB,OAAOtB,IAAI,CAACsB,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;;;IAG/D,OAAO,cAAc;EACvB;EAEA;EACA2B,aAAaA,CAACC,SAAiB;IAC7B;IACA;IACA,OAAO,QAAQ;EACjB;EAEAC,sBAAsBA,CAACnC,QAAgB;IACrCrC,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEoC,QAAQ,CAAC;IACxErC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAC1DmB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACzB,MAAM,CAAC;IAEtD;IACA,MAAMK,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC8C,GAAI;IAElE,IAAI,CAACzC,QAAQ,EAAE;MACbmB,OAAO,CAAC9D,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE,IAAI,CAACsD,mBAAmB,CAACmE,SAAS,CAChC,wDAAwD,CACzD;MACD;;IAGF,IAAI,CAACtB,QAAQ,EAAE;MACbrC,OAAO,CAAC9D,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACsD,mBAAmB,CAACmE,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF;IACA,MAAMc,UAAU,GAAG,IAAI,CAACP,aAAa,CAAC7B,QAAQ,CAAC;IAE/CrC,OAAO,CAACC,GAAG,CACT,yCAAyCoC,QAAQ,KAAKoC,UAAU,iBAAiB5F,QAAQ,EAAE,CAC5F;IAED,IAAI;MACF,IACE6F,OAAO,CAAC,oCAAoCD,UAAU,eAAe,CAAC,EACtE;QACAzE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACR,OAAO,GAAG,IAAI;QACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;QAEjB;QACAwE,UAAU,CAAC,MAAK;UACd,IAAI,CAACvB,aAAa,CACfqF,sBAAsB,CAAC3F,QAAQ,EAAEwD,QAAQ,CAAC,CAC1ClB,SAAS,CAAC;YACTC,IAAI,EAAGmC,QAAQ,IAAI;cACjBvD,OAAO,CAACC,GAAG,CACT,gBAAgBwE,UAAU,mCAAmC,EAC7DlB,QAAQ,CACT;cACD,IAAI,CAAC9D,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAACgE,WAAW,CAClC,GAAGiB,UAAU,uCAAuC,CACrD;cAED;cACA,IAAI,CAAChE,UAAU,CAAC5B,QAAQ,CAAC;YAC3B,CAAC;YACD3C,KAAK,EAAGA,KAAK,IAAI;cACf8D,OAAO,CAAC9D,KAAK,CACX,4CAA4CuI,UAAU,IAAI,EAC1DvI,KAAK,CACN;cACD8D,OAAO,CAAC9D,KAAK,CAAC,sBAAsB,EAAE;gBACpCyI,MAAM,EAAEzI,KAAK,CAACyI,MAAM;gBACpBjB,OAAO,EAAExH,KAAK,CAACwH,OAAO;gBACtBxH,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACuD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACvD,KAAK,GAAG,wCAAwCuI,UAAU,kBAC7DvI,KAAK,CAACwH,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAAClE,mBAAmB,CAACmE,SAAS,CAChC,qCAAqC,IAAI,CAACzH,KAAK,EAAE,CAClD;YACH;WACD,CAAC;QACN,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACL8D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAO/D,KAAU,EAAE;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEwH,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAAClE,mBAAmB,CAACmE,SAAS,CAAC,cAAc,IAAI,CAACzH,KAAK,EAAE,CAAC;;EAElE;EAEA;EACAQ,YAAYA,CAAA;IACVsD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzED,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAC1DmB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACzB,MAAM,CAAC;IAEtD;IACA,MAAMK,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC8C,GAAI;IAElE,IAAI,CAACzC,QAAQ,EAAE;MACbmB,OAAO,CAAC9D,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACsD,mBAAmB,CAACmE,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF3D,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEpB,QAAQ,CAAC;IAE5D,IAAI;MACF,IACE6F,OAAO,CACL,gDAAgD,IAAI,CAAClG,MAAM,CAACC,IAAI,mCAAmC,CACpG,EACD;QACAuB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACR,OAAO,GAAG,IAAI;QACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;QAEjB;QACAwE,UAAU,CAAC,MAAK;UACd,IAAI,CAACvB,aAAa,CAACzC,YAAY,CAACmC,QAAQ,CAAC,CAACsC,SAAS,CAAC;YAClDC,IAAI,EAAGmC,QAAQ,IAAI;cACjBvD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEsD,QAAQ,CAAC;cAC/D,IAAI,CAAC9D,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAACgE,WAAW,CAClC,aAAa,IAAI,CAAChF,MAAM,CAACC,IAAI,gCAAgC,CAC9D;cAED;cACAiC,UAAU,CAAC,MAAK;gBACd,IAAI,CAACnB,MAAM,CAACkE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;cAC1C,CAAC,EAAE,GAAG,CAAC;YACT,CAAC;YACDvH,KAAK,EAAGA,KAAK,IAAI;cACf8D,OAAO,CAAC9D,KAAK,CACX,4CAA4C,EAC5CA,KAAK,CACN;cACD8D,OAAO,CAAC9D,KAAK,CAAC,sBAAsB,EAAE;gBACpCyI,MAAM,EAAEzI,KAAK,CAACyI,MAAM;gBACpBjB,OAAO,EAAExH,KAAK,CAACwH,OAAO;gBACtBxH,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACuD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACvD,KAAK,GAAG,qCACXA,KAAK,CAACwH,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAAClE,mBAAmB,CAACmE,SAAS,CAChC,kCAAkC,IAAI,CAACzH,KAAK,EAAE,CAC/C;YACH;WACD,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACL8D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAO/D,KAAU,EAAE;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEwH,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAAClE,mBAAmB,CAACmE,SAAS,CAAC,cAAc,IAAI,CAACzH,KAAK,EAAE,CAAC;;EAElE;;;uBAltBW+C,mBAAmB,EAAAvD,EAAA,CAAAkJ,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApJ,EAAA,CAAAkJ,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAtJ,EAAA,CAAAkJ,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAxJ,EAAA,CAAAkJ,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAkJ,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAA3J,EAAA,CAAAkJ,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnBtG,mBAAmB;MAAAuG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApS5BpK,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAG7CD,EAAA,CAAAE,SAAA,cAEO;UAKPF,EAAA,CAAAC,cAAA,eAEC;UAQOD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,IAKF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAS,UAAA,mBAAA6J,sDAAA;YAAA,OAASD,GAAA,CAAA7H,MAAA,EAAQ;UAAA,EAAC;UAGlBxC,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAI,MAAA,gCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAgC,UAAA,KAAAuI,mCAAA,kBAiBM;UAGNvK,EAAA,CAAAgC,UAAA,KAAAwI,mCAAA,mBAoBM;UAGNxK,EAAA,CAAAgC,UAAA,KAAAyI,mCAAA,oBA0KM;UACRzK,EAAA,CAAAG,YAAA,EAAM;;;UA7OMH,EAAA,CAAAK,SAAA,IACF;UADEL,EAAA,CAAAM,kBAAA,MAAA+J,GAAA,CAAAhJ,UAAA,0DACF;UAEErB,EAAA,CAAAK,SAAA,GAKF;UALEL,EAAA,CAAAM,kBAAA,MAAA+J,GAAA,CAAAhJ,UAAA,sJAKF;UAgBLrB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAiB,UAAA,SAAAoJ,GAAA,CAAAtG,OAAA,CAAa;UAmBV/D,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAiB,UAAA,SAAAoJ,GAAA,CAAA7J,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAiB,UAAA,UAAAoJ,GAAA,CAAAtG,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}