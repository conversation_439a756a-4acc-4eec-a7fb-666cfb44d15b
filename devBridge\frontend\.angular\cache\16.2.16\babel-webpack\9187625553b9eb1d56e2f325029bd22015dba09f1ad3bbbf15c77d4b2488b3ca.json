{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.AUTH_API = 'http://localhost:3000/api/auth';\n    this.ADMIN_API = 'http://localhost:3000/api/admin';\n  }\n  // Auth endpoints\n  signup(data) {\n    return this.http.post(`${this.AUTH_API}/signup`, data);\n  }\n  verifyEmail(data) {\n    return this.http.post(`${this.AUTH_API}/verify-email`, data);\n  }\n  login(data) {\n    return this.http.post(`${this.AUTH_API}/login`, data);\n  }\n  forgotPassword(email) {\n    return this.http.post(`${this.AUTH_API}/forgot-password`, {\n      email\n    });\n  }\n  resetPassword(data) {\n    return this.http.post(`${this.AUTH_API}/reset-password`, data);\n  }\n  getProfile(token) {\n    return this.http.get(`${this.AUTH_API}/profile`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  updateProfile(formData, token) {\n    return this.http.put(`${this.AUTH_API}/update-profile`, formData, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  removeProfileImage(token) {\n    return this.http.delete(`${this.AUTH_API}/remove-profile-image`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  changePassword(data, token) {\n    return this.http.put(`${this.AUTH_API}/change-password`, data, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  // Admin endpoints\n  getAllUsers(token) {\n    return this.http.get(`${this.ADMIN_API}/users`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  updateUserRole(userId, role, token) {\n    return this.http.put(`${this.ADMIN_API}/users/${userId}/role`, {\n      role\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  deleteUser(userId, token) {\n    return this.http.delete(`${this.ADMIN_API}/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  toggleUserActivation(userId, isActive, token) {\n    return this.http.put(`${this.ADMIN_API}/users/${userId}/activation`, {\n      isActive\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  getUserById(userId, token) {\n    return this.http.get(`${this.ADMIN_API}/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  getUserRole() {\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    return user?.role || '';\n  }\n  isAdmin() {\n    return this.getUserRole() === 'admin';\n  }\n  isAuthenticated() {\n    const token = localStorage.getItem('token');\n    return !!token;\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  getCurrentUser() {\n    const user = localStorage.getItem('user');\n    return user ? JSON.parse(user) : null;\n  }\n  logout() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  }\n  resendCode(email) {\n    return this.http.post(`${this.AUTH_API}/resend-code`, {\n      email\n    });\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthService", "constructor", "http", "AUTH_API", "ADMIN_API", "signup", "data", "post", "verifyEmail", "login", "forgotPassword", "email", "resetPassword", "getProfile", "token", "get", "headers", "Authorization", "updateProfile", "formData", "put", "removeProfileImage", "delete", "changePassword", "getAllUsers", "updateUserRole", "userId", "role", "deleteUser", "toggleUserActivation", "isActive", "getUserById", "getUserRole", "user", "JSON", "parse", "localStorage", "getItem", "isAdmin", "isAuthenticated", "getToken", "getCurrentUser", "logout", "removeItem", "resendCode", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AuthService {\r\n  private AUTH_API = 'http://localhost:3000/api/auth';\r\n  private ADMIN_API = 'http://localhost:3000/api/admin';\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Auth endpoints\r\n  signup(data: any) {\r\n    return this.http.post(`${this.AUTH_API}/signup`, data);\r\n  }\r\n\r\n  verifyEmail(data: { email: string; code: string }) {\r\n    return this.http.post(`${this.AUTH_API}/verify-email`, data);\r\n  }\r\n\r\n  login(data: any) {\r\n    return this.http.post(`${this.AUTH_API}/login`, data);\r\n  }\r\n\r\n  forgotPassword(email: string) {\r\n    return this.http.post(`${this.AUTH_API}/forgot-password`, { email });\r\n  }\r\n\r\n  resetPassword(data: { email: string; code: string; newPassword: string }) {\r\n    return this.http.post(`${this.AUTH_API}/reset-password`, data);\r\n  }\r\n\r\n  getProfile(token: string) {\r\n    return this.http.get(`${this.AUTH_API}/profile`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  updateProfile(formData: FormData, token: string) {\r\n    return this.http.put(`${this.AUTH_API}/update-profile`, formData, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  removeProfileImage(token: string) {\r\n    return this.http.delete(`${this.AUTH_API}/remove-profile-image`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  changePassword(data: any, token: string) {\r\n    return this.http.put(`${this.AUTH_API}/change-password`, data, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  // Admin endpoints\r\n  getAllUsers(token: string) {\r\n    return this.http.get(`${this.ADMIN_API}/users`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  updateUserRole(userId: string, role: string, token: string) {\r\n    return this.http.put(\r\n      `${this.ADMIN_API}/users/${userId}/role`,\r\n      { role },\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n  }\r\n\r\n  deleteUser(userId: string, token: string) {\r\n    return this.http.delete(`${this.ADMIN_API}/users/${userId}`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  toggleUserActivation(userId: string, isActive: boolean, token: string) {\r\n    return this.http.put(\r\n      `${this.ADMIN_API}/users/${userId}/activation`,\r\n      { isActive },\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserById(userId: string, token: string) {\r\n    return this.http.get(`${this.ADMIN_API}/users/${userId}`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  getUserRole(): string {\r\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n    return user?.role || '';\r\n  }\r\n\r\n  isAdmin(): boolean {\r\n    return this.getUserRole() === 'admin';\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    const token = localStorage.getItem('token');\r\n    return !!token;\r\n  }\r\n\r\n  getToken(): string | null {\r\n    return localStorage.getItem('token');\r\n  }\r\n\r\n  getCurrentUser(): any {\r\n    const user = localStorage.getItem('user');\r\n    return user ? JSON.parse(user) : null;\r\n  }\r\n\r\n  logout(): void {\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('user');\r\n  }\r\n\r\n  resendCode(email: string) {\r\n    return this.http.post(`${this.AUTH_API}/resend-code`, { email });\r\n  }\r\n}\r\n"], "mappings": ";;AAIA,OAAM,MAAOA,WAAW;EAItBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,QAAQ,GAAG,gCAAgC;IAC3C,KAAAC,SAAS,GAAG,iCAAiC;EAEd;EAEvC;EACAC,MAAMA,CAACC,IAAS;IACd,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,SAAS,EAAEG,IAAI,CAAC;EACxD;EAEAE,WAAWA,CAACF,IAAqC;IAC/C,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,eAAe,EAAEG,IAAI,CAAC;EAC9D;EAEAG,KAAKA,CAACH,IAAS;IACb,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,QAAQ,EAAEG,IAAI,CAAC;EACvD;EAEAI,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAACT,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,kBAAkB,EAAE;MAAEQ;IAAK,CAAE,CAAC;EACtE;EAEAC,aAAaA,CAACN,IAA0D;IACtE,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,iBAAiB,EAAEG,IAAI,CAAC;EAChE;EAEAO,UAAUA,CAACC,KAAa;IACtB,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACZ,QAAQ,UAAU,EAAE;MAC/Ca,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAI,aAAaA,CAACC,QAAkB,EAAEL,KAAa;IAC7C,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACjB,QAAQ,iBAAiB,EAAEgB,QAAQ,EAAE;MAChEH,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAO,kBAAkBA,CAACP,KAAa;IAC9B,OAAO,IAAI,CAACZ,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,CAACnB,QAAQ,uBAAuB,EAAE;MAC/Da,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAS,cAAcA,CAACjB,IAAS,EAAEQ,KAAa;IACrC,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACjB,QAAQ,kBAAkB,EAAEG,IAAI,EAAE;MAC7DU,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEA;EACAU,WAAWA,CAACV,KAAa;IACvB,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACX,SAAS,QAAQ,EAAE;MAC9CY,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAW,cAAcA,CAACC,MAAc,EAAEC,IAAY,EAAEb,KAAa;IACxD,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAClB,GAAG,IAAI,CAAChB,SAAS,UAAUsB,MAAM,OAAO,EACxC;MAAEC;IAAI,CAAE,EACR;MACEX,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CACF;EACH;EAEAc,UAAUA,CAACF,MAAc,EAAEZ,KAAa;IACtC,OAAO,IAAI,CAACZ,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,CAAClB,SAAS,UAAUsB,MAAM,EAAE,EAAE;MAC3DV,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAe,oBAAoBA,CAACH,MAAc,EAAEI,QAAiB,EAAEhB,KAAa;IACnE,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAClB,GAAG,IAAI,CAAChB,SAAS,UAAUsB,MAAM,aAAa,EAC9C;MAAEI;IAAQ,CAAE,EACZ;MACEd,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CACF;EACH;EAEAiB,WAAWA,CAACL,MAAc,EAAEZ,KAAa;IACvC,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACX,SAAS,UAAUsB,MAAM,EAAE,EAAE;MACxDV,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAkB,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D,OAAOJ,IAAI,EAAEN,IAAI,IAAI,EAAE;EACzB;EAEAW,OAAOA,CAAA;IACL,OAAO,IAAI,CAACN,WAAW,EAAE,KAAK,OAAO;EACvC;EAEAO,eAAeA,CAAA;IACb,MAAMzB,KAAK,GAAGsB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,CAAC,CAACvB,KAAK;EAChB;EAEA0B,QAAQA,CAAA;IACN,OAAOJ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACtC;EAEAI,cAAcA,CAAA;IACZ,MAAMR,IAAI,GAAGG,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACzC,OAAOJ,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI;EACvC;EAEAS,MAAMA,CAAA;IACJN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;EACjC;EAEAC,UAAUA,CAACjC,KAAa;IACtB,OAAO,IAAI,CAACT,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,cAAc,EAAE;MAAEQ;IAAK,CAAE,CAAC;EAClE;;;uBAxHWX,WAAW,EAAA6C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXhD,WAAW;MAAAiD,OAAA,EAAXjD,WAAW,CAAAkD,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}