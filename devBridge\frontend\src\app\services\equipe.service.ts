import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, Subject } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';
import {
  Equipe,
  CreateTeamRequest,
  UpdateTeamRequest,
  AddMemberRequest,
  RemoveMemberRequest,
  TeamSearchFilters,
  TeamListResponse
} from '../models/equipe.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class EquipeService {
  private apiUrl = `${environment.urlBackend}teams`;

  // Sujets pour notifier les changements
  private teamCreatedSubject = new Subject<Equipe>();
  private teamUpdatedSubject = new Subject<Equipe>();
  private teamDeletedSubject = new Subject<string>();
  private teamsRefreshSubject = new Subject<void>();

  // Observables publics
  public teamCreated$ = this.teamCreatedSubject.asObservable();
  public teamUpdated$ = this.teamUpdatedSubject.asObservable();
  public teamDeleted$ = this.teamDeletedSubject.asObservable();
  public teamsRefresh$ = this.teamsRefreshSubject.asObservable();

  constructor(private http: HttpClient) {
    console.log('API URL:', this.apiUrl);
  }

  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    console.log('🔍 Debug Auth Headers:');
    console.log('- Token exists:', !!token);
    console.log('- Token preview:', token ? token.substring(0, 20) + '...' : 'null');
    console.log('- User exists:', !!user);
    console.log('- User data:', user ? JSON.parse(user) : 'null');

    if (!token) {
      console.error('❌ No authentication token found in localStorage');
      console.log('📋 Available localStorage keys:', Object.keys(localStorage));
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('📤 Headers being sent:', headers);
    return headers;
  }

  getEquipes(): Observable<Equipe[]> {
    console.log('Fetching teams from:', this.apiUrl);
    return this.http.get<{ teams: Equipe[]; pagination?: any } | Equipe[]>(this.apiUrl, {
      headers: this.getAuthHeaders()
    }).pipe(
      map((response) => {
        console.log('✅ Raw teams response from backend:', response);
        // Gérer les deux formats de réponse possibles
        if (Array.isArray(response)) {
          // Format ancien : tableau direct
          console.log('✅ Teams extracted (array format):', response);
          return response;
        } else if (response && (response as any).teams) {
          // Format nouveau : objet avec propriété teams
          const teams = (response as any).teams;
          console.log('✅ Teams extracted (object format):', teams);
          return teams;
        } else {
          // Fallback
          console.log('✅ Teams fallback:', []);
          return [];
        }
      }),
      tap((data) => console.log('Teams processed:', data)),
      catchError(this.handleError)
    );
  }

  getEquipe(id: string): Observable<Equipe> {
    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);
    return this.http.get<Equipe>(`${this.apiUrl}/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap((data) => console.log('Team received:', data)),
      catchError(this.handleError)
    );
  }

  addEquipe(equipe: Equipe): Observable<Equipe> {
    console.log('🚀 Starting team creation...');
    console.log('📝 Team data:', equipe);
    console.log('🌐 API URL:', this.apiUrl);

    const headers = this.getAuthHeaders();

    return this.http.post<{ message: string; team: Equipe }>(this.apiUrl, equipe, { headers }).pipe(
      map((response) => {
        console.log('✅ Raw response from backend:', response);
        // Extraire l'équipe de la réponse
        const team = response.team || response;
        console.log('✅ Team extracted:', team);
        return team;
      }),
      tap((data) => {
        console.log('✅ Team created successfully:', data);
        // Notifier que l'équipe a été créée
        this.teamCreatedSubject.next(data);
        // Déclencher un rafraîchissement de la liste
        this.teamsRefreshSubject.next();
      }),
      catchError((error) => {
        console.error('❌ Error creating team:', error);
        console.error('📊 Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.error?.message,
          url: error.url,
          headers: error.headers
        });
        return this.handleError(error);
      })
    );
  }

  updateEquipe(id: string, equipe: Equipe): Observable<Equipe> {
    console.log(`Updating team with id ${id}:`, equipe);
    return this.http.put<{ message: string; team: Equipe }>(`${this.apiUrl}/${id}`, equipe, {
      headers: this.getAuthHeaders()
    }).pipe(
      map((response) => {
        console.log('✅ Raw update response from backend:', response);
        // Extraire l'équipe de la réponse
        const team = response.team || response;
        console.log('✅ Team extracted from update:', team);
        return team;
      }),
      tap((data) => {
        console.log('Team updated, response:', data);
        // Notifier que l'équipe a été mise à jour
        this.teamUpdatedSubject.next(data);
        // Déclencher un rafraîchissement de la liste
        this.teamsRefreshSubject.next();
      }),
      catchError(this.handleError)
    );
  }

  deleteEquipe(id: string): Observable<any> {
    console.log(`Deleting team with id ${id}`);
    console.log(`API URL: ${this.apiUrl}/${id}`);

    return this.http.delete(`${this.apiUrl}/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap((data) => {
        console.log('Team deleted, response:', data);
        // Notifier que l'équipe a été supprimée
        this.teamDeletedSubject.next(id);
        // Déclencher un rafraîchissement de la liste
        this.teamsRefreshSubject.next();
      }),
      catchError((error) => {
        console.error('Error deleting team:', error);
        console.error('Request URL:', `${this.apiUrl}/${id}`);
        return this.handleError(error);
      })
    );
  }

  addMembreToEquipe(teamId: string, membre: any): Observable<any> {
    console.log(`Adding member to team ${teamId}:`, membre);

    // Créer l'objet attendu par le backend
    const memberData = {
      userId: membre.id,
      role: membre.role || 'membre', // Utiliser le rôle spécifié ou "membre" par défaut
    };

    console.log('Sending to backend:', memberData);
    console.log('Team ID type:', typeof teamId, 'value:', teamId);
    console.log('User ID type:', typeof membre.id, 'value:', membre.id);

    // Utiliser la route directe pour ajouter un membre à une équipe
    return this.http
      .post<any>(`${this.apiUrl}/${teamId}/members`, memberData, {
        headers: this.getAuthHeaders()
      })
      .pipe(
        tap((data) => console.log('Member added, response:', data)),
        catchError(this.handleError)
      );
  }

  removeMembreFromEquipe(teamId: string, membreId: string): Observable<any> {
    console.log(`Removing member ${membreId} from team ${teamId}`);
    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);

    // Utiliser la route directe pour supprimer un membre d'une équipe
    return this.http
      .delete<any>(`${this.apiUrl}/${teamId}/members/${membreId}`, {
        headers: this.getAuthHeaders()
      })
      .pipe(
        tap((data) => console.log('Member removed, response:', data)),
        catchError((error) => {
          console.error('Error removing member:', error);
          console.error(
            'Request URL:',
            `${this.apiUrl}/${teamId}/members/${membreId}`
          );
          return this.handleError(error);
        })
      );
  }

  /**
   * Récupère les détails des membres d'une équipe
   * @param teamId ID de l'équipe
   * @returns Observable contenant la liste des membres avec leurs détails
   */
  getTeamMembers(teamId: string): Observable<any[]> {
    console.log(`Fetching team members for team ${teamId}`);
    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres
    return this.http.get<any>(`${this.apiUrl}/${teamId}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      map((team) => {
        console.log('Team data received:', team);
        // Transformer les IDs des membres en objets avec l'ID et le rôle
        if (team && team.members) {
          return team.members.map((memberId: string) => ({
            user: memberId,
            role: 'membre', // Par défaut, tous les membres ont le rôle "membre"
            _id: memberId, // Utiliser l'ID du membre comme ID du TeamMember
          }));
        }
        return [];
      }),
      tap((data) => console.log('Team members processed:', data)),
      catchError(this.handleError)
    );
  }

  // Nouvelles méthodes améliorées

  /**
   * Récupère toutes les équipes avec filtres et pagination
   */
  getEquipesWithFilters(filters?: TeamSearchFilters): Observable<TeamListResponse> {
    let params = new HttpParams();

    if (filters) {
      if (filters.status) params = params.set('status', filters.status);
      if (filters.isPublic !== undefined) params = params.set('isPublic', filters.isPublic.toString());
      if (filters.search) params = params.set('search', filters.search);
      if (filters.page) params = params.set('page', filters.page.toString());
      if (filters.limit) params = params.set('limit', filters.limit.toString());
    }

    return this.http.get<TeamListResponse>(this.apiUrl, {
      params,
      headers: this.getAuthHeaders()
    }).pipe(
      tap((data) => console.log('Teams with filters received:', data)),
      catchError(this.handleError)
    );
  }

  /**
   * Crée une nouvelle équipe avec les nouvelles fonctionnalités
   */
  createEquipe(teamData: CreateTeamRequest): Observable<{ message: string; team: Equipe }> {
    return this.http.post<{ message: string; team: Equipe }>(this.apiUrl, teamData, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap((data) => console.log('Team created:', data)),
      catchError(this.handleError)
    );
  }

  /**
   * Met à jour une équipe avec les nouvelles fonctionnalités
   */
  updateEquipeAdvanced(id: string, teamData: UpdateTeamRequest): Observable<{ message: string; team: Equipe }> {
    return this.http.put<{ message: string; team: Equipe }>(`${this.apiUrl}/${id}`, teamData, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap((data) => console.log('Team updated:', data)),
      catchError(this.handleError)
    );
  }

  /**
   * Ajoute un membre à une équipe (nouvelle version)
   */
  addMemberToTeam(teamId: string, memberData: AddMemberRequest): Observable<{ message: string; team: Equipe; newMember: any }> {
    return this.http.post<{ message: string; team: Equipe; newMember: any }>(`${this.apiUrl}/${teamId}/members`, memberData, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap((data) => console.log('Member added to team:', data)),
      catchError(this.handleError)
    );
  }

  /**
   * Retire un membre d'une équipe (nouvelle version)
   */
  removeMemberFromTeam(teamId: string, memberData: RemoveMemberRequest): Observable<{ message: string; team: Equipe }> {
    return this.http.delete<{ message: string; team: Equipe }>(`${this.apiUrl}/${teamId}/members`, {
      headers: this.getAuthHeaders(),
      body: memberData
    }).pipe(
      tap((data) => console.log('Member removed from team:', data)),
      catchError(this.handleError)
    );
  }

  /**
   * Récupère les équipes d'un utilisateur
   */
  getUserTeams(userId?: string): Observable<{ teams: Equipe[]; count: number }> {
    const url = userId ? `${this.apiUrl}/user/${userId}` : `${this.apiUrl}/my-teams`;

    return this.http.get<{ teams: Equipe[]; count: number }>(url, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap((data) => console.log('User teams received:', data)),
      catchError(this.handleError)
    );
  }

  /**
   * Archive une équipe
   */
  archiveTeam(teamId: string): Observable<{ message: string; team: Equipe }> {
    return this.updateEquipeAdvanced(teamId, { status: 'archived' });
  }

  /**
   * Active une équipe
   */
  activateTeam(teamId: string): Observable<{ message: string; team: Equipe }> {
    return this.updateEquipeAdvanced(teamId, { status: 'active' });
  }

  /**
   * Vérifie si un utilisateur peut rejoindre une équipe
   */
  canJoinTeam(team: Equipe): boolean {
    return team.status === 'active' &&
           team.isPublic === true &&
           !team.isFullTeam;
  }

  /**
   * Vérifie si un utilisateur est admin d'une équipe
   */
  isTeamAdmin(team: Equipe, userId: string): boolean {
    if (typeof team.admin === 'string') {
      return team.admin === userId;
    } else if (team.admin && typeof team.admin === 'object') {
      return team.admin._id === userId || (team.admin as any).id === userId;
    }
    return false;
  }

  /**
   * Vérifie si un utilisateur est membre d'une équipe
   */
  isTeamMember(team: Equipe, userId: string): boolean {
    if (!team.members) return false;

    return team.members.some(member => {
      if (typeof member === 'string') {
        return member === userId;
      } else if (member && typeof member === 'object') {
        return member._id === userId || (member as any).id === userId;
      }
      return false;
    });
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = '';

    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Erreur client: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      const status = error.status;
      const message = error.error?.message || error.statusText;

      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;

      // Log des détails supplémentaires pour le débogage
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        error: error.error,
      });

      if (status === 0) {
        console.error(
          "Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau."
        );
      }
    }

    console.error('API Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
