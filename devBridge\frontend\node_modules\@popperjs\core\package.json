{"name": "@popperjs/core", "version": "2.11.8", "description": "Tooltip and Popover Positioning Engine", "main": "dist/cjs/popper.js", "main:umd": "dist/umd/popper.js", "module": "lib/index.js", "unpkg": "dist/umd/popper.min.js", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "github:popperjs/popper-core", "keywords": ["tooltip", "popover", "dropdown", "popup", "popper", "positioning engine"], "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}, "files": ["index.d.ts", "/dist", "/lib"], "sideEffects": false, "scripts": {"clean": "rimraf lib && rimraf dist && rimraf test/visual/dist", "test": "yarn test:unit && yarn test:functional", "test:unit": "jest --coverage src", "test:functional": "DEV_PORT=`get-port` jest tests/functional", "test:flow": "flow", "test:typescript": "tsc --project tests/typescript/tsconfig.json", "test:eslint": "eslint .", "dev": "NODE_ENV=dev concurrently 'yarn serve' 'yarn build:dev --watch'", "serve": "serve -l ${DEV_PORT:-5000} tests/visual", "build": "yarn clean && yarn build:es && yarn build:esbrowser && yarn build:bundles && yarn build:flow && yarn build:typescript", "build:es": "babel src -d lib --ignore '**/*.test.js','**/__mocks__'", "build:esbrowser": "BROWSER_COMPAT=true yarn build:es -d dist/esm", "build:bundles": "rollup -c .config/rollup.config.js", "build:dev": "NODE_ENV=dev babel src -d tests/visual/dist", "build:flow": "flow-copy-source --ignore \"**/*.test.js\" src lib && replace-in-files --string=__DEV__ --replacement=false 'lib/**/*.flow'", "build:typescript": "rimraf dist/typescript; flow-to-ts \"src/**/*.js\" --write --inline-utility-types; tsc-silent --project .config/tsconfig.json --createSourceFile .config/createSourceFile.js --suppress @; rimraf \"src/**/*.ts\"", "prepublishOnly": "yarn build && pinst --disable", "prepare": "husky install .config/husky", "postpublish": "pinst --enable"}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "proseWrap": "always"}, "babel": {"extends": "./.config/babel.config"}, "jest": {"preset": "./.config/jest.config"}, "eslintConfig": {"extends": "./.config/eslint.config"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "devDependencies": {"@ampproject/rollup-plugin-closure-compiler": "^0.26.0", "@atomico/rollup-plugin-sizes": "^1.1.4", "@babel/cli": "^7.12.17", "@babel/core": "^7.12.17", "@babel/plugin-transform-flow-strip-types": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.17", "@babel/preset-env": "^7.12.17", "@fezvrasta/tsc-silent": "^1.3.0", "@khanacademy/flow-to-ts": "^0.3.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-replace": "^2.3.4", "babel-eslint": "^10.0.3", "babel-jest": "^26.6.3", "babel-plugin-add-import-extension": "^1.4.4", "babel-plugin-annotate-pure-calls": "^0.4.0", "babel-plugin-dev-expression": "^0.2.2", "babel-plugin-inline-replace-variables": "^1.3.1", "babel-plugin-transform-inline-environment-variables": "^0.4.3", "concurrently": "^5.3.0", "dotenv": "^8.2.0", "eslint": "^7.20.0", "eslint-plugin-flowtype": "^5.2.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-unused-imports": "^1.1.0", "flow-bin": "^0.139.0", "flow-copy-source": "^2.0.9", "get-port-cli": "^2.0.0", "husky": "^5.0.9", "jest": "^26.6.3", "jest-environment-jsdom-sixteen": "^1.0.3", "jest-environment-puppeteer": "^4.4.0", "jest-image-snapshot": "^4.3.0", "jest-puppeteer": "^4.4.0", "pinst": "^2.1.4", "poster": "^0.0.9", "prettier": "^2.2.1", "pretty-quick": "^3.1.0", "puppeteer": "^10.4.0", "replace-in-files-cli": "^1.0.0", "rollup": "^2.39.0", "rollup-plugin-flow-entry": "^0.3.3", "rollup-plugin-license": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^4.2.0", "serve": "^11.3.2", "typescript": "^4.1.5"}}