{"ast": null, "code": "/**\n * Creates a keyed JS object from an array, given a function to produce the keys\n * and a function to produce the values from each item in the array.\n * ```ts\n * const phoneBook = [\n *   { name: '<PERSON>', num: '555-1234' },\n *   { name: '<PERSON>', num: '867-5309' }\n * ]\n *\n * // { Jon: '555-1234', <PERSON>: '867-5309' }\n * const phonesByName = keyValMap(\n *   phoneBook,\n *   entry => entry.name,\n *   entry => entry.num\n * )\n * ```\n */\nexport function keyValMap(list, keyFn, valFn) {\n  const result = Object.create(null);\n  for (const item of list) {\n    result[keyFn(item)] = valFn(item);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["keyValMap", "list", "keyFn", "valFn", "result", "Object", "create", "item"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/jsutils/keyValMap.mjs"], "sourcesContent": ["/**\n * Creates a keyed JS object from an array, given a function to produce the keys\n * and a function to produce the values from each item in the array.\n * ```ts\n * const phoneBook = [\n *   { name: '<PERSON>', num: '555-1234' },\n *   { name: '<PERSON>', num: '867-5309' }\n * ]\n *\n * // { Jon: '555-1234', <PERSON>: '867-5309' }\n * const phonesByName = keyValMap(\n *   phoneBook,\n *   entry => entry.name,\n *   entry => entry.num\n * )\n * ```\n */\nexport function keyValMap(list, keyFn, valFn) {\n  const result = Object.create(null);\n\n  for (const item of list) {\n    result[keyFn(item)] = valFn(item);\n  }\n\n  return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAC5C,MAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAElC,KAAK,MAAMC,IAAI,IAAIN,IAAI,EAAE;IACvBG,MAAM,CAACF,KAAK,CAACK,IAAI,CAAC,CAAC,GAAGJ,KAAK,CAACI,IAAI,CAAC;EACnC;EAEA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}