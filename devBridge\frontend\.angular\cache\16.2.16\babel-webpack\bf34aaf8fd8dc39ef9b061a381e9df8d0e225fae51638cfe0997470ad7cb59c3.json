{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { print } from \"../../utilities/index.js\";\nvar defaultHttpOptions = {\n  includeQuery: true,\n  includeExtensions: false,\n  preserveHeaderCase: false\n};\nvar defaultHeaders = {\n  // headers are case insensitive (https://stackoverflow.com/a/5259004)\n  accept: \"*/*\",\n  // The content-type header describes the type of the body of the request, and\n  // so it typically only is sent with requests that actually have bodies. One\n  // could imagine that Apollo Client would remove this header when constructing\n  // a GET request (which has no body), but we historically have not done that.\n  // This means that browsers will preflight all Apollo Client requests (even\n  // GET requests). Apollo Server's CSRF prevention feature (introduced in\n  // AS3.7) takes advantage of this fact and does not block requests with this\n  // header. If you want to drop this header from GET requests, then you should\n  // probably replace it with a `apollo-require-preflight` header, or servers\n  // with CSRF prevention enabled might block your GET request. See\n  // https://www.apollographql.com/docs/apollo-server/security/cors/#preventing-cross-site-request-forgery-csrf\n  // for more details.\n  \"content-type\": \"application/json\"\n};\nvar defaultOptions = {\n  method: \"POST\"\n};\nexport var fallbackHttpConfig = {\n  http: defaultHttpOptions,\n  headers: defaultHeaders,\n  options: defaultOptions\n};\nexport var defaultPrinter = function (ast, printer) {\n  return printer(ast);\n};\nexport function selectHttpOptionsAndBody(operation, fallbackConfig) {\n  var configs = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    configs[_i - 2] = arguments[_i];\n  }\n  configs.unshift(fallbackConfig);\n  return selectHttpOptionsAndBodyInternal.apply(void 0, __spreadArray([operation, defaultPrinter], configs, false));\n}\nexport function selectHttpOptionsAndBodyInternal(operation, printer) {\n  var configs = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    configs[_i - 2] = arguments[_i];\n  }\n  var options = {};\n  var http = {};\n  configs.forEach(function (config) {\n    options = __assign(__assign(__assign({}, options), config.options), {\n      headers: __assign(__assign({}, options.headers), config.headers)\n    });\n    if (config.credentials) {\n      options.credentials = config.credentials;\n    }\n    http = __assign(__assign({}, http), config.http);\n  });\n  if (options.headers) {\n    options.headers = removeDuplicateHeaders(options.headers, http.preserveHeaderCase);\n  }\n  //The body depends on the http options\n  var operationName = operation.operationName,\n    extensions = operation.extensions,\n    variables = operation.variables,\n    query = operation.query;\n  var body = {\n    operationName: operationName,\n    variables: variables\n  };\n  if (http.includeExtensions) body.extensions = extensions;\n  // not sending the query (i.e persisted queries)\n  if (http.includeQuery) body.query = printer(query, print);\n  return {\n    options: options,\n    body: body\n  };\n}\n// Remove potential duplicate header names, preserving last (by insertion order).\n// This is done to prevent unintentionally duplicating a header instead of\n// overwriting it (See #8447 and #8449).\nfunction removeDuplicateHeaders(headers, preserveHeaderCase) {\n  // If we're not preserving the case, just remove duplicates w/ normalization.\n  if (!preserveHeaderCase) {\n    var normalizedHeaders_1 = {};\n    Object.keys(Object(headers)).forEach(function (name) {\n      normalizedHeaders_1[name.toLowerCase()] = headers[name];\n    });\n    return normalizedHeaders_1;\n  }\n  // If we are preserving the case, remove duplicates w/ normalization,\n  // preserving the original name.\n  // This allows for non-http-spec-compliant servers that expect intentionally\n  // capitalized header names (See #6741).\n  var headerData = {};\n  Object.keys(Object(headers)).forEach(function (name) {\n    headerData[name.toLowerCase()] = {\n      originalName: name,\n      value: headers[name]\n    };\n  });\n  var normalizedHeaders = {};\n  Object.keys(headerData).forEach(function (name) {\n    normalizedHeaders[headerData[name].originalName] = headerData[name].value;\n  });\n  return normalizedHeaders;\n}", "map": {"version": 3, "names": ["__assign", "__spread<PERSON><PERSON>y", "print", "defaultHttpOptions", "<PERSON><PERSON><PERSON><PERSON>", "includeExtensions", "preserveHeaderCase", "defaultHeaders", "accept", "defaultOptions", "method", "fallbackHttpConfig", "http", "headers", "options", "defaultPrinter", "ast", "printer", "selectHttpOptionsAndBody", "operation", "fallbackConfig", "configs", "_i", "arguments", "length", "unshift", "selectHttpOptionsAndBodyInternal", "apply", "for<PERSON>ach", "config", "credentials", "removeDuplicateHeaders", "operationName", "extensions", "variables", "query", "body", "normalizedHeaders_1", "Object", "keys", "name", "toLowerCase", "headerData", "originalName", "value", "normalizedHeaders"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/http/selectHttpOptionsAndBody.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { print } from \"../../utilities/index.js\";\nvar defaultHttpOptions = {\n    includeQuery: true,\n    includeExtensions: false,\n    preserveHeaderCase: false,\n};\nvar defaultHeaders = {\n    // headers are case insensitive (https://stackoverflow.com/a/5259004)\n    accept: \"*/*\",\n    // The content-type header describes the type of the body of the request, and\n    // so it typically only is sent with requests that actually have bodies. One\n    // could imagine that Apollo Client would remove this header when constructing\n    // a GET request (which has no body), but we historically have not done that.\n    // This means that browsers will preflight all Apollo Client requests (even\n    // GET requests). Apollo Server's CSRF prevention feature (introduced in\n    // AS3.7) takes advantage of this fact and does not block requests with this\n    // header. If you want to drop this header from GET requests, then you should\n    // probably replace it with a `apollo-require-preflight` header, or servers\n    // with CSRF prevention enabled might block your GET request. See\n    // https://www.apollographql.com/docs/apollo-server/security/cors/#preventing-cross-site-request-forgery-csrf\n    // for more details.\n    \"content-type\": \"application/json\",\n};\nvar defaultOptions = {\n    method: \"POST\",\n};\nexport var fallbackHttpConfig = {\n    http: defaultHttpOptions,\n    headers: defaultHeaders,\n    options: defaultOptions,\n};\nexport var defaultPrinter = function (ast, printer) { return printer(ast); };\nexport function selectHttpOptionsAndBody(operation, fallbackConfig) {\n    var configs = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        configs[_i - 2] = arguments[_i];\n    }\n    configs.unshift(fallbackConfig);\n    return selectHttpOptionsAndBodyInternal.apply(void 0, __spreadArray([operation,\n        defaultPrinter], configs, false));\n}\nexport function selectHttpOptionsAndBodyInternal(operation, printer) {\n    var configs = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        configs[_i - 2] = arguments[_i];\n    }\n    var options = {};\n    var http = {};\n    configs.forEach(function (config) {\n        options = __assign(__assign(__assign({}, options), config.options), { headers: __assign(__assign({}, options.headers), config.headers) });\n        if (config.credentials) {\n            options.credentials = config.credentials;\n        }\n        http = __assign(__assign({}, http), config.http);\n    });\n    if (options.headers) {\n        options.headers = removeDuplicateHeaders(options.headers, http.preserveHeaderCase);\n    }\n    //The body depends on the http options\n    var operationName = operation.operationName, extensions = operation.extensions, variables = operation.variables, query = operation.query;\n    var body = { operationName: operationName, variables: variables };\n    if (http.includeExtensions)\n        body.extensions = extensions;\n    // not sending the query (i.e persisted queries)\n    if (http.includeQuery)\n        body.query = printer(query, print);\n    return {\n        options: options,\n        body: body,\n    };\n}\n// Remove potential duplicate header names, preserving last (by insertion order).\n// This is done to prevent unintentionally duplicating a header instead of\n// overwriting it (See #8447 and #8449).\nfunction removeDuplicateHeaders(headers, preserveHeaderCase) {\n    // If we're not preserving the case, just remove duplicates w/ normalization.\n    if (!preserveHeaderCase) {\n        var normalizedHeaders_1 = {};\n        Object.keys(Object(headers)).forEach(function (name) {\n            normalizedHeaders_1[name.toLowerCase()] = headers[name];\n        });\n        return normalizedHeaders_1;\n    }\n    // If we are preserving the case, remove duplicates w/ normalization,\n    // preserving the original name.\n    // This allows for non-http-spec-compliant servers that expect intentionally\n    // capitalized header names (See #6741).\n    var headerData = {};\n    Object.keys(Object(headers)).forEach(function (name) {\n        headerData[name.toLowerCase()] = {\n            originalName: name,\n            value: headers[name],\n        };\n    });\n    var normalizedHeaders = {};\n    Object.keys(headerData).forEach(function (name) {\n        normalizedHeaders[headerData[name].originalName] = headerData[name].value;\n    });\n    return normalizedHeaders;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AAC/C,SAASC,KAAK,QAAQ,0BAA0B;AAChD,IAAIC,kBAAkB,GAAG;EACrBC,YAAY,EAAE,IAAI;EAClBC,iBAAiB,EAAE,KAAK;EACxBC,kBAAkB,EAAE;AACxB,CAAC;AACD,IAAIC,cAAc,GAAG;EACjB;EACAC,MAAM,EAAE,KAAK;EACb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,cAAc,EAAE;AACpB,CAAC;AACD,IAAIC,cAAc,GAAG;EACjBC,MAAM,EAAE;AACZ,CAAC;AACD,OAAO,IAAIC,kBAAkB,GAAG;EAC5BC,IAAI,EAAET,kBAAkB;EACxBU,OAAO,EAAEN,cAAc;EACvBO,OAAO,EAAEL;AACb,CAAC;AACD,OAAO,IAAIM,cAAc,GAAG,SAAAA,CAAUC,GAAG,EAAEC,OAAO,EAAE;EAAE,OAAOA,OAAO,CAACD,GAAG,CAAC;AAAE,CAAC;AAC5E,OAAO,SAASE,wBAAwBA,CAACC,SAAS,EAAEC,cAAc,EAAE;EAChE,IAAIC,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,OAAO,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACnC;EACAD,OAAO,CAACI,OAAO,CAACL,cAAc,CAAC;EAC/B,OAAOM,gCAAgC,CAACC,KAAK,CAAC,KAAK,CAAC,EAAE1B,aAAa,CAAC,CAACkB,SAAS,EAC1EJ,cAAc,CAAC,EAAEM,OAAO,EAAE,KAAK,CAAC,CAAC;AACzC;AACA,OAAO,SAASK,gCAAgCA,CAACP,SAAS,EAAEF,OAAO,EAAE;EACjE,IAAII,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,OAAO,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACnC;EACA,IAAIR,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIF,IAAI,GAAG,CAAC,CAAC;EACbS,OAAO,CAACO,OAAO,CAAC,UAAUC,MAAM,EAAE;IAC9Bf,OAAO,GAAGd,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEc,OAAO,CAAC,EAAEe,MAAM,CAACf,OAAO,CAAC,EAAE;MAAED,OAAO,EAAEb,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEc,OAAO,CAACD,OAAO,CAAC,EAAEgB,MAAM,CAAChB,OAAO;IAAE,CAAC,CAAC;IACzI,IAAIgB,MAAM,CAACC,WAAW,EAAE;MACpBhB,OAAO,CAACgB,WAAW,GAAGD,MAAM,CAACC,WAAW;IAC5C;IACAlB,IAAI,GAAGZ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEY,IAAI,CAAC,EAAEiB,MAAM,CAACjB,IAAI,CAAC;EACpD,CAAC,CAAC;EACF,IAAIE,OAAO,CAACD,OAAO,EAAE;IACjBC,OAAO,CAACD,OAAO,GAAGkB,sBAAsB,CAACjB,OAAO,CAACD,OAAO,EAAED,IAAI,CAACN,kBAAkB,CAAC;EACtF;EACA;EACA,IAAI0B,aAAa,GAAGb,SAAS,CAACa,aAAa;IAAEC,UAAU,GAAGd,SAAS,CAACc,UAAU;IAAEC,SAAS,GAAGf,SAAS,CAACe,SAAS;IAAEC,KAAK,GAAGhB,SAAS,CAACgB,KAAK;EACxI,IAAIC,IAAI,GAAG;IAAEJ,aAAa,EAAEA,aAAa;IAAEE,SAAS,EAAEA;EAAU,CAAC;EACjE,IAAItB,IAAI,CAACP,iBAAiB,EACtB+B,IAAI,CAACH,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIrB,IAAI,CAACR,YAAY,EACjBgC,IAAI,CAACD,KAAK,GAAGlB,OAAO,CAACkB,KAAK,EAAEjC,KAAK,CAAC;EACtC,OAAO;IACHY,OAAO,EAAEA,OAAO;IAChBsB,IAAI,EAAEA;EACV,CAAC;AACL;AACA;AACA;AACA;AACA,SAASL,sBAAsBA,CAAClB,OAAO,EAAEP,kBAAkB,EAAE;EACzD;EACA,IAAI,CAACA,kBAAkB,EAAE;IACrB,IAAI+B,mBAAmB,GAAG,CAAC,CAAC;IAC5BC,MAAM,CAACC,IAAI,CAACD,MAAM,CAACzB,OAAO,CAAC,CAAC,CAACe,OAAO,CAAC,UAAUY,IAAI,EAAE;MACjDH,mBAAmB,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG5B,OAAO,CAAC2B,IAAI,CAAC;IAC3D,CAAC,CAAC;IACF,OAAOH,mBAAmB;EAC9B;EACA;EACA;EACA;EACA;EACA,IAAIK,UAAU,GAAG,CAAC,CAAC;EACnBJ,MAAM,CAACC,IAAI,CAACD,MAAM,CAACzB,OAAO,CAAC,CAAC,CAACe,OAAO,CAAC,UAAUY,IAAI,EAAE;IACjDE,UAAU,CAACF,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG;MAC7BE,YAAY,EAAEH,IAAI;MAClBI,KAAK,EAAE/B,OAAO,CAAC2B,IAAI;IACvB,CAAC;EACL,CAAC,CAAC;EACF,IAAIK,iBAAiB,GAAG,CAAC,CAAC;EAC1BP,MAAM,CAACC,IAAI,CAACG,UAAU,CAAC,CAACd,OAAO,CAAC,UAAUY,IAAI,EAAE;IAC5CK,iBAAiB,CAACH,UAAU,CAACF,IAAI,CAAC,CAACG,YAAY,CAAC,GAAGD,UAAU,CAACF,IAAI,CAAC,CAACI,KAAK;EAC7E,CAAC,CAAC;EACF,OAAOC,iBAAiB;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}