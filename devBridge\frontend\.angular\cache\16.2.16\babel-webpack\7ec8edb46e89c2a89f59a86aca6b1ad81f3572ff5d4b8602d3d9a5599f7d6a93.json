{"ast": null, "code": "/**\n * Returns true if the value acts like a Promise, i.e. has a \"then\" function,\n * otherwise returns false.\n */\nexport function isPromise(value) {\n  return typeof (value === null || value === void 0 ? void 0 : value.then) === 'function';\n}", "map": {"version": 3, "names": ["isPromise", "value", "then"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/jsutils/isPromise.mjs"], "sourcesContent": ["/**\n * Returns true if the value acts like a Promise, i.e. has a \"then\" function,\n * otherwise returns false.\n */\nexport function isPromise(value) {\n  return (\n    typeof (value === null || value === void 0 ? void 0 : value.then) ===\n    'function'\n  );\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAE;EAC/B,OACE,QAAQA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,CAAC,KACjE,UAAU;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}