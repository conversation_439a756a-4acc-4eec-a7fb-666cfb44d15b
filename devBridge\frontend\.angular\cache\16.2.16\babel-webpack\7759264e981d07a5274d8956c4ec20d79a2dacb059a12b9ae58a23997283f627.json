{"ast": null, "code": "import { secondsInMinute } from \"./constants.js\";\n\n/**\n * @name secondsToMinutes\n * @category Conversion Helpers\n * @summary Convert seconds to minutes.\n *\n * @description\n * Convert a number of seconds to a full number of minutes.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in minutes\n *\n * @example\n * // Convert 120 seconds into minutes\n * const result = secondsToMinutes(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToMinutes(119)\n * //=> 1\n */\nexport function secondsToMinutes(seconds) {\n  const minutes = seconds / secondsInMinute;\n  return Math.trunc(minutes);\n}\n\n// Fallback for modularized imports:\nexport default secondsToMinutes;", "map": {"version": 3, "names": ["secondsInMinute", "secondsToMinutes", "seconds", "minutes", "Math", "trunc"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/secondsToMinutes.js"], "sourcesContent": ["import { secondsInMinute } from \"./constants.js\";\n\n/**\n * @name secondsToMinutes\n * @category Conversion Helpers\n * @summary Convert seconds to minutes.\n *\n * @description\n * Convert a number of seconds to a full number of minutes.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in minutes\n *\n * @example\n * // Convert 120 seconds into minutes\n * const result = secondsToMinutes(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToMinutes(119)\n * //=> 1\n */\nexport function secondsToMinutes(seconds) {\n  const minutes = seconds / secondsInMinute;\n  return Math.trunc(minutes);\n}\n\n// Fallback for modularized imports:\nexport default secondsToMinutes;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gBAAgB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACxC,MAAMC,OAAO,GAAGD,OAAO,GAAGF,eAAe;EACzC,OAAOI,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;AAC5B;;AAEA;AACA,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}