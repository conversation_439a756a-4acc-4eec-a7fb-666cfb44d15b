{"ast": null, "code": "import { addSeconds } from \"./addSeconds.js\";\n\n/**\n * The {@link subSeconds} function options.\n */\n\n/**\n * Subtract the specified number of seconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of seconds to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the seconds subtracted\n *\n * @example\n * // Subtract 30 seconds from 10 July 2014 12:45:00:\n * const result = subSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:44:30\n */\nexport function subSeconds(date, amount, options) {\n  return addSeconds(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subSeconds;", "map": {"version": 3, "names": ["addSeconds", "subSeconds", "date", "amount", "options"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/subSeconds.js"], "sourcesContent": ["import { addSeconds } from \"./addSeconds.js\";\n\n/**\n * The {@link subSeconds} function options.\n */\n\n/**\n * Subtract the specified number of seconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of seconds to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the seconds subtracted\n *\n * @example\n * // Subtract 30 seconds from 10 July 2014 12:45:00:\n * const result = subSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:44:30\n */\nexport function subSeconds(date, amount, options) {\n  return addSeconds(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subSeconds;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAChD,OAAOJ,UAAU,CAACE,IAAI,EAAE,CAACC,MAAM,EAAEC,OAAO,CAAC;AAC3C;;AAEA;AACA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}