{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EquipeService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlBackend}teams`;\n    console.log('API URL:', this.apiUrl);\n  }\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    return {\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    };\n  }\n  getEquipes() {\n    console.log('Fetching teams from:', this.apiUrl);\n    return this.http.get(this.apiUrl).pipe(tap(data => console.log('Teams received:', data)), catchError(this.handleError));\n  }\n  getEquipe(id) {\n    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\n    return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Team received:', data)), catchError(this.handleError));\n  }\n  addEquipe(equipe) {\n    console.log('Adding team:', equipe);\n    return this.http.post(this.apiUrl, equipe, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team added, response:', data)), catchError(this.handleError));\n  }\n  updateEquipe(id, equipe) {\n    console.log(`Updating team with id ${id}:`, equipe);\n    return this.http.put(`${this.apiUrl}/${id}`, equipe, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team updated, response:', data)), catchError(this.handleError));\n  }\n  deleteEquipe(id) {\n    console.log(`Deleting team with id ${id}`);\n    console.log(`API URL: ${this.apiUrl}/${id}`);\n    return this.http.delete(`${this.apiUrl}/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team deleted, response:', data)), catchError(error => {\n      console.error('Error deleting team:', error);\n      console.error('Request URL:', `${this.apiUrl}/${id}`);\n      return this.handleError(error);\n    }));\n  }\n  addMembreToEquipe(teamId, membre) {\n    console.log(`Adding member to team ${teamId}:`, membre);\n    // Créer l'objet attendu par le backend\n    const memberData = {\n      userId: membre.id,\n      role: membre.role || 'membre' // Utiliser le rôle spécifié ou \"membre\" par défaut\n    };\n\n    console.log('Sending to backend:', memberData);\n    console.log('Team ID type:', typeof teamId, 'value:', teamId);\n    console.log('User ID type:', typeof membre.id, 'value:', membre.id);\n    // Utiliser la route directe pour ajouter un membre à une équipe\n    return this.http.post(`${this.apiUrl}/${teamId}/members`, memberData).pipe(tap(data => console.log('Member added, response:', data)), catchError(this.handleError));\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    console.log(`Removing member ${membreId} from team ${teamId}`);\n    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\n    // Utiliser la route directe pour supprimer un membre d'une équipe\n    return this.http.delete(`${this.apiUrl}/${teamId}/members/${membreId}`).pipe(tap(data => console.log('Member removed, response:', data)), catchError(error => {\n      console.error('Error removing member:', error);\n      console.error('Request URL:', `${this.apiUrl}/${teamId}/members/${membreId}`);\n      return this.handleError(error);\n    }));\n  }\n  /**\n   * Récupère les détails des membres d'une équipe\n   * @param teamId ID de l'équipe\n   * @returns Observable contenant la liste des membres avec leurs détails\n   */\n  getTeamMembers(teamId) {\n    console.log(`Fetching team members for team ${teamId}`);\n    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\n    return this.http.get(`${this.apiUrl}/${teamId}`).pipe(map(team => {\n      console.log('Team data received:', team);\n      // Transformer les IDs des membres en objets avec l'ID et le rôle\n      if (team && team.members) {\n        return team.members.map(memberId => ({\n          user: memberId,\n          role: 'membre',\n          _id: memberId // Utiliser l'ID du membre comme ID du TeamMember\n        }));\n      }\n\n      return [];\n    }), tap(data => console.log('Team members processed:', data)), catchError(this.handleError));\n  }\n  // Nouvelles méthodes améliorées\n  /**\n   * Récupère toutes les équipes avec filtres et pagination\n   */\n  getEquipesWithFilters(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      if (filters.status) params = params.set('status', filters.status);\n      if (filters.isPublic !== undefined) params = params.set('isPublic', filters.isPublic.toString());\n      if (filters.search) params = params.set('search', filters.search);\n      if (filters.page) params = params.set('page', filters.page.toString());\n      if (filters.limit) params = params.set('limit', filters.limit.toString());\n    }\n    return this.http.get(this.apiUrl, {\n      params,\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Teams with filters received:', data)), catchError(this.handleError));\n  }\n  /**\n   * Crée une nouvelle équipe avec les nouvelles fonctionnalités\n   */\n  createEquipe(teamData) {\n    return this.http.post(this.apiUrl, teamData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team created:', data)), catchError(this.handleError));\n  }\n  /**\n   * Met à jour une équipe avec les nouvelles fonctionnalités\n   */\n  updateEquipeAdvanced(id, teamData) {\n    return this.http.put(`${this.apiUrl}/${id}`, teamData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team updated:', data)), catchError(this.handleError));\n  }\n  /**\n   * Ajoute un membre à une équipe (nouvelle version)\n   */\n  addMemberToTeam(teamId, memberData) {\n    return this.http.post(`${this.apiUrl}/${teamId}/members`, memberData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Member added to team:', data)), catchError(this.handleError));\n  }\n  /**\n   * Retire un membre d'une équipe (nouvelle version)\n   */\n  removeMemberFromTeam(teamId, memberData) {\n    return this.http.delete(`${this.apiUrl}/${teamId}/members`, {\n      headers: this.getAuthHeaders(),\n      body: memberData\n    }).pipe(tap(data => console.log('Member removed from team:', data)), catchError(this.handleError));\n  }\n  /**\n   * Récupère les équipes d'un utilisateur\n   */\n  getUserTeams(userId) {\n    const url = userId ? `${this.apiUrl}/user/${userId}` : `${this.apiUrl}/my-teams`;\n    return this.http.get(url, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('User teams received:', data)), catchError(this.handleError));\n  }\n  /**\n   * Archive une équipe\n   */\n  archiveTeam(teamId) {\n    return this.updateEquipeAdvanced(teamId, {\n      status: 'archived'\n    });\n  }\n  /**\n   * Active une équipe\n   */\n  activateTeam(teamId) {\n    return this.updateEquipeAdvanced(teamId, {\n      status: 'active'\n    });\n  }\n  /**\n   * Vérifie si un utilisateur peut rejoindre une équipe\n   */\n  canJoinTeam(team) {\n    return team.status === 'active' && team.isPublic === true && !team.isFullTeam;\n  }\n  /**\n   * Vérifie si un utilisateur est admin d'une équipe\n   */\n  isTeamAdmin(team, userId) {\n    if (typeof team.admin === 'string') {\n      return team.admin === userId;\n    } else if (team.admin && typeof team.admin === 'object') {\n      return team.admin._id === userId || team.admin.id === userId;\n    }\n    return false;\n  }\n  /**\n   * Vérifie si un utilisateur est membre d'une équipe\n   */\n  isTeamMember(team, userId) {\n    if (!team.members) return false;\n    return team.members.some(member => {\n      if (typeof member === 'string') {\n        return member === userId;\n      } else if (member && typeof member === 'object') {\n        return member._id === userId || member.id === userId;\n      }\n      return false;\n    });\n  }\n  handleError(error) {\n    let errorMessage = '';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Erreur client: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      const status = error.status;\n      const message = error.error?.message || error.statusText;\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\n      // Log des détails supplémentaires pour le débogage\n      console.error('Error details:', {\n        status: error.status,\n        statusText: error.statusText,\n        url: error.url,\n        error: error.error\n      });\n      if (status === 0) {\n        console.error(\"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\");\n      }\n    }\n    console.error('API Error:', errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function EquipeService_Factory(t) {\n      return new (t || EquipeService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EquipeService,\n      factory: EquipeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "throwError", "catchError", "tap", "map", "environment", "EquipeService", "constructor", "http", "apiUrl", "urlBackend", "console", "log", "getAuthHeaders", "token", "localStorage", "getItem", "getEquipes", "get", "pipe", "data", "handleError", "getEquipe", "id", "addEquipe", "equipe", "post", "headers", "updateEquipe", "put", "deleteEquipe", "delete", "error", "addMembreToEquipe", "teamId", "membre", "memberData", "userId", "role", "removeMembreFromEquipe", "membreId", "getTeamMembers", "team", "members", "memberId", "user", "_id", "getEquipesWithFilters", "filters", "params", "status", "set", "isPublic", "undefined", "toString", "search", "page", "limit", "createEquipe", "teamData", "updateEquipeAdvanced", "addMemberToTeam", "removeMemberFromTeam", "body", "getUserTeams", "url", "archiveTeam", "activateTeam", "canJoinTeam", "isFullTeam", "isTeamAdmin", "admin", "isTeamMember", "some", "member", "errorMessage", "ErrorEvent", "message", "statusText", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\services\\equipe.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap, map } from 'rxjs/operators';\r\nimport {\r\n  Equipe,\r\n  CreateTeamRequest,\r\n  UpdateTeamRequest,\r\n  AddMemberRequest,\r\n  RemoveMemberRequest,\r\n  TeamSearchFilters,\r\n  TeamListResponse\r\n} from '../models/equipe.model';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class EquipeService {\r\n  private apiUrl = `${environment.urlBackend}teams`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('API URL:', this.apiUrl);\r\n  }\r\n\r\n  private getAuthHeaders() {\r\n    const token = localStorage.getItem('token');\r\n    return {\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    };\r\n  }\r\n\r\n  getEquipes(): Observable<Equipe[]> {\r\n    console.log('Fetching teams from:', this.apiUrl);\r\n    return this.http.get<Equipe[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Teams received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  getEquipe(id: string): Observable<Equipe> {\r\n    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\r\n    return this.http.get<Equipe>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Team received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  addEquipe(equipe: Equipe): Observable<Equipe> {\r\n    console.log('Adding team:', equipe);\r\n    return this.http.post<Equipe>(this.apiUrl, equipe, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team added, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  updateEquipe(id: string, equipe: Equipe): Observable<Equipe> {\r\n    console.log(`Updating team with id ${id}:`, equipe);\r\n    return this.http.put<Equipe>(`${this.apiUrl}/${id}`, equipe, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team updated, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  deleteEquipe(id: string): Observable<any> {\r\n    console.log(`Deleting team with id ${id}`);\r\n    console.log(`API URL: ${this.apiUrl}/${id}`);\r\n\r\n    return this.http.delete(`${this.apiUrl}/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team deleted, response:', data)),\r\n      catchError((error) => {\r\n        console.error('Error deleting team:', error);\r\n        console.error('Request URL:', `${this.apiUrl}/${id}`);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  addMembreToEquipe(teamId: string, membre: any): Observable<any> {\r\n    console.log(`Adding member to team ${teamId}:`, membre);\r\n\r\n    // Créer l'objet attendu par le backend\r\n    const memberData = {\r\n      userId: membre.id,\r\n      role: membre.role || 'membre', // Utiliser le rôle spécifié ou \"membre\" par défaut\r\n    };\r\n\r\n    console.log('Sending to backend:', memberData);\r\n    console.log('Team ID type:', typeof teamId, 'value:', teamId);\r\n    console.log('User ID type:', typeof membre.id, 'value:', membre.id);\r\n\r\n    // Utiliser la route directe pour ajouter un membre à une équipe\r\n    return this.http\r\n      .post<any>(`${this.apiUrl}/${teamId}/members`, memberData)\r\n      .pipe(\r\n        tap((data) => console.log('Member added, response:', data)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  removeMembreFromEquipe(teamId: string, membreId: string): Observable<any> {\r\n    console.log(`Removing member ${membreId} from team ${teamId}`);\r\n    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\r\n\r\n    // Utiliser la route directe pour supprimer un membre d'une équipe\r\n    return this.http\r\n      .delete<any>(`${this.apiUrl}/${teamId}/members/${membreId}`)\r\n      .pipe(\r\n        tap((data) => console.log('Member removed, response:', data)),\r\n        catchError((error) => {\r\n          console.error('Error removing member:', error);\r\n          console.error(\r\n            'Request URL:',\r\n            `${this.apiUrl}/${teamId}/members/${membreId}`\r\n          );\r\n          return this.handleError(error);\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Récupère les détails des membres d'une équipe\r\n   * @param teamId ID de l'équipe\r\n   * @returns Observable contenant la liste des membres avec leurs détails\r\n   */\r\n  getTeamMembers(teamId: string): Observable<any[]> {\r\n    console.log(`Fetching team members for team ${teamId}`);\r\n    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\r\n    return this.http.get<any>(`${this.apiUrl}/${teamId}`).pipe(\r\n      map((team) => {\r\n        console.log('Team data received:', team);\r\n        // Transformer les IDs des membres en objets avec l'ID et le rôle\r\n        if (team && team.members) {\r\n          return team.members.map((memberId: string) => ({\r\n            user: memberId,\r\n            role: 'membre', // Par défaut, tous les membres ont le rôle \"membre\"\r\n            _id: memberId, // Utiliser l'ID du membre comme ID du TeamMember\r\n          }));\r\n        }\r\n        return [];\r\n      }),\r\n      tap((data) => console.log('Team members processed:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Nouvelles méthodes améliorées\r\n\r\n  /**\r\n   * Récupère toutes les équipes avec filtres et pagination\r\n   */\r\n  getEquipesWithFilters(filters?: TeamSearchFilters): Observable<TeamListResponse> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      if (filters.status) params = params.set('status', filters.status);\r\n      if (filters.isPublic !== undefined) params = params.set('isPublic', filters.isPublic.toString());\r\n      if (filters.search) params = params.set('search', filters.search);\r\n      if (filters.page) params = params.set('page', filters.page.toString());\r\n      if (filters.limit) params = params.set('limit', filters.limit.toString());\r\n    }\r\n\r\n    return this.http.get<TeamListResponse>(this.apiUrl, {\r\n      params,\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Teams with filters received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Crée une nouvelle équipe avec les nouvelles fonctionnalités\r\n   */\r\n  createEquipe(teamData: CreateTeamRequest): Observable<{ message: string; team: Equipe }> {\r\n    return this.http.post<{ message: string; team: Equipe }>(this.apiUrl, teamData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team created:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Met à jour une équipe avec les nouvelles fonctionnalités\r\n   */\r\n  updateEquipeAdvanced(id: string, teamData: UpdateTeamRequest): Observable<{ message: string; team: Equipe }> {\r\n    return this.http.put<{ message: string; team: Equipe }>(`${this.apiUrl}/${id}`, teamData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team updated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Ajoute un membre à une équipe (nouvelle version)\r\n   */\r\n  addMemberToTeam(teamId: string, memberData: AddMemberRequest): Observable<{ message: string; team: Equipe; newMember: any }> {\r\n    return this.http.post<{ message: string; team: Equipe; newMember: any }>(`${this.apiUrl}/${teamId}/members`, memberData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Member added to team:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Retire un membre d'une équipe (nouvelle version)\r\n   */\r\n  removeMemberFromTeam(teamId: string, memberData: RemoveMemberRequest): Observable<{ message: string; team: Equipe }> {\r\n    return this.http.delete<{ message: string; team: Equipe }>(`${this.apiUrl}/${teamId}/members`, {\r\n      headers: this.getAuthHeaders(),\r\n      body: memberData\r\n    }).pipe(\r\n      tap((data) => console.log('Member removed from team:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupère les équipes d'un utilisateur\r\n   */\r\n  getUserTeams(userId?: string): Observable<{ teams: Equipe[]; count: number }> {\r\n    const url = userId ? `${this.apiUrl}/user/${userId}` : `${this.apiUrl}/my-teams`;\r\n\r\n    return this.http.get<{ teams: Equipe[]; count: number }>(url, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('User teams received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Archive une équipe\r\n   */\r\n  archiveTeam(teamId: string): Observable<{ message: string; team: Equipe }> {\r\n    return this.updateEquipeAdvanced(teamId, { status: 'archived' });\r\n  }\r\n\r\n  /**\r\n   * Active une équipe\r\n   */\r\n  activateTeam(teamId: string): Observable<{ message: string; team: Equipe }> {\r\n    return this.updateEquipeAdvanced(teamId, { status: 'active' });\r\n  }\r\n\r\n  /**\r\n   * Vérifie si un utilisateur peut rejoindre une équipe\r\n   */\r\n  canJoinTeam(team: Equipe): boolean {\r\n    return team.status === 'active' &&\r\n           team.isPublic === true &&\r\n           !team.isFullTeam;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si un utilisateur est admin d'une équipe\r\n   */\r\n  isTeamAdmin(team: Equipe, userId: string): boolean {\r\n    if (typeof team.admin === 'string') {\r\n      return team.admin === userId;\r\n    } else if (team.admin && typeof team.admin === 'object') {\r\n      return team.admin._id === userId || (team.admin as any).id === userId;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si un utilisateur est membre d'une équipe\r\n   */\r\n  isTeamMember(team: Equipe, userId: string): boolean {\r\n    if (!team.members) return false;\r\n\r\n    return team.members.some(member => {\r\n      if (typeof member === 'string') {\r\n        return member === userId;\r\n      } else if (member && typeof member === 'object') {\r\n        return member._id === userId || (member as any).id === userId;\r\n      }\r\n      return false;\r\n    });\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Erreur client: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      const status = error.status;\r\n      const message = error.error?.message || error.statusText;\r\n\r\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\r\n\r\n      // Log des détails supplémentaires pour le débogage\r\n      console.error('Error details:', {\r\n        status: error.status,\r\n        statusText: error.statusText,\r\n        url: error.url,\r\n        error: error.error,\r\n      });\r\n\r\n      if (status === 0) {\r\n        console.error(\r\n          \"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\"\r\n        );\r\n      }\r\n    }\r\n\r\n    console.error('API Error:', errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAwCA,UAAU,QAAQ,sBAAsB;AAChF,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,EAAEC,GAAG,QAAQ,gBAAgB;AAUrD,SAASC,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,OAAO;IAG/CC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACH,MAAM,CAAC;EACtC;EAEQI,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO;MACL,eAAe,EAAE,UAAUF,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB;EACH;EAEAG,UAAUA,CAAA;IACRN,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACH,MAAM,CAAC;IAChD,OAAO,IAAI,CAACD,IAAI,CAACU,GAAG,CAAW,IAAI,CAACT,MAAM,CAAC,CAACU,IAAI,CAC9ChB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEQ,IAAI,CAAC,CAAC,EACnDlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEAC,SAASA,CAACC,EAAU;IAClBZ,OAAO,CAACC,GAAG,CAAC,yBAAyBW,EAAE,UAAU,IAAI,CAACd,MAAM,IAAIc,EAAE,EAAE,CAAC;IACrE,OAAO,IAAI,CAACf,IAAI,CAACU,GAAG,CAAS,GAAG,IAAI,CAACT,MAAM,IAAIc,EAAE,EAAE,CAAC,CAACJ,IAAI,CACvDhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEQ,IAAI,CAAC,CAAC,EAClDlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEAG,SAASA,CAACC,MAAc;IACtBd,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEa,MAAM,CAAC;IACnC,OAAO,IAAI,CAACjB,IAAI,CAACkB,IAAI,CAAS,IAAI,CAACjB,MAAM,EAAEgB,MAAM,EAAE;MACjDE,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEQ,IAAI,CAAC,CAAC,EACzDlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEAO,YAAYA,CAACL,EAAU,EAAEE,MAAc;IACrCd,OAAO,CAACC,GAAG,CAAC,yBAAyBW,EAAE,GAAG,EAAEE,MAAM,CAAC;IACnD,OAAO,IAAI,CAACjB,IAAI,CAACqB,GAAG,CAAS,GAAG,IAAI,CAACpB,MAAM,IAAIc,EAAE,EAAE,EAAEE,MAAM,EAAE;MAC3DE,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEQ,IAAI,CAAC,CAAC,EAC3DlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEAS,YAAYA,CAACP,EAAU;IACrBZ,OAAO,CAACC,GAAG,CAAC,yBAAyBW,EAAE,EAAE,CAAC;IAC1CZ,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACH,MAAM,IAAIc,EAAE,EAAE,CAAC;IAE5C,OAAO,IAAI,CAACf,IAAI,CAACuB,MAAM,CAAC,GAAG,IAAI,CAACtB,MAAM,IAAIc,EAAE,EAAE,EAAE;MAC9CI,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEQ,IAAI,CAAC,CAAC,EAC3DlB,UAAU,CAAE8B,KAAK,IAAI;MACnBrB,OAAO,CAACqB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CrB,OAAO,CAACqB,KAAK,CAAC,cAAc,EAAE,GAAG,IAAI,CAACvB,MAAM,IAAIc,EAAE,EAAE,CAAC;MACrD,OAAO,IAAI,CAACF,WAAW,CAACW,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAC,iBAAiBA,CAACC,MAAc,EAAEC,MAAW;IAC3CxB,OAAO,CAACC,GAAG,CAAC,yBAAyBsB,MAAM,GAAG,EAAEC,MAAM,CAAC;IAEvD;IACA,MAAMC,UAAU,GAAG;MACjBC,MAAM,EAAEF,MAAM,CAACZ,EAAE;MACjBe,IAAI,EAAEH,MAAM,CAACG,IAAI,IAAI,QAAQ,CAAE;KAChC;;IAED3B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwB,UAAU,CAAC;IAC9CzB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOsB,MAAM,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAC7DvB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOuB,MAAM,CAACZ,EAAE,EAAE,QAAQ,EAAEY,MAAM,CAACZ,EAAE,CAAC;IAEnE;IACA,OAAO,IAAI,CAACf,IAAI,CACbkB,IAAI,CAAM,GAAG,IAAI,CAACjB,MAAM,IAAIyB,MAAM,UAAU,EAAEE,UAAU,CAAC,CACzDjB,IAAI,CACHhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEQ,IAAI,CAAC,CAAC,EAC3DlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACL;EAEAkB,sBAAsBA,CAACL,MAAc,EAAEM,QAAgB;IACrD7B,OAAO,CAACC,GAAG,CAAC,mBAAmB4B,QAAQ,cAAcN,MAAM,EAAE,CAAC;IAC9DvB,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACH,MAAM,IAAIyB,MAAM,YAAYM,QAAQ,EAAE,CAAC;IAEpE;IACA,OAAO,IAAI,CAAChC,IAAI,CACbuB,MAAM,CAAM,GAAG,IAAI,CAACtB,MAAM,IAAIyB,MAAM,YAAYM,QAAQ,EAAE,CAAC,CAC3DrB,IAAI,CACHhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEQ,IAAI,CAAC,CAAC,EAC7DlB,UAAU,CAAE8B,KAAK,IAAI;MACnBrB,OAAO,CAACqB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CrB,OAAO,CAACqB,KAAK,CACX,cAAc,EACd,GAAG,IAAI,CAACvB,MAAM,IAAIyB,MAAM,YAAYM,QAAQ,EAAE,CAC/C;MACD,OAAO,IAAI,CAACnB,WAAW,CAACW,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAS,cAAcA,CAACP,MAAc;IAC3BvB,OAAO,CAACC,GAAG,CAAC,kCAAkCsB,MAAM,EAAE,CAAC;IACvD;IACA,OAAO,IAAI,CAAC1B,IAAI,CAACU,GAAG,CAAM,GAAG,IAAI,CAACT,MAAM,IAAIyB,MAAM,EAAE,CAAC,CAACf,IAAI,CACxDf,GAAG,CAAEsC,IAAI,IAAI;MACX/B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE8B,IAAI,CAAC;MACxC;MACA,IAAIA,IAAI,IAAIA,IAAI,CAACC,OAAO,EAAE;QACxB,OAAOD,IAAI,CAACC,OAAO,CAACvC,GAAG,CAAEwC,QAAgB,KAAM;UAC7CC,IAAI,EAAED,QAAQ;UACdN,IAAI,EAAE,QAAQ;UACdQ,GAAG,EAAEF,QAAQ,CAAE;SAChB,CAAC,CAAC;;;MAEL,OAAO,EAAE;IACX,CAAC,CAAC,EACFzC,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEQ,IAAI,CAAC,CAAC,EAC3DlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;EAEA;;;EAGA0B,qBAAqBA,CAACC,OAA2B;IAC/C,IAAIC,MAAM,GAAG,IAAIjD,UAAU,EAAE;IAE7B,IAAIgD,OAAO,EAAE;MACX,IAAIA,OAAO,CAACE,MAAM,EAAED,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,QAAQ,EAAEH,OAAO,CAACE,MAAM,CAAC;MACjE,IAAIF,OAAO,CAACI,QAAQ,KAAKC,SAAS,EAAEJ,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,UAAU,EAAEH,OAAO,CAACI,QAAQ,CAACE,QAAQ,EAAE,CAAC;MAChG,IAAIN,OAAO,CAACO,MAAM,EAAEN,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,QAAQ,EAAEH,OAAO,CAACO,MAAM,CAAC;MACjE,IAAIP,OAAO,CAACQ,IAAI,EAAEP,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,MAAM,EAAEH,OAAO,CAACQ,IAAI,CAACF,QAAQ,EAAE,CAAC;MACtE,IAAIN,OAAO,CAACS,KAAK,EAAER,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,OAAO,EAAEH,OAAO,CAACS,KAAK,CAACH,QAAQ,EAAE,CAAC;;IAG3E,OAAO,IAAI,CAAC9C,IAAI,CAACU,GAAG,CAAmB,IAAI,CAACT,MAAM,EAAE;MAClDwC,MAAM;MACNtB,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEQ,IAAI,CAAC,CAAC,EAChElB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAqC,YAAYA,CAACC,QAA2B;IACtC,OAAO,IAAI,CAACnD,IAAI,CAACkB,IAAI,CAAoC,IAAI,CAACjB,MAAM,EAAEkD,QAAQ,EAAE;MAC9EhC,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,IAAI,CAAC,CAAC,EACjDlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAuC,oBAAoBA,CAACrC,EAAU,EAAEoC,QAA2B;IAC1D,OAAO,IAAI,CAACnD,IAAI,CAACqB,GAAG,CAAoC,GAAG,IAAI,CAACpB,MAAM,IAAIc,EAAE,EAAE,EAAEoC,QAAQ,EAAE;MACxFhC,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,IAAI,CAAC,CAAC,EACjDlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAwC,eAAeA,CAAC3B,MAAc,EAAEE,UAA4B;IAC1D,OAAO,IAAI,CAAC5B,IAAI,CAACkB,IAAI,CAAoD,GAAG,IAAI,CAACjB,MAAM,IAAIyB,MAAM,UAAU,EAAEE,UAAU,EAAE;MACvHT,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEQ,IAAI,CAAC,CAAC,EACzDlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAyC,oBAAoBA,CAAC5B,MAAc,EAAEE,UAA+B;IAClE,OAAO,IAAI,CAAC5B,IAAI,CAACuB,MAAM,CAAoC,GAAG,IAAI,CAACtB,MAAM,IAAIyB,MAAM,UAAU,EAAE;MAC7FP,OAAO,EAAE,IAAI,CAACd,cAAc,EAAE;MAC9BkD,IAAI,EAAE3B;KACP,CAAC,CAACjB,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEQ,IAAI,CAAC,CAAC,EAC7DlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGA2C,YAAYA,CAAC3B,MAAe;IAC1B,MAAM4B,GAAG,GAAG5B,MAAM,GAAG,GAAG,IAAI,CAAC5B,MAAM,SAAS4B,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC5B,MAAM,WAAW;IAEhF,OAAO,IAAI,CAACD,IAAI,CAACU,GAAG,CAAqC+C,GAAG,EAAE;MAC5DtC,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CAAC,CAACM,IAAI,CACLhB,GAAG,CAAEiB,IAAI,IAAKT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEQ,IAAI,CAAC,CAAC,EACxDlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGA6C,WAAWA,CAAChC,MAAc;IACxB,OAAO,IAAI,CAAC0B,oBAAoB,CAAC1B,MAAM,EAAE;MAAEgB,MAAM,EAAE;IAAU,CAAE,CAAC;EAClE;EAEA;;;EAGAiB,YAAYA,CAACjC,MAAc;IACzB,OAAO,IAAI,CAAC0B,oBAAoB,CAAC1B,MAAM,EAAE;MAAEgB,MAAM,EAAE;IAAQ,CAAE,CAAC;EAChE;EAEA;;;EAGAkB,WAAWA,CAAC1B,IAAY;IACtB,OAAOA,IAAI,CAACQ,MAAM,KAAK,QAAQ,IACxBR,IAAI,CAACU,QAAQ,KAAK,IAAI,IACtB,CAACV,IAAI,CAAC2B,UAAU;EACzB;EAEA;;;EAGAC,WAAWA,CAAC5B,IAAY,EAAEL,MAAc;IACtC,IAAI,OAAOK,IAAI,CAAC6B,KAAK,KAAK,QAAQ,EAAE;MAClC,OAAO7B,IAAI,CAAC6B,KAAK,KAAKlC,MAAM;KAC7B,MAAM,IAAIK,IAAI,CAAC6B,KAAK,IAAI,OAAO7B,IAAI,CAAC6B,KAAK,KAAK,QAAQ,EAAE;MACvD,OAAO7B,IAAI,CAAC6B,KAAK,CAACzB,GAAG,KAAKT,MAAM,IAAKK,IAAI,CAAC6B,KAAa,CAAChD,EAAE,KAAKc,MAAM;;IAEvE,OAAO,KAAK;EACd;EAEA;;;EAGAmC,YAAYA,CAAC9B,IAAY,EAAEL,MAAc;IACvC,IAAI,CAACK,IAAI,CAACC,OAAO,EAAE,OAAO,KAAK;IAE/B,OAAOD,IAAI,CAACC,OAAO,CAAC8B,IAAI,CAACC,MAAM,IAAG;MAChC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAOA,MAAM,KAAKrC,MAAM;OACzB,MAAM,IAAIqC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC/C,OAAOA,MAAM,CAAC5B,GAAG,KAAKT,MAAM,IAAKqC,MAAc,CAACnD,EAAE,KAAKc,MAAM;;MAE/D,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEQhB,WAAWA,CAACW,KAAwB;IAC1C,IAAI2C,YAAY,GAAG,EAAE;IAErB,IAAI3C,KAAK,CAACA,KAAK,YAAY4C,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,kBAAkB3C,KAAK,CAACA,KAAK,CAAC6C,OAAO,EAAE;KACvD,MAAM;MACL;MACA,MAAM3B,MAAM,GAAGlB,KAAK,CAACkB,MAAM;MAC3B,MAAM2B,OAAO,GAAG7C,KAAK,CAACA,KAAK,EAAE6C,OAAO,IAAI7C,KAAK,CAAC8C,UAAU;MAExDH,YAAY,GAAG,wBAAwBzB,MAAM,cAAc2B,OAAO,EAAE;MAEpE;MACAlE,OAAO,CAACqB,KAAK,CAAC,gBAAgB,EAAE;QAC9BkB,MAAM,EAAElB,KAAK,CAACkB,MAAM;QACpB4B,UAAU,EAAE9C,KAAK,CAAC8C,UAAU;QAC5Bb,GAAG,EAAEjC,KAAK,CAACiC,GAAG;QACdjC,KAAK,EAAEA,KAAK,CAACA;OACd,CAAC;MAEF,IAAIkB,MAAM,KAAK,CAAC,EAAE;QAChBvC,OAAO,CAACqB,KAAK,CACX,uEAAuE,CACxE;;;IAILrB,OAAO,CAACqB,KAAK,CAAC,YAAY,EAAE2C,YAAY,CAAC;IACzC,OAAO1E,UAAU,CAAC,MAAM,IAAI8E,KAAK,CAACJ,YAAY,CAAC,CAAC;EAClD;;;uBAhTWrE,aAAa,EAAA0E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAb7E,aAAa;MAAA8E,OAAA,EAAb9E,aAAa,CAAA+E,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}