{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: EquipeLayoutComponent,\n  children: [\n  // Liste des équipes (page principale)\n  {\n    path: '',\n    component: EquipeComponent\n  }, {\n    path: 'liste',\n    redirectTo: '',\n    pathMatch: 'full'\n  },\n  // Création d'équipe (formulaire)\n  {\n    path: 'creation',\n    component: EquipeFormComponent\n  }, {\n    path: 'ajouter',\n    redirectTo: 'creation',\n    pathMatch: 'full'\n  }, {\n    path: 'nouveau',\n    redirectTo: 'creation',\n    pathMatch: 'full'\n  },\n  // Autres routes nécessaires\n  {\n    path: 'modifier/:id',\n    component: EquipeFormComponent\n  }, {\n    path: 'detail/:id',\n    component: EquipeDetailComponent\n  }, {\n    path: 'tasks/:id',\n    component: TaskListComponent\n  }]\n}];\nexport class EquipesRoutingModule {\n  static {\n    this.ɵfac = function EquipesRoutingModule_Factory(t) {\n      return new (t || EquipesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EquipesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EquipesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "EquipeFormComponent", "EquipeDetailComponent", "TaskListComponent", "EquipeComponent", "EquipeLayoutComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "EquipesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipes-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\r\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\r\nimport { TaskListComponent } from './task-list/task-list.component';\r\nimport { EquipeComponent } from './equipe/equipe.component';\r\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\r\nimport { EquipeSimpleComponent } from './equipe-simple/equipe-simple.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: EquipeLayoutComponent,\r\n    children: [\r\n      // Liste des équipes (page principale)\r\n      { path: '', component: EquipeComponent },\r\n      { path: 'liste', redirectTo: '', pathMatch: 'full' },\r\n\r\n      // Création d'équipe (formulaire)\r\n      { path: 'creation', component: EquipeFormComponent },\r\n      { path: 'ajouter', redirectTo: 'creation', pathMatch: 'full' },\r\n      { path: 'nouveau', redirectTo: 'creation', pathMatch: 'full' },\r\n\r\n      // Autres routes nécessaires\r\n      { path: 'modifier/:id', component: EquipeFormComponent },\r\n      { path: 'detail/:id', component: EquipeDetailComponent },\r\n      { path: 'tasks/:id', component: TaskListComponent },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class EquipesRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AAEtD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,qBAAqB,QAAQ,yCAAyC;;;AAG/E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,qBAAqB;EAChCI,QAAQ,EAAE;EACR;EACA;IAAEF,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEJ;EAAe,CAAE,EACxC;IAAEG,IAAI,EAAE,OAAO;IAAEG,UAAU,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAM,CAAE;EAEpD;EACA;IAAEJ,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEP;EAAmB,CAAE,EACpD;IAAEM,IAAI,EAAE,SAAS;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EAC9D;IAAEJ,IAAI,EAAE,SAAS;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;EAE9D;EACA;IAAEJ,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEP;EAAmB,CAAE,EACxD;IAAEM,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEN;EAAqB,CAAE,EACxD;IAAEK,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEL;EAAiB,CAAE;CAEtD,CACF;AAMD,OAAM,MAAOS,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBZ,YAAY,CAACa,QAAQ,CAACP,MAAM,CAAC,EAC7BN,YAAY;IAAA;EAAA;;;2EAEXY,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAf,YAAA;IAAAgB,OAAA,GAFrBhB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}