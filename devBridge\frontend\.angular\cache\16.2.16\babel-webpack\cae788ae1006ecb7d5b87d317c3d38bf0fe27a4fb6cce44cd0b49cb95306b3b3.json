{"ast": null, "code": "import { toDate } from \"../toDate.js\";\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport function getTimezoneOffsetInMilliseconds(date) {\n  const _date = toDate(date);\n  const utcDate = new Date(Date.UTC(_date.getFullYear(), _date.getMonth(), _date.getDate(), _date.getHours(), _date.getMinutes(), _date.getSeconds(), _date.getMilliseconds()));\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}", "map": {"version": 3, "names": ["toDate", "getTimezoneOffsetInMilliseconds", "date", "_date", "utcDate", "Date", "UTC", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "setUTCFullYear"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.js"], "sourcesContent": ["import { toDate } from \"../toDate.js\";\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport function getTimezoneOffsetInMilliseconds(date) {\n  const _date = toDate(date);\n  const utcDate = new Date(\n    Date.UTC(\n      _date.getFullYear(),\n      _date.getMonth(),\n      _date.getDate(),\n      _date.getHours(),\n      _date.getMinutes(),\n      _date.getSeconds(),\n      _date.getMilliseconds(),\n    ),\n  );\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,MAAMC,KAAK,GAAGH,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAME,OAAO,GAAG,IAAIC,IAAI,CACtBA,IAAI,CAACC,GAAG,CACNH,KAAK,CAACI,WAAW,CAAC,CAAC,EACnBJ,KAAK,CAACK,QAAQ,CAAC,CAAC,EAChBL,KAAK,CAACM,OAAO,CAAC,CAAC,EACfN,KAAK,CAACO,QAAQ,CAAC,CAAC,EAChBP,KAAK,CAACQ,UAAU,CAAC,CAAC,EAClBR,KAAK,CAACS,UAAU,CAAC,CAAC,EAClBT,KAAK,CAACU,eAAe,CAAC,CACxB,CACF,CAAC;EACDT,OAAO,CAACU,cAAc,CAACX,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC;EAC3C,OAAO,CAACL,IAAI,GAAG,CAACE,OAAO;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}