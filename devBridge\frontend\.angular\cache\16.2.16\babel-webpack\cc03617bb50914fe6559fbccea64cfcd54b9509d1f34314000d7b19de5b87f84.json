{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfHour } from \"./startOfHour.js\";\n\n/**\n * The {@link isSameHour} function options.\n */\n\n/**\n * @name isSameHour\n * @category Hour Helpers\n * @summary Are the given dates in the same hour (and same day)?\n *\n * @description\n * Are the given dates in the same hour (and same day)?\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same hour (and same day)\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 4 September 06:30:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 6, 30))\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 5 September 06:00:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 5, 6, 0))\n * //=> false\n */\nexport function isSameHour(dateLeft, dateRight, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(options?.in, dateLeft, dateRight);\n  return +startOfHour(dateLeft_) === +startOfHour(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameHour;", "map": {"version": 3, "names": ["normalizeDates", "startOfHour", "isSameHour", "dateLeft", "dateRight", "options", "dateLeft_", "dateRight_", "in"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/isSameHour.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfHour } from \"./startOfHour.js\";\n\n/**\n * The {@link isSameHour} function options.\n */\n\n/**\n * @name isSameHour\n * @category Hour Helpers\n * @summary Are the given dates in the same hour (and same day)?\n *\n * @description\n * Are the given dates in the same hour (and same day)?\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same hour (and same day)\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 4 September 06:30:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 6, 30))\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 5 September 06:00:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 5, 6, 0))\n * //=> false\n */\nexport function isSameHour(dateLeft, dateRight, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    dateLeft,\n    dateRight,\n  );\n  return +startOfHour(dateLeft_) === +startOfHour(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameHour;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACvD,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,GAAGP,cAAc,CAC5CK,OAAO,EAAEG,EAAE,EACXL,QAAQ,EACRC,SACF,CAAC;EACD,OAAO,CAACH,WAAW,CAACK,SAAS,CAAC,KAAK,CAACL,WAAW,CAACM,UAAU,CAAC;AAC7D;;AAEA;AACA,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}