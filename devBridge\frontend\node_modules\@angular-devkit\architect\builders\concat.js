"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
const rxjs_1 = require("rxjs");
const src_1 = require("../src");
exports.default = (0, src_1.createBuilder)((options, context) => {
    const allRuns = [];
    context.reportProgress(0, (options.targets ? options.targets.length : 0) +
        (options.builders ? options.builders.length : 0));
    if (options.targets) {
        allRuns.push(...options.targets.map(({ target: targetStr, overrides }) => {
            const [project, target, configuration] = targetStr.split(/:/g, 3);
            return () => context.scheduleTarget({ project, target, configuration }, overrides || {});
        }));
    }
    if (options.builders) {
        allRuns.push(...options.builders.map(({ builder, options }) => {
            return () => context.scheduleBuilder(builder, options || {});
        }));
    }
    let stop = null;
    let i = 0;
    context.reportProgress(i++, allRuns.length);
    return (0, rxjs_1.from)(allRuns).pipe((0, rxjs_1.concatMap)((fn) => stop
        ? (0, rxjs_1.of)(null)
        : (0, rxjs_1.from)(fn()).pipe((0, rxjs_1.switchMap)((run) => (run === null ? (0, rxjs_1.of)(null) : run.output.pipe((0, rxjs_1.first)()))))), (0, rxjs_1.map)((output) => {
        context.reportProgress(i++, allRuns.length);
        if (output === null || stop !== null) {
            return stop || { success: false };
        }
        else if (output.success === false) {
            return (stop = output);
        }
        else {
            return output;
        }
    }), (0, rxjs_1.last)());
});
//# sourceMappingURL=data:application/json;base64,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