{"ast": null, "code": "import { Kind } from \"graphql\";\nimport { <PERSON>Impl, SetImpl, warnOnImproperCacheImplementation } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport equal from \"@wry/equality\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { createFragmentMap, getFragmentDefinitions } from \"../utilities/index.js\";\n/** @internal */\nexport function maskFragment(data, document, cache, fragmentName) {\n  if (!cache.fragmentMatches) {\n    if (globalThis.__DEV__ !== false) {\n      warnOnImproperCacheImplementation();\n    }\n    return data;\n  }\n  var fragments = document.definitions.filter(function (node) {\n    return node.kind === Kind.FRAGMENT_DEFINITION;\n  });\n  if (typeof fragmentName === \"undefined\") {\n    invariant(fragments.length === 1, 49, fragments.length);\n    fragmentName = fragments[0].name.value;\n  }\n  var fragment = fragments.find(function (fragment) {\n    return fragment.name.value === fragmentName;\n  });\n  invariant(!!fragment, 50, fragmentName);\n  if (data == null) {\n    // Maintain the original `null` or `undefined` value\n    return data;\n  }\n  if (equal(data, {})) {\n    // Return early and skip the masking algorithm if we don't have any data\n    // yet. This can happen when cache.diff returns an empty object which is\n    // used from watchFragment.\n    return data;\n  }\n  return maskDefinition(data, fragment.selectionSet, {\n    operationType: \"fragment\",\n    operationName: fragment.name.value,\n    fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n    cache: cache,\n    mutableTargets: new MapImpl(),\n    knownChanged: new SetImpl()\n  });\n}", "map": {"version": 3, "names": ["Kind", "MapImpl", "SetImpl", "warnOnImproperCacheImplementation", "invariant", "equal", "maskDefinition", "createFragmentMap", "getFragmentDefinitions", "maskFragment", "data", "document", "cache", "fragmentName", "fragmentMatches", "globalThis", "__DEV__", "fragments", "definitions", "filter", "node", "kind", "FRAGMENT_DEFINITION", "length", "name", "value", "fragment", "find", "selectionSet", "operationType", "operationName", "fragmentMap", "mutableTargets", "knownChanged"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/masking/maskFragment.js"], "sourcesContent": ["import { Kind } from \"graphql\";\nimport { MapImpl, SetImpl, warnOnImproperCacheImplementation, } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport equal from \"@wry/equality\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { createFragmentMap, getFragmentDefinitions, } from \"../utilities/index.js\";\n/** @internal */\nexport function maskFragment(data, document, cache, fragmentName) {\n    if (!cache.fragmentMatches) {\n        if (globalThis.__DEV__ !== false) {\n            warnOnImproperCacheImplementation();\n        }\n        return data;\n    }\n    var fragments = document.definitions.filter(function (node) {\n        return node.kind === Kind.FRAGMENT_DEFINITION;\n    });\n    if (typeof fragmentName === \"undefined\") {\n        invariant(fragments.length === 1, 49, fragments.length);\n        fragmentName = fragments[0].name.value;\n    }\n    var fragment = fragments.find(function (fragment) { return fragment.name.value === fragmentName; });\n    invariant(!!fragment, 50, fragmentName);\n    if (data == null) {\n        // Maintain the original `null` or `undefined` value\n        return data;\n    }\n    if (equal(data, {})) {\n        // Return early and skip the masking algorithm if we don't have any data\n        // yet. This can happen when cache.diff returns an empty object which is\n        // used from watchFragment.\n        return data;\n    }\n    return maskDefinition(data, fragment.selectionSet, {\n        operationType: \"fragment\",\n        operationName: fragment.name.value,\n        fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n        cache: cache,\n        mutableTargets: new MapImpl(),\n        knownChanged: new SetImpl(),\n    });\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,SAAS;AAC9B,SAASC,OAAO,EAAEC,OAAO,EAAEC,iCAAiC,QAAS,YAAY;AACjF,SAASC,SAAS,QAAQ,+BAA+B;AACzD,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,iBAAiB,EAAEC,sBAAsB,QAAS,uBAAuB;AAClF;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,YAAY,EAAE;EAC9D,IAAI,CAACD,KAAK,CAACE,eAAe,EAAE;IACxB,IAAIC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;MAC9Bb,iCAAiC,CAAC,CAAC;IACvC;IACA,OAAOO,IAAI;EACf;EACA,IAAIO,SAAS,GAAGN,QAAQ,CAACO,WAAW,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAE;IACxD,OAAOA,IAAI,CAACC,IAAI,KAAKrB,IAAI,CAACsB,mBAAmB;EACjD,CAAC,CAAC;EACF,IAAI,OAAOT,YAAY,KAAK,WAAW,EAAE;IACrCT,SAAS,CAACa,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE,EAAE,EAAEN,SAAS,CAACM,MAAM,CAAC;IACvDV,YAAY,GAAGI,SAAS,CAAC,CAAC,CAAC,CAACO,IAAI,CAACC,KAAK;EAC1C;EACA,IAAIC,QAAQ,GAAGT,SAAS,CAACU,IAAI,CAAC,UAAUD,QAAQ,EAAE;IAAE,OAAOA,QAAQ,CAACF,IAAI,CAACC,KAAK,KAAKZ,YAAY;EAAE,CAAC,CAAC;EACnGT,SAAS,CAAC,CAAC,CAACsB,QAAQ,EAAE,EAAE,EAAEb,YAAY,CAAC;EACvC,IAAIH,IAAI,IAAI,IAAI,EAAE;IACd;IACA,OAAOA,IAAI;EACf;EACA,IAAIL,KAAK,CAACK,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;IACjB;IACA;IACA;IACA,OAAOA,IAAI;EACf;EACA,OAAOJ,cAAc,CAACI,IAAI,EAAEgB,QAAQ,CAACE,YAAY,EAAE;IAC/CC,aAAa,EAAE,UAAU;IACzBC,aAAa,EAAEJ,QAAQ,CAACF,IAAI,CAACC,KAAK;IAClCM,WAAW,EAAExB,iBAAiB,CAACC,sBAAsB,CAACG,QAAQ,CAAC,CAAC;IAChEC,KAAK,EAAEA,KAAK;IACZoB,cAAc,EAAE,IAAI/B,OAAO,CAAC,CAAC;IAC7BgC,YAAY,EAAE,IAAI/B,OAAO,CAAC;EAC9B,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}