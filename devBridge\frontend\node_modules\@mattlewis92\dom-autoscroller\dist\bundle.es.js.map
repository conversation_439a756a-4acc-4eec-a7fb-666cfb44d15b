{"version": 3, "file": "bundle.es.js", "sources": ["../src/index.js"], "sourcesContent": ["import {boolean} from 'type-func';\nimport {\n    hasElement,\n    addElements,\n    removeElements\n} from 'dom-set';\n\nimport {\n    createPointCB,\n    getClientRect as getRect,\n    pointInside\n} from 'dom-plane';\n\nimport mousemoveDispatcher from 'dom-mousemove-dispatcher';\n\nconst prefix = [ 'webkit', 'moz', 'ms', 'o' ];\n\nconst requestFrame = (() => {\n\n    if (typeof window === \"undefined\") {\n        return () => {};\n    }\n\n    for ( let i = 0, limit = prefix.length ; i < limit && ! window.requestAnimationFrame ; ++i ) {\n        window.requestAnimationFrame = window[ prefix[ i ] + 'RequestAnimationFrame' ];\n    }\n\n    if ( ! window.requestAnimationFrame ) {\n        let lastTime = 0;\n\n        window.requestAnimationFrame = callback => {\n            const now   = new Date().getTime();\n            const ttc   = Math.max( 0, 16 - now - lastTime );\n            const timer = window.setTimeout( () => callback( now + ttc ), ttc );\n\n            lastTime = now + ttc;\n\n            return timer;\n        };\n    }\n\n    return window.requestAnimationFrame.bind( window );\n})();\n\nconst cancelFrame = (() => {\n\n    if (typeof window === \"undefined\") {\n        return () => {};\n    }\n\n    for ( let i = 0, limit = prefix.length ; i < limit && ! window.cancelAnimationFrame ; ++i ) {\n        window.cancelAnimationFrame = window[ prefix[ i ] + 'CancelAnimationFrame' ] || window[ prefix[ i ] + 'CancelRequestAnimationFrame' ];\n    }\n\n    if ( ! window.cancelAnimationFrame ) {\n        window.cancelAnimationFrame = timer => {\n            window.clearTimeout( timer );\n        };\n    }\n\n    return window.cancelAnimationFrame.bind( window );\n})();\n\nfunction AutoScroller(elements, options = {}){\n    const self = this;\n    let maxSpeed = 4, scrolling = false;\n\n    if (typeof options.margin !== 'object') {\n        const margin = options.margin || -1;\n\n        this.margin = {\n            left: margin,\n            right: margin,\n            top: margin,\n            bottom: margin\n        }\n    } else {\n        this.margin = options.margin;\n    }\n\n    //this.scrolling = false;\n    this.scrollWhenOutside = options.scrollWhenOutside || false;\n\n    let point = {},\n        pointCB = createPointCB(point),\n        dispatcher = mousemoveDispatcher(),\n        down = false;\n\n    window.addEventListener('mousemove', pointCB, false);\n    window.addEventListener('touchmove', pointCB, false);\n\n    if(!isNaN(options.maxSpeed)){\n        maxSpeed = options.maxSpeed;\n    }\n\n    if (typeof maxSpeed !== 'object') {\n        maxSpeed = {\n            left: maxSpeed,\n            right: maxSpeed,\n            top: maxSpeed,\n            bottom: maxSpeed\n        }\n    }\n\n    this.autoScroll = boolean(options.autoScroll);\n    this.syncMove = boolean(options.syncMove, false);\n\n    this.destroy = function(forceCleanAnimation) {\n        window.removeEventListener('mousemove', pointCB, false);\n        window.removeEventListener('touchmove', pointCB, false);\n        window.removeEventListener('mousedown', onDown, false);\n        window.removeEventListener('touchstart', onDown, false);\n        window.removeEventListener('mouseup', onUp, false);\n        window.removeEventListener('touchend', onUp, false);\n        window.removeEventListener('pointerup', onUp, false);\n        window.removeEventListener('mouseleave', onMouseOut, false);\n\n        window.removeEventListener('mousemove', onMove, false);\n        window.removeEventListener('touchmove', onMove, false);\n\n        window.removeEventListener('scroll', setScroll, true);\n        elements = [];\n        if(forceCleanAnimation){\n          cleanAnimation();\n        }\n    };\n\n    this.add = function(...element){\n        addElements(elements, ...element);\n        return this;\n    };\n\n    this.remove = function(...element){\n        return removeElements(elements, ...element);\n    };\n\n    let hasWindow = null, windowAnimationFrame;\n\n    if(Object.prototype.toString.call(elements) !== '[object Array]'){\n        elements = [elements];\n    }\n\n    (function(temp){\n        elements = [];\n        temp.forEach(function(element){\n            if(element === window){\n                hasWindow = window;\n            }else{\n                self.add(element);\n            }\n        })\n    }(elements));\n\n    Object.defineProperties(this, {\n        down: {\n            get: function(){ return down; }\n        },\n        maxSpeed: {\n            get: function(){ return maxSpeed; }\n        },\n        point: {\n            get: function(){ return point; }\n        },\n        scrolling: {\n            get: function(){ return scrolling; }\n        }\n    });\n\n    let n = 0, current = null, animationFrame;\n\n    window.addEventListener('mousedown', onDown, false);\n    window.addEventListener('touchstart', onDown, false);\n    window.addEventListener('mouseup', onUp, false);\n    window.addEventListener('touchend', onUp, false);\n\n    /*\n    IE does not trigger mouseup event when scrolling.\n    It is a known issue that Microsoft won't fix.\n    https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\n    IE supports pointer events instead\n    */\n    window.addEventListener('pointerup', onUp, false);\n\n    window.addEventListener('mousemove', onMove, false);\n    window.addEventListener('touchmove', onMove, false);\n\n    window.addEventListener('mouseleave', onMouseOut, false);\n\n    window.addEventListener('scroll', setScroll, true);\n\n    function setScroll(e){\n\n        for(let i=0; i<elements.length; i++){\n            if(elements[i] === e.target){\n                scrolling = true;\n                break;\n            }\n        }\n\n        if(scrolling){\n            requestFrame(()=>scrolling = false)\n        }\n    }\n\n    function onDown(){\n        down = true;\n    }\n\n    function onUp(){\n        down = false;\n        cleanAnimation();\n    }\n    function cleanAnimation(){\n      cancelFrame(animationFrame);\n      cancelFrame(windowAnimationFrame);\n    }\n    function onMouseOut(){\n        down = false;\n    }\n\n    function getTarget(target){\n        if(!target){\n            return null;\n        }\n\n        if(current === target){\n            return target;\n        }\n\n        if(hasElement(elements, target)){\n            return target;\n        }\n\n        while(target = target.parentNode){\n            if(hasElement(elements, target)){\n                return target;\n            }\n        }\n\n        return null;\n    }\n\n    function getElementUnderPoint(){\n        let underPoint = null;\n\n        for(var i=0; i<elements.length; i++){\n            if(inside(point, elements[i])){\n                underPoint = elements[i];\n            }\n        }\n\n        return underPoint;\n    }\n\n\n    function onMove(event){\n\n        if(!self.autoScroll()) return;\n\n        if(event['dispatched']){ return; }\n\n        let target = event.target, body = document.body;\n\n        if(current && !inside(point, current)){\n            if(!self.scrollWhenOutside){\n                current = null;\n            }\n        }\n\n        if(target && target.parentNode === body){\n            //The special condition to improve speed.\n            target = getElementUnderPoint();\n        }else{\n            target = getTarget(target);\n\n            if(!target){\n                target = getElementUnderPoint();\n            }\n        }\n\n\n        if(target && target !== current){\n            current = target;\n        }\n\n        if(hasWindow){\n            cancelFrame(windowAnimationFrame);\n            windowAnimationFrame = requestFrame(scrollWindow);\n        }\n\n\n        if(!current){\n            return;\n        }\n\n        cancelFrame(animationFrame);\n        animationFrame = requestFrame(scrollTick);\n    }\n\n    function scrollWindow(){\n        autoScroll(hasWindow);\n\n        cancelFrame(windowAnimationFrame);\n        windowAnimationFrame = requestFrame(scrollWindow);\n    }\n\n    function scrollTick(){\n\n        if(!current){\n            return;\n        }\n\n        autoScroll(current);\n\n        cancelFrame(animationFrame);\n        animationFrame = requestFrame(scrollTick);\n\n    }\n\n\n    function autoScroll(el){\n        let rect = getRect(el), scrollx, scrolly;\n\n        if(point.x < rect.left + self.margin.left){\n            scrollx = Math.floor(\n                Math.max(-1, (point.x - rect.left) / self.margin.left - 1) * self.maxSpeed.left\n            );\n        }else if(point.x > rect.right - self.margin.right){\n            scrollx = Math.ceil(\n                Math.min(1, (point.x - rect.right) / self.margin.right + 1) * self.maxSpeed.right\n            );\n        }else{\n            scrollx = 0;\n        }\n\n        if(point.y < rect.top + self.margin.top){\n            scrolly = Math.floor(\n                Math.max(-1, (point.y - rect.top) / self.margin.top - 1) * self.maxSpeed.top\n            );\n        }else if(point.y > rect.bottom - self.margin.bottom){\n            scrolly = Math.ceil(\n                Math.min(1, (point.y - rect.bottom) / self.margin.bottom + 1) * self.maxSpeed.bottom\n            );\n        }else{\n            scrolly = 0;\n        }\n\n        if(self.syncMove()){\n            /*\n            Notes about mousemove event dispatch.\n            screen(X/Y) should need to be updated.\n            Some other properties might need to be set.\n            Keep the syncMove option default false until all inconsistencies are taken care of.\n            */\n            dispatcher.dispatch(el, {\n                pageX: point.pageX + scrollx,\n                pageY: point.pageY + scrolly,\n                clientX: point.x + scrollx,\n                clientY: point.y + scrolly\n            });\n        }\n\n        setTimeout(()=>{\n\n            if(scrolly){\n                scrollY(el, scrolly);\n            }\n\n            if(scrollx){\n                scrollX(el, scrollx);\n            }\n\n        });\n    }\n\n    function scrollY(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\n        }else{\n            el.scrollTop += amount;\n        }\n    }\n\n    function scrollX(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\n        }else{\n            el.scrollLeft += amount;\n        }\n    }\n\n}\n\nexport default function AutoScrollerFactory(element, options){\n    return new AutoScroller(element, options);\n}\n\nfunction inside(point, el, rect){\n    if(!rect){\n        return pointInside(point, el);\n    }else{\n        return (point.y > rect.top && point.y < rect.bottom &&\n                point.x > rect.left && point.x < rect.right);\n    }\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\ngit push -u origin master\n*/\n"], "names": ["const", "let", "getRect"], "mappings": ";;;;;AAeAA,IAAM,MAAM,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;;AAE9CA,IAAM,YAAY,GAAG,CAAC,YAAG;;IAErB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,YAAG,EAAK,CAAC;KACnB;;IAED,MAAMC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,IAAI,EAAE,MAAM,CAAC,qBAAqB,GAAG,EAAE,CAAC,GAAG;QACzF,MAAM,CAAC,qBAAqB,GAAG,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,CAAC;KAClF;;IAED,KAAK,EAAE,MAAM,CAAC,qBAAqB,GAAG;QAClCA,IAAI,QAAQ,GAAG,CAAC,CAAC;;QAEjB,MAAM,CAAC,qBAAqB,GAAG,UAAA,QAAQ,EAAC;YACpCD,IAAM,GAAG,KAAK,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YACnCA,IAAM,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,QAAQ,EAAE,CAAC;YACjDA,IAAM,KAAK,GAAG,MAAM,CAAC,UAAU,EAAE,YAAG,SAAG,QAAQ,EAAE,GAAG,GAAG,GAAG,EAAE,GAAA,EAAE,GAAG,EAAE,CAAC;;YAEpE,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;;YAErB,OAAO,KAAK,CAAC;SAChB,CAAC;KACL;;IAED,OAAO,MAAM,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CACtD,GAAG,CAAC;;AAELA,IAAM,WAAW,GAAG,CAAC,YAAG;;IAEpB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,YAAG,EAAK,CAAC;KACnB;;IAED,MAAMC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,IAAI,EAAE,MAAM,CAAC,oBAAoB,GAAG,EAAE,CAAC,GAAG;QACxF,MAAM,CAAC,oBAAoB,GAAG,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,sBAAsB,EAAE,IAAI,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,6BAA6B,EAAE,CAAC;KACzI;;IAED,KAAK,EAAE,MAAM,CAAC,oBAAoB,GAAG;QACjC,MAAM,CAAC,oBAAoB,GAAG,UAAA,KAAK,EAAC;YAChC,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC;SAChC,CAAC;KACL;;IAED,OAAO,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CACrD,GAAG,CAAC;;AAEL,SAAS,YAAY,CAAC,QAAQ,EAAE,OAAY,CAAC;qCAAN,GAAG,EAAE;;IACxCD,IAAM,IAAI,GAAG,IAAI,CAAC;IAClBC,IAAI,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC;;IAEpC,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;QACpCD,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;;QAEpC,IAAI,CAAC,MAAM,GAAG;YACV,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,MAAM;YACb,GAAG,EAAE,MAAM;YACX,MAAM,EAAE,MAAM;SACjB,CAAA;KACJ,MAAM;QACH,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;KAChC;;;IAGD,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,KAAK,CAAC;;IAE5DC,IAAI,KAAK,GAAG,EAAE;QACV,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC;QAC9B,UAAU,GAAG,mBAAmB,EAAE;QAClC,IAAI,GAAG,KAAK,CAAC;;IAEjB,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACrD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;;IAErD,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACxB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;KAC/B;;IAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAC9B,QAAQ,GAAG;YACP,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,QAAQ;YACf,GAAG,EAAE,QAAQ;YACb,MAAM,EAAE,QAAQ;SACnB,CAAA;KACJ;;IAED,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;;IAEjD,IAAI,CAAC,OAAO,GAAG,SAAS,mBAAmB,EAAE;QACzC,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;;QAE5D,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;;QAEvD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACtD,QAAQ,GAAG,EAAE,CAAC;QACd,GAAG,mBAAmB,CAAC;UACrB,cAAc,EAAE,CAAC;SAClB;KACJ,CAAC;;IAEF,IAAI,CAAC,GAAG,GAAG,UAAoB;;;;QAC3B,WAAW,MAAA,CAAC,UAAA,QAAQ,WAAE,OAAU,EAAA,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;KACf,CAAC;;IAEF,IAAI,CAAC,MAAM,GAAG,UAAoB;;;;QAC9B,OAAO,cAAc,MAAA,CAAC,UAAA,QAAQ,WAAE,OAAU,EAAA,CAAC,CAAC;KAC/C,CAAC;;IAEFA,IAAI,SAAS,GAAG,IAAI,EAAE,oBAAoB,CAAC;;IAE3C,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB,CAAC;QAC7D,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;KACzB;;IAED,CAAC,SAAS,IAAI,CAAC;QACX,QAAQ,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,CAAC,SAAS,OAAO,CAAC;YAC1B,GAAG,OAAO,KAAK,MAAM,CAAC;gBAClB,SAAS,GAAG,MAAM,CAAC;aACtB,IAAI;gBACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACrB;SACJ,CAAC,CAAA;KACL,CAAC,QAAQ,CAAC,EAAE;;IAEb,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;QAC1B,IAAI,EAAE;YACF,GAAG,EAAE,UAAU,EAAE,OAAO,IAAI,CAAC,EAAE;SAClC;QACD,QAAQ,EAAE;YACN,GAAG,EAAE,UAAU,EAAE,OAAO,QAAQ,CAAC,EAAE;SACtC;QACD,KAAK,EAAE;YACH,GAAG,EAAE,UAAU,EAAE,OAAO,KAAK,CAAC,EAAE;SACnC;QACD,SAAS,EAAE;YACP,GAAG,EAAE,UAAU,EAAE,OAAO,SAAS,CAAC,EAAE;SACvC;KACJ,CAAC,CAAC;;IAEHA,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE,cAAc,CAAC;;IAE1C,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAChD,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;;;;;;;;IAQjD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;;IAElD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;;IAEpD,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;;IAEzD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;;IAEnD,SAAS,SAAS,CAAC,CAAC,CAAC;;QAEjB,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YAChC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;gBACxB,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM;aACT;SACJ;;QAED,GAAG,SAAS,CAAC;YACT,YAAY,CAAC,WAAE,SAAE,SAAS,GAAG,KAAK,GAAA,CAAC,CAAA;SACtC;KACJ;;IAED,SAAS,MAAM,EAAE;QACb,IAAI,GAAG,IAAI,CAAC;KACf;;IAED,SAAS,IAAI,EAAE;QACX,IAAI,GAAG,KAAK,CAAC;QACb,cAAc,EAAE,CAAC;KACpB;IACD,SAAS,cAAc,EAAE;MACvB,WAAW,CAAC,cAAc,CAAC,CAAC;MAC5B,WAAW,CAAC,oBAAoB,CAAC,CAAC;KACnC;IACD,SAAS,UAAU,EAAE;QACjB,IAAI,GAAG,KAAK,CAAC;KAChB;;IAED,SAAS,SAAS,CAAC,MAAM,CAAC;QACtB,GAAG,CAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;SACf;;QAED,GAAG,OAAO,KAAK,MAAM,CAAC;YAClB,OAAO,MAAM,CAAC;SACjB;;QAED,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5B,OAAO,MAAM,CAAC;SACjB;;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;YAC7B,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC5B,OAAO,MAAM,CAAC;aACjB;SACJ;;QAED,OAAO,IAAI,CAAC;KACf;;IAED,SAAS,oBAAoB,EAAE;QAC3BA,IAAI,UAAU,GAAG,IAAI,CAAC;;QAEtB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YAChC,GAAG,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC5B;SACJ;;QAED,OAAO,UAAU,CAAC;KACrB;;;IAGD,SAAS,MAAM,CAAC,KAAK,CAAC;;QAElB,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAA,OAAO,EAAA;;QAE9B,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;;QAElCA,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;;QAEhD,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACvB,OAAO,GAAG,IAAI,CAAC;aAClB;SACJ;;QAED,GAAG,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC;;YAEpC,MAAM,GAAG,oBAAoB,EAAE,CAAC;SACnC,IAAI;YACD,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;;YAE3B,GAAG,CAAC,MAAM,CAAC;gBACP,MAAM,GAAG,oBAAoB,EAAE,CAAC;aACnC;SACJ;;;QAGD,GAAG,MAAM,IAAI,MAAM,KAAK,OAAO,CAAC;YAC5B,OAAO,GAAG,MAAM,CAAC;SACpB;;QAED,GAAG,SAAS,CAAC;YACT,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAClC,oBAAoB,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;SACrD;;;QAGD,GAAG,CAAC,OAAO,CAAC;YACR,OAAO;SACV;;QAED,WAAW,CAAC,cAAc,CAAC,CAAC;QAC5B,cAAc,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;KAC7C;;IAED,SAAS,YAAY,EAAE;QACnB,UAAU,CAAC,SAAS,CAAC,CAAC;;QAEtB,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAClC,oBAAoB,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;KACrD;;IAED,SAAS,UAAU,EAAE;;QAEjB,GAAG,CAAC,OAAO,CAAC;YACR,OAAO;SACV;;QAED,UAAU,CAAC,OAAO,CAAC,CAAC;;QAEpB,WAAW,CAAC,cAAc,CAAC,CAAC;QAC5B,cAAc,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;;KAE7C;;;IAGD,SAAS,UAAU,CAAC,EAAE,CAAC;QACnBA,IAAI,IAAI,GAAGC,aAAO,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;;QAEzC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACtC,OAAO,GAAG,IAAI,CAAC,KAAK;gBAChB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI;aAClF,CAAC;SACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAC9C,OAAO,GAAG,IAAI,CAAC,IAAI;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK;aACpF,CAAC;SACL,IAAI;YACD,OAAO,GAAG,CAAC,CAAC;SACf;;QAED,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACpC,OAAO,GAAG,IAAI,CAAC,KAAK;gBAChB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG;aAC/E,CAAC;SACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAChD,OAAO,GAAG,IAAI,CAAC,IAAI;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM;aACvF,CAAC;SACL,IAAI;YACD,OAAO,GAAG,CAAC,CAAC;SACf;;QAED,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;;;;;;;YAOf,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO;gBAC5B,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO;gBAC5B,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO;gBAC1B,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO;aAC7B,CAAC,CAAC;SACN;;QAED,UAAU,CAAC,WAAE;;YAET,GAAG,OAAO,CAAC;gBACP,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aACxB;;YAED,GAAG,OAAO,CAAC;gBACP,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aACxB;;SAEJ,CAAC,CAAC;KACN;;IAED,SAAS,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;QACxB,GAAG,EAAE,KAAK,MAAM,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;SAC5D,IAAI;YACD,EAAE,CAAC,SAAS,IAAI,MAAM,CAAC;SAC1B;KACJ;;IAED,SAAS,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;QACxB,GAAG,EAAE,KAAK,MAAM,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;SAC5D,IAAI;YACD,EAAE,CAAC,UAAU,IAAI,MAAM,CAAC;SAC3B;KACJ;;CAEJ;;AAED,AAAe,SAAS,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;IACzD,OAAO,IAAI,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAC7C;;AAED,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IAC5B,GAAG,CAAC,IAAI,CAAC;QACL,OAAO,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;KACjC,IAAI;QACD,QAAQ,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;gBAC3C,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;KACxD;CACJ;;;;;;;"}