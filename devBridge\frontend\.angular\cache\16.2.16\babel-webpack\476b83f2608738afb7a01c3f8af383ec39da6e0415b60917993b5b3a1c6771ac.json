{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { toObjMap } from '../jsutils/toObjMap.mjs';\nimport { DirectiveLocation } from '../language/directiveLocation.mjs';\nimport { assertName } from './assertName.mjs';\nimport { argsToArgsConfig, defineArguments, GraphQLNonNull } from './definition.mjs';\nimport { GraphQLBoolean, GraphQLString } from './scalars.mjs';\n/**\n * Test if the given value is a GraphQL directive.\n */\n\nexport function isDirective(directive) {\n  return instanceOf(directive, GraphQLDirective);\n}\nexport function assertDirective(directive) {\n  if (!isDirective(directive)) {\n    throw new Error(`Expected ${inspect(directive)} to be a GraphQL directive.`);\n  }\n  return directive;\n}\n/**\n * Custom extensions\n *\n * @remarks\n * Use a unique identifier name for your extension, for example the name of\n * your library or project. Do not use a shortened identifier as this increases\n * the risk of conflicts. We recommend you add at most one extension field,\n * an object which can contain all the values you need.\n */\n\n/**\n * Directives are used by the GraphQL runtime as a way of modifying execution\n * behavior. Type system creators will usually not create these directly.\n */\nexport class GraphQLDirective {\n  constructor(config) {\n    var _config$isRepeatable, _config$args;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.locations = config.locations;\n    this.isRepeatable = (_config$isRepeatable = config.isRepeatable) !== null && _config$isRepeatable !== void 0 ? _config$isRepeatable : false;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    Array.isArray(config.locations) || devAssert(false, `@${config.name} locations must be an Array.`);\n    const args = (_config$args = config.args) !== null && _config$args !== void 0 ? _config$args : {};\n    isObjectLike(args) && !Array.isArray(args) || devAssert(false, `@${config.name} args must be an object with argument names as keys.`);\n    this.args = defineArguments(args);\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLDirective';\n  }\n  toConfig() {\n    return {\n      name: this.name,\n      description: this.description,\n      locations: this.locations,\n      args: argsToArgsConfig(this.args),\n      isRepeatable: this.isRepeatable,\n      extensions: this.extensions,\n      astNode: this.astNode\n    };\n  }\n  toString() {\n    return '@' + this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\n\n/**\n * Used to conditionally include fields or fragments.\n */\nexport const GraphQLIncludeDirective = new GraphQLDirective({\n  name: 'include',\n  description: 'Directs the executor to include this field or fragment only when the `if` argument is true.',\n  locations: [DirectiveLocation.FIELD, DirectiveLocation.FRAGMENT_SPREAD, DirectiveLocation.INLINE_FRAGMENT],\n  args: {\n    if: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      description: 'Included when true.'\n    }\n  }\n});\n/**\n * Used to conditionally skip (exclude) fields or fragments.\n */\n\nexport const GraphQLSkipDirective = new GraphQLDirective({\n  name: 'skip',\n  description: 'Directs the executor to skip this field or fragment when the `if` argument is true.',\n  locations: [DirectiveLocation.FIELD, DirectiveLocation.FRAGMENT_SPREAD, DirectiveLocation.INLINE_FRAGMENT],\n  args: {\n    if: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      description: 'Skipped when true.'\n    }\n  }\n});\n/**\n * Constant string used for default reason for a deprecation.\n */\n\nexport const DEFAULT_DEPRECATION_REASON = 'No longer supported';\n/**\n * Used to declare element of a GraphQL schema as deprecated.\n */\n\nexport const GraphQLDeprecatedDirective = new GraphQLDirective({\n  name: 'deprecated',\n  description: 'Marks an element of a GraphQL schema as no longer supported.',\n  locations: [DirectiveLocation.FIELD_DEFINITION, DirectiveLocation.ARGUMENT_DEFINITION, DirectiveLocation.INPUT_FIELD_DEFINITION, DirectiveLocation.ENUM_VALUE],\n  args: {\n    reason: {\n      type: GraphQLString,\n      description: 'Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).',\n      defaultValue: DEFAULT_DEPRECATION_REASON\n    }\n  }\n});\n/**\n * Used to provide a URL for specifying the behavior of custom scalar definitions.\n */\n\nexport const GraphQLSpecifiedByDirective = new GraphQLDirective({\n  name: 'specifiedBy',\n  description: 'Exposes a URL that specifies the behavior of this scalar.',\n  locations: [DirectiveLocation.SCALAR],\n  args: {\n    url: {\n      type: new GraphQLNonNull(GraphQLString),\n      description: 'The URL that specifies the behavior of this scalar.'\n    }\n  }\n});\n/**\n * The full list of specified directives.\n */\n\nexport const specifiedDirectives = Object.freeze([GraphQLIncludeDirective, GraphQLSkipDirective, GraphQLDeprecatedDirective, GraphQLSpecifiedByDirective]);\nexport function isSpecifiedDirective(directive) {\n  return specifiedDirectives.some(({\n    name\n  }) => name === directive.name);\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "instanceOf", "isObjectLike", "toObjMap", "DirectiveLocation", "assertName", "argsToArgsConfig", "defineArguments", "GraphQLNonNull", "GraphQLBoolean", "GraphQLString", "isDirective", "directive", "GraphQLDirective", "assertDirective", "Error", "constructor", "config", "_config$isRepeatable", "_config$args", "name", "description", "locations", "isRepeatable", "extensions", "astNode", "Array", "isArray", "args", "Symbol", "toStringTag", "toConfig", "toString", "toJSON", "GraphQLIncludeDirective", "FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "if", "type", "GraphQLSkipDirective", "DEFAULT_DEPRECATION_REASON", "GraphQLDeprecatedDirective", "FIELD_DEFINITION", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "ENUM_VALUE", "reason", "defaultValue", "GraphQLSpecifiedByDirective", "SCALAR", "url", "specifiedDirectives", "Object", "freeze", "isSpecifiedDirective", "some"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/type/directives.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { toObjMap } from '../jsutils/toObjMap.mjs';\nimport { DirectiveLocation } from '../language/directiveLocation.mjs';\nimport { assertName } from './assertName.mjs';\nimport {\n  argsToArgsConfig,\n  defineArguments,\n  GraphQLNonNull,\n} from './definition.mjs';\nimport { GraphQLBoolean, GraphQLString } from './scalars.mjs';\n/**\n * Test if the given value is a GraphQL directive.\n */\n\nexport function isDirective(directive) {\n  return instanceOf(directive, GraphQLDirective);\n}\nexport function assertDirective(directive) {\n  if (!isDirective(directive)) {\n    throw new Error(\n      `Expected ${inspect(directive)} to be a GraphQL directive.`,\n    );\n  }\n\n  return directive;\n}\n/**\n * Custom extensions\n *\n * @remarks\n * Use a unique identifier name for your extension, for example the name of\n * your library or project. Do not use a shortened identifier as this increases\n * the risk of conflicts. We recommend you add at most one extension field,\n * an object which can contain all the values you need.\n */\n\n/**\n * Directives are used by the GraphQL runtime as a way of modifying execution\n * behavior. Type system creators will usually not create these directly.\n */\nexport class GraphQLDirective {\n  constructor(config) {\n    var _config$isRepeatable, _config$args;\n\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.locations = config.locations;\n    this.isRepeatable =\n      (_config$isRepeatable = config.isRepeatable) !== null &&\n      _config$isRepeatable !== void 0\n        ? _config$isRepeatable\n        : false;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    Array.isArray(config.locations) ||\n      devAssert(false, `@${config.name} locations must be an Array.`);\n    const args =\n      (_config$args = config.args) !== null && _config$args !== void 0\n        ? _config$args\n        : {};\n    (isObjectLike(args) && !Array.isArray(args)) ||\n      devAssert(\n        false,\n        `@${config.name} args must be an object with argument names as keys.`,\n      );\n    this.args = defineArguments(args);\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLDirective';\n  }\n\n  toConfig() {\n    return {\n      name: this.name,\n      description: this.description,\n      locations: this.locations,\n      args: argsToArgsConfig(this.args),\n      isRepeatable: this.isRepeatable,\n      extensions: this.extensions,\n      astNode: this.astNode,\n    };\n  }\n\n  toString() {\n    return '@' + this.name;\n  }\n\n  toJSON() {\n    return this.toString();\n  }\n}\n\n/**\n * Used to conditionally include fields or fragments.\n */\nexport const GraphQLIncludeDirective = new GraphQLDirective({\n  name: 'include',\n  description:\n    'Directs the executor to include this field or fragment only when the `if` argument is true.',\n  locations: [\n    DirectiveLocation.FIELD,\n    DirectiveLocation.FRAGMENT_SPREAD,\n    DirectiveLocation.INLINE_FRAGMENT,\n  ],\n  args: {\n    if: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      description: 'Included when true.',\n    },\n  },\n});\n/**\n * Used to conditionally skip (exclude) fields or fragments.\n */\n\nexport const GraphQLSkipDirective = new GraphQLDirective({\n  name: 'skip',\n  description:\n    'Directs the executor to skip this field or fragment when the `if` argument is true.',\n  locations: [\n    DirectiveLocation.FIELD,\n    DirectiveLocation.FRAGMENT_SPREAD,\n    DirectiveLocation.INLINE_FRAGMENT,\n  ],\n  args: {\n    if: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      description: 'Skipped when true.',\n    },\n  },\n});\n/**\n * Constant string used for default reason for a deprecation.\n */\n\nexport const DEFAULT_DEPRECATION_REASON = 'No longer supported';\n/**\n * Used to declare element of a GraphQL schema as deprecated.\n */\n\nexport const GraphQLDeprecatedDirective = new GraphQLDirective({\n  name: 'deprecated',\n  description: 'Marks an element of a GraphQL schema as no longer supported.',\n  locations: [\n    DirectiveLocation.FIELD_DEFINITION,\n    DirectiveLocation.ARGUMENT_DEFINITION,\n    DirectiveLocation.INPUT_FIELD_DEFINITION,\n    DirectiveLocation.ENUM_VALUE,\n  ],\n  args: {\n    reason: {\n      type: GraphQLString,\n      description:\n        'Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).',\n      defaultValue: DEFAULT_DEPRECATION_REASON,\n    },\n  },\n});\n/**\n * Used to provide a URL for specifying the behavior of custom scalar definitions.\n */\n\nexport const GraphQLSpecifiedByDirective = new GraphQLDirective({\n  name: 'specifiedBy',\n  description: 'Exposes a URL that specifies the behavior of this scalar.',\n  locations: [DirectiveLocation.SCALAR],\n  args: {\n    url: {\n      type: new GraphQLNonNull(GraphQLString),\n      description: 'The URL that specifies the behavior of this scalar.',\n    },\n  },\n});\n/**\n * The full list of specified directives.\n */\n\nexport const specifiedDirectives = Object.freeze([\n  GraphQLIncludeDirective,\n  GraphQLSkipDirective,\n  GraphQLDeprecatedDirective,\n  GraphQLSpecifiedByDirective,\n]);\nexport function isSpecifiedDirective(directive) {\n  return specifiedDirectives.some(({ name }) => name === directive.name);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SACEC,gBAAgB,EAChBC,eAAe,EACfC,cAAc,QACT,kBAAkB;AACzB,SAASC,cAAc,EAAEC,aAAa,QAAQ,eAAe;AAC7D;AACA;AACA;;AAEA,OAAO,SAASC,WAAWA,CAACC,SAAS,EAAE;EACrC,OAAOX,UAAU,CAACW,SAAS,EAAEC,gBAAgB,CAAC;AAChD;AACA,OAAO,SAASC,eAAeA,CAACF,SAAS,EAAE;EACzC,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,EAAE;IAC3B,MAAM,IAAIG,KAAK,CACZ,YAAWf,OAAO,CAACY,SAAS,CAAE,6BACjC,CAAC;EACH;EAEA,OAAOA,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,CAAC;EAC5BG,WAAWA,CAACC,MAAM,EAAE;IAClB,IAAIC,oBAAoB,EAAEC,YAAY;IAEtC,IAAI,CAACC,IAAI,GAAGf,UAAU,CAACY,MAAM,CAACG,IAAI,CAAC;IACnC,IAAI,CAACC,WAAW,GAAGJ,MAAM,CAACI,WAAW;IACrC,IAAI,CAACC,SAAS,GAAGL,MAAM,CAACK,SAAS;IACjC,IAAI,CAACC,YAAY,GACf,CAACL,oBAAoB,GAAGD,MAAM,CAACM,YAAY,MAAM,IAAI,IACrDL,oBAAoB,KAAK,KAAK,CAAC,GAC3BA,oBAAoB,GACpB,KAAK;IACX,IAAI,CAACM,UAAU,GAAGrB,QAAQ,CAACc,MAAM,CAACO,UAAU,CAAC;IAC7C,IAAI,CAACC,OAAO,GAAGR,MAAM,CAACQ,OAAO;IAC7BC,KAAK,CAACC,OAAO,CAACV,MAAM,CAACK,SAAS,CAAC,IAC7BvB,SAAS,CAAC,KAAK,EAAG,IAAGkB,MAAM,CAACG,IAAK,8BAA6B,CAAC;IACjE,MAAMQ,IAAI,GACR,CAACT,YAAY,GAAGF,MAAM,CAACW,IAAI,MAAM,IAAI,IAAIT,YAAY,KAAK,KAAK,CAAC,GAC5DA,YAAY,GACZ,CAAC,CAAC;IACPjB,YAAY,CAAC0B,IAAI,CAAC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACC,IAAI,CAAC,IACzC7B,SAAS,CACP,KAAK,EACJ,IAAGkB,MAAM,CAACG,IAAK,sDAClB,CAAC;IACH,IAAI,CAACQ,IAAI,GAAGrB,eAAe,CAACqB,IAAI,CAAC;EACnC;EAEA,KAAKC,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,kBAAkB;EAC3B;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAO;MACLX,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBM,IAAI,EAAEtB,gBAAgB,CAAC,IAAI,CAACsB,IAAI,CAAC;MACjCL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,OAAO,EAAE,IAAI,CAACA;IAChB,CAAC;EACH;EAEAO,QAAQA,CAAA,EAAG;IACT,OAAO,GAAG,GAAG,IAAI,CAACZ,IAAI;EACxB;EAEAa,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC;EACxB;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAME,uBAAuB,GAAG,IAAIrB,gBAAgB,CAAC;EAC1DO,IAAI,EAAE,SAAS;EACfC,WAAW,EACT,6FAA6F;EAC/FC,SAAS,EAAE,CACTlB,iBAAiB,CAAC+B,KAAK,EACvB/B,iBAAiB,CAACgC,eAAe,EACjChC,iBAAiB,CAACiC,eAAe,CAClC;EACDT,IAAI,EAAE;IACJU,EAAE,EAAE;MACFC,IAAI,EAAE,IAAI/B,cAAc,CAACC,cAAc,CAAC;MACxCY,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC;AACF;AACA;AACA;;AAEA,OAAO,MAAMmB,oBAAoB,GAAG,IAAI3B,gBAAgB,CAAC;EACvDO,IAAI,EAAE,MAAM;EACZC,WAAW,EACT,qFAAqF;EACvFC,SAAS,EAAE,CACTlB,iBAAiB,CAAC+B,KAAK,EACvB/B,iBAAiB,CAACgC,eAAe,EACjChC,iBAAiB,CAACiC,eAAe,CAClC;EACDT,IAAI,EAAE;IACJU,EAAE,EAAE;MACFC,IAAI,EAAE,IAAI/B,cAAc,CAACC,cAAc,CAAC;MACxCY,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC;AACF;AACA;AACA;;AAEA,OAAO,MAAMoB,0BAA0B,GAAG,qBAAqB;AAC/D;AACA;AACA;;AAEA,OAAO,MAAMC,0BAA0B,GAAG,IAAI7B,gBAAgB,CAAC;EAC7DO,IAAI,EAAE,YAAY;EAClBC,WAAW,EAAE,8DAA8D;EAC3EC,SAAS,EAAE,CACTlB,iBAAiB,CAACuC,gBAAgB,EAClCvC,iBAAiB,CAACwC,mBAAmB,EACrCxC,iBAAiB,CAACyC,sBAAsB,EACxCzC,iBAAiB,CAAC0C,UAAU,CAC7B;EACDlB,IAAI,EAAE;IACJmB,MAAM,EAAE;MACNR,IAAI,EAAE7B,aAAa;MACnBW,WAAW,EACT,qNAAqN;MACvN2B,YAAY,EAAEP;IAChB;EACF;AACF,CAAC,CAAC;AACF;AACA;AACA;;AAEA,OAAO,MAAMQ,2BAA2B,GAAG,IAAIpC,gBAAgB,CAAC;EAC9DO,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,2DAA2D;EACxEC,SAAS,EAAE,CAAClB,iBAAiB,CAAC8C,MAAM,CAAC;EACrCtB,IAAI,EAAE;IACJuB,GAAG,EAAE;MACHZ,IAAI,EAAE,IAAI/B,cAAc,CAACE,aAAa,CAAC;MACvCW,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC;AACF;AACA;AACA;;AAEA,OAAO,MAAM+B,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC,CAC/CpB,uBAAuB,EACvBM,oBAAoB,EACpBE,0BAA0B,EAC1BO,2BAA2B,CAC5B,CAAC;AACF,OAAO,SAASM,oBAAoBA,CAAC3C,SAAS,EAAE;EAC9C,OAAOwC,mBAAmB,CAACI,IAAI,CAAC,CAAC;IAAEpC;EAAK,CAAC,KAAKA,IAAI,KAAKR,SAAS,CAACQ,IAAI,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}