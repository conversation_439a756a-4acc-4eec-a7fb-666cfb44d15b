{"ast": null, "code": "import { invariant } from '../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique input field names\n *\n * A GraphQL input object value is only valid if all supplied fields are\n * uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Input-Object-Field-Uniqueness\n */\nexport function UniqueInputFieldNamesRule(context) {\n  const knownNameStack = [];\n  let knownNames = Object.create(null);\n  return {\n    ObjectValue: {\n      enter() {\n        knownNameStack.push(knownNames);\n        knownNames = Object.create(null);\n      },\n      leave() {\n        const prevKnownNames = knownNameStack.pop();\n        prevKnownNames || invariant(false);\n        knownNames = prevKnownNames;\n      }\n    },\n    ObjectField(node) {\n      const fieldName = node.name.value;\n      if (knownNames[fieldName]) {\n        context.reportError(new GraphQLError(`There can be only one input field named \"${fieldName}\".`, {\n          nodes: [knownNames[fieldName], node.name]\n        }));\n      } else {\n        knownNames[fieldName] = node.name;\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["invariant", "GraphQLError", "UniqueInputFieldNamesRule", "context", "knownNameStack", "knownNames", "Object", "create", "ObjectValue", "enter", "push", "leave", "prevKnownNames", "pop", "ObjectField", "node", "fieldName", "name", "value", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/UniqueInputFieldNamesRule.mjs"], "sourcesContent": ["import { invariant } from '../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique input field names\n *\n * A GraphQL input object value is only valid if all supplied fields are\n * uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Input-Object-Field-Uniqueness\n */\nexport function UniqueInputFieldNamesRule(context) {\n  const knownNameStack = [];\n  let knownNames = Object.create(null);\n  return {\n    ObjectValue: {\n      enter() {\n        knownNameStack.push(knownNames);\n        knownNames = Object.create(null);\n      },\n\n      leave() {\n        const prevKnownNames = knownNameStack.pop();\n        prevKnownNames || invariant(false);\n        knownNames = prevKnownNames;\n      },\n    },\n\n    ObjectField(node) {\n      const fieldName = node.name.value;\n\n      if (knownNames[fieldName]) {\n        context.reportError(\n          new GraphQLError(\n            `There can be only one input field named \"${fieldName}\".`,\n            {\n              nodes: [knownNames[fieldName], node.name],\n            },\n          ),\n        );\n      } else {\n        knownNames[fieldName] = node.name;\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,6BAA6B;AACvD,SAASC,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAACC,OAAO,EAAE;EACjD,MAAMC,cAAc,GAAG,EAAE;EACzB,IAAIC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACpC,OAAO;IACLC,WAAW,EAAE;MACXC,KAAKA,CAAA,EAAG;QACNL,cAAc,CAACM,IAAI,CAACL,UAAU,CAAC;QAC/BA,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAClC,CAAC;MAEDI,KAAKA,CAAA,EAAG;QACN,MAAMC,cAAc,GAAGR,cAAc,CAACS,GAAG,CAAC,CAAC;QAC3CD,cAAc,IAAIZ,SAAS,CAAC,KAAK,CAAC;QAClCK,UAAU,GAAGO,cAAc;MAC7B;IACF,CAAC;IAEDE,WAAWA,CAACC,IAAI,EAAE;MAChB,MAAMC,SAAS,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK;MAEjC,IAAIb,UAAU,CAACW,SAAS,CAAC,EAAE;QACzBb,OAAO,CAACgB,WAAW,CACjB,IAAIlB,YAAY,CACb,4CAA2Ce,SAAU,IAAG,EACzD;UACEI,KAAK,EAAE,CAACf,UAAU,CAACW,SAAS,CAAC,EAAED,IAAI,CAACE,IAAI;QAC1C,CACF,CACF,CAAC;MACH,CAAC,MAAM;QACLZ,UAAU,CAACW,SAAS,CAAC,GAAGD,IAAI,CAACE,IAAI;MACnC;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}