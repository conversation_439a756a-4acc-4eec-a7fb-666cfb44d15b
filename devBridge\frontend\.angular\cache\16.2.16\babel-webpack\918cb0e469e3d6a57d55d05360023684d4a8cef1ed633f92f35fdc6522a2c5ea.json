{"ast": null, "code": "import { getLocation } from './location.mjs';\n\n/**\n * Ren<PERSON> a helpful description of the location in the GraphQL Source document.\n */\nexport function printLocation(location) {\n  return printSourceLocation(location.source, getLocation(location.source, location.start));\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nexport function printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n    return locationStr + printPrefixedLines([[`${lineNum} |`, subLines[0]], ...subLines.slice(1, subLineIndex + 1).map(subLine => ['|', subLine]), ['|', '^'.padStart(subLineColumnNum)], ['|', subLines[subLineIndex + 1]]]);\n  }\n  return locationStr + printPrefixedLines([\n  // Lines specified like this: [\"prefix\", \"string\"],\n  [`${lineNum - 1} |`, lines[lineIndex - 1]], [`${lineNum} |`, locationLine], ['|', '^'.padStart(columnNum)], [`${lineNum + 1} |`, lines[lineIndex + 1]]]);\n}\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines.map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : '')).join('\\n');\n}", "map": {"version": 3, "names": ["getLocation", "printLocation", "location", "printSourceLocation", "source", "start", "sourceLocation", "firstLineColumnOffset", "locationOffset", "column", "body", "padStart", "lineIndex", "line", "lineOffset", "lineNum", "columnOffset", "columnNum", "locationStr", "name", "lines", "split", "locationLine", "length", "subLineIndex", "Math", "floor", "subLineColumnNum", "subLines", "i", "push", "slice", "printPrefixedLines", "map", "subLine", "existingLines", "filter", "_", "undefined", "padLen", "max", "prefix", "join"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/language/printLocation.mjs"], "sourcesContent": ["import { getLocation } from './location.mjs';\n\n/**\n * Ren<PERSON> a helpful description of the location in the GraphQL Source document.\n */\nexport function printLocation(location) {\n  return printSourceLocation(\n    location.source,\n    getLocation(location.source, location.start),\n  );\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nexport function printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n\n    return (\n      locationStr +\n      printPrefixedLines([\n        [`${lineNum} |`, subLines[0]],\n        ...subLines.slice(1, subLineIndex + 1).map((subLine) => ['|', subLine]),\n        ['|', '^'.padStart(subLineColumnNum)],\n        ['|', subLines[subLineIndex + 1]],\n      ])\n    );\n  }\n\n  return (\n    locationStr +\n    printPrefixedLines([\n      // Lines specified like this: [\"prefix\", \"string\"],\n      [`${lineNum - 1} |`, lines[lineIndex - 1]],\n      [`${lineNum} |`, locationLine],\n      ['|', '^'.padStart(columnNum)],\n      [`${lineNum + 1} |`, lines[lineIndex + 1]],\n    ])\n  );\n}\n\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines\n    .map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : ''))\n    .join('\\n');\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;;AAE5C;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAE;EACtC,OAAOC,mBAAmB,CACxBD,QAAQ,CAACE,MAAM,EACfJ,WAAW,CAACE,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,KAAK,CAC7C,CAAC;AACH;AACA;AACA;AACA;;AAEA,OAAO,SAASF,mBAAmBA,CAACC,MAAM,EAAEE,cAAc,EAAE;EAC1D,MAAMC,qBAAqB,GAAGH,MAAM,CAACI,cAAc,CAACC,MAAM,GAAG,CAAC;EAC9D,MAAMC,IAAI,GAAG,EAAE,CAACC,QAAQ,CAACJ,qBAAqB,CAAC,GAAGH,MAAM,CAACM,IAAI;EAC7D,MAAME,SAAS,GAAGN,cAAc,CAACO,IAAI,GAAG,CAAC;EACzC,MAAMC,UAAU,GAAGV,MAAM,CAACI,cAAc,CAACK,IAAI,GAAG,CAAC;EACjD,MAAME,OAAO,GAAGT,cAAc,CAACO,IAAI,GAAGC,UAAU;EAChD,MAAME,YAAY,GAAGV,cAAc,CAACO,IAAI,KAAK,CAAC,GAAGN,qBAAqB,GAAG,CAAC;EAC1E,MAAMU,SAAS,GAAGX,cAAc,CAACG,MAAM,GAAGO,YAAY;EACtD,MAAME,WAAW,GAAI,GAAEd,MAAM,CAACe,IAAK,IAAGJ,OAAQ,IAAGE,SAAU,IAAG;EAC9D,MAAMG,KAAK,GAAGV,IAAI,CAACW,KAAK,CAAC,cAAc,CAAC;EACxC,MAAMC,YAAY,GAAGF,KAAK,CAACR,SAAS,CAAC,CAAC,CAAC;;EAEvC,IAAIU,YAAY,CAACC,MAAM,GAAG,GAAG,EAAE;IAC7B,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACT,SAAS,GAAG,EAAE,CAAC;IAC/C,MAAMU,gBAAgB,GAAGV,SAAS,GAAG,EAAE;IACvC,MAAMW,QAAQ,GAAG,EAAE;IAEnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,YAAY,CAACC,MAAM,EAAEM,CAAC,IAAI,EAAE,EAAE;MAChDD,QAAQ,CAACE,IAAI,CAACR,YAAY,CAACS,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9C;IAEA,OACEX,WAAW,GACXc,kBAAkB,CAAC,CACjB,CAAE,GAAEjB,OAAQ,IAAG,EAAEa,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC7B,GAAGA,QAAQ,CAACG,KAAK,CAAC,CAAC,EAAEP,YAAY,GAAG,CAAC,CAAC,CAACS,GAAG,CAAEC,OAAO,IAAK,CAAC,GAAG,EAAEA,OAAO,CAAC,CAAC,EACvE,CAAC,GAAG,EAAE,GAAG,CAACvB,QAAQ,CAACgB,gBAAgB,CAAC,CAAC,EACrC,CAAC,GAAG,EAAEC,QAAQ,CAACJ,YAAY,GAAG,CAAC,CAAC,CAAC,CAClC,CAAC;EAEN;EAEA,OACEN,WAAW,GACXc,kBAAkB,CAAC;EACjB;EACA,CAAE,GAAEjB,OAAO,GAAG,CAAE,IAAG,EAAEK,KAAK,CAACR,SAAS,GAAG,CAAC,CAAC,CAAC,EAC1C,CAAE,GAAEG,OAAQ,IAAG,EAAEO,YAAY,CAAC,EAC9B,CAAC,GAAG,EAAE,GAAG,CAACX,QAAQ,CAACM,SAAS,CAAC,CAAC,EAC9B,CAAE,GAAEF,OAAO,GAAG,CAAE,IAAG,EAAEK,KAAK,CAACR,SAAS,GAAG,CAAC,CAAC,CAAC,CAC3C,CAAC;AAEN;AAEA,SAASoB,kBAAkBA,CAACZ,KAAK,EAAE;EACjC,MAAMe,aAAa,GAAGf,KAAK,CAACgB,MAAM,CAAC,CAAC,CAACC,CAAC,EAAExB,IAAI,CAAC,KAAKA,IAAI,KAAKyB,SAAS,CAAC;EACrE,MAAMC,MAAM,GAAGd,IAAI,CAACe,GAAG,CAAC,GAAGL,aAAa,CAACF,GAAG,CAAC,CAAC,CAACQ,MAAM,CAAC,KAAKA,MAAM,CAAClB,MAAM,CAAC,CAAC;EAC1E,OAAOY,aAAa,CACjBF,GAAG,CAAC,CAAC,CAACQ,MAAM,EAAE5B,IAAI,CAAC,KAAK4B,MAAM,CAAC9B,QAAQ,CAAC4B,MAAM,CAAC,IAAI1B,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC3E6B,IAAI,CAAC,IAAI,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}