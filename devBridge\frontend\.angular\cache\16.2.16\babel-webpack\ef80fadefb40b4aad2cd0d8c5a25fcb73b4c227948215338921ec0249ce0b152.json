{"ast": null, "code": "import { getOperationName } from \"../../utilities/index.js\";\nexport function transformOperation(operation) {\n  var transformedOperation = {\n    variables: operation.variables || {},\n    extensions: operation.extensions || {},\n    operationName: operation.operationName,\n    query: operation.query\n  };\n  // Best guess at an operation name\n  if (!transformedOperation.operationName) {\n    transformedOperation.operationName = typeof transformedOperation.query !== \"string\" ? getOperationName(transformedOperation.query) || undefined : \"\";\n  }\n  return transformedOperation;\n}", "map": {"version": 3, "names": ["getOperationName", "transformOperation", "operation", "transformedOperation", "variables", "extensions", "operationName", "query", "undefined"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/utils/transformOperation.js"], "sourcesContent": ["import { getOperationName } from \"../../utilities/index.js\";\nexport function transformOperation(operation) {\n    var transformedOperation = {\n        variables: operation.variables || {},\n        extensions: operation.extensions || {},\n        operationName: operation.operationName,\n        query: operation.query,\n    };\n    // Best guess at an operation name\n    if (!transformedOperation.operationName) {\n        transformedOperation.operationName =\n            typeof transformedOperation.query !== \"string\" ?\n                getOperationName(transformedOperation.query) || undefined\n                : \"\";\n    }\n    return transformedOperation;\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAO,SAASC,kBAAkBA,CAACC,SAAS,EAAE;EAC1C,IAAIC,oBAAoB,GAAG;IACvBC,SAAS,EAAEF,SAAS,CAACE,SAAS,IAAI,CAAC,CAAC;IACpCC,UAAU,EAAEH,SAAS,CAACG,UAAU,IAAI,CAAC,CAAC;IACtCC,aAAa,EAAEJ,SAAS,CAACI,aAAa;IACtCC,KAAK,EAAEL,SAAS,CAACK;EACrB,CAAC;EACD;EACA,IAAI,CAACJ,oBAAoB,CAACG,aAAa,EAAE;IACrCH,oBAAoB,CAACG,aAAa,GAC9B,OAAOH,oBAAoB,CAACI,KAAK,KAAK,QAAQ,GAC1CP,gBAAgB,CAACG,oBAAoB,CAACI,KAAK,CAAC,IAAIC,SAAS,GACvD,EAAE;EAChB;EACA,OAAOL,oBAAoB;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}