<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Équipes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Équipes</h1>
        
        <div class="section">
            <h2>🔐 Authentification</h2>
            <input type="text" id="token" placeholder="Collez votre token JWT ici">
            <button class="button" onclick="testAuth()">Tester Token</button>
            <div id="authResult" class="result"></div>
        </div>

        <div class="section">
            <h2>📋 Lister les Équipes</h2>
            <button class="button" onclick="getTeams()">Récupérer Équipes</button>
            <div id="teamsResult" class="result"></div>
        </div>

        <div class="section">
            <h2>➕ Créer une Équipe</h2>
            <input type="text" id="teamName" placeholder="Nom de l'équipe" value="Test Équipe">
            <textarea id="teamDescription" placeholder="Description de l'équipe">Description de test pour l'équipe</textarea>
            <button class="button" onclick="createTeam()">Créer Équipe</button>
            <div id="createResult" class="result"></div>
        </div>

        <div class="section">
            <h2>🔍 Diagnostic</h2>
            <button class="button" onclick="checkLocalStorage()">Vérifier LocalStorage</button>
            <button class="button" onclick="testCORS()">Tester CORS</button>
            <div id="diagnosticResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        function getToken() {
            return document.getElementById('token').value || localStorage.getItem('token');
        }
        
        function getHeaders() {
            const token = getToken();
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
        }
        
        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }
        
        async function testAuth() {
            const token = getToken();
            if (!token) {
                displayResult('authResult', { error: 'Aucun token fourni' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/profile`, {
                    headers: getHeaders()
                });
                
                const data = await response.json();
                displayResult('authResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                displayResult('authResult', { error: error.message }, true);
            }
        }
        
        async function getTeams() {
            try {
                const response = await fetch(`${API_BASE}/teams`, {
                    headers: getHeaders()
                });
                
                const data = await response.json();
                displayResult('teamsResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                displayResult('teamsResult', { error: error.message }, true);
            }
        }
        
        async function createTeam() {
            const name = document.getElementById('teamName').value;
            const description = document.getElementById('teamDescription').value;
            
            if (!name || !description) {
                displayResult('createResult', { error: 'Nom et description requis' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/teams`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        name: name,
                        description: description
                    })
                });
                
                const data = await response.json();
                displayResult('createResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                displayResult('createResult', { error: error.message }, true);
            }
        }
        
        function checkLocalStorage() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            displayResult('diagnosticResult', {
                localStorage: {
                    token: token ? `${token.substring(0, 30)}...` : null,
                    user: user ? JSON.parse(user) : null,
                    allKeys: Object.keys(localStorage)
                }
            });
        }
        
        async function testCORS() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                displayResult('diagnosticResult', {
                    cors: 'OK',
                    health: data
                });
            } catch (error) {
                displayResult('diagnosticResult', {
                    cors: 'ERREUR',
                    error: error.message
                }, true);
            }
        }
        
        // Auto-remplir le token depuis localStorage au chargement
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                document.getElementById('token').value = token;
            }
        };
    </script>
</body>
</html>
