{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { storeKeyNameFromField, argumentsObjectFromField, isReference, getStoreKeyName, isNonNullObject, stringifyForDisplay } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName, storeValueIsStoreObject, selectionSetMatchesResult, TypeOrFieldNameRegExp, defaultDataIdFromObject, isArray } from \"./helpers.js\";\nimport { cacheSlot } from \"./reactiveVars.js\";\nimport { keyArgsFnFromSpecifier, keyFieldsFnFromSpecifier } from \"./key-extractor.js\";\nimport { disableWarningsSlot } from \"../../masking/index.js\";\nfunction argsFromFieldSpecifier(spec) {\n  return spec.args !== void 0 ? spec.args : spec.field ? argumentsObjectFromField(spec.field, spec.variables) : null;\n}\nvar nullKeyFieldsFn = function () {\n  return void 0;\n};\nvar simpleKeyArgsFn = function (_args, context) {\n  return context.fieldName;\n};\n// These merge functions can be selected by specifying merge:true or\n// merge:false in a field policy.\nvar mergeTrueFn = function (existing, incoming, _a) {\n  var mergeObjects = _a.mergeObjects;\n  return mergeObjects(existing, incoming);\n};\nvar mergeFalseFn = function (_, incoming) {\n  return incoming;\n};\nvar Policies = /** @class */function () {\n  function Policies(config) {\n    this.config = config;\n    this.typePolicies = Object.create(null);\n    this.toBeAdded = Object.create(null);\n    // Map from subtype names to sets of supertype names. Note that this\n    // representation inverts the structure of possibleTypes (whose keys are\n    // supertypes and whose values are arrays of subtypes) because it tends\n    // to be much more efficient to search upwards than downwards.\n    this.supertypeMap = new Map();\n    // Any fuzzy subtypes specified by possibleTypes will be converted to\n    // RegExp objects and recorded here. Every key of this map can also be\n    // found in supertypeMap. In many cases this Map will be empty, which\n    // means no fuzzy subtype checking will happen in fragmentMatches.\n    this.fuzzySubtypes = new Map();\n    this.rootIdsByTypename = Object.create(null);\n    this.rootTypenamesById = Object.create(null);\n    this.usingPossibleTypes = false;\n    this.config = __assign({\n      dataIdFromObject: defaultDataIdFromObject\n    }, config);\n    this.cache = this.config.cache;\n    this.setRootTypename(\"Query\");\n    this.setRootTypename(\"Mutation\");\n    this.setRootTypename(\"Subscription\");\n    if (config.possibleTypes) {\n      this.addPossibleTypes(config.possibleTypes);\n    }\n    if (config.typePolicies) {\n      this.addTypePolicies(config.typePolicies);\n    }\n  }\n  Policies.prototype.identify = function (object, partialContext) {\n    var _a;\n    var policies = this;\n    var typename = partialContext && (partialContext.typename || ((_a = partialContext.storeObject) === null || _a === void 0 ? void 0 : _a.__typename)) || object.__typename;\n    // It should be possible to write root Query fields with writeFragment,\n    // using { __typename: \"Query\", ... } as the data, but it does not make\n    // sense to allow the same identification behavior for the Mutation and\n    // Subscription types, since application code should never be writing\n    // directly to (or reading directly from) those root objects.\n    if (typename === this.rootTypenamesById.ROOT_QUERY) {\n      return [\"ROOT_QUERY\"];\n    }\n    // Default context.storeObject to object if not otherwise provided.\n    var storeObject = partialContext && partialContext.storeObject || object;\n    var context = __assign(__assign({}, partialContext), {\n      typename: typename,\n      storeObject: storeObject,\n      readField: partialContext && partialContext.readField || function () {\n        var options = normalizeReadFieldOptions(arguments, storeObject);\n        return policies.readField(options, {\n          store: policies.cache[\"data\"],\n          variables: options.variables\n        });\n      }\n    });\n    var id;\n    var policy = typename && this.getTypePolicy(typename);\n    var keyFn = policy && policy.keyFn || this.config.dataIdFromObject;\n    disableWarningsSlot.withValue(true, function () {\n      while (keyFn) {\n        var specifierOrId = keyFn(__assign(__assign({}, object), storeObject), context);\n        if (isArray(specifierOrId)) {\n          keyFn = keyFieldsFnFromSpecifier(specifierOrId);\n        } else {\n          id = specifierOrId;\n          break;\n        }\n      }\n    });\n    id = id ? String(id) : void 0;\n    return context.keyObject ? [id, context.keyObject] : [id];\n  };\n  Policies.prototype.addTypePolicies = function (typePolicies) {\n    var _this = this;\n    Object.keys(typePolicies).forEach(function (typename) {\n      var _a = typePolicies[typename],\n        queryType = _a.queryType,\n        mutationType = _a.mutationType,\n        subscriptionType = _a.subscriptionType,\n        incoming = __rest(_a, [\"queryType\", \"mutationType\", \"subscriptionType\"]);\n      // Though {query,mutation,subscription}Type configurations are rare,\n      // it's important to call setRootTypename as early as possible,\n      // since these configurations should apply consistently for the\n      // entire lifetime of the cache. Also, since only one __typename can\n      // qualify as one of these root types, these three properties cannot\n      // be inherited, unlike the rest of the incoming properties. That\n      // restriction is convenient, because the purpose of this.toBeAdded\n      // is to delay the processing of type/field policies until the first\n      // time they're used, allowing policies to be added in any order as\n      // long as all relevant policies (including policies for supertypes)\n      // have been added by the time a given policy is used for the first\n      // time. In other words, since inheritance doesn't matter for these\n      // properties, there's also no need to delay their processing using\n      // the this.toBeAdded queue.\n      if (queryType) _this.setRootTypename(\"Query\", typename);\n      if (mutationType) _this.setRootTypename(\"Mutation\", typename);\n      if (subscriptionType) _this.setRootTypename(\"Subscription\", typename);\n      if (hasOwn.call(_this.toBeAdded, typename)) {\n        _this.toBeAdded[typename].push(incoming);\n      } else {\n        _this.toBeAdded[typename] = [incoming];\n      }\n    });\n  };\n  Policies.prototype.updateTypePolicy = function (typename, incoming) {\n    var _this = this;\n    var existing = this.getTypePolicy(typename);\n    var keyFields = incoming.keyFields,\n      fields = incoming.fields;\n    function setMerge(existing, merge) {\n      existing.merge = typeof merge === \"function\" ? merge\n      // Pass merge:true as a shorthand for a merge implementation\n      // that returns options.mergeObjects(existing, incoming).\n      : merge === true ? mergeTrueFn\n      // Pass merge:false to make incoming always replace existing\n      // without any warnings about data clobbering.\n      : merge === false ? mergeFalseFn : existing.merge;\n    }\n    // Type policies can define merge functions, as an alternative to\n    // using field policies to merge child objects.\n    setMerge(existing, incoming.merge);\n    existing.keyFn =\n    // Pass false to disable normalization for this typename.\n    keyFields === false ? nullKeyFieldsFn\n    // Pass an array of strings to use those fields to compute a\n    // composite ID for objects of this typename.\n    : isArray(keyFields) ? keyFieldsFnFromSpecifier(keyFields)\n    // Pass a function to take full control over identification.\n    : typeof keyFields === \"function\" ? keyFields\n    // Leave existing.keyFn unchanged if above cases fail.\n    : existing.keyFn;\n    if (fields) {\n      Object.keys(fields).forEach(function (fieldName) {\n        var existing = _this.getFieldPolicy(typename, fieldName, true);\n        var incoming = fields[fieldName];\n        if (typeof incoming === \"function\") {\n          existing.read = incoming;\n        } else {\n          var keyArgs = incoming.keyArgs,\n            read = incoming.read,\n            merge = incoming.merge;\n          existing.keyFn =\n          // Pass false to disable argument-based differentiation of\n          // field identities.\n          keyArgs === false ? simpleKeyArgsFn\n          // Pass an array of strings to use named arguments to\n          // compute a composite identity for the field.\n          : isArray(keyArgs) ? keyArgsFnFromSpecifier(keyArgs)\n          // Pass a function to take full control over field identity.\n          : typeof keyArgs === \"function\" ? keyArgs\n          // Leave existing.keyFn unchanged if above cases fail.\n          : existing.keyFn;\n          if (typeof read === \"function\") {\n            existing.read = read;\n          }\n          setMerge(existing, merge);\n        }\n        if (existing.read && existing.merge) {\n          // If we have both a read and a merge function, assume\n          // keyArgs:false, because read and merge together can take\n          // responsibility for interpreting arguments in and out. This\n          // default assumption can always be overridden by specifying\n          // keyArgs explicitly in the FieldPolicy.\n          existing.keyFn = existing.keyFn || simpleKeyArgsFn;\n        }\n      });\n    }\n  };\n  Policies.prototype.setRootTypename = function (which, typename) {\n    if (typename === void 0) {\n      typename = which;\n    }\n    var rootId = \"ROOT_\" + which.toUpperCase();\n    var old = this.rootTypenamesById[rootId];\n    if (typename !== old) {\n      invariant(!old || old === which, 6, which);\n      // First, delete any old __typename associated with this rootId from\n      // rootIdsByTypename.\n      if (old) delete this.rootIdsByTypename[old];\n      // Now make this the only __typename that maps to this rootId.\n      this.rootIdsByTypename[typename] = rootId;\n      // Finally, update the __typename associated with this rootId.\n      this.rootTypenamesById[rootId] = typename;\n    }\n  };\n  Policies.prototype.addPossibleTypes = function (possibleTypes) {\n    var _this = this;\n    this.usingPossibleTypes = true;\n    Object.keys(possibleTypes).forEach(function (supertype) {\n      // Make sure all types have an entry in this.supertypeMap, even if\n      // their supertype set is empty, so we can return false immediately\n      // from policies.fragmentMatches for unknown supertypes.\n      _this.getSupertypeSet(supertype, true);\n      possibleTypes[supertype].forEach(function (subtype) {\n        _this.getSupertypeSet(subtype, true).add(supertype);\n        var match = subtype.match(TypeOrFieldNameRegExp);\n        if (!match || match[0] !== subtype) {\n          // TODO Don't interpret just any invalid typename as a RegExp.\n          _this.fuzzySubtypes.set(subtype, new RegExp(subtype));\n        }\n      });\n    });\n  };\n  Policies.prototype.getTypePolicy = function (typename) {\n    var _this = this;\n    if (!hasOwn.call(this.typePolicies, typename)) {\n      var policy_1 = this.typePolicies[typename] = Object.create(null);\n      policy_1.fields = Object.create(null);\n      // When the TypePolicy for typename is first accessed, instead of\n      // starting with an empty policy object, inherit any properties or\n      // fields from the type policies of the supertypes of typename.\n      //\n      // Any properties or fields defined explicitly within the TypePolicy\n      // for typename will take precedence, and if there are multiple\n      // supertypes, the properties of policies whose types were added\n      // later via addPossibleTypes will take precedence over those of\n      // earlier supertypes. TODO Perhaps we should warn about these\n      // conflicts in development, and recommend defining the property\n      // explicitly in the subtype policy?\n      //\n      // Field policy inheritance is atomic/shallow: you can't inherit a\n      // field policy and then override just its read function, since read\n      // and merge functions often need to cooperate, so changing only one\n      // of them would be a recipe for inconsistency.\n      //\n      // Once the TypePolicy for typename has been accessed, its properties can\n      // still be updated directly using addTypePolicies, but future changes to\n      // inherited supertype policies will not be reflected in this subtype\n      // policy, because this code runs at most once per typename.\n      var supertypes_1 = this.supertypeMap.get(typename);\n      if (!supertypes_1 && this.fuzzySubtypes.size) {\n        // To make the inheritance logic work for unknown typename strings that\n        // may have fuzzy supertypes, we give this typename an empty supertype\n        // set and then populate it with any fuzzy supertypes that match.\n        supertypes_1 = this.getSupertypeSet(typename, true);\n        // This only works for typenames that are directly matched by a fuzzy\n        // supertype. What if there is an intermediate chain of supertypes?\n        // While possible, that situation can only be solved effectively by\n        // specifying the intermediate relationships via possibleTypes, manually\n        // and in a non-fuzzy way.\n        this.fuzzySubtypes.forEach(function (regExp, fuzzy) {\n          if (regExp.test(typename)) {\n            // The fuzzy parameter is just the original string version of regExp\n            // (not a valid __typename string), but we can look up the\n            // associated supertype(s) in this.supertypeMap.\n            var fuzzySupertypes = _this.supertypeMap.get(fuzzy);\n            if (fuzzySupertypes) {\n              fuzzySupertypes.forEach(function (supertype) {\n                return supertypes_1.add(supertype);\n              });\n            }\n          }\n        });\n      }\n      if (supertypes_1 && supertypes_1.size) {\n        supertypes_1.forEach(function (supertype) {\n          var _a = _this.getTypePolicy(supertype),\n            fields = _a.fields,\n            rest = __rest(_a, [\"fields\"]);\n          Object.assign(policy_1, rest);\n          Object.assign(policy_1.fields, fields);\n        });\n      }\n    }\n    var inbox = this.toBeAdded[typename];\n    if (inbox && inbox.length) {\n      // Merge the pending policies into this.typePolicies, in the order they\n      // were originally passed to addTypePolicy.\n      inbox.splice(0).forEach(function (policy) {\n        _this.updateTypePolicy(typename, policy);\n      });\n    }\n    return this.typePolicies[typename];\n  };\n  Policies.prototype.getFieldPolicy = function (typename, fieldName, createIfMissing) {\n    if (typename) {\n      var fieldPolicies = this.getTypePolicy(typename).fields;\n      return fieldPolicies[fieldName] || createIfMissing && (fieldPolicies[fieldName] = Object.create(null));\n    }\n  };\n  Policies.prototype.getSupertypeSet = function (subtype, createIfMissing) {\n    var supertypeSet = this.supertypeMap.get(subtype);\n    if (!supertypeSet && createIfMissing) {\n      this.supertypeMap.set(subtype, supertypeSet = new Set());\n    }\n    return supertypeSet;\n  };\n  Policies.prototype.fragmentMatches = function (fragment, typename, result, variables) {\n    var _this = this;\n    if (!fragment.typeCondition) return true;\n    // If the fragment has a type condition but the object we're matching\n    // against does not have a __typename, the fragment cannot match.\n    if (!typename) return false;\n    var supertype = fragment.typeCondition.name.value;\n    // Common case: fragment type condition and __typename are the same.\n    if (typename === supertype) return true;\n    if (this.usingPossibleTypes && this.supertypeMap.has(supertype)) {\n      var typenameSupertypeSet = this.getSupertypeSet(typename, true);\n      var workQueue_1 = [typenameSupertypeSet];\n      var maybeEnqueue_1 = function (subtype) {\n        var supertypeSet = _this.getSupertypeSet(subtype, false);\n        if (supertypeSet && supertypeSet.size && workQueue_1.indexOf(supertypeSet) < 0) {\n          workQueue_1.push(supertypeSet);\n        }\n      };\n      // We need to check fuzzy subtypes only if we encountered fuzzy\n      // subtype strings in addPossibleTypes, and only while writing to\n      // the cache, since that's when selectionSetMatchesResult gives a\n      // strong signal of fragment matching. The StoreReader class calls\n      // policies.fragmentMatches without passing a result object, so\n      // needToCheckFuzzySubtypes is always false while reading.\n      var needToCheckFuzzySubtypes = !!(result && this.fuzzySubtypes.size);\n      var checkingFuzzySubtypes = false;\n      // It's important to keep evaluating workQueue.length each time through\n      // the loop, because the queue can grow while we're iterating over it.\n      for (var i = 0; i < workQueue_1.length; ++i) {\n        var supertypeSet = workQueue_1[i];\n        if (supertypeSet.has(supertype)) {\n          if (!typenameSupertypeSet.has(supertype)) {\n            if (checkingFuzzySubtypes) {\n              globalThis.__DEV__ !== false && invariant.warn(7, typename, supertype);\n            }\n            // Record positive results for faster future lookup.\n            // Unfortunately, we cannot safely cache negative results,\n            // because new possibleTypes data could always be added to the\n            // Policies class.\n            typenameSupertypeSet.add(supertype);\n          }\n          return true;\n        }\n        supertypeSet.forEach(maybeEnqueue_1);\n        if (needToCheckFuzzySubtypes &&\n        // Start checking fuzzy subtypes only after exhausting all\n        // non-fuzzy subtypes (after the final iteration of the loop).\n        i === workQueue_1.length - 1 &&\n        // We could wait to compare fragment.selectionSet to result\n        // after we verify the supertype, but this check is often less\n        // expensive than that search, and we will have to do the\n        // comparison anyway whenever we find a potential match.\n        selectionSetMatchesResult(fragment.selectionSet, result, variables)) {\n          // We don't always need to check fuzzy subtypes (if no result\n          // was provided, or !this.fuzzySubtypes.size), but, when we do,\n          // we only want to check them once.\n          needToCheckFuzzySubtypes = false;\n          checkingFuzzySubtypes = true;\n          // If we find any fuzzy subtypes that match typename, extend the\n          // workQueue to search through the supertypes of those fuzzy\n          // subtypes. Otherwise the for-loop will terminate and we'll\n          // return false below.\n          this.fuzzySubtypes.forEach(function (regExp, fuzzyString) {\n            var match = typename.match(regExp);\n            if (match && match[0] === typename) {\n              maybeEnqueue_1(fuzzyString);\n            }\n          });\n        }\n      }\n    }\n    return false;\n  };\n  Policies.prototype.hasKeyArgs = function (typename, fieldName) {\n    var policy = this.getFieldPolicy(typename, fieldName, false);\n    return !!(policy && policy.keyFn);\n  };\n  Policies.prototype.getStoreFieldName = function (fieldSpec) {\n    var typename = fieldSpec.typename,\n      fieldName = fieldSpec.fieldName;\n    var policy = this.getFieldPolicy(typename, fieldName, false);\n    var storeFieldName;\n    var keyFn = policy && policy.keyFn;\n    if (keyFn && typename) {\n      var context = {\n        typename: typename,\n        fieldName: fieldName,\n        field: fieldSpec.field || null,\n        variables: fieldSpec.variables\n      };\n      var args = argsFromFieldSpecifier(fieldSpec);\n      while (keyFn) {\n        var specifierOrString = keyFn(args, context);\n        if (isArray(specifierOrString)) {\n          keyFn = keyArgsFnFromSpecifier(specifierOrString);\n        } else {\n          // If the custom keyFn returns a falsy value, fall back to\n          // fieldName instead.\n          storeFieldName = specifierOrString || fieldName;\n          break;\n        }\n      }\n    }\n    if (storeFieldName === void 0) {\n      storeFieldName = fieldSpec.field ? storeKeyNameFromField(fieldSpec.field, fieldSpec.variables) : getStoreKeyName(fieldName, argsFromFieldSpecifier(fieldSpec));\n    }\n    // Returning false from a keyArgs function is like configuring\n    // keyArgs: false, but more dynamic.\n    if (storeFieldName === false) {\n      return fieldName;\n    }\n    // Make sure custom field names start with the actual field.name.value\n    // of the field, so we can always figure out which properties of a\n    // StoreObject correspond to which original field names.\n    return fieldName === fieldNameFromStoreName(storeFieldName) ? storeFieldName : fieldName + \":\" + storeFieldName;\n  };\n  Policies.prototype.readField = function (options, context) {\n    var objectOrReference = options.from;\n    if (!objectOrReference) return;\n    var nameOrField = options.field || options.fieldName;\n    if (!nameOrField) return;\n    if (options.typename === void 0) {\n      var typename = context.store.getFieldValue(objectOrReference, \"__typename\");\n      if (typename) options.typename = typename;\n    }\n    var storeFieldName = this.getStoreFieldName(options);\n    var fieldName = fieldNameFromStoreName(storeFieldName);\n    var existing = context.store.getFieldValue(objectOrReference, storeFieldName);\n    var policy = this.getFieldPolicy(options.typename, fieldName, false);\n    var read = policy && policy.read;\n    if (read) {\n      var readOptions = makeFieldFunctionOptions(this, objectOrReference, options, context, context.store.getStorage(isReference(objectOrReference) ? objectOrReference.__ref : objectOrReference, storeFieldName));\n      // Call read(existing, readOptions) with cacheSlot holding this.cache.\n      return cacheSlot.withValue(this.cache, read, [existing, readOptions]);\n    }\n    return existing;\n  };\n  Policies.prototype.getReadFunction = function (typename, fieldName) {\n    var policy = this.getFieldPolicy(typename, fieldName, false);\n    return policy && policy.read;\n  };\n  Policies.prototype.getMergeFunction = function (parentTypename, fieldName, childTypename) {\n    var policy = this.getFieldPolicy(parentTypename, fieldName, false);\n    var merge = policy && policy.merge;\n    if (!merge && childTypename) {\n      policy = this.getTypePolicy(childTypename);\n      merge = policy && policy.merge;\n    }\n    return merge;\n  };\n  Policies.prototype.runMergeFunction = function (existing, incoming, _a, context, storage) {\n    var field = _a.field,\n      typename = _a.typename,\n      merge = _a.merge;\n    if (merge === mergeTrueFn) {\n      // Instead of going to the trouble of creating a full\n      // FieldFunctionOptions object and calling mergeTrueFn, we can\n      // simply call mergeObjects, as mergeTrueFn would.\n      return makeMergeObjectsFunction(context.store)(existing, incoming);\n    }\n    if (merge === mergeFalseFn) {\n      // Likewise for mergeFalseFn, whose implementation is even simpler.\n      return incoming;\n    }\n    // If cache.writeQuery or cache.writeFragment was called with\n    // options.overwrite set to true, we still call merge functions, but\n    // the existing data is always undefined, so the merge function will\n    // not attempt to combine the incoming data with the existing data.\n    if (context.overwrite) {\n      existing = void 0;\n    }\n    return merge(existing, incoming, makeFieldFunctionOptions(this,\n    // Unlike options.readField for read functions, we do not fall\n    // back to the current object if no foreignObjOrRef is provided,\n    // because it's not clear what the current object should be for\n    // merge functions: the (possibly undefined) existing object, or\n    // the incoming object? If you think your merge function needs\n    // to read sibling fields in order to produce a new value for\n    // the current field, you might want to rethink your strategy,\n    // because that's a recipe for making merge behavior sensitive\n    // to the order in which fields are written into the cache.\n    // However, readField(name, ref) is useful for merge functions\n    // that need to deduplicate child objects and references.\n    void 0, {\n      typename: typename,\n      fieldName: field.name.value,\n      field: field,\n      variables: context.variables\n    }, context, storage || Object.create(null)));\n  };\n  return Policies;\n}();\nexport { Policies };\nfunction makeFieldFunctionOptions(policies, objectOrReference, fieldSpec, context, storage) {\n  var storeFieldName = policies.getStoreFieldName(fieldSpec);\n  var fieldName = fieldNameFromStoreName(storeFieldName);\n  var variables = fieldSpec.variables || context.variables;\n  var _a = context.store,\n    toReference = _a.toReference,\n    canRead = _a.canRead;\n  return {\n    args: argsFromFieldSpecifier(fieldSpec),\n    field: fieldSpec.field || null,\n    fieldName: fieldName,\n    storeFieldName: storeFieldName,\n    variables: variables,\n    isReference: isReference,\n    toReference: toReference,\n    storage: storage,\n    cache: policies.cache,\n    canRead: canRead,\n    readField: function () {\n      return policies.readField(normalizeReadFieldOptions(arguments, objectOrReference, variables), context);\n    },\n    mergeObjects: makeMergeObjectsFunction(context.store)\n  };\n}\nexport function normalizeReadFieldOptions(readFieldArgs, objectOrReference, variables) {\n  var fieldNameOrOptions = readFieldArgs[0],\n    from = readFieldArgs[1],\n    argc = readFieldArgs.length;\n  var options;\n  if (typeof fieldNameOrOptions === \"string\") {\n    options = {\n      fieldName: fieldNameOrOptions,\n      // Default to objectOrReference only when no second argument was\n      // passed for the from parameter, not when undefined is explicitly\n      // passed as the second argument.\n      from: argc > 1 ? from : objectOrReference\n    };\n  } else {\n    options = __assign({}, fieldNameOrOptions);\n    // Default to objectOrReference only when fieldNameOrOptions.from is\n    // actually omitted, rather than just undefined.\n    if (!hasOwn.call(options, \"from\")) {\n      options.from = objectOrReference;\n    }\n  }\n  if (globalThis.__DEV__ !== false && options.from === void 0) {\n    globalThis.__DEV__ !== false && invariant.warn(8, stringifyForDisplay(Array.from(readFieldArgs)));\n  }\n  if (void 0 === options.variables) {\n    options.variables = variables;\n  }\n  return options;\n}\nfunction makeMergeObjectsFunction(store) {\n  return function mergeObjects(existing, incoming) {\n    if (isArray(existing) || isArray(incoming)) {\n      throw newInvariantError(9);\n    }\n    // These dynamic checks are necessary because the parameters of a\n    // custom merge function can easily have the any type, so the type\n    // system cannot always enforce the StoreObject | Reference parameter\n    // types of options.mergeObjects.\n    if (isNonNullObject(existing) && isNonNullObject(incoming)) {\n      var eType = store.getFieldValue(existing, \"__typename\");\n      var iType = store.getFieldValue(incoming, \"__typename\");\n      var typesDiffer = eType && iType && eType !== iType;\n      if (typesDiffer) {\n        return incoming;\n      }\n      if (isReference(existing) && storeValueIsStoreObject(incoming)) {\n        // Update the normalized EntityStore for the entity identified by\n        // existing.__ref, preferring/overwriting any fields contributed by the\n        // newer incoming StoreObject.\n        store.merge(existing.__ref, incoming);\n        return existing;\n      }\n      if (storeValueIsStoreObject(existing) && isReference(incoming)) {\n        // Update the normalized EntityStore for the entity identified by\n        // incoming.__ref, taking fields from the older existing object only if\n        // those fields are not already present in the newer StoreObject\n        // identified by incoming.__ref.\n        store.merge(existing, incoming.__ref);\n        return incoming;\n      }\n      if (storeValueIsStoreObject(existing) && storeValueIsStoreObject(incoming)) {\n        return __assign(__assign({}, existing), incoming);\n      }\n    }\n    return incoming;\n  };\n}", "map": {"version": 3, "names": ["__assign", "__rest", "invariant", "newInvariantError", "storeKeyNameFromField", "argumentsObjectFromField", "isReference", "getStoreKeyName", "isNonNullObject", "stringifyForDisplay", "hasOwn", "fieldNameFromStoreName", "storeValueIsStoreObject", "selectionSetMatchesResult", "TypeOrFieldNameRegExp", "defaultDataIdFromObject", "isArray", "cacheSlot", "keyArgsFnFromSpecifier", "keyFieldsFnFromSpecifier", "disableWarningsSlot", "argsFromFieldSpecifier", "spec", "args", "field", "variables", "nullKeyFieldsFn", "simpleKeyArgsFn", "_args", "context", "fieldName", "mergeTrueFn", "existing", "incoming", "_a", "mergeObjects", "mergeFalseFn", "_", "Policies", "config", "typePolicies", "Object", "create", "toBeAdded", "supertypeMap", "Map", "fuzzySubtypes", "rootIdsByTypename", "rootTypenamesById", "usingPossibleTypes", "dataIdFromObject", "cache", "setRootTypename", "possibleTypes", "addPossibleTypes", "addTypePolicies", "prototype", "identify", "object", "partialContext", "policies", "typename", "storeObject", "__typename", "ROOT_QUERY", "readField", "options", "normalizeReadFieldOptions", "arguments", "store", "id", "policy", "getTypePolicy", "keyFn", "with<PERSON><PERSON><PERSON>", "specifierOrId", "String", "keyObject", "_this", "keys", "for<PERSON>ach", "queryType", "mutationType", "subscriptionType", "call", "push", "updateTypePolicy", "keyFields", "fields", "setMerge", "merge", "getFieldPolicy", "read", "keyArgs", "which", "rootId", "toUpperCase", "old", "supertype", "getSupertypeSet", "subtype", "add", "match", "set", "RegExp", "policy_1", "supertypes_1", "get", "size", "regExp", "fuzzy", "test", "fuzzySupertypes", "rest", "assign", "inbox", "length", "splice", "createIfMissing", "fieldPolicies", "supertypeSet", "Set", "fragmentMatches", "fragment", "result", "typeCondition", "name", "value", "has", "typenameSupertypeSet", "workQueue_1", "maybeEnqueue_1", "indexOf", "needToCheckFuzzySubtypes", "checkingFuzzySubtypes", "i", "globalThis", "__DEV__", "warn", "selectionSet", "fuzzyString", "has<PERSON>eyArg<PERSON>", "getStoreFieldName", "fieldSpec", "storeFieldName", "specifierOrString", "objectOrReference", "from", "nameOrField", "getFieldValue", "readOptions", "makeFieldFunctionOptions", "getStorage", "__ref", "getReadFunction", "getMergeFunction", "parentTypename", "childTypename", "runMergeFunction", "storage", "makeMergeObjectsFunction", "overwrite", "toReference", "canRead", "readFieldArgs", "fieldNameOrOptions", "argc", "Array", "eType", "iType", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/policies.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { storeKeyNameFromField, argumentsObjectFromField, isReference, getStoreKeyName, isNonNullObject, stringifyForDisplay, } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName, storeValueIsStoreObject, selectionSetMatchesResult, TypeOrFieldNameRegExp, defaultDataIdFromObject, isArray, } from \"./helpers.js\";\nimport { cacheSlot } from \"./reactiveVars.js\";\nimport { keyArgsFnFromSpecifier, keyFieldsFnFromSpecifier, } from \"./key-extractor.js\";\nimport { disableWarningsSlot } from \"../../masking/index.js\";\nfunction argsFromFieldSpecifier(spec) {\n    return (spec.args !== void 0 ? spec.args\n        : spec.field ? argumentsObjectFromField(spec.field, spec.variables)\n            : null);\n}\nvar nullKeyFieldsFn = function () { return void 0; };\nvar simpleKeyArgsFn = function (_args, context) { return context.fieldName; };\n// These merge functions can be selected by specifying merge:true or\n// merge:false in a field policy.\nvar mergeTrueFn = function (existing, incoming, _a) {\n    var mergeObjects = _a.mergeObjects;\n    return mergeObjects(existing, incoming);\n};\nvar mergeFalseFn = function (_, incoming) { return incoming; };\nvar Policies = /** @class */ (function () {\n    function Policies(config) {\n        this.config = config;\n        this.typePolicies = Object.create(null);\n        this.toBeAdded = Object.create(null);\n        // Map from subtype names to sets of supertype names. Note that this\n        // representation inverts the structure of possibleTypes (whose keys are\n        // supertypes and whose values are arrays of subtypes) because it tends\n        // to be much more efficient to search upwards than downwards.\n        this.supertypeMap = new Map();\n        // Any fuzzy subtypes specified by possibleTypes will be converted to\n        // RegExp objects and recorded here. Every key of this map can also be\n        // found in supertypeMap. In many cases this Map will be empty, which\n        // means no fuzzy subtype checking will happen in fragmentMatches.\n        this.fuzzySubtypes = new Map();\n        this.rootIdsByTypename = Object.create(null);\n        this.rootTypenamesById = Object.create(null);\n        this.usingPossibleTypes = false;\n        this.config = __assign({ dataIdFromObject: defaultDataIdFromObject }, config);\n        this.cache = this.config.cache;\n        this.setRootTypename(\"Query\");\n        this.setRootTypename(\"Mutation\");\n        this.setRootTypename(\"Subscription\");\n        if (config.possibleTypes) {\n            this.addPossibleTypes(config.possibleTypes);\n        }\n        if (config.typePolicies) {\n            this.addTypePolicies(config.typePolicies);\n        }\n    }\n    Policies.prototype.identify = function (object, partialContext) {\n        var _a;\n        var policies = this;\n        var typename = (partialContext &&\n            (partialContext.typename || ((_a = partialContext.storeObject) === null || _a === void 0 ? void 0 : _a.__typename))) ||\n            object.__typename;\n        // It should be possible to write root Query fields with writeFragment,\n        // using { __typename: \"Query\", ... } as the data, but it does not make\n        // sense to allow the same identification behavior for the Mutation and\n        // Subscription types, since application code should never be writing\n        // directly to (or reading directly from) those root objects.\n        if (typename === this.rootTypenamesById.ROOT_QUERY) {\n            return [\"ROOT_QUERY\"];\n        }\n        // Default context.storeObject to object if not otherwise provided.\n        var storeObject = (partialContext && partialContext.storeObject) || object;\n        var context = __assign(__assign({}, partialContext), { typename: typename, storeObject: storeObject, readField: (partialContext && partialContext.readField) ||\n                function () {\n                    var options = normalizeReadFieldOptions(arguments, storeObject);\n                    return policies.readField(options, {\n                        store: policies.cache[\"data\"],\n                        variables: options.variables,\n                    });\n                } });\n        var id;\n        var policy = typename && this.getTypePolicy(typename);\n        var keyFn = (policy && policy.keyFn) || this.config.dataIdFromObject;\n        disableWarningsSlot.withValue(true, function () {\n            while (keyFn) {\n                var specifierOrId = keyFn(__assign(__assign({}, object), storeObject), context);\n                if (isArray(specifierOrId)) {\n                    keyFn = keyFieldsFnFromSpecifier(specifierOrId);\n                }\n                else {\n                    id = specifierOrId;\n                    break;\n                }\n            }\n        });\n        id = id ? String(id) : void 0;\n        return context.keyObject ? [id, context.keyObject] : [id];\n    };\n    Policies.prototype.addTypePolicies = function (typePolicies) {\n        var _this = this;\n        Object.keys(typePolicies).forEach(function (typename) {\n            var _a = typePolicies[typename], queryType = _a.queryType, mutationType = _a.mutationType, subscriptionType = _a.subscriptionType, incoming = __rest(_a, [\"queryType\", \"mutationType\", \"subscriptionType\"]);\n            // Though {query,mutation,subscription}Type configurations are rare,\n            // it's important to call setRootTypename as early as possible,\n            // since these configurations should apply consistently for the\n            // entire lifetime of the cache. Also, since only one __typename can\n            // qualify as one of these root types, these three properties cannot\n            // be inherited, unlike the rest of the incoming properties. That\n            // restriction is convenient, because the purpose of this.toBeAdded\n            // is to delay the processing of type/field policies until the first\n            // time they're used, allowing policies to be added in any order as\n            // long as all relevant policies (including policies for supertypes)\n            // have been added by the time a given policy is used for the first\n            // time. In other words, since inheritance doesn't matter for these\n            // properties, there's also no need to delay their processing using\n            // the this.toBeAdded queue.\n            if (queryType)\n                _this.setRootTypename(\"Query\", typename);\n            if (mutationType)\n                _this.setRootTypename(\"Mutation\", typename);\n            if (subscriptionType)\n                _this.setRootTypename(\"Subscription\", typename);\n            if (hasOwn.call(_this.toBeAdded, typename)) {\n                _this.toBeAdded[typename].push(incoming);\n            }\n            else {\n                _this.toBeAdded[typename] = [incoming];\n            }\n        });\n    };\n    Policies.prototype.updateTypePolicy = function (typename, incoming) {\n        var _this = this;\n        var existing = this.getTypePolicy(typename);\n        var keyFields = incoming.keyFields, fields = incoming.fields;\n        function setMerge(existing, merge) {\n            existing.merge =\n                typeof merge === \"function\" ? merge\n                    // Pass merge:true as a shorthand for a merge implementation\n                    // that returns options.mergeObjects(existing, incoming).\n                    : merge === true ? mergeTrueFn\n                        // Pass merge:false to make incoming always replace existing\n                        // without any warnings about data clobbering.\n                        : merge === false ? mergeFalseFn\n                            : existing.merge;\n        }\n        // Type policies can define merge functions, as an alternative to\n        // using field policies to merge child objects.\n        setMerge(existing, incoming.merge);\n        existing.keyFn =\n            // Pass false to disable normalization for this typename.\n            keyFields === false ? nullKeyFieldsFn\n                // Pass an array of strings to use those fields to compute a\n                // composite ID for objects of this typename.\n                : isArray(keyFields) ? keyFieldsFnFromSpecifier(keyFields)\n                    // Pass a function to take full control over identification.\n                    : typeof keyFields === \"function\" ? keyFields\n                        // Leave existing.keyFn unchanged if above cases fail.\n                        : existing.keyFn;\n        if (fields) {\n            Object.keys(fields).forEach(function (fieldName) {\n                var existing = _this.getFieldPolicy(typename, fieldName, true);\n                var incoming = fields[fieldName];\n                if (typeof incoming === \"function\") {\n                    existing.read = incoming;\n                }\n                else {\n                    var keyArgs = incoming.keyArgs, read = incoming.read, merge = incoming.merge;\n                    existing.keyFn =\n                        // Pass false to disable argument-based differentiation of\n                        // field identities.\n                        keyArgs === false ? simpleKeyArgsFn\n                            // Pass an array of strings to use named arguments to\n                            // compute a composite identity for the field.\n                            : isArray(keyArgs) ? keyArgsFnFromSpecifier(keyArgs)\n                                // Pass a function to take full control over field identity.\n                                : typeof keyArgs === \"function\" ? keyArgs\n                                    // Leave existing.keyFn unchanged if above cases fail.\n                                    : existing.keyFn;\n                    if (typeof read === \"function\") {\n                        existing.read = read;\n                    }\n                    setMerge(existing, merge);\n                }\n                if (existing.read && existing.merge) {\n                    // If we have both a read and a merge function, assume\n                    // keyArgs:false, because read and merge together can take\n                    // responsibility for interpreting arguments in and out. This\n                    // default assumption can always be overridden by specifying\n                    // keyArgs explicitly in the FieldPolicy.\n                    existing.keyFn = existing.keyFn || simpleKeyArgsFn;\n                }\n            });\n        }\n    };\n    Policies.prototype.setRootTypename = function (which, typename) {\n        if (typename === void 0) { typename = which; }\n        var rootId = \"ROOT_\" + which.toUpperCase();\n        var old = this.rootTypenamesById[rootId];\n        if (typename !== old) {\n            invariant(!old || old === which, 6, which);\n            // First, delete any old __typename associated with this rootId from\n            // rootIdsByTypename.\n            if (old)\n                delete this.rootIdsByTypename[old];\n            // Now make this the only __typename that maps to this rootId.\n            this.rootIdsByTypename[typename] = rootId;\n            // Finally, update the __typename associated with this rootId.\n            this.rootTypenamesById[rootId] = typename;\n        }\n    };\n    Policies.prototype.addPossibleTypes = function (possibleTypes) {\n        var _this = this;\n        this.usingPossibleTypes = true;\n        Object.keys(possibleTypes).forEach(function (supertype) {\n            // Make sure all types have an entry in this.supertypeMap, even if\n            // their supertype set is empty, so we can return false immediately\n            // from policies.fragmentMatches for unknown supertypes.\n            _this.getSupertypeSet(supertype, true);\n            possibleTypes[supertype].forEach(function (subtype) {\n                _this.getSupertypeSet(subtype, true).add(supertype);\n                var match = subtype.match(TypeOrFieldNameRegExp);\n                if (!match || match[0] !== subtype) {\n                    // TODO Don't interpret just any invalid typename as a RegExp.\n                    _this.fuzzySubtypes.set(subtype, new RegExp(subtype));\n                }\n            });\n        });\n    };\n    Policies.prototype.getTypePolicy = function (typename) {\n        var _this = this;\n        if (!hasOwn.call(this.typePolicies, typename)) {\n            var policy_1 = (this.typePolicies[typename] = Object.create(null));\n            policy_1.fields = Object.create(null);\n            // When the TypePolicy for typename is first accessed, instead of\n            // starting with an empty policy object, inherit any properties or\n            // fields from the type policies of the supertypes of typename.\n            //\n            // Any properties or fields defined explicitly within the TypePolicy\n            // for typename will take precedence, and if there are multiple\n            // supertypes, the properties of policies whose types were added\n            // later via addPossibleTypes will take precedence over those of\n            // earlier supertypes. TODO Perhaps we should warn about these\n            // conflicts in development, and recommend defining the property\n            // explicitly in the subtype policy?\n            //\n            // Field policy inheritance is atomic/shallow: you can't inherit a\n            // field policy and then override just its read function, since read\n            // and merge functions often need to cooperate, so changing only one\n            // of them would be a recipe for inconsistency.\n            //\n            // Once the TypePolicy for typename has been accessed, its properties can\n            // still be updated directly using addTypePolicies, but future changes to\n            // inherited supertype policies will not be reflected in this subtype\n            // policy, because this code runs at most once per typename.\n            var supertypes_1 = this.supertypeMap.get(typename);\n            if (!supertypes_1 && this.fuzzySubtypes.size) {\n                // To make the inheritance logic work for unknown typename strings that\n                // may have fuzzy supertypes, we give this typename an empty supertype\n                // set and then populate it with any fuzzy supertypes that match.\n                supertypes_1 = this.getSupertypeSet(typename, true);\n                // This only works for typenames that are directly matched by a fuzzy\n                // supertype. What if there is an intermediate chain of supertypes?\n                // While possible, that situation can only be solved effectively by\n                // specifying the intermediate relationships via possibleTypes, manually\n                // and in a non-fuzzy way.\n                this.fuzzySubtypes.forEach(function (regExp, fuzzy) {\n                    if (regExp.test(typename)) {\n                        // The fuzzy parameter is just the original string version of regExp\n                        // (not a valid __typename string), but we can look up the\n                        // associated supertype(s) in this.supertypeMap.\n                        var fuzzySupertypes = _this.supertypeMap.get(fuzzy);\n                        if (fuzzySupertypes) {\n                            fuzzySupertypes.forEach(function (supertype) {\n                                return supertypes_1.add(supertype);\n                            });\n                        }\n                    }\n                });\n            }\n            if (supertypes_1 && supertypes_1.size) {\n                supertypes_1.forEach(function (supertype) {\n                    var _a = _this.getTypePolicy(supertype), fields = _a.fields, rest = __rest(_a, [\"fields\"]);\n                    Object.assign(policy_1, rest);\n                    Object.assign(policy_1.fields, fields);\n                });\n            }\n        }\n        var inbox = this.toBeAdded[typename];\n        if (inbox && inbox.length) {\n            // Merge the pending policies into this.typePolicies, in the order they\n            // were originally passed to addTypePolicy.\n            inbox.splice(0).forEach(function (policy) {\n                _this.updateTypePolicy(typename, policy);\n            });\n        }\n        return this.typePolicies[typename];\n    };\n    Policies.prototype.getFieldPolicy = function (typename, fieldName, createIfMissing) {\n        if (typename) {\n            var fieldPolicies = this.getTypePolicy(typename).fields;\n            return (fieldPolicies[fieldName] ||\n                (createIfMissing && (fieldPolicies[fieldName] = Object.create(null))));\n        }\n    };\n    Policies.prototype.getSupertypeSet = function (subtype, createIfMissing) {\n        var supertypeSet = this.supertypeMap.get(subtype);\n        if (!supertypeSet && createIfMissing) {\n            this.supertypeMap.set(subtype, (supertypeSet = new Set()));\n        }\n        return supertypeSet;\n    };\n    Policies.prototype.fragmentMatches = function (fragment, typename, result, variables) {\n        var _this = this;\n        if (!fragment.typeCondition)\n            return true;\n        // If the fragment has a type condition but the object we're matching\n        // against does not have a __typename, the fragment cannot match.\n        if (!typename)\n            return false;\n        var supertype = fragment.typeCondition.name.value;\n        // Common case: fragment type condition and __typename are the same.\n        if (typename === supertype)\n            return true;\n        if (this.usingPossibleTypes && this.supertypeMap.has(supertype)) {\n            var typenameSupertypeSet = this.getSupertypeSet(typename, true);\n            var workQueue_1 = [typenameSupertypeSet];\n            var maybeEnqueue_1 = function (subtype) {\n                var supertypeSet = _this.getSupertypeSet(subtype, false);\n                if (supertypeSet &&\n                    supertypeSet.size &&\n                    workQueue_1.indexOf(supertypeSet) < 0) {\n                    workQueue_1.push(supertypeSet);\n                }\n            };\n            // We need to check fuzzy subtypes only if we encountered fuzzy\n            // subtype strings in addPossibleTypes, and only while writing to\n            // the cache, since that's when selectionSetMatchesResult gives a\n            // strong signal of fragment matching. The StoreReader class calls\n            // policies.fragmentMatches without passing a result object, so\n            // needToCheckFuzzySubtypes is always false while reading.\n            var needToCheckFuzzySubtypes = !!(result && this.fuzzySubtypes.size);\n            var checkingFuzzySubtypes = false;\n            // It's important to keep evaluating workQueue.length each time through\n            // the loop, because the queue can grow while we're iterating over it.\n            for (var i = 0; i < workQueue_1.length; ++i) {\n                var supertypeSet = workQueue_1[i];\n                if (supertypeSet.has(supertype)) {\n                    if (!typenameSupertypeSet.has(supertype)) {\n                        if (checkingFuzzySubtypes) {\n                            globalThis.__DEV__ !== false && invariant.warn(7, typename, supertype);\n                        }\n                        // Record positive results for faster future lookup.\n                        // Unfortunately, we cannot safely cache negative results,\n                        // because new possibleTypes data could always be added to the\n                        // Policies class.\n                        typenameSupertypeSet.add(supertype);\n                    }\n                    return true;\n                }\n                supertypeSet.forEach(maybeEnqueue_1);\n                if (needToCheckFuzzySubtypes &&\n                    // Start checking fuzzy subtypes only after exhausting all\n                    // non-fuzzy subtypes (after the final iteration of the loop).\n                    i === workQueue_1.length - 1 &&\n                    // We could wait to compare fragment.selectionSet to result\n                    // after we verify the supertype, but this check is often less\n                    // expensive than that search, and we will have to do the\n                    // comparison anyway whenever we find a potential match.\n                    selectionSetMatchesResult(fragment.selectionSet, result, variables)) {\n                    // We don't always need to check fuzzy subtypes (if no result\n                    // was provided, or !this.fuzzySubtypes.size), but, when we do,\n                    // we only want to check them once.\n                    needToCheckFuzzySubtypes = false;\n                    checkingFuzzySubtypes = true;\n                    // If we find any fuzzy subtypes that match typename, extend the\n                    // workQueue to search through the supertypes of those fuzzy\n                    // subtypes. Otherwise the for-loop will terminate and we'll\n                    // return false below.\n                    this.fuzzySubtypes.forEach(function (regExp, fuzzyString) {\n                        var match = typename.match(regExp);\n                        if (match && match[0] === typename) {\n                            maybeEnqueue_1(fuzzyString);\n                        }\n                    });\n                }\n            }\n        }\n        return false;\n    };\n    Policies.prototype.hasKeyArgs = function (typename, fieldName) {\n        var policy = this.getFieldPolicy(typename, fieldName, false);\n        return !!(policy && policy.keyFn);\n    };\n    Policies.prototype.getStoreFieldName = function (fieldSpec) {\n        var typename = fieldSpec.typename, fieldName = fieldSpec.fieldName;\n        var policy = this.getFieldPolicy(typename, fieldName, false);\n        var storeFieldName;\n        var keyFn = policy && policy.keyFn;\n        if (keyFn && typename) {\n            var context = {\n                typename: typename,\n                fieldName: fieldName,\n                field: fieldSpec.field || null,\n                variables: fieldSpec.variables,\n            };\n            var args = argsFromFieldSpecifier(fieldSpec);\n            while (keyFn) {\n                var specifierOrString = keyFn(args, context);\n                if (isArray(specifierOrString)) {\n                    keyFn = keyArgsFnFromSpecifier(specifierOrString);\n                }\n                else {\n                    // If the custom keyFn returns a falsy value, fall back to\n                    // fieldName instead.\n                    storeFieldName = specifierOrString || fieldName;\n                    break;\n                }\n            }\n        }\n        if (storeFieldName === void 0) {\n            storeFieldName =\n                fieldSpec.field ?\n                    storeKeyNameFromField(fieldSpec.field, fieldSpec.variables)\n                    : getStoreKeyName(fieldName, argsFromFieldSpecifier(fieldSpec));\n        }\n        // Returning false from a keyArgs function is like configuring\n        // keyArgs: false, but more dynamic.\n        if (storeFieldName === false) {\n            return fieldName;\n        }\n        // Make sure custom field names start with the actual field.name.value\n        // of the field, so we can always figure out which properties of a\n        // StoreObject correspond to which original field names.\n        return fieldName === fieldNameFromStoreName(storeFieldName) ? storeFieldName\n            : fieldName + \":\" + storeFieldName;\n    };\n    Policies.prototype.readField = function (options, context) {\n        var objectOrReference = options.from;\n        if (!objectOrReference)\n            return;\n        var nameOrField = options.field || options.fieldName;\n        if (!nameOrField)\n            return;\n        if (options.typename === void 0) {\n            var typename = context.store.getFieldValue(objectOrReference, \"__typename\");\n            if (typename)\n                options.typename = typename;\n        }\n        var storeFieldName = this.getStoreFieldName(options);\n        var fieldName = fieldNameFromStoreName(storeFieldName);\n        var existing = context.store.getFieldValue(objectOrReference, storeFieldName);\n        var policy = this.getFieldPolicy(options.typename, fieldName, false);\n        var read = policy && policy.read;\n        if (read) {\n            var readOptions = makeFieldFunctionOptions(this, objectOrReference, options, context, context.store.getStorage(isReference(objectOrReference) ?\n                objectOrReference.__ref\n                : objectOrReference, storeFieldName));\n            // Call read(existing, readOptions) with cacheSlot holding this.cache.\n            return cacheSlot.withValue(this.cache, read, [\n                existing,\n                readOptions,\n            ]);\n        }\n        return existing;\n    };\n    Policies.prototype.getReadFunction = function (typename, fieldName) {\n        var policy = this.getFieldPolicy(typename, fieldName, false);\n        return policy && policy.read;\n    };\n    Policies.prototype.getMergeFunction = function (parentTypename, fieldName, childTypename) {\n        var policy = this.getFieldPolicy(parentTypename, fieldName, false);\n        var merge = policy && policy.merge;\n        if (!merge && childTypename) {\n            policy = this.getTypePolicy(childTypename);\n            merge = policy && policy.merge;\n        }\n        return merge;\n    };\n    Policies.prototype.runMergeFunction = function (existing, incoming, _a, context, storage) {\n        var field = _a.field, typename = _a.typename, merge = _a.merge;\n        if (merge === mergeTrueFn) {\n            // Instead of going to the trouble of creating a full\n            // FieldFunctionOptions object and calling mergeTrueFn, we can\n            // simply call mergeObjects, as mergeTrueFn would.\n            return makeMergeObjectsFunction(context.store)(existing, incoming);\n        }\n        if (merge === mergeFalseFn) {\n            // Likewise for mergeFalseFn, whose implementation is even simpler.\n            return incoming;\n        }\n        // If cache.writeQuery or cache.writeFragment was called with\n        // options.overwrite set to true, we still call merge functions, but\n        // the existing data is always undefined, so the merge function will\n        // not attempt to combine the incoming data with the existing data.\n        if (context.overwrite) {\n            existing = void 0;\n        }\n        return merge(existing, incoming, makeFieldFunctionOptions(this, \n        // Unlike options.readField for read functions, we do not fall\n        // back to the current object if no foreignObjOrRef is provided,\n        // because it's not clear what the current object should be for\n        // merge functions: the (possibly undefined) existing object, or\n        // the incoming object? If you think your merge function needs\n        // to read sibling fields in order to produce a new value for\n        // the current field, you might want to rethink your strategy,\n        // because that's a recipe for making merge behavior sensitive\n        // to the order in which fields are written into the cache.\n        // However, readField(name, ref) is useful for merge functions\n        // that need to deduplicate child objects and references.\n        void 0, {\n            typename: typename,\n            fieldName: field.name.value,\n            field: field,\n            variables: context.variables,\n        }, context, storage || Object.create(null)));\n    };\n    return Policies;\n}());\nexport { Policies };\nfunction makeFieldFunctionOptions(policies, objectOrReference, fieldSpec, context, storage) {\n    var storeFieldName = policies.getStoreFieldName(fieldSpec);\n    var fieldName = fieldNameFromStoreName(storeFieldName);\n    var variables = fieldSpec.variables || context.variables;\n    var _a = context.store, toReference = _a.toReference, canRead = _a.canRead;\n    return {\n        args: argsFromFieldSpecifier(fieldSpec),\n        field: fieldSpec.field || null,\n        fieldName: fieldName,\n        storeFieldName: storeFieldName,\n        variables: variables,\n        isReference: isReference,\n        toReference: toReference,\n        storage: storage,\n        cache: policies.cache,\n        canRead: canRead,\n        readField: function () {\n            return policies.readField(normalizeReadFieldOptions(arguments, objectOrReference, variables), context);\n        },\n        mergeObjects: makeMergeObjectsFunction(context.store),\n    };\n}\nexport function normalizeReadFieldOptions(readFieldArgs, objectOrReference, variables) {\n    var fieldNameOrOptions = readFieldArgs[0], from = readFieldArgs[1], argc = readFieldArgs.length;\n    var options;\n    if (typeof fieldNameOrOptions === \"string\") {\n        options = {\n            fieldName: fieldNameOrOptions,\n            // Default to objectOrReference only when no second argument was\n            // passed for the from parameter, not when undefined is explicitly\n            // passed as the second argument.\n            from: argc > 1 ? from : objectOrReference,\n        };\n    }\n    else {\n        options = __assign({}, fieldNameOrOptions);\n        // Default to objectOrReference only when fieldNameOrOptions.from is\n        // actually omitted, rather than just undefined.\n        if (!hasOwn.call(options, \"from\")) {\n            options.from = objectOrReference;\n        }\n    }\n    if (globalThis.__DEV__ !== false && options.from === void 0) {\n        globalThis.__DEV__ !== false && invariant.warn(8, stringifyForDisplay(Array.from(readFieldArgs)));\n    }\n    if (void 0 === options.variables) {\n        options.variables = variables;\n    }\n    return options;\n}\nfunction makeMergeObjectsFunction(store) {\n    return function mergeObjects(existing, incoming) {\n        if (isArray(existing) || isArray(incoming)) {\n            throw newInvariantError(9);\n        }\n        // These dynamic checks are necessary because the parameters of a\n        // custom merge function can easily have the any type, so the type\n        // system cannot always enforce the StoreObject | Reference parameter\n        // types of options.mergeObjects.\n        if (isNonNullObject(existing) && isNonNullObject(incoming)) {\n            var eType = store.getFieldValue(existing, \"__typename\");\n            var iType = store.getFieldValue(incoming, \"__typename\");\n            var typesDiffer = eType && iType && eType !== iType;\n            if (typesDiffer) {\n                return incoming;\n            }\n            if (isReference(existing) && storeValueIsStoreObject(incoming)) {\n                // Update the normalized EntityStore for the entity identified by\n                // existing.__ref, preferring/overwriting any fields contributed by the\n                // newer incoming StoreObject.\n                store.merge(existing.__ref, incoming);\n                return existing;\n            }\n            if (storeValueIsStoreObject(existing) && isReference(incoming)) {\n                // Update the normalized EntityStore for the entity identified by\n                // incoming.__ref, taking fields from the older existing object only if\n                // those fields are not already present in the newer StoreObject\n                // identified by incoming.__ref.\n                store.merge(existing, incoming.__ref);\n                return incoming;\n            }\n            if (storeValueIsStoreObject(existing) &&\n                storeValueIsStoreObject(incoming)) {\n                return __assign(__assign({}, existing), incoming);\n            }\n        }\n        return incoming;\n    };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,kCAAkC;AAC/E,SAASC,qBAAqB,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,eAAe,EAAEC,mBAAmB,QAAS,0BAA0B;AAC/J,SAASC,MAAM,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,yBAAyB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,OAAO,QAAS,cAAc;AAC3K,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,sBAAsB,EAAEC,wBAAwB,QAAS,oBAAoB;AACtF,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAClC,OAAQA,IAAI,CAACC,IAAI,KAAK,KAAK,CAAC,GAAGD,IAAI,CAACC,IAAI,GAClCD,IAAI,CAACE,KAAK,GAAGnB,wBAAwB,CAACiB,IAAI,CAACE,KAAK,EAAEF,IAAI,CAACG,SAAS,CAAC,GAC7D,IAAI;AAClB;AACA,IAAIC,eAAe,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,KAAK,CAAC;AAAE,CAAC;AACpD,IAAIC,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAE;EAAE,OAAOA,OAAO,CAACC,SAAS;AAAE,CAAC;AAC7E;AACA;AACA,IAAIC,WAAW,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EAChD,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAY;EAClC,OAAOA,YAAY,CAACH,QAAQ,EAAEC,QAAQ,CAAC;AAC3C,CAAC;AACD,IAAIG,YAAY,GAAG,SAAAA,CAAUC,CAAC,EAAEJ,QAAQ,EAAE;EAAE,OAAOA,QAAQ;AAAE,CAAC;AAC9D,IAAIK,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,MAAM,EAAE;IACtB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACvC,IAAI,CAACC,SAAS,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACpC;IACA;IACA;IACA;IACA,IAAI,CAACE,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B;IACA;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,IAAID,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACE,iBAAiB,GAAGN,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACM,iBAAiB,GAAGP,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACO,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACV,MAAM,GAAGvC,QAAQ,CAAC;MAAEkD,gBAAgB,EAAEnC;IAAwB,CAAC,EAAEwB,MAAM,CAAC;IAC7E,IAAI,CAACY,KAAK,GAAG,IAAI,CAACZ,MAAM,CAACY,KAAK;IAC9B,IAAI,CAACC,eAAe,CAAC,OAAO,CAAC;IAC7B,IAAI,CAACA,eAAe,CAAC,UAAU,CAAC;IAChC,IAAI,CAACA,eAAe,CAAC,cAAc,CAAC;IACpC,IAAIb,MAAM,CAACc,aAAa,EAAE;MACtB,IAAI,CAACC,gBAAgB,CAACf,MAAM,CAACc,aAAa,CAAC;IAC/C;IACA,IAAId,MAAM,CAACC,YAAY,EAAE;MACrB,IAAI,CAACe,eAAe,CAAChB,MAAM,CAACC,YAAY,CAAC;IAC7C;EACJ;EACAF,QAAQ,CAACkB,SAAS,CAACC,QAAQ,GAAG,UAAUC,MAAM,EAAEC,cAAc,EAAE;IAC5D,IAAIzB,EAAE;IACN,IAAI0B,QAAQ,GAAG,IAAI;IACnB,IAAIC,QAAQ,GAAIF,cAAc,KACzBA,cAAc,CAACE,QAAQ,KAAK,CAAC3B,EAAE,GAAGyB,cAAc,CAACG,WAAW,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,UAAU,CAAC,CAAC,IACnHL,MAAM,CAACK,UAAU;IACrB;IACA;IACA;IACA;IACA;IACA,IAAIF,QAAQ,KAAK,IAAI,CAACb,iBAAiB,CAACgB,UAAU,EAAE;MAChD,OAAO,CAAC,YAAY,CAAC;IACzB;IACA;IACA,IAAIF,WAAW,GAAIH,cAAc,IAAIA,cAAc,CAACG,WAAW,IAAKJ,MAAM;IAC1E,IAAI7B,OAAO,GAAG7B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2D,cAAc,CAAC,EAAE;MAAEE,QAAQ,EAAEA,QAAQ;MAAEC,WAAW,EAAEA,WAAW;MAAEG,SAAS,EAAGN,cAAc,IAAIA,cAAc,CAACM,SAAS,IACnJ,YAAY;QACR,IAAIC,OAAO,GAAGC,yBAAyB,CAACC,SAAS,EAAEN,WAAW,CAAC;QAC/D,OAAOF,QAAQ,CAACK,SAAS,CAACC,OAAO,EAAE;UAC/BG,KAAK,EAAET,QAAQ,CAACT,KAAK,CAAC,MAAM,CAAC;UAC7B1B,SAAS,EAAEyC,OAAO,CAACzC;QACvB,CAAC,CAAC;MACN;IAAE,CAAC,CAAC;IACZ,IAAI6C,EAAE;IACN,IAAIC,MAAM,GAAGV,QAAQ,IAAI,IAAI,CAACW,aAAa,CAACX,QAAQ,CAAC;IACrD,IAAIY,KAAK,GAAIF,MAAM,IAAIA,MAAM,CAACE,KAAK,IAAK,IAAI,CAAClC,MAAM,CAACW,gBAAgB;IACpE9B,mBAAmB,CAACsD,SAAS,CAAC,IAAI,EAAE,YAAY;MAC5C,OAAOD,KAAK,EAAE;QACV,IAAIE,aAAa,GAAGF,KAAK,CAACzE,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0D,MAAM,CAAC,EAAEI,WAAW,CAAC,EAAEjC,OAAO,CAAC;QAC/E,IAAIb,OAAO,CAAC2D,aAAa,CAAC,EAAE;UACxBF,KAAK,GAAGtD,wBAAwB,CAACwD,aAAa,CAAC;QACnD,CAAC,MACI;UACDL,EAAE,GAAGK,aAAa;UAClB;QACJ;MACJ;IACJ,CAAC,CAAC;IACFL,EAAE,GAAGA,EAAE,GAAGM,MAAM,CAACN,EAAE,CAAC,GAAG,KAAK,CAAC;IAC7B,OAAOzC,OAAO,CAACgD,SAAS,GAAG,CAACP,EAAE,EAAEzC,OAAO,CAACgD,SAAS,CAAC,GAAG,CAACP,EAAE,CAAC;EAC7D,CAAC;EACDhC,QAAQ,CAACkB,SAAS,CAACD,eAAe,GAAG,UAAUf,YAAY,EAAE;IACzD,IAAIsC,KAAK,GAAG,IAAI;IAChBrC,MAAM,CAACsC,IAAI,CAACvC,YAAY,CAAC,CAACwC,OAAO,CAAC,UAAUnB,QAAQ,EAAE;MAClD,IAAI3B,EAAE,GAAGM,YAAY,CAACqB,QAAQ,CAAC;QAAEoB,SAAS,GAAG/C,EAAE,CAAC+C,SAAS;QAAEC,YAAY,GAAGhD,EAAE,CAACgD,YAAY;QAAEC,gBAAgB,GAAGjD,EAAE,CAACiD,gBAAgB;QAAElD,QAAQ,GAAGhC,MAAM,CAACiC,EAAE,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;MAC3M;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI+C,SAAS,EACTH,KAAK,CAAC1B,eAAe,CAAC,OAAO,EAAES,QAAQ,CAAC;MAC5C,IAAIqB,YAAY,EACZJ,KAAK,CAAC1B,eAAe,CAAC,UAAU,EAAES,QAAQ,CAAC;MAC/C,IAAIsB,gBAAgB,EAChBL,KAAK,CAAC1B,eAAe,CAAC,cAAc,EAAES,QAAQ,CAAC;MACnD,IAAInD,MAAM,CAAC0E,IAAI,CAACN,KAAK,CAACnC,SAAS,EAAEkB,QAAQ,CAAC,EAAE;QACxCiB,KAAK,CAACnC,SAAS,CAACkB,QAAQ,CAAC,CAACwB,IAAI,CAACpD,QAAQ,CAAC;MAC5C,CAAC,MACI;QACD6C,KAAK,CAACnC,SAAS,CAACkB,QAAQ,CAAC,GAAG,CAAC5B,QAAQ,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN,CAAC;EACDK,QAAQ,CAACkB,SAAS,CAAC8B,gBAAgB,GAAG,UAAUzB,QAAQ,EAAE5B,QAAQ,EAAE;IAChE,IAAI6C,KAAK,GAAG,IAAI;IAChB,IAAI9C,QAAQ,GAAG,IAAI,CAACwC,aAAa,CAACX,QAAQ,CAAC;IAC3C,IAAI0B,SAAS,GAAGtD,QAAQ,CAACsD,SAAS;MAAEC,MAAM,GAAGvD,QAAQ,CAACuD,MAAM;IAC5D,SAASC,QAAQA,CAACzD,QAAQ,EAAE0D,KAAK,EAAE;MAC/B1D,QAAQ,CAAC0D,KAAK,GACV,OAAOA,KAAK,KAAK,UAAU,GAAGA;MAC1B;MACA;MAAA,EACEA,KAAK,KAAK,IAAI,GAAG3D;MACf;MACA;MAAA,EACE2D,KAAK,KAAK,KAAK,GAAGtD,YAAY,GAC1BJ,QAAQ,CAAC0D,KAAK;IACpC;IACA;IACA;IACAD,QAAQ,CAACzD,QAAQ,EAAEC,QAAQ,CAACyD,KAAK,CAAC;IAClC1D,QAAQ,CAACyC,KAAK;IACV;IACAc,SAAS,KAAK,KAAK,GAAG7D;IAClB;IACA;IAAA,EACEV,OAAO,CAACuE,SAAS,CAAC,GAAGpE,wBAAwB,CAACoE,SAAS;IACrD;IAAA,EACE,OAAOA,SAAS,KAAK,UAAU,GAAGA;IAChC;IAAA,EACEvD,QAAQ,CAACyC,KAAK;IAChC,IAAIe,MAAM,EAAE;MACR/C,MAAM,CAACsC,IAAI,CAACS,MAAM,CAAC,CAACR,OAAO,CAAC,UAAUlD,SAAS,EAAE;QAC7C,IAAIE,QAAQ,GAAG8C,KAAK,CAACa,cAAc,CAAC9B,QAAQ,EAAE/B,SAAS,EAAE,IAAI,CAAC;QAC9D,IAAIG,QAAQ,GAAGuD,MAAM,CAAC1D,SAAS,CAAC;QAChC,IAAI,OAAOG,QAAQ,KAAK,UAAU,EAAE;UAChCD,QAAQ,CAAC4D,IAAI,GAAG3D,QAAQ;QAC5B,CAAC,MACI;UACD,IAAI4D,OAAO,GAAG5D,QAAQ,CAAC4D,OAAO;YAAED,IAAI,GAAG3D,QAAQ,CAAC2D,IAAI;YAAEF,KAAK,GAAGzD,QAAQ,CAACyD,KAAK;UAC5E1D,QAAQ,CAACyC,KAAK;UACV;UACA;UACAoB,OAAO,KAAK,KAAK,GAAGlE;UAChB;UACA;UAAA,EACEX,OAAO,CAAC6E,OAAO,CAAC,GAAG3E,sBAAsB,CAAC2E,OAAO;UAC/C;UAAA,EACE,OAAOA,OAAO,KAAK,UAAU,GAAGA;UAC9B;UAAA,EACE7D,QAAQ,CAACyC,KAAK;UAChC,IAAI,OAAOmB,IAAI,KAAK,UAAU,EAAE;YAC5B5D,QAAQ,CAAC4D,IAAI,GAAGA,IAAI;UACxB;UACAH,QAAQ,CAACzD,QAAQ,EAAE0D,KAAK,CAAC;QAC7B;QACA,IAAI1D,QAAQ,CAAC4D,IAAI,IAAI5D,QAAQ,CAAC0D,KAAK,EAAE;UACjC;UACA;UACA;UACA;UACA;UACA1D,QAAQ,CAACyC,KAAK,GAAGzC,QAAQ,CAACyC,KAAK,IAAI9C,eAAe;QACtD;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;EACDW,QAAQ,CAACkB,SAAS,CAACJ,eAAe,GAAG,UAAU0C,KAAK,EAAEjC,QAAQ,EAAE;IAC5D,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAGiC,KAAK;IAAE;IAC7C,IAAIC,MAAM,GAAG,OAAO,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;IAC1C,IAAIC,GAAG,GAAG,IAAI,CAACjD,iBAAiB,CAAC+C,MAAM,CAAC;IACxC,IAAIlC,QAAQ,KAAKoC,GAAG,EAAE;MAClB/F,SAAS,CAAC,CAAC+F,GAAG,IAAIA,GAAG,KAAKH,KAAK,EAAE,CAAC,EAAEA,KAAK,CAAC;MAC1C;MACA;MACA,IAAIG,GAAG,EACH,OAAO,IAAI,CAAClD,iBAAiB,CAACkD,GAAG,CAAC;MACtC;MACA,IAAI,CAAClD,iBAAiB,CAACc,QAAQ,CAAC,GAAGkC,MAAM;MACzC;MACA,IAAI,CAAC/C,iBAAiB,CAAC+C,MAAM,CAAC,GAAGlC,QAAQ;IAC7C;EACJ,CAAC;EACDvB,QAAQ,CAACkB,SAAS,CAACF,gBAAgB,GAAG,UAAUD,aAAa,EAAE;IAC3D,IAAIyB,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC7B,kBAAkB,GAAG,IAAI;IAC9BR,MAAM,CAACsC,IAAI,CAAC1B,aAAa,CAAC,CAAC2B,OAAO,CAAC,UAAUkB,SAAS,EAAE;MACpD;MACA;MACA;MACApB,KAAK,CAACqB,eAAe,CAACD,SAAS,EAAE,IAAI,CAAC;MACtC7C,aAAa,CAAC6C,SAAS,CAAC,CAAClB,OAAO,CAAC,UAAUoB,OAAO,EAAE;QAChDtB,KAAK,CAACqB,eAAe,CAACC,OAAO,EAAE,IAAI,CAAC,CAACC,GAAG,CAACH,SAAS,CAAC;QACnD,IAAII,KAAK,GAAGF,OAAO,CAACE,KAAK,CAACxF,qBAAqB,CAAC;QAChD,IAAI,CAACwF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKF,OAAO,EAAE;UAChC;UACAtB,KAAK,CAAChC,aAAa,CAACyD,GAAG,CAACH,OAAO,EAAE,IAAII,MAAM,CAACJ,OAAO,CAAC,CAAC;QACzD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD9D,QAAQ,CAACkB,SAAS,CAACgB,aAAa,GAAG,UAAUX,QAAQ,EAAE;IACnD,IAAIiB,KAAK,GAAG,IAAI;IAChB,IAAI,CAACpE,MAAM,CAAC0E,IAAI,CAAC,IAAI,CAAC5C,YAAY,EAAEqB,QAAQ,CAAC,EAAE;MAC3C,IAAI4C,QAAQ,GAAI,IAAI,CAACjE,YAAY,CAACqB,QAAQ,CAAC,GAAGpB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAE;MAClE+D,QAAQ,CAACjB,MAAM,GAAG/C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIgE,YAAY,GAAG,IAAI,CAAC9D,YAAY,CAAC+D,GAAG,CAAC9C,QAAQ,CAAC;MAClD,IAAI,CAAC6C,YAAY,IAAI,IAAI,CAAC5D,aAAa,CAAC8D,IAAI,EAAE;QAC1C;QACA;QACA;QACAF,YAAY,GAAG,IAAI,CAACP,eAAe,CAACtC,QAAQ,EAAE,IAAI,CAAC;QACnD;QACA;QACA;QACA;QACA;QACA,IAAI,CAACf,aAAa,CAACkC,OAAO,CAAC,UAAU6B,MAAM,EAAEC,KAAK,EAAE;UAChD,IAAID,MAAM,CAACE,IAAI,CAAClD,QAAQ,CAAC,EAAE;YACvB;YACA;YACA;YACA,IAAImD,eAAe,GAAGlC,KAAK,CAAClC,YAAY,CAAC+D,GAAG,CAACG,KAAK,CAAC;YACnD,IAAIE,eAAe,EAAE;cACjBA,eAAe,CAAChC,OAAO,CAAC,UAAUkB,SAAS,EAAE;gBACzC,OAAOQ,YAAY,CAACL,GAAG,CAACH,SAAS,CAAC;cACtC,CAAC,CAAC;YACN;UACJ;QACJ,CAAC,CAAC;MACN;MACA,IAAIQ,YAAY,IAAIA,YAAY,CAACE,IAAI,EAAE;QACnCF,YAAY,CAAC1B,OAAO,CAAC,UAAUkB,SAAS,EAAE;UACtC,IAAIhE,EAAE,GAAG4C,KAAK,CAACN,aAAa,CAAC0B,SAAS,CAAC;YAAEV,MAAM,GAAGtD,EAAE,CAACsD,MAAM;YAAEyB,IAAI,GAAGhH,MAAM,CAACiC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;UAC1FO,MAAM,CAACyE,MAAM,CAACT,QAAQ,EAAEQ,IAAI,CAAC;UAC7BxE,MAAM,CAACyE,MAAM,CAACT,QAAQ,CAACjB,MAAM,EAAEA,MAAM,CAAC;QAC1C,CAAC,CAAC;MACN;IACJ;IACA,IAAI2B,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACkB,QAAQ,CAAC;IACpC,IAAIsD,KAAK,IAAIA,KAAK,CAACC,MAAM,EAAE;MACvB;MACA;MACAD,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC,CAACrC,OAAO,CAAC,UAAUT,MAAM,EAAE;QACtCO,KAAK,CAACQ,gBAAgB,CAACzB,QAAQ,EAAEU,MAAM,CAAC;MAC5C,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAC/B,YAAY,CAACqB,QAAQ,CAAC;EACtC,CAAC;EACDvB,QAAQ,CAACkB,SAAS,CAACmC,cAAc,GAAG,UAAU9B,QAAQ,EAAE/B,SAAS,EAAEwF,eAAe,EAAE;IAChF,IAAIzD,QAAQ,EAAE;MACV,IAAI0D,aAAa,GAAG,IAAI,CAAC/C,aAAa,CAACX,QAAQ,CAAC,CAAC2B,MAAM;MACvD,OAAQ+B,aAAa,CAACzF,SAAS,CAAC,IAC3BwF,eAAe,KAAKC,aAAa,CAACzF,SAAS,CAAC,GAAGW,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAE;IAC7E;EACJ,CAAC;EACDJ,QAAQ,CAACkB,SAAS,CAAC2C,eAAe,GAAG,UAAUC,OAAO,EAAEkB,eAAe,EAAE;IACrE,IAAIE,YAAY,GAAG,IAAI,CAAC5E,YAAY,CAAC+D,GAAG,CAACP,OAAO,CAAC;IACjD,IAAI,CAACoB,YAAY,IAAIF,eAAe,EAAE;MAClC,IAAI,CAAC1E,YAAY,CAAC2D,GAAG,CAACH,OAAO,EAAGoB,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAE,CAAC;IAC9D;IACA,OAAOD,YAAY;EACvB,CAAC;EACDlF,QAAQ,CAACkB,SAAS,CAACkE,eAAe,GAAG,UAAUC,QAAQ,EAAE9D,QAAQ,EAAE+D,MAAM,EAAEnG,SAAS,EAAE;IAClF,IAAIqD,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC6C,QAAQ,CAACE,aAAa,EACvB,OAAO,IAAI;IACf;IACA;IACA,IAAI,CAAChE,QAAQ,EACT,OAAO,KAAK;IAChB,IAAIqC,SAAS,GAAGyB,QAAQ,CAACE,aAAa,CAACC,IAAI,CAACC,KAAK;IACjD;IACA,IAAIlE,QAAQ,KAAKqC,SAAS,EACtB,OAAO,IAAI;IACf,IAAI,IAAI,CAACjD,kBAAkB,IAAI,IAAI,CAACL,YAAY,CAACoF,GAAG,CAAC9B,SAAS,CAAC,EAAE;MAC7D,IAAI+B,oBAAoB,GAAG,IAAI,CAAC9B,eAAe,CAACtC,QAAQ,EAAE,IAAI,CAAC;MAC/D,IAAIqE,WAAW,GAAG,CAACD,oBAAoB,CAAC;MACxC,IAAIE,cAAc,GAAG,SAAAA,CAAU/B,OAAO,EAAE;QACpC,IAAIoB,YAAY,GAAG1C,KAAK,CAACqB,eAAe,CAACC,OAAO,EAAE,KAAK,CAAC;QACxD,IAAIoB,YAAY,IACZA,YAAY,CAACZ,IAAI,IACjBsB,WAAW,CAACE,OAAO,CAACZ,YAAY,CAAC,GAAG,CAAC,EAAE;UACvCU,WAAW,CAAC7C,IAAI,CAACmC,YAAY,CAAC;QAClC;MACJ,CAAC;MACD;MACA;MACA;MACA;MACA;MACA;MACA,IAAIa,wBAAwB,GAAG,CAAC,EAAET,MAAM,IAAI,IAAI,CAAC9E,aAAa,CAAC8D,IAAI,CAAC;MACpE,IAAI0B,qBAAqB,GAAG,KAAK;MACjC;MACA;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACd,MAAM,EAAE,EAAEmB,CAAC,EAAE;QACzC,IAAIf,YAAY,GAAGU,WAAW,CAACK,CAAC,CAAC;QACjC,IAAIf,YAAY,CAACQ,GAAG,CAAC9B,SAAS,CAAC,EAAE;UAC7B,IAAI,CAAC+B,oBAAoB,CAACD,GAAG,CAAC9B,SAAS,CAAC,EAAE;YACtC,IAAIoC,qBAAqB,EAAE;cACvBE,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIvI,SAAS,CAACwI,IAAI,CAAC,CAAC,EAAE7E,QAAQ,EAAEqC,SAAS,CAAC;YAC1E;YACA;YACA;YACA;YACA;YACA+B,oBAAoB,CAAC5B,GAAG,CAACH,SAAS,CAAC;UACvC;UACA,OAAO,IAAI;QACf;QACAsB,YAAY,CAACxC,OAAO,CAACmD,cAAc,CAAC;QACpC,IAAIE,wBAAwB;QACxB;QACA;QACAE,CAAC,KAAKL,WAAW,CAACd,MAAM,GAAG,CAAC;QAC5B;QACA;QACA;QACA;QACAvG,yBAAyB,CAAC8G,QAAQ,CAACgB,YAAY,EAAEf,MAAM,EAAEnG,SAAS,CAAC,EAAE;UACrE;UACA;UACA;UACA4G,wBAAwB,GAAG,KAAK;UAChCC,qBAAqB,GAAG,IAAI;UAC5B;UACA;UACA;UACA;UACA,IAAI,CAACxF,aAAa,CAACkC,OAAO,CAAC,UAAU6B,MAAM,EAAE+B,WAAW,EAAE;YACtD,IAAItC,KAAK,GAAGzC,QAAQ,CAACyC,KAAK,CAACO,MAAM,CAAC;YAClC,IAAIP,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKzC,QAAQ,EAAE;cAChCsE,cAAc,CAACS,WAAW,CAAC;YAC/B;UACJ,CAAC,CAAC;QACN;MACJ;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACDtG,QAAQ,CAACkB,SAAS,CAACqF,UAAU,GAAG,UAAUhF,QAAQ,EAAE/B,SAAS,EAAE;IAC3D,IAAIyC,MAAM,GAAG,IAAI,CAACoB,cAAc,CAAC9B,QAAQ,EAAE/B,SAAS,EAAE,KAAK,CAAC;IAC5D,OAAO,CAAC,EAAEyC,MAAM,IAAIA,MAAM,CAACE,KAAK,CAAC;EACrC,CAAC;EACDnC,QAAQ,CAACkB,SAAS,CAACsF,iBAAiB,GAAG,UAAUC,SAAS,EAAE;IACxD,IAAIlF,QAAQ,GAAGkF,SAAS,CAAClF,QAAQ;MAAE/B,SAAS,GAAGiH,SAAS,CAACjH,SAAS;IAClE,IAAIyC,MAAM,GAAG,IAAI,CAACoB,cAAc,CAAC9B,QAAQ,EAAE/B,SAAS,EAAE,KAAK,CAAC;IAC5D,IAAIkH,cAAc;IAClB,IAAIvE,KAAK,GAAGF,MAAM,IAAIA,MAAM,CAACE,KAAK;IAClC,IAAIA,KAAK,IAAIZ,QAAQ,EAAE;MACnB,IAAIhC,OAAO,GAAG;QACVgC,QAAQ,EAAEA,QAAQ;QAClB/B,SAAS,EAAEA,SAAS;QACpBN,KAAK,EAAEuH,SAAS,CAACvH,KAAK,IAAI,IAAI;QAC9BC,SAAS,EAAEsH,SAAS,CAACtH;MACzB,CAAC;MACD,IAAIF,IAAI,GAAGF,sBAAsB,CAAC0H,SAAS,CAAC;MAC5C,OAAOtE,KAAK,EAAE;QACV,IAAIwE,iBAAiB,GAAGxE,KAAK,CAAClD,IAAI,EAAEM,OAAO,CAAC;QAC5C,IAAIb,OAAO,CAACiI,iBAAiB,CAAC,EAAE;UAC5BxE,KAAK,GAAGvD,sBAAsB,CAAC+H,iBAAiB,CAAC;QACrD,CAAC,MACI;UACD;UACA;UACAD,cAAc,GAAGC,iBAAiB,IAAInH,SAAS;UAC/C;QACJ;MACJ;IACJ;IACA,IAAIkH,cAAc,KAAK,KAAK,CAAC,EAAE;MAC3BA,cAAc,GACVD,SAAS,CAACvH,KAAK,GACXpB,qBAAqB,CAAC2I,SAAS,CAACvH,KAAK,EAAEuH,SAAS,CAACtH,SAAS,CAAC,GACzDlB,eAAe,CAACuB,SAAS,EAAET,sBAAsB,CAAC0H,SAAS,CAAC,CAAC;IAC3E;IACA;IACA;IACA,IAAIC,cAAc,KAAK,KAAK,EAAE;MAC1B,OAAOlH,SAAS;IACpB;IACA;IACA;IACA;IACA,OAAOA,SAAS,KAAKnB,sBAAsB,CAACqI,cAAc,CAAC,GAAGA,cAAc,GACtElH,SAAS,GAAG,GAAG,GAAGkH,cAAc;EAC1C,CAAC;EACD1G,QAAQ,CAACkB,SAAS,CAACS,SAAS,GAAG,UAAUC,OAAO,EAAErC,OAAO,EAAE;IACvD,IAAIqH,iBAAiB,GAAGhF,OAAO,CAACiF,IAAI;IACpC,IAAI,CAACD,iBAAiB,EAClB;IACJ,IAAIE,WAAW,GAAGlF,OAAO,CAAC1C,KAAK,IAAI0C,OAAO,CAACpC,SAAS;IACpD,IAAI,CAACsH,WAAW,EACZ;IACJ,IAAIlF,OAAO,CAACL,QAAQ,KAAK,KAAK,CAAC,EAAE;MAC7B,IAAIA,QAAQ,GAAGhC,OAAO,CAACwC,KAAK,CAACgF,aAAa,CAACH,iBAAiB,EAAE,YAAY,CAAC;MAC3E,IAAIrF,QAAQ,EACRK,OAAO,CAACL,QAAQ,GAAGA,QAAQ;IACnC;IACA,IAAImF,cAAc,GAAG,IAAI,CAACF,iBAAiB,CAAC5E,OAAO,CAAC;IACpD,IAAIpC,SAAS,GAAGnB,sBAAsB,CAACqI,cAAc,CAAC;IACtD,IAAIhH,QAAQ,GAAGH,OAAO,CAACwC,KAAK,CAACgF,aAAa,CAACH,iBAAiB,EAAEF,cAAc,CAAC;IAC7E,IAAIzE,MAAM,GAAG,IAAI,CAACoB,cAAc,CAACzB,OAAO,CAACL,QAAQ,EAAE/B,SAAS,EAAE,KAAK,CAAC;IACpE,IAAI8D,IAAI,GAAGrB,MAAM,IAAIA,MAAM,CAACqB,IAAI;IAChC,IAAIA,IAAI,EAAE;MACN,IAAI0D,WAAW,GAAGC,wBAAwB,CAAC,IAAI,EAAEL,iBAAiB,EAAEhF,OAAO,EAAErC,OAAO,EAAEA,OAAO,CAACwC,KAAK,CAACmF,UAAU,CAAClJ,WAAW,CAAC4I,iBAAiB,CAAC,GACzIA,iBAAiB,CAACO,KAAK,GACrBP,iBAAiB,EAAEF,cAAc,CAAC,CAAC;MACzC;MACA,OAAO/H,SAAS,CAACyD,SAAS,CAAC,IAAI,CAACvB,KAAK,EAAEyC,IAAI,EAAE,CACzC5D,QAAQ,EACRsH,WAAW,CACd,CAAC;IACN;IACA,OAAOtH,QAAQ;EACnB,CAAC;EACDM,QAAQ,CAACkB,SAAS,CAACkG,eAAe,GAAG,UAAU7F,QAAQ,EAAE/B,SAAS,EAAE;IAChE,IAAIyC,MAAM,GAAG,IAAI,CAACoB,cAAc,CAAC9B,QAAQ,EAAE/B,SAAS,EAAE,KAAK,CAAC;IAC5D,OAAOyC,MAAM,IAAIA,MAAM,CAACqB,IAAI;EAChC,CAAC;EACDtD,QAAQ,CAACkB,SAAS,CAACmG,gBAAgB,GAAG,UAAUC,cAAc,EAAE9H,SAAS,EAAE+H,aAAa,EAAE;IACtF,IAAItF,MAAM,GAAG,IAAI,CAACoB,cAAc,CAACiE,cAAc,EAAE9H,SAAS,EAAE,KAAK,CAAC;IAClE,IAAI4D,KAAK,GAAGnB,MAAM,IAAIA,MAAM,CAACmB,KAAK;IAClC,IAAI,CAACA,KAAK,IAAImE,aAAa,EAAE;MACzBtF,MAAM,GAAG,IAAI,CAACC,aAAa,CAACqF,aAAa,CAAC;MAC1CnE,KAAK,GAAGnB,MAAM,IAAIA,MAAM,CAACmB,KAAK;IAClC;IACA,OAAOA,KAAK;EAChB,CAAC;EACDpD,QAAQ,CAACkB,SAAS,CAACsG,gBAAgB,GAAG,UAAU9H,QAAQ,EAAEC,QAAQ,EAAEC,EAAE,EAAEL,OAAO,EAAEkI,OAAO,EAAE;IACtF,IAAIvI,KAAK,GAAGU,EAAE,CAACV,KAAK;MAAEqC,QAAQ,GAAG3B,EAAE,CAAC2B,QAAQ;MAAE6B,KAAK,GAAGxD,EAAE,CAACwD,KAAK;IAC9D,IAAIA,KAAK,KAAK3D,WAAW,EAAE;MACvB;MACA;MACA;MACA,OAAOiI,wBAAwB,CAACnI,OAAO,CAACwC,KAAK,CAAC,CAACrC,QAAQ,EAAEC,QAAQ,CAAC;IACtE;IACA,IAAIyD,KAAK,KAAKtD,YAAY,EAAE;MACxB;MACA,OAAOH,QAAQ;IACnB;IACA;IACA;IACA;IACA;IACA,IAAIJ,OAAO,CAACoI,SAAS,EAAE;MACnBjI,QAAQ,GAAG,KAAK,CAAC;IACrB;IACA,OAAO0D,KAAK,CAAC1D,QAAQ,EAAEC,QAAQ,EAAEsH,wBAAwB,CAAC,IAAI;IAC9D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,CAAC,EAAE;MACJ1F,QAAQ,EAAEA,QAAQ;MAClB/B,SAAS,EAAEN,KAAK,CAACsG,IAAI,CAACC,KAAK;MAC3BvG,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEI,OAAO,CAACJ;IACvB,CAAC,EAAEI,OAAO,EAAEkI,OAAO,IAAItH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,CAAC;EACD,OAAOJ,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,SAASA,QAAQ;AACjB,SAASiH,wBAAwBA,CAAC3F,QAAQ,EAAEsF,iBAAiB,EAAEH,SAAS,EAAElH,OAAO,EAAEkI,OAAO,EAAE;EACxF,IAAIf,cAAc,GAAGpF,QAAQ,CAACkF,iBAAiB,CAACC,SAAS,CAAC;EAC1D,IAAIjH,SAAS,GAAGnB,sBAAsB,CAACqI,cAAc,CAAC;EACtD,IAAIvH,SAAS,GAAGsH,SAAS,CAACtH,SAAS,IAAII,OAAO,CAACJ,SAAS;EACxD,IAAIS,EAAE,GAAGL,OAAO,CAACwC,KAAK;IAAE6F,WAAW,GAAGhI,EAAE,CAACgI,WAAW;IAAEC,OAAO,GAAGjI,EAAE,CAACiI,OAAO;EAC1E,OAAO;IACH5I,IAAI,EAAEF,sBAAsB,CAAC0H,SAAS,CAAC;IACvCvH,KAAK,EAAEuH,SAAS,CAACvH,KAAK,IAAI,IAAI;IAC9BM,SAAS,EAAEA,SAAS;IACpBkH,cAAc,EAAEA,cAAc;IAC9BvH,SAAS,EAAEA,SAAS;IACpBnB,WAAW,EAAEA,WAAW;IACxB4J,WAAW,EAAEA,WAAW;IACxBH,OAAO,EAAEA,OAAO;IAChB5G,KAAK,EAAES,QAAQ,CAACT,KAAK;IACrBgH,OAAO,EAAEA,OAAO;IAChBlG,SAAS,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAOL,QAAQ,CAACK,SAAS,CAACE,yBAAyB,CAACC,SAAS,EAAE8E,iBAAiB,EAAEzH,SAAS,CAAC,EAAEI,OAAO,CAAC;IAC1G,CAAC;IACDM,YAAY,EAAE6H,wBAAwB,CAACnI,OAAO,CAACwC,KAAK;EACxD,CAAC;AACL;AACA,OAAO,SAASF,yBAAyBA,CAACiG,aAAa,EAAElB,iBAAiB,EAAEzH,SAAS,EAAE;EACnF,IAAI4I,kBAAkB,GAAGD,aAAa,CAAC,CAAC,CAAC;IAAEjB,IAAI,GAAGiB,aAAa,CAAC,CAAC,CAAC;IAAEE,IAAI,GAAGF,aAAa,CAAChD,MAAM;EAC/F,IAAIlD,OAAO;EACX,IAAI,OAAOmG,kBAAkB,KAAK,QAAQ,EAAE;IACxCnG,OAAO,GAAG;MACNpC,SAAS,EAAEuI,kBAAkB;MAC7B;MACA;MACA;MACAlB,IAAI,EAAEmB,IAAI,GAAG,CAAC,GAAGnB,IAAI,GAAGD;IAC5B,CAAC;EACL,CAAC,MACI;IACDhF,OAAO,GAAGlE,QAAQ,CAAC,CAAC,CAAC,EAAEqK,kBAAkB,CAAC;IAC1C;IACA;IACA,IAAI,CAAC3J,MAAM,CAAC0E,IAAI,CAAClB,OAAO,EAAE,MAAM,CAAC,EAAE;MAC/BA,OAAO,CAACiF,IAAI,GAAGD,iBAAiB;IACpC;EACJ;EACA,IAAIV,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIvE,OAAO,CAACiF,IAAI,KAAK,KAAK,CAAC,EAAE;IACzDX,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIvI,SAAS,CAACwI,IAAI,CAAC,CAAC,EAAEjI,mBAAmB,CAAC8J,KAAK,CAACpB,IAAI,CAACiB,aAAa,CAAC,CAAC,CAAC;EACrG;EACA,IAAI,KAAK,CAAC,KAAKlG,OAAO,CAACzC,SAAS,EAAE;IAC9ByC,OAAO,CAACzC,SAAS,GAAGA,SAAS;EACjC;EACA,OAAOyC,OAAO;AAClB;AACA,SAAS8F,wBAAwBA,CAAC3F,KAAK,EAAE;EACrC,OAAO,SAASlC,YAAYA,CAACH,QAAQ,EAAEC,QAAQ,EAAE;IAC7C,IAAIjB,OAAO,CAACgB,QAAQ,CAAC,IAAIhB,OAAO,CAACiB,QAAQ,CAAC,EAAE;MACxC,MAAM9B,iBAAiB,CAAC,CAAC,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA,IAAIK,eAAe,CAACwB,QAAQ,CAAC,IAAIxB,eAAe,CAACyB,QAAQ,CAAC,EAAE;MACxD,IAAIuI,KAAK,GAAGnG,KAAK,CAACgF,aAAa,CAACrH,QAAQ,EAAE,YAAY,CAAC;MACvD,IAAIyI,KAAK,GAAGpG,KAAK,CAACgF,aAAa,CAACpH,QAAQ,EAAE,YAAY,CAAC;MACvD,IAAIyI,WAAW,GAAGF,KAAK,IAAIC,KAAK,IAAID,KAAK,KAAKC,KAAK;MACnD,IAAIC,WAAW,EAAE;QACb,OAAOzI,QAAQ;MACnB;MACA,IAAI3B,WAAW,CAAC0B,QAAQ,CAAC,IAAIpB,uBAAuB,CAACqB,QAAQ,CAAC,EAAE;QAC5D;QACA;QACA;QACAoC,KAAK,CAACqB,KAAK,CAAC1D,QAAQ,CAACyH,KAAK,EAAExH,QAAQ,CAAC;QACrC,OAAOD,QAAQ;MACnB;MACA,IAAIpB,uBAAuB,CAACoB,QAAQ,CAAC,IAAI1B,WAAW,CAAC2B,QAAQ,CAAC,EAAE;QAC5D;QACA;QACA;QACA;QACAoC,KAAK,CAACqB,KAAK,CAAC1D,QAAQ,EAAEC,QAAQ,CAACwH,KAAK,CAAC;QACrC,OAAOxH,QAAQ;MACnB;MACA,IAAIrB,uBAAuB,CAACoB,QAAQ,CAAC,IACjCpB,uBAAuB,CAACqB,QAAQ,CAAC,EAAE;QACnC,OAAOjC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgC,QAAQ,CAAC,EAAEC,QAAQ,CAAC;MACrD;IACJ;IACA,OAAOA,QAAQ;EACnB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}