{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarYears } from \"./differenceInCalendarYears.js\";\n\n/**\n * The {@link differenceInYears} function options.\n */\n\n/**\n * @name differenceInYears\n * @category Year Helpers\n * @summary Get the number of full years between the given dates.\n *\n * @description\n * Get the number of full years between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full years\n *\n * @example\n * // How many full years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInYears(new Date(2015, 1, 11), new Date(2013, 11, 31))\n * //=> 1\n */\nexport function differenceInYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n\n  // -1 if the left date is earlier than the right date\n  // 2023-12-31 - 2024-01-01 = -1\n  const sign = compareAsc(laterDate_, earlierDate_);\n\n  // First calculate the difference in calendar years\n  // 2024-01-01 - 2023-12-31 = 1 year\n  const diff = Math.abs(differenceInCalendarYears(laterDate_, earlierDate_));\n\n  // Now we need to calculate if the difference is full. To do that we set\n  // both dates to the same year and check if the both date's month and day\n  // form a full year.\n  laterDate_.setFullYear(1584);\n  earlierDate_.setFullYear(1584);\n\n  // For it to be true, when the later date is indeed later than the earlier date\n  // (2026-02-01 - 2023-12-10 = 3 years), the difference is full if\n  // the normalized later date is also later than the normalized earlier date.\n  // In our example, 1584-02-01 is earlier than 1584-12-10, so the difference\n  // is partial, hence we need to subtract 1 from the difference 3 - 1 = 2.\n  const partial = compareAsc(laterDate_, earlierDate_) === -sign;\n  const result = sign * (diff - +partial);\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInYears;", "map": {"version": 3, "names": ["normalizeDates", "compareAsc", "differenceInCalendarYears", "differenceInYears", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "sign", "diff", "Math", "abs", "setFullYear", "partial", "result"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/differenceInYears.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarYears } from \"./differenceInCalendarYears.js\";\n\n/**\n * The {@link differenceInYears} function options.\n */\n\n/**\n * @name differenceInYears\n * @category Year Helpers\n * @summary Get the number of full years between the given dates.\n *\n * @description\n * Get the number of full years between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full years\n *\n * @example\n * // How many full years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInYears(new Date(2015, 1, 11), new Date(2013, 11, 31))\n * //=> 1\n */\nexport function differenceInYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  // -1 if the left date is earlier than the right date\n  // 2023-12-31 - 2024-01-01 = -1\n  const sign = compareAsc(laterDate_, earlierDate_);\n\n  // First calculate the difference in calendar years\n  // 2024-01-01 - 2023-12-31 = 1 year\n  const diff = Math.abs(differenceInCalendarYears(laterDate_, earlierDate_));\n\n  // Now we need to calculate if the difference is full. To do that we set\n  // both dates to the same year and check if the both date's month and day\n  // form a full year.\n  laterDate_.setFullYear(1584);\n  earlierDate_.setFullYear(1584);\n\n  // For it to be true, when the later date is indeed later than the earlier date\n  // (2026-02-01 - 2023-12-10 = 3 years), the difference is full if\n  // the normalized later date is also later than the normalized earlier date.\n  // In our example, 1584-02-01 is earlier than 1584-12-10, so the difference\n  // is partial, hence we need to subtract 1 from the difference 3 - 1 = 2.\n  const partial = compareAsc(laterDate_, earlierDate_) === -sign;\n\n  const result = sign * (diff - +partial);\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInYears;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,yBAAyB,QAAQ,gCAAgC;;AAE1E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACjE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGR,cAAc,CAC/CM,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;;EAED;EACA;EACA,MAAMK,IAAI,GAAGT,UAAU,CAACM,UAAU,EAAEC,YAAY,CAAC;;EAEjD;EACA;EACA,MAAMG,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACX,yBAAyB,CAACK,UAAU,EAAEC,YAAY,CAAC,CAAC;;EAE1E;EACA;EACA;EACAD,UAAU,CAACO,WAAW,CAAC,IAAI,CAAC;EAC5BN,YAAY,CAACM,WAAW,CAAC,IAAI,CAAC;;EAE9B;EACA;EACA;EACA;EACA;EACA,MAAMC,OAAO,GAAGd,UAAU,CAACM,UAAU,EAAEC,YAAY,CAAC,KAAK,CAACE,IAAI;EAE9D,MAAMM,MAAM,GAAGN,IAAI,IAAIC,IAAI,GAAG,CAACI,OAAO,CAAC;;EAEvC;EACA,OAAOC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA,eAAeb,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}