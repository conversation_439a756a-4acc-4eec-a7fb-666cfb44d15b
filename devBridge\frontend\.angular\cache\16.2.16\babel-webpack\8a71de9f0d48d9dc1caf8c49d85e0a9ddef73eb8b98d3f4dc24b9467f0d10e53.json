{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/notification.service\";\nimport * as i5 from \"@angular/common\";\nfunction EquipeSimpleComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵelement(1, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class EquipeSimpleComponent {\n  constructor(equipeService, authService, router, notificationService) {\n    this.equipeService = equipeService;\n    this.authService = authService;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.creating = false;\n    this.currentUserId = null;\n  }\n  ngOnInit() {\n    this.getCurrentUserId();\n  }\n  getCurrentUserId() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.authService.getProfile(token).subscribe({\n        next: user => {\n          this.currentUserId = user._id || user.id;\n          console.log('Utilisateur connecté:', user);\n        },\n        error: error => {\n          console.error('Erreur lors de la récupération du profil:', error);\n          this.notificationService.showError('Erreur d\\'authentification. Veuillez vous reconnecter.');\n        }\n      });\n    } else {\n      this.notificationService.showError('Vous devez être connecté pour créer une équipe.');\n      this.router.navigate(['/login']);\n    }\n  }\n  createTeam() {\n    if (!this.currentUserId) {\n      this.notificationService.showError('Erreur d\\'authentification. Veuillez vous reconnecter.');\n      return;\n    }\n    this.creating = true;\n    // Générer un nom unique avec timestamp\n    const timestamp = new Date().toLocaleString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).replace(/[\\/\\s:]/g, '');\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    const userName = user.firstName || user.name || 'Utilisateur';\n    const newTeam = {\n      name: `Équipe ${userName} ${timestamp}`,\n      description: `Équipe créée automatiquement le ${new Date().toLocaleString('fr-FR')}`,\n      admin: this.currentUserId\n    };\n    console.log('🚀 Création d\\'équipe automatique:', newTeam);\n    this.equipeService.addEquipe(newTeam).subscribe({\n      next: response => {\n        console.log('✅ Équipe créée avec succès:', response);\n        this.creating = false;\n        this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès !`);\n        // Rediriger vers la page des équipes après 1.5 secondes\n        setTimeout(() => {\n          this.router.navigate(['/equipes']);\n        }, 1500);\n      },\n      error: error => {\n        console.error('❌ Erreur lors de la création:', error);\n        this.creating = false;\n        if (error.status === 400 && error.message?.includes('nom existe déjà')) {\n          // Si le nom existe, essayer avec un suffixe aléatoire\n          const randomSuffix = Math.floor(Math.random() * 1000);\n          const retryTeam = {\n            ...newTeam,\n            name: `${newTeam.name}-${randomSuffix}`\n          };\n          console.log('🔄 Retry avec nom modifié:', retryTeam);\n          this.retryCreateTeam(retryTeam);\n        } else {\n          this.notificationService.showError(`Erreur lors de la création: ${error.message || 'Erreur inconnue'}`);\n        }\n      }\n    });\n  }\n  retryCreateTeam(team) {\n    this.creating = true;\n    this.equipeService.addEquipe(team).subscribe({\n      next: response => {\n        console.log('✅ Équipe créée avec succès (retry):', response);\n        this.creating = false;\n        this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès !`);\n        setTimeout(() => {\n          this.router.navigate(['/equipes/liste']);\n        }, 1500);\n      },\n      error: error => {\n        console.error('❌ Erreur lors du retry:', error);\n        this.creating = false;\n        this.notificationService.showError(`Impossible de créer l'équipe: ${error.message || 'Erreur inconnue'}`);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function EquipeSimpleComponent_Factory(t) {\n      return new (t || EquipeSimpleComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeSimpleComponent,\n      selectors: [[\"app-equipe-simple\"]],\n      decls: 36,\n      vars: 4,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\", \"flex\", \"items-center\", \"justify-center\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"relative\", \"z-10\", \"max-w-md\", \"w-full\", \"mx-auto\", \"p-6\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-2xl\", \"shadow-2xl\", \"dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)]\", \"overflow-hidden\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"backdrop-blur-sm\"], [1, \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-8\", \"text-center\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"bg-gradient-to-br\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-2xl\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-3\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"leading-relaxed\", \"mb-8\"], [1, \"px-8\", \"pb-8\"], [1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"py-4\", \"px-6\", \"rounded-xl\", \"font-semibold\", \"text-lg\", \"transition-all\", \"duration-300\", \"hover:scale-[1.02]\", \"hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", 3, \"disabled\", \"click\"], [\"class\", \"absolute inset-0 flex items-center justify-center\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-center\", \"transition-opacity\"], [1, \"fas\", \"fa-plus-circle\", \"mr-3\", \"group-hover:rotate-90\", \"transition-transform\", \"duration-300\"], [1, \"absolute\", \"inset-0\", \"bg-white/10\", \"transform\", \"scale-x-0\", \"group-hover:scale-x-100\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [1, \"w-6\", \"h-6\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\"]],\n      template: function EquipeSimpleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\", 9);\n          i0.ɵɵelement(20, \"div\", 10)(21, \"div\", 11);\n          i0.ɵɵelementStart(22, \"div\", 12)(23, \"div\", 13);\n          i0.ɵɵelement(24, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"h1\", 15);\n          i0.ɵɵtext(26, \" Cr\\u00E9ation Rapide d'\\u00C9quipe \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"p\", 16);\n          i0.ɵɵtext(28, \" Cr\\u00E9ez instantan\\u00E9ment une nouvelle \\u00E9quipe avec des param\\u00E8tres par d\\u00E9faut. Vous pourrez la personnaliser plus tard. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 17)(30, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function EquipeSimpleComponent_Template_button_click_30_listener() {\n            return ctx.createTeam();\n          });\n          i0.ɵɵtemplate(31, EquipeSimpleComponent_span_31_Template, 2, 0, \"span\", 19);\n          i0.ɵɵelementStart(32, \"span\", 20);\n          i0.ɵɵelement(33, \"i\", 21);\n          i0.ɵɵtext(34, \" Cr\\u00E9er \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"div\", 22);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(30);\n          i0.ɵɵproperty(\"disabled\", ctx.creating);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.creating);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"opacity-0\", ctx.creating);\n        }\n      },\n      dependencies: [i5.NgIf],\n      styles: [\"\\n\\n@keyframes _ngcontent-%COMP%_pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 20px rgba(79, 95, 173, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 30px rgba(79, 95, 173, 0.5);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n\\n\\n\\n.main-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_float 3s ease-in-out infinite;\\n}\\n\\n\\n\\n.btn-shine[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.btn-shine[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    rgba(255, 255, 255, 0.2),\\n    transparent\\n  );\\n  transition: left 0.5s;\\n}\\n\\n.btn-shine[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   .pulse-glow[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse-glow-dark 2s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-glow-dark {\\n  0%, 100% {\\n    box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 30px rgba(0, 247, 255, 0.5);\\n  }\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%] {\\n  color-scheme: dark;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_spin-smooth {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.animate-spin-smooth[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin-smooth 1s linear infinite;\\n}\\n\\n\\n\\n.hover-lift[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n\\n.hover-lift[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n\\n\\n.notification-enter[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n\\n@media (max-width: 640px) {\\n  .main-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  \\n  .card-container[_ngcontent-%COMP%] {\\n    margin: 0 1rem;\\n  }\\n}\\n\\n\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .main-icon[_ngcontent-%COMP%], .pulse-glow[_ngcontent-%COMP%], .dark[_ngcontent-%COMP%]   .pulse-glow[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  \\n  .hover-lift[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n}\\n\\n\\n\\nbutton[_ngcontent-%COMP%]:focus-visible {\\n  outline: 2px solid #4f5fad;\\n  outline-offset: 2px;\\n}\\n\\n.dark[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:focus-visible {\\n  outline-color: #00f7ff;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "EquipeSimpleComponent", "constructor", "equipeService", "authService", "router", "notificationService", "creating", "currentUserId", "ngOnInit", "getCurrentUserId", "token", "localStorage", "getItem", "getProfile", "subscribe", "next", "user", "_id", "id", "console", "log", "error", "showError", "navigate", "createTeam", "timestamp", "Date", "toLocaleString", "day", "month", "year", "hour", "minute", "replace", "JSON", "parse", "userName", "firstName", "name", "newTeam", "description", "admin", "addEquipe", "response", "showSuccess", "setTimeout", "status", "message", "includes", "randomSuffix", "Math", "floor", "random", "retryTeam", "retryCreateTeam", "team", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "AuthService", "i3", "Router", "i4", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeSimpleComponent_Template", "rf", "ctx", "ɵɵtext", "ɵɵlistener", "EquipeSimpleComponent_Template_button_click_30_listener", "ɵɵtemplate", "EquipeSimpleComponent_span_31_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-simple\\equipe-simple.component.ts", "C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-simple\\equipe-simple.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { AuthService } from 'src/app/services/auth.service';\n\n@Component({\n  selector: 'app-equipe-simple',\n  templateUrl: './equipe-simple.component.html',\n  styleUrls: ['./equipe-simple.component.css']\n})\nexport class EquipeSimpleComponent implements OnInit {\n  creating = false;\n  currentUserId: string | null = null;\n\n  constructor(\n    private equipeService: EquipeService,\n    private authService: AuthService,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    this.getCurrentUserId();\n  }\n\n  getCurrentUserId(): void {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.authService.getProfile(token).subscribe({\n        next: (user: any) => {\n          this.currentUserId = user._id || user.id;\n          console.log('Utilisateur connecté:', user);\n        },\n        error: (error) => {\n          console.error('Erreur lors de la récupération du profil:', error);\n          this.notificationService.showError('Erreur d\\'authentification. Veuillez vous reconnecter.');\n        }\n      });\n    } else {\n      this.notificationService.showError('Vous devez être connecté pour créer une équipe.');\n      this.router.navigate(['/login']);\n    }\n  }\n\n  createTeam(): void {\n    if (!this.currentUserId) {\n      this.notificationService.showError('Erreur d\\'authentification. Veuillez vous reconnecter.');\n      return;\n    }\n\n    this.creating = true;\n\n    // Générer un nom unique avec timestamp\n    const timestamp = new Date().toLocaleString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).replace(/[\\/\\s:]/g, '');\n\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    const userName = user.firstName || user.name || 'Utilisateur';\n\n    const newTeam = {\n      name: `Équipe ${userName} ${timestamp}`,\n      description: `Équipe créée automatiquement le ${new Date().toLocaleString('fr-FR')}`,\n      admin: this.currentUserId\n    };\n\n    console.log('🚀 Création d\\'équipe automatique:', newTeam);\n\n    this.equipeService.addEquipe(newTeam).subscribe({\n      next: (response) => {\n        console.log('✅ Équipe créée avec succès:', response);\n        this.creating = false;\n        this.notificationService.showSuccess(\n          `L'équipe \"${response.name}\" a été créée avec succès !`\n        );\n\n        // Rediriger vers la page des équipes après 1.5 secondes\n        setTimeout(() => {\n          this.router.navigate(['/equipes']);\n        }, 1500);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors de la création:', error);\n        this.creating = false;\n\n        if (error.status === 400 && error.message?.includes('nom existe déjà')) {\n          // Si le nom existe, essayer avec un suffixe aléatoire\n          const randomSuffix = Math.floor(Math.random() * 1000);\n          const retryTeam = {\n            ...newTeam,\n            name: `${newTeam.name}-${randomSuffix}`\n          };\n\n          console.log('🔄 Retry avec nom modifié:', retryTeam);\n          this.retryCreateTeam(retryTeam);\n        } else {\n          this.notificationService.showError(\n            `Erreur lors de la création: ${error.message || 'Erreur inconnue'}`\n          );\n        }\n      }\n    });\n  }\n\n  private retryCreateTeam(team: any): void {\n    this.creating = true;\n\n    this.equipeService.addEquipe(team).subscribe({\n      next: (response) => {\n        console.log('✅ Équipe créée avec succès (retry):', response);\n        this.creating = false;\n        this.notificationService.showSuccess(\n          `L'équipe \"${response.name}\" a été créée avec succès !`\n        );\n\n        setTimeout(() => {\n          this.router.navigate(['/equipes/liste']);\n        }, 1500);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du retry:', error);\n        this.creating = false;\n        this.notificationService.showError(\n          `Impossible de créer l'équipe: ${error.message || 'Erreur inconnue'}`\n        );\n      }\n    });\n  }\n\n\n}\n", "<div class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden flex items-center justify-center\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"></div>\n    <div class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content -->\n  <div class=\"relative z-10 max-w-md w-full mx-auto p-6\">\n    <!-- Card Container -->\n    <div class=\"bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-2xl dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\">\n\n      <!-- Header with gradient -->\n      <div class=\"relative\">\n        <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"></div>\n        <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"></div>\n\n        <div class=\"p-8 text-center\">\n          <!-- Icon -->\n          <div class=\"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-full flex items-center justify-center shadow-lg\">\n            <i class=\"fas fa-users text-white text-2xl\"></i>\n          </div>\n\n          <!-- Title -->\n          <h1 class=\"text-2xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-3 tracking-wide\">\n            Création Rapide d'Équipe\n          </h1>\n\n          <!-- Description -->\n          <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm leading-relaxed mb-8\">\n            Créez instantanément une nouvelle équipe avec des paramètres par défaut.\n            Vous pourrez la personnaliser plus tard.\n          </p>\n        </div>\n      </div>\n\n      <!-- Action Section -->\n      <div class=\"px-8 pb-8\">\n        <!-- Main Create Button -->\n        <button\n          (click)=\"createTeam()\"\n          [disabled]=\"creating\"\n          class=\"w-full relative overflow-hidden group bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n        >\n          <!-- Loading Spinner -->\n          <span *ngIf=\"creating\" class=\"absolute inset-0 flex items-center justify-center\">\n            <div class=\"w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n          </span>\n\n          <!-- Button Content -->\n          <span [class.opacity-0]=\"creating\" class=\"flex items-center justify-center transition-opacity\">\n            <i class=\"fas fa-plus-circle mr-3 group-hover:rotate-90 transition-transform duration-300\"></i>\n            Créer\n          </span>\n\n          <!-- Hover Effect -->\n          <div class=\"absolute inset-0 bg-white/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left\"></div>\n        </button>\n\n\n      </div>\n    </div>\n\n\n  </div>\n</div>\n"], "mappings": ";;;;;;;;IC8DUA,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,cAA6F;IAC/FF,EAAA,CAAAG,YAAA,EAAO;;;ADrDjB,OAAM,MAAOC,qBAAqB;EAIhCC,YACUC,aAA4B,EAC5BC,WAAwB,EACxBC,MAAc,EACdC,mBAAwC;IAHxC,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAP7B,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,aAAa,GAAkB,IAAI;EAOhC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAACP,WAAW,CAACU,UAAU,CAACH,KAAK,CAAC,CAACI,SAAS,CAAC;QAC3CC,IAAI,EAAGC,IAAS,IAAI;UAClB,IAAI,CAACT,aAAa,GAAGS,IAAI,CAACC,GAAG,IAAID,IAAI,CAACE,EAAE;UACxCC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,IAAI,CAAC;QAC5C,CAAC;QACDK,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UACjE,IAAI,CAAChB,mBAAmB,CAACiB,SAAS,CAAC,wDAAwD,CAAC;QAC9F;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACjB,mBAAmB,CAACiB,SAAS,CAAC,iDAAiD,CAAC;MACrF,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACjB,aAAa,EAAE;MACvB,IAAI,CAACF,mBAAmB,CAACiB,SAAS,CAAC,wDAAwD,CAAC;MAC5F;;IAGF,IAAI,CAAChB,QAAQ,GAAG,IAAI;IAEpB;IACA,MAAMmB,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACC,cAAc,CAAC,OAAO,EAAE;MACnDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAE1B,MAAMjB,IAAI,GAAGkB,IAAI,CAACC,KAAK,CAACxB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D,MAAMwB,QAAQ,GAAGpB,IAAI,CAACqB,SAAS,IAAIrB,IAAI,CAACsB,IAAI,IAAI,aAAa;IAE7D,MAAMC,OAAO,GAAG;MACdD,IAAI,EAAE,UAAUF,QAAQ,IAAIX,SAAS,EAAE;MACvCe,WAAW,EAAE,mCAAmC,IAAId,IAAI,EAAE,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE;MACpFc,KAAK,EAAE,IAAI,CAAClC;KACb;IAEDY,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmB,OAAO,CAAC;IAE1D,IAAI,CAACrC,aAAa,CAACwC,SAAS,CAACH,OAAO,CAAC,CAACzB,SAAS,CAAC;MAC9CC,IAAI,EAAG4B,QAAQ,IAAI;QACjBxB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEuB,QAAQ,CAAC;QACpD,IAAI,CAACrC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACD,mBAAmB,CAACuC,WAAW,CAClC,aAAaD,QAAQ,CAACL,IAAI,6BAA6B,CACxD;QAED;QACAO,UAAU,CAAC,MAAK;UACd,IAAI,CAACzC,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACf,QAAQ,GAAG,KAAK;QAErB,IAAIe,KAAK,CAACyB,MAAM,KAAK,GAAG,IAAIzB,KAAK,CAAC0B,OAAO,EAAEC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UACtE;UACA,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAAC;UACrD,MAAMC,SAAS,GAAG;YAChB,GAAGd,OAAO;YACVD,IAAI,EAAE,GAAGC,OAAO,CAACD,IAAI,IAAIW,YAAY;WACtC;UAED9B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiC,SAAS,CAAC;UACpD,IAAI,CAACC,eAAe,CAACD,SAAS,CAAC;SAChC,MAAM;UACL,IAAI,CAAChD,mBAAmB,CAACiB,SAAS,CAChC,+BAA+BD,KAAK,CAAC0B,OAAO,IAAI,iBAAiB,EAAE,CACpE;;MAEL;KACD,CAAC;EACJ;EAEQO,eAAeA,CAACC,IAAS;IAC/B,IAAI,CAACjD,QAAQ,GAAG,IAAI;IAEpB,IAAI,CAACJ,aAAa,CAACwC,SAAS,CAACa,IAAI,CAAC,CAACzC,SAAS,CAAC;MAC3CC,IAAI,EAAG4B,QAAQ,IAAI;QACjBxB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEuB,QAAQ,CAAC;QAC5D,IAAI,CAACrC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACD,mBAAmB,CAACuC,WAAW,CAClC,aAAaD,QAAQ,CAACL,IAAI,6BAA6B,CACxD;QAEDO,UAAU,CAAC,MAAK;UACd,IAAI,CAACzC,MAAM,CAACmB,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACf,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACD,mBAAmB,CAACiB,SAAS,CAChC,iCAAiCD,KAAK,CAAC0B,OAAO,IAAI,iBAAiB,EAAE,CACtE;MACH;KACD,CAAC;EACJ;;;uBAzHW/C,qBAAqB,EAAAJ,EAAA,CAAA4D,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA9D,EAAA,CAAA4D,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAhE,EAAA,CAAA4D,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAlE,EAAA,CAAA4D,iBAAA,CAAAO,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBhE,qBAAqB;MAAAiE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlC3E,EAAA,CAAAC,cAAA,aAAmH;UAG/GD,EAAA,CAAAE,SAAA,aAA6K;UAI7KF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,cAAuD;UAMjDD,EAAA,CAAAE,SAAA,eAAwI;UAGxIF,EAAA,CAAAC,cAAA,eAA6B;UAGzBD,EAAA,CAAAE,SAAA,aAAgD;UAClDF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAqF;UACnFD,EAAA,CAAA6E,MAAA,4CACF;UAAA7E,EAAA,CAAAG,YAAA,EAAK;UAGLH,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAA6E,MAAA,oJAEF;UAAA7E,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAC,cAAA,eAAuB;UAGnBD,EAAA,CAAA8E,UAAA,mBAAAC,wDAAA;YAAA,OAASH,GAAA,CAAAhD,UAAA,EAAY;UAAA,EAAC;UAKtB5B,EAAA,CAAAgF,UAAA,KAAAC,sCAAA,mBAEO;UAGPjF,EAAA,CAAAC,cAAA,gBAA+F;UAC7FD,EAAA,CAAAE,SAAA,aAA+F;UAC/FF,EAAA,CAAA6E,MAAA,oBACF;UAAA7E,EAAA,CAAAG,YAAA,EAAO;UAGPH,EAAA,CAAAE,SAAA,eAA0I;UAC5IF,EAAA,CAAAG,YAAA,EAAS;;;UAhBPH,EAAA,CAAAkF,SAAA,IAAqB;UAArBlF,EAAA,CAAAmF,UAAA,aAAAP,GAAA,CAAAlE,QAAA,CAAqB;UAIdV,EAAA,CAAAkF,SAAA,GAAc;UAAdlF,EAAA,CAAAmF,UAAA,SAAAP,GAAA,CAAAlE,QAAA,CAAc;UAKfV,EAAA,CAAAkF,SAAA,GAA4B;UAA5BlF,EAAA,CAAAoF,WAAA,cAAAR,GAAA,CAAAlE,QAAA,CAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}