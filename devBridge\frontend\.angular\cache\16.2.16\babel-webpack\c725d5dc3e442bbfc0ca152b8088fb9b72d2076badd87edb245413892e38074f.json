{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { parse } from '../language/parser.mjs';\nimport { specifiedDirectives } from '../type/directives.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\nimport { assertValidSDL } from '../validation/validate.mjs';\nimport { extendSchemaImpl } from './extendSchema.mjs';\n\n/**\n * This takes the ast of a schema document produced by the parse function in\n * src/language/parser.js.\n *\n * If no schema definition is provided, then it will look for types named Query,\n * Mutation and Subscription.\n *\n * Given that AST it constructs a GraphQLSchema. The resulting schema\n * has no resolve methods, so execution will use default resolvers.\n */\nexport function buildASTSchema(documentAST, options) {\n  documentAST != null && documentAST.kind === Kind.DOCUMENT || devAssert(false, 'Must provide valid Document AST.');\n  if ((options === null || options === void 0 ? void 0 : options.assumeValid) !== true && (options === null || options === void 0 ? void 0 : options.assumeValidSDL) !== true) {\n    assertValidSDL(documentAST);\n  }\n  const emptySchemaConfig = {\n    description: undefined,\n    types: [],\n    directives: [],\n    extensions: Object.create(null),\n    extensionASTNodes: [],\n    assumeValid: false\n  };\n  const config = extendSchemaImpl(emptySchemaConfig, documentAST, options);\n  if (config.astNode == null) {\n    for (const type of config.types) {\n      switch (type.name) {\n        // Note: While this could make early assertions to get the correctly\n        // typed values below, that would throw immediately while type system\n        // validation with validateSchema() will produce more actionable results.\n        case 'Query':\n          // @ts-expect-error validated in `validateSchema`\n          config.query = type;\n          break;\n        case 'Mutation':\n          // @ts-expect-error validated in `validateSchema`\n          config.mutation = type;\n          break;\n        case 'Subscription':\n          // @ts-expect-error validated in `validateSchema`\n          config.subscription = type;\n          break;\n      }\n    }\n  }\n  const directives = [...config.directives,\n  // If specified directives were not explicitly declared, add them.\n  ...specifiedDirectives.filter(stdDirective => config.directives.every(directive => directive.name !== stdDirective.name))];\n  return new GraphQLSchema({\n    ...config,\n    directives\n  });\n}\n/**\n * A helper function to build a GraphQLSchema directly from a source\n * document.\n */\n\nexport function buildSchema(source, options) {\n  const document = parse(source, {\n    noLocation: options === null || options === void 0 ? void 0 : options.noLocation,\n    allowLegacyFragmentVariables: options === null || options === void 0 ? void 0 : options.allowLegacyFragmentVariables\n  });\n  return buildASTSchema(document, {\n    assumeValidSDL: options === null || options === void 0 ? void 0 : options.assumeValidSDL,\n    assumeValid: options === null || options === void 0 ? void 0 : options.assumeValid\n  });\n}", "map": {"version": 3, "names": ["devAssert", "Kind", "parse", "specifiedDirectives", "GraphQLSchema", "assertValidSDL", "extendSchemaImpl", "buildASTSchema", "documentAST", "options", "kind", "DOCUMENT", "<PERSON><PERSON><PERSON><PERSON>", "assumeValidSDL", "emptySchemaConfig", "description", "undefined", "types", "directives", "extensions", "Object", "create", "extensionASTNodes", "config", "astNode", "type", "name", "query", "mutation", "subscription", "filter", "stdDirective", "every", "directive", "buildSchema", "source", "document", "noLocation", "allowLegacyFragmentVariables"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/utilities/buildASTSchema.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { parse } from '../language/parser.mjs';\nimport { specifiedDirectives } from '../type/directives.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\nimport { assertValidSDL } from '../validation/validate.mjs';\nimport { extendSchemaImpl } from './extendSchema.mjs';\n\n/**\n * This takes the ast of a schema document produced by the parse function in\n * src/language/parser.js.\n *\n * If no schema definition is provided, then it will look for types named Query,\n * Mutation and Subscription.\n *\n * Given that AST it constructs a GraphQLSchema. The resulting schema\n * has no resolve methods, so execution will use default resolvers.\n */\nexport function buildASTSchema(documentAST, options) {\n  (documentAST != null && documentAST.kind === Kind.DOCUMENT) ||\n    devAssert(false, 'Must provide valid Document AST.');\n\n  if (\n    (options === null || options === void 0 ? void 0 : options.assumeValid) !==\n      true &&\n    (options === null || options === void 0\n      ? void 0\n      : options.assumeValidSDL) !== true\n  ) {\n    assertValidSDL(documentAST);\n  }\n\n  const emptySchemaConfig = {\n    description: undefined,\n    types: [],\n    directives: [],\n    extensions: Object.create(null),\n    extensionASTNodes: [],\n    assumeValid: false,\n  };\n  const config = extendSchemaImpl(emptySchemaConfig, documentAST, options);\n\n  if (config.astNode == null) {\n    for (const type of config.types) {\n      switch (type.name) {\n        // Note: While this could make early assertions to get the correctly\n        // typed values below, that would throw immediately while type system\n        // validation with validateSchema() will produce more actionable results.\n        case 'Query':\n          // @ts-expect-error validated in `validateSchema`\n          config.query = type;\n          break;\n\n        case 'Mutation':\n          // @ts-expect-error validated in `validateSchema`\n          config.mutation = type;\n          break;\n\n        case 'Subscription':\n          // @ts-expect-error validated in `validateSchema`\n          config.subscription = type;\n          break;\n      }\n    }\n  }\n\n  const directives = [\n    ...config.directives, // If specified directives were not explicitly declared, add them.\n    ...specifiedDirectives.filter((stdDirective) =>\n      config.directives.every(\n        (directive) => directive.name !== stdDirective.name,\n      ),\n    ),\n  ];\n  return new GraphQLSchema({ ...config, directives });\n}\n/**\n * A helper function to build a GraphQLSchema directly from a source\n * document.\n */\n\nexport function buildSchema(source, options) {\n  const document = parse(source, {\n    noLocation:\n      options === null || options === void 0 ? void 0 : options.noLocation,\n    allowLegacyFragmentVariables:\n      options === null || options === void 0\n        ? void 0\n        : options.allowLegacyFragmentVariables,\n  });\n  return buildASTSchema(document, {\n    assumeValidSDL:\n      options === null || options === void 0 ? void 0 : options.assumeValidSDL,\n    assumeValid:\n      options === null || options === void 0 ? void 0 : options.assumeValid,\n  });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,gBAAgB,QAAQ,oBAAoB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAClDD,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,IAAI,KAAKT,IAAI,CAACU,QAAQ,IACxDX,SAAS,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAEtD,IACE,CAACS,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,WAAW,MACpE,IAAI,IACN,CAACH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GACnC,KAAK,CAAC,GACNA,OAAO,CAACI,cAAc,MAAM,IAAI,EACpC;IACAR,cAAc,CAACG,WAAW,CAAC;EAC7B;EAEA,MAAMM,iBAAiB,GAAG;IACxBC,WAAW,EAAEC,SAAS;IACtBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAEC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/BC,iBAAiB,EAAE,EAAE;IACrBV,WAAW,EAAE;EACf,CAAC;EACD,MAAMW,MAAM,GAAGjB,gBAAgB,CAACQ,iBAAiB,EAAEN,WAAW,EAAEC,OAAO,CAAC;EAExE,IAAIc,MAAM,CAACC,OAAO,IAAI,IAAI,EAAE;IAC1B,KAAK,MAAMC,IAAI,IAAIF,MAAM,CAACN,KAAK,EAAE;MAC/B,QAAQQ,IAAI,CAACC,IAAI;QACf;QACA;QACA;QACA,KAAK,OAAO;UACV;UACAH,MAAM,CAACI,KAAK,GAAGF,IAAI;UACnB;QAEF,KAAK,UAAU;UACb;UACAF,MAAM,CAACK,QAAQ,GAAGH,IAAI;UACtB;QAEF,KAAK,cAAc;UACjB;UACAF,MAAM,CAACM,YAAY,GAAGJ,IAAI;UAC1B;MACJ;IACF;EACF;EAEA,MAAMP,UAAU,GAAG,CACjB,GAAGK,MAAM,CAACL,UAAU;EAAE;EACtB,GAAGf,mBAAmB,CAAC2B,MAAM,CAAEC,YAAY,IACzCR,MAAM,CAACL,UAAU,CAACc,KAAK,CACpBC,SAAS,IAAKA,SAAS,CAACP,IAAI,KAAKK,YAAY,CAACL,IACjD,CACF,CAAC,CACF;EACD,OAAO,IAAItB,aAAa,CAAC;IAAE,GAAGmB,MAAM;IAAEL;EAAW,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASgB,WAAWA,CAACC,MAAM,EAAE1B,OAAO,EAAE;EAC3C,MAAM2B,QAAQ,GAAGlC,KAAK,CAACiC,MAAM,EAAE;IAC7BE,UAAU,EACR5B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC4B,UAAU;IACtEC,4BAA4B,EAC1B7B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAClC,KAAK,CAAC,GACNA,OAAO,CAAC6B;EAChB,CAAC,CAAC;EACF,OAAO/B,cAAc,CAAC6B,QAAQ,EAAE;IAC9BvB,cAAc,EACZJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,cAAc;IAC1ED,WAAW,EACTH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG;EAC9D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}