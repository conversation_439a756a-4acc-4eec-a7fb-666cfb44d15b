/* Modern styles for the teams component */

/* Custom animations and styles for the modern teams interface */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(79, 95, 173, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(79, 95, 173, 0.5);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Card hover effects */
.team-card {
  animation: fadeInUp 0.5s ease-out;
}

.team-card:hover {
  animation: float 2s ease-in-out infinite;
}

/* Loading spinner enhancement */
.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Text truncation for descriptions */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for modal */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(79, 95, 173, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 95, 173, 0.5);
}

/* Dark mode scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 247, 255, 0.3);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 247, 255, 0.5);
}

/* Button shine effect */
.btn-shine {
  position: relative;
  overflow: hidden;
}

.btn-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-shine:hover::before {
  left: 100%;
}

/* Focus states for accessibility */
.focus-ring:focus {
  outline: 2px solid #4f5fad;
  outline-offset: 2px;
}

.dark .focus-ring:focus {
  outline-color: #00f7ff;
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
  .team-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .team-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .team-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Animation delays for staggered effects */
.team-card:nth-child(1) { animation-delay: 0.1s; }
.team-card:nth-child(2) { animation-delay: 0.2s; }
.team-card:nth-child(3) { animation-delay: 0.3s; }
.team-card:nth-child(4) { animation-delay: 0.4s; }
.team-card:nth-child(5) { animation-delay: 0.5s; }
.team-card:nth-child(6) { animation-delay: 0.6s; }

/* Modal backdrop blur effect */
.modal-backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Improved form input states */
.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.15);
}

.dark .form-input:focus {
  box-shadow: 0 4px 12px rgba(0, 247, 255, 0.15);
}

/* Success and error states */
.input-success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.input-error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Notification styles */
.notification-enter {
  animation: slideInRight 0.3s ease-out;
}

.notification-leave {
  animation: slideInRight 0.3s ease-out reverse;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .team-card {
    border: 2px solid;
  }

  .btn-primary {
    border: 2px solid;
  }
}
