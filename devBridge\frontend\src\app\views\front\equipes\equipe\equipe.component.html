<div class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-[10%] left-[5%] w-96 h-96 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"></div>
    <div class="absolute bottom-[10%] right-[5%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#7826b5]/5 to-transparent dark:from-[#4f5fad]/3 dark:to-transparent blur-3xl"></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.02]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Header Section -->
    <div class="mb-8">
      <div class="text-center mb-6">
        <h1 class="text-4xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-3 tracking-wide">
          <i class="fas fa-users mr-3"></i>
          Gestion des Équipes
        </h1>
        <p class="text-[#6d6870] dark:text-[#a0a0a0] text-lg max-w-2xl mx-auto">
          Créez, gérez et organisez vos équipes de travail en toute simplicité
        </p>
      </div>
    </div>

    <!-- Error Message -->
    <div *ngIf="error" class="mb-6">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 flex items-start">
        <div class="text-red-500 dark:text-red-400 mr-3 mt-0.5">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="flex-1">
          <h3 class="font-semibold text-red-800 dark:text-red-300 mb-1">Erreur</h3>
          <p class="text-red-700 dark:text-red-400 text-sm">{{ error }}</p>
        </div>
        <button
          (click)="error = ''"
          class="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 ml-3"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div *ngIf="loading" class="flex justify-center items-center py-12">
      <div class="relative">
        <div class="w-16 h-16 border-4 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-full"></div>
        <div class="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin"></div>
      </div>
    </div>

    <!-- Action Bar -->
    <div class="mb-8">
      <div class="bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 p-6">
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div class="flex items-center">
            <h2 class="text-2xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mr-4">
              Équipes
            </h2>
            <span class="bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium">
              {{ equipes.length }} équipe{{ equipes.length !== 1 ? 's' : '' }}
            </span>
          </div>

          <div class="flex items-center gap-3">
            <button
              (click)="loadEquipes()"
              [disabled]="loading"
              class="flex items-center px-4 py-2 bg-[#6d6870]/10 dark:bg-[#e0e0e0]/10 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] rounded-xl font-medium transition-all duration-300 hover:scale-105 border border-[#6d6870]/20 dark:border-[#e0e0e0]/20 hover:border-[#4f5fad]/40 dark:hover:border-[#00f7ff]/40"
            >
              <i class="fas fa-sync-alt mr-2" [class.animate-spin]="loading"></i>
              Rafraîchir
            </button>

            <button
              routerLink="/equipes/creation"
              class="flex items-center px-6 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)]"
            >
              <i class="fas fa-plus mr-2"></i>
              Nouvelle Équipe
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Teams Grid -->
    <div class="mb-8">
      <!-- Empty State -->
      <div *ngIf="equipes.length === 0 && !loading" class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-full flex items-center justify-center">
          <i class="fas fa-users text-3xl text-[#4f5fad] dark:text-[#00f7ff]"></i>
        </div>
        <h3 class="text-xl font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-3">
          Aucune équipe trouvée
        </h3>
        <p class="text-[#6d6870] dark:text-[#a0a0a0] mb-6 max-w-md mx-auto">
          Commencez par créer votre première équipe pour organiser votre travail collaboratif.
        </p>
        <button
          routerLink="/equipes/creation"
          class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg"
        >
          <i class="fas fa-plus mr-2"></i>
          Créer ma première équipe
        </button>
      </div>

      <!-- Teams Cards -->
      <div *ngIf="equipes.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          *ngFor="let equipe of equipes; trackBy: trackByEquipeId"
          class="group bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl dark:hover:shadow-[0_25px_60px_rgba(0,0,0,0.4)]"
        >
          <!-- Card Header -->
          <div class="relative">
            <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]"></div>
            <div class="p-6 pb-4">
              <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                  <h3 class="text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 group-hover:text-[#7826b5] dark:group-hover:text-[#4f5fad] transition-colors">
                    {{ equipe.name }}
                  </h3>
                  <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm line-clamp-2">
                    {{ equipe.description || 'Aucune description disponible' }}
                  </p>
                </div>
                <div class="ml-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-xl flex items-center justify-center">
                    <i class="fas fa-users text-[#4f5fad] dark:text-[#00f7ff]"></i>
                  </div>
                </div>
              </div>

              <!-- Team Stats -->
              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center text-[#6d6870] dark:text-[#a0a0a0]">
                  <i class="fas fa-user-tie mr-2"></i>
                  <span>Admin: {{ equipe.admin || 'Non défini' }}</span>
                </div>
                <div class="flex items-center text-[#6d6870] dark:text-[#a0a0a0]">
                  <i class="fas fa-users mr-2"></i>
                  <span>{{ equipe.members?.length || 0 }} membre{{ (equipe.members?.length || 0) !== 1 ? 's' : '' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Card Actions -->
          <div class="px-6 pb-6">
            <div class="flex items-center gap-2">
              <button
                (click)="editEquipe(equipe)"
                class="flex-1 flex items-center justify-center px-3 py-2 bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] hover:bg-[#4f5fad]/20 dark:hover:bg-[#00f7ff]/20 rounded-lg font-medium transition-all duration-200 text-sm"
              >
                <i class="fas fa-edit mr-2"></i>
                Modifier
              </button>

              <button
                (click)="showMembreModal(equipe)"
                class="flex-1 flex items-center justify-center px-3 py-2 bg-[#7826b5]/10 dark:bg-[#4f5fad]/10 text-[#7826b5] dark:text-[#4f5fad] hover:bg-[#7826b5]/20 dark:hover:bg-[#4f5fad]/20 rounded-lg font-medium transition-all duration-200 text-sm"
              >
                <i class="fas fa-users-cog mr-2"></i>
                Membres
              </button>

              <button
                (click)="equipe._id && deleteEquipe(equipe._id)"
                class="px-3 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200"
                title="Supprimer l'équipe"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modern Modal for Member Management -->
    <div
      *ngIf="selectedEquipe"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      id="membreModal"
      [class.hidden]="!showMemberModal"
      (click)="closeMemberModal($event)"
    >
      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-2xl dark:shadow-[0_25px_60px_rgba(0,0,0,0.4)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 w-full max-w-2xl max-h-[90vh] overflow-hidden"
        (click)="$event.stopPropagation()"
      >

        <!-- Modal Header -->
        <div class="relative">
          <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]"></div>
          <div class="p-6 pb-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-users-cog text-[#4f5fad] dark:text-[#00f7ff]"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-1">
                    Gestion des Membres
                  </h3>
                  <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm">
                    Équipe: {{ selectedEquipe.name }}
                  </p>
                </div>
              </div>

              <button
                (click)="closeMemberModal()"
                class="text-[#6d6870] dark:text-[#a0a0a0] hover:text-red-500 dark:hover:text-red-400 transition-colors p-2"
                title="Fermer"
              >
                <i class="fas fa-times text-lg"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Modal Content -->
        <div class="px-6 pb-6 max-h-[calc(90vh-120px)] overflow-y-auto">

          <!-- Current Members Section -->
          <div class="mb-8">
            <h4 class="text-lg font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-4 flex items-center">
              <i class="fas fa-users mr-2"></i>
              Membres Actuels
              <span class="ml-2 bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-1 rounded-full text-sm font-medium">
                {{ selectedEquipe.members?.length || 0 }}
              </span>
            </h4>

            <!-- Members List -->
            <div *ngIf="selectedEquipe.members && selectedEquipe.members.length > 0; else noMembers" class="space-y-3">
              <div
                *ngFor="let membreId of selectedEquipe.members; trackBy: trackByMemberId"
                class="flex items-center justify-between p-4 bg-[#f8f9fa] dark:bg-[#2a2a2a] rounded-xl border border-[#e0e0e0] dark:border-[#404040]"
              >
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-full flex items-center justify-center mr-3">
                    <i class="fas fa-user text-[#4f5fad] dark:text-[#00f7ff] text-sm"></i>
                  </div>
                  <div>
                    <p class="font-medium text-[#2c3e50] dark:text-[#e0e0e0]">{{ membreId }}</p>
                    <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">Membre de l'équipe</p>
                  </div>
                </div>

                <button
                  (click)="removeMembreFromEquipe(selectedEquipe._id, getMemberId(membreId))"
                  class="px-3 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200 text-sm font-medium"
                  title="Retirer ce membre"
                >
                  <i class="fas fa-user-minus mr-1"></i>
                  Retirer
                </button>
              </div>
            </div>

            <ng-template #noMembers>
              <div class="text-center py-8">
                <div class="w-16 h-16 mx-auto mb-4 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-full flex items-center justify-center">
                  <i class="fas fa-user-slash text-[#6d6870] dark:text-[#a0a0a0] text-xl"></i>
                </div>
                <p class="text-[#6d6870] dark:text-[#a0a0a0] font-medium">Aucun membre dans cette équipe</p>
                <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm mt-1">Ajoutez des membres pour commencer à collaborer</p>
              </div>
            </ng-template>
          </div>

          <!-- Add Member Section -->
          <div class="mb-6">
            <h4 class="text-lg font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-4 flex items-center">
              <i class="fas fa-user-plus mr-2"></i>
              Ajouter un Membre
            </h4>

            <div class="space-y-4">
              <div class="flex gap-3">
                <div class="flex-1 relative">
                  <input
                    #membreIdInput
                    type="text"
                    placeholder="ID du membre à ajouter"
                    class="w-full px-4 py-3 bg-[#f8f9fa] dark:bg-[#2a2a2a] border border-[#e0e0e0] dark:border-[#404040] rounded-xl text-[#2c3e50] dark:text-[#e0e0e0] placeholder-[#6c757d] dark:placeholder-[#888] focus:outline-none focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all duration-200"
                  />
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <i class="fas fa-user text-[#6d6870] dark:text-[#a0a0a0]"></i>
                  </div>
                </div>

                <button
                  (click)="addMembreToEquipe(selectedEquipe._id, membreIdInput.value); membreIdInput.value = ''"
                  [disabled]="!selectedEquipe || !selectedEquipe._id || !membreIdInput.value"
                  class="px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <i class="fas fa-plus mr-2"></i>
                  Ajouter
                </button>
              </div>

              <div class="bg-[#4f5fad]/5 dark:bg-[#00f7ff]/5 rounded-xl p-4 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20">
                <div class="flex items-start">
                  <div class="text-[#4f5fad] dark:text-[#00f7ff] mr-3 mt-0.5">
                    <i class="fas fa-info-circle"></i>
                  </div>
                  <div class="flex-1">
                    <h5 class="font-semibold text-[#4f5fad] dark:text-[#00f7ff] text-sm mb-1">
                      Information importante
                    </h5>
                    <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm">
                      Pour ajouter un membre, vous devez d'abord créer le membre dans la section des membres.
                      Utilisez l'ID exact du membre pour l'ajouter à cette équipe.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Modal Actions -->
          <div class="flex justify-end pt-4 border-t border-[#e0e0e0] dark:border-[#404040]">
            <button
              (click)="closeMemberModal()"
              class="px-6 py-3 bg-[#6d6870]/10 dark:bg-[#e0e0e0]/10 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] rounded-xl font-medium transition-all duration-200 border border-[#6d6870]/20 dark:border-[#e0e0e0]/20"
            >
              <i class="fas fa-times mr-2"></i>
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
