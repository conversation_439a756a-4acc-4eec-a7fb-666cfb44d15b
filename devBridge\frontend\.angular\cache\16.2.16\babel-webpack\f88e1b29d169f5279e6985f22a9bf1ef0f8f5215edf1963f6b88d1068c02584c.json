{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isNonNullType } from '../../type/definition.mjs';\nimport { isTypeSubTypeOf } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables in allowed position\n *\n * Variable usages must be compatible with the arguments they are passed to.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Usages-are-Allowed\n */\nexport function VariablesInAllowedPositionRule(context) {\n  let varDefMap = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        varDefMap = Object.create(null);\n      },\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n        for (const {\n          node,\n          type,\n          defaultValue\n        } of usages) {\n          const varName = node.name.value;\n          const varDef = varDefMap[varName];\n          if (varDef && type) {\n            // A var type is allowed if it is the same or more strict (e.g. is\n            // a subtype of) than the expected type. It can be more strict if\n            // the variable type is non-null when the expected type is nullable.\n            // If both are list types, the variable item type can be more strict\n            // than the expected item type (contravariant).\n            const schema = context.getSchema();\n            const varType = typeFromAST(schema, varDef.type);\n            if (varType && !allowedVariableUsage(schema, varType, varDef.defaultValue, type, defaultValue)) {\n              const varTypeStr = inspect(varType);\n              const typeStr = inspect(type);\n              context.reportError(new GraphQLError(`Variable \"$${varName}\" of type \"${varTypeStr}\" used in position expecting type \"${typeStr}\".`, {\n                nodes: [varDef, node]\n              }));\n            }\n          }\n        }\n      }\n    },\n    VariableDefinition(node) {\n      varDefMap[node.variable.name.value] = node;\n    }\n  };\n}\n/**\n * Returns true if the variable is allowed in the location it was found,\n * which includes considering if default values exist for either the variable\n * or the location at which it is located.\n */\n\nfunction allowedVariableUsage(schema, varType, varDefaultValue, locationType, locationDefaultValue) {\n  if (isNonNullType(locationType) && !isNonNullType(varType)) {\n    const hasNonNullVariableDefaultValue = varDefaultValue != null && varDefaultValue.kind !== Kind.NULL;\n    const hasLocationDefaultValue = locationDefaultValue !== undefined;\n    if (!hasNonNullVariableDefaultValue && !hasLocationDefaultValue) {\n      return false;\n    }\n    const nullableLocationType = locationType.ofType;\n    return isTypeSubTypeOf(schema, varType, nullableLocationType);\n  }\n  return isTypeSubTypeOf(schema, varType, locationType);\n}", "map": {"version": 3, "names": ["inspect", "GraphQLError", "Kind", "isNonNullType", "isTypeSubTypeOf", "typeFromAST", "VariablesInAllowedPositionRule", "context", "varDefMap", "Object", "create", "OperationDefinition", "enter", "leave", "operation", "usages", "getRecursiveVariableUsages", "node", "type", "defaultValue", "varName", "name", "value", "varDef", "schema", "getSchema", "varType", "allowedVariableUsage", "varTypeStr", "typeStr", "reportError", "nodes", "VariableDefinition", "variable", "varDefaultValue", "locationType", "locationDefaultValue", "hasNonNullVariableDefaultValue", "kind", "NULL", "hasLocationDefaultValue", "undefined", "nullableLocationType", "ofType"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/VariablesInAllowedPositionRule.mjs"], "sourcesContent": ["import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isNonNullType } from '../../type/definition.mjs';\nimport { isTypeSubTypeOf } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables in allowed position\n *\n * Variable usages must be compatible with the arguments they are passed to.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Usages-are-Allowed\n */\nexport function VariablesInAllowedPositionRule(context) {\n  let varDefMap = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        varDefMap = Object.create(null);\n      },\n\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n\n        for (const { node, type, defaultValue } of usages) {\n          const varName = node.name.value;\n          const varDef = varDefMap[varName];\n\n          if (varDef && type) {\n            // A var type is allowed if it is the same or more strict (e.g. is\n            // a subtype of) than the expected type. It can be more strict if\n            // the variable type is non-null when the expected type is nullable.\n            // If both are list types, the variable item type can be more strict\n            // than the expected item type (contravariant).\n            const schema = context.getSchema();\n            const varType = typeFromAST(schema, varDef.type);\n\n            if (\n              varType &&\n              !allowedVariableUsage(\n                schema,\n                varType,\n                varDef.defaultValue,\n                type,\n                defaultValue,\n              )\n            ) {\n              const varTypeStr = inspect(varType);\n              const typeStr = inspect(type);\n              context.reportError(\n                new GraphQLError(\n                  `Variable \"$${varName}\" of type \"${varTypeStr}\" used in position expecting type \"${typeStr}\".`,\n                  {\n                    nodes: [varDef, node],\n                  },\n                ),\n              );\n            }\n          }\n        }\n      },\n    },\n\n    VariableDefinition(node) {\n      varDefMap[node.variable.name.value] = node;\n    },\n  };\n}\n/**\n * Returns true if the variable is allowed in the location it was found,\n * which includes considering if default values exist for either the variable\n * or the location at which it is located.\n */\n\nfunction allowedVariableUsage(\n  schema,\n  varType,\n  varDefaultValue,\n  locationType,\n  locationDefaultValue,\n) {\n  if (isNonNullType(locationType) && !isNonNullType(varType)) {\n    const hasNonNullVariableDefaultValue =\n      varDefaultValue != null && varDefaultValue.kind !== Kind.NULL;\n    const hasLocationDefaultValue = locationDefaultValue !== undefined;\n\n    if (!hasNonNullVariableDefaultValue && !hasLocationDefaultValue) {\n      return false;\n    }\n\n    const nullableLocationType = locationType.ofType;\n    return isTypeSubTypeOf(schema, varType, nullableLocationType);\n  }\n\n  return isTypeSubTypeOf(schema, varType, locationType);\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,eAAe,QAAQ,qCAAqC;AACrE,SAASC,WAAW,QAAQ,iCAAiC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,8BAA8BA,CAACC,OAAO,EAAE;EACtD,IAAIC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACnC,OAAO;IACLC,mBAAmB,EAAE;MACnBC,KAAKA,CAAA,EAAG;QACNJ,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACjC,CAAC;MAEDG,KAAKA,CAACC,SAAS,EAAE;QACf,MAAMC,MAAM,GAAGR,OAAO,CAACS,0BAA0B,CAACF,SAAS,CAAC;QAE5D,KAAK,MAAM;UAAEG,IAAI;UAAEC,IAAI;UAAEC;QAAa,CAAC,IAAIJ,MAAM,EAAE;UACjD,MAAMK,OAAO,GAAGH,IAAI,CAACI,IAAI,CAACC,KAAK;UAC/B,MAAMC,MAAM,GAAGf,SAAS,CAACY,OAAO,CAAC;UAEjC,IAAIG,MAAM,IAAIL,IAAI,EAAE;YAClB;YACA;YACA;YACA;YACA;YACA,MAAMM,MAAM,GAAGjB,OAAO,CAACkB,SAAS,CAAC,CAAC;YAClC,MAAMC,OAAO,GAAGrB,WAAW,CAACmB,MAAM,EAAED,MAAM,CAACL,IAAI,CAAC;YAEhD,IACEQ,OAAO,IACP,CAACC,oBAAoB,CACnBH,MAAM,EACNE,OAAO,EACPH,MAAM,CAACJ,YAAY,EACnBD,IAAI,EACJC,YACF,CAAC,EACD;cACA,MAAMS,UAAU,GAAG5B,OAAO,CAAC0B,OAAO,CAAC;cACnC,MAAMG,OAAO,GAAG7B,OAAO,CAACkB,IAAI,CAAC;cAC7BX,OAAO,CAACuB,WAAW,CACjB,IAAI7B,YAAY,CACb,cAAamB,OAAQ,cAAaQ,UAAW,sCAAqCC,OAAQ,IAAG,EAC9F;gBACEE,KAAK,EAAE,CAACR,MAAM,EAAEN,IAAI;cACtB,CACF,CACF,CAAC;YACH;UACF;QACF;MACF;IACF,CAAC;IAEDe,kBAAkBA,CAACf,IAAI,EAAE;MACvBT,SAAS,CAACS,IAAI,CAACgB,QAAQ,CAACZ,IAAI,CAACC,KAAK,CAAC,GAAGL,IAAI;IAC5C;EACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASU,oBAAoBA,CAC3BH,MAAM,EACNE,OAAO,EACPQ,eAAe,EACfC,YAAY,EACZC,oBAAoB,EACpB;EACA,IAAIjC,aAAa,CAACgC,YAAY,CAAC,IAAI,CAAChC,aAAa,CAACuB,OAAO,CAAC,EAAE;IAC1D,MAAMW,8BAA8B,GAClCH,eAAe,IAAI,IAAI,IAAIA,eAAe,CAACI,IAAI,KAAKpC,IAAI,CAACqC,IAAI;IAC/D,MAAMC,uBAAuB,GAAGJ,oBAAoB,KAAKK,SAAS;IAElE,IAAI,CAACJ,8BAA8B,IAAI,CAACG,uBAAuB,EAAE;MAC/D,OAAO,KAAK;IACd;IAEA,MAAME,oBAAoB,GAAGP,YAAY,CAACQ,MAAM;IAChD,OAAOvC,eAAe,CAACoB,MAAM,EAAEE,OAAO,EAAEgB,oBAAoB,CAAC;EAC/D;EAEA,OAAOtC,eAAe,CAACoB,MAAM,EAAEE,OAAO,EAAES,YAAY,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}