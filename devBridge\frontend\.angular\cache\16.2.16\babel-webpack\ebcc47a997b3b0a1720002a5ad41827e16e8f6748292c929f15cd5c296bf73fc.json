{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfDecade} function options.\n */\n\n/**\n * @name endOfDecade\n * @category Decade Helpers\n * @summary Return the end of a decade for the given date.\n *\n * @description\n * Return the end of a decade for the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a decade\n *\n * @example\n * // The end of a decade for 12 May 1984 00:00:00:\n * const result = endOfDecade(new Date(1984, 4, 12, 00, 00, 00))\n * //=> Dec 31 1989 23:59:59.999\n */\nexport function endOfDecade(date, options) {\n  // TODO: Switch to more technical definition in of decades that start with 1\n  // end with 0. I.e. 2001-2010 instead of current 2000-2009. It's a breaking\n  // change, so it can only be done in 4.0.\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = 9 + Math.floor(year / 10) * 10;\n  _date.setFullYear(decade, 11, 31);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfDecade;", "map": {"version": 3, "names": ["toDate", "endOfDecade", "date", "options", "_date", "in", "year", "getFullYear", "decade", "Math", "floor", "setFullYear", "setHours"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/endOfDecade.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfDecade} function options.\n */\n\n/**\n * @name endOfDecade\n * @category Decade Helpers\n * @summary Return the end of a decade for the given date.\n *\n * @description\n * Return the end of a decade for the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a decade\n *\n * @example\n * // The end of a decade for 12 May 1984 00:00:00:\n * const result = endOfDecade(new Date(1984, 4, 12, 00, 00, 00))\n * //=> Dec 31 1989 23:59:59.999\n */\nexport function endOfDecade(date, options) {\n  // TODO: Switch to more technical definition in of decades that start with 1\n  // end with 0. I.e. 2001-2010 instead of current 2000-2009. It's a breaking\n  // change, so it can only be done in 4.0.\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = 9 + Math.floor(year / 10) * 10;\n  _date.setFullYear(decade, 11, 31);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfDecade;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC;EACA;EACA;EACA,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChC,MAAMC,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE;EAC7CF,KAAK,CAACO,WAAW,CAACH,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC;EACjCJ,KAAK,CAACQ,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC/B,OAAOR,KAAK;AACd;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}