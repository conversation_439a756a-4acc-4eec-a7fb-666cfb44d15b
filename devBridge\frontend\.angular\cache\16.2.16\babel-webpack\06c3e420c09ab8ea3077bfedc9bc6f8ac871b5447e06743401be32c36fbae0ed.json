{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction EquipeComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58)(2, \"div\", 59);\n    i0.ɵɵelement(3, \"i\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 61)(5, \"h3\", 62);\n    i0.ɵɵtext(6, \"Erreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 63);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_25_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.error = \"\");\n    });\n    i0.ɵɵelement(10, \"i\", 65);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.error);\n  }\n}\nfunction EquipeComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67);\n    i0.ɵɵelement(2, \"div\", 68)(3, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 73);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 74);\n    i0.ɵɵtext(6, \" Commencez par cr\\u00E9er votre premi\\u00E8re \\u00E9quipe pour organiser votre travail collaboratif. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_43_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.scrollToForm());\n    });\n    i0.ɵɵelement(8, \"i\", 24);\n    i0.ɵɵtext(9, \" Cr\\u00E9er ma premi\\u00E8re \\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_44_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 67);\n    i0.ɵɵelement(2, \"div\", 79);\n    i0.ɵɵelementStart(3, \"div\", 80)(4, \"div\", 81)(5, \"div\", 61)(6, \"h3\", 82);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 83);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 84)(11, \"div\", 85);\n    i0.ɵɵelement(12, \"i\", 86);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 87)(14, \"div\", 88);\n    i0.ɵɵelement(15, \"i\", 89);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 88);\n    i0.ɵɵelement(19, \"i\", 90);\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\", 91)(23, \"div\", 92)(24, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_44_div_1_Template_button_click_24_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const equipe_r14 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.editEquipe(equipe_r14));\n    });\n    i0.ɵɵelement(25, \"i\", 94);\n    i0.ɵɵtext(26, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_44_div_1_Template_button_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const equipe_r14 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.showMembreModal(equipe_r14));\n    });\n    i0.ɵɵelement(28, \"i\", 96);\n    i0.ɵɵtext(29, \" Membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_44_div_1_Template_button_click_30_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const equipe_r14 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r18.deleteEquipe(equipe_r14._id));\n    });\n    i0.ɵɵelement(31, \"i\", 98);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r14 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.description || \"Aucune description disponible\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Admin: \", equipe_r14.admin || \"Non d\\u00E9fini\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", (equipe_r14.members == null ? null : equipe_r14.members.length) || 0, \" membre\", ((equipe_r14.members == null ? null : equipe_r14.members.length) || 0) !== 1 ? \"s\" : \"\", \"\");\n  }\n}\nfunction EquipeComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtemplate(1, EquipeComponent_div_44_div_1_Template, 32, 5, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes)(\"ngForTrackBy\", ctx_r3.trackByEquipeId);\n  }\n}\nfunction EquipeComponent_span_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 99);\n  }\n}\nfunction EquipeComponent_button_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_button_73_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.cancelEdit());\n    });\n    i0.ɵɵtext(1, \" Annuler \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_82_div_6_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 110)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_82_div_6_li_2_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const membreId_r26 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.removeMembreFromEquipe(ctx_r27.selectedEquipe._id, ctx_r27.getMemberId(membreId_r26)));\n    });\n    i0.ɵɵtext(4, \" Retirer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const membreId_r26 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(membreId_r26);\n  }\n}\nfunction EquipeComponent_div_82_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 108);\n    i0.ɵɵtemplate(2, EquipeComponent_div_82_div_6_li_2_Template, 5, 1, \"li\", 109);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.selectedEquipe.members);\n  }\n}\nfunction EquipeComponent_div_82_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 42);\n    i0.ɵɵtext(1, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"h6\");\n    i0.ɵɵtext(5, \"Membres actuels:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EquipeComponent_div_82_div_6_Template, 3, 1, \"div\", 101);\n    i0.ɵɵtemplate(7, EquipeComponent_div_82_ng_template_7_Template, 2, 0, \"ng-template\", null, 102, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"h6\");\n    i0.ɵɵtext(11, \"Ajouter un membre:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 103);\n    i0.ɵɵelement(13, \"input\", 104, 105);\n    i0.ɵɵelementStart(15, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_82_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const _r24 = i0.ɵɵreference(14);\n      const ctx_r29 = i0.ɵɵnextContext();\n      ctx_r29.addMembreToEquipe(ctx_r29.selectedEquipe._id, _r24.value);\n      return i0.ɵɵresetView(_r24.value = \"\");\n    });\n    i0.ɵɵtext(16, \" Ajouter \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"small\", 42);\n    i0.ɵɵtext(18, \"Entrez l'ID du membre \\u00E0 ajouter \\u00E0 l'\\u00E9quipe\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 107)(20, \"p\", 31)(21, \"strong\");\n    i0.ɵɵtext(22, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Pour ajouter un membre, vous devez d'abord cr\\u00E9er le membre dans la section des membres. \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r22 = i0.ɵɵreference(8);\n    const _r24 = i0.ɵɵreference(14);\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00C9quipe: \", ctx_r8.selectedEquipe.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.selectedEquipe.members && ctx_r8.selectedEquipe.members.length > 0)(\"ngIfElse\", _r22);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", !ctx_r8.selectedEquipe || !ctx_r8.selectedEquipe._id || !_r24.value);\n  }\n}\nexport class EquipeComponent {\n  constructor(equipeService, membreService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.equipes = [];\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.selectedEquipe = null;\n    this.isEditing = false;\n    this.membres = [];\n    this.loading = false;\n    this.error = '';\n  }\n  // Méthode utilitaire pour extraire l'ID d'un membre\n  getMemberId(membre) {\n    if (typeof membre === 'string') {\n      return membre;\n    }\n    return membre._id || membre.id || '';\n  }\n  ngOnInit() {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: data => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: data => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: response => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = {\n          name: '',\n          description: ''\n        }; // Clear input\n        this.loading = false;\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: error => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n  editEquipe(equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: updatedEquipe => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = {\n            name: '',\n            description: ''\n          };\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: error => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: response => {\n          console.log('Team deleted successfully:', response);\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n          }\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: error => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  showMembreModal(equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n  addMembreToEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n    this.loading = true;\n    // Create a proper Membre object that matches what the API expects\n    const membre = {\n      id: membreId\n    };\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: response => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: error => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: response => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: error => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function EquipeComponent_Factory(t) {\n      return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeComponent,\n      selectors: [[\"app-equipe\"]],\n      decls: 86,\n      vars: 19,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-96\", \"h-96\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#7826b5]/5\", \"to-transparent\", \"dark:from-[#4f5fad]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.02]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"relative\", \"z-10\", \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"py-8\"], [1, \"mb-8\"], [1, \"text-center\", \"mb-6\"], [1, \"text-4xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-3\", \"tracking-wide\"], [1, \"fas\", \"fa-users\", \"mr-3\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-lg\", \"max-w-2xl\", \"mx-auto\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"flex justify-center items-center py-12\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-2xl\", \"shadow-lg\", \"dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)]\", \"border\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\", \"p-6\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"justify-between\", \"items-center\", \"gap-4\"], [1, \"flex\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mr-4\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"flex\", \"items-center\", \"gap-3\"], [1, \"flex\", \"items-center\", \"px-4\", \"py-2\", \"bg-[#6d6870]/10\", \"dark:bg-[#e0e0e0]/10\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"border\", \"border-[#6d6870]/20\", \"dark:border-[#e0e0e0]/20\", \"hover:border-[#4f5fad]/40\", \"dark:hover:border-[#00f7ff]/40\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-2\"], [1, \"flex\", \"items-center\", \"px-6\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"name\", \"required\", \"\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [1, \"invalid-feedback\"], [\"for\", \"description\", 1, \"form-label\"], [\"id\", \"description\", \"rows\", \"3\", \"placeholder\", \"Entrez une description pour cette \\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [1, \"text-muted\"], [1, \"d-flex\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-secondary ms-2\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"membreModal\", \"tabindex\", \"-1\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [1, \"mb-6\"], [1, \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-xl\", \"p-4\", \"flex\", \"items-start\"], [1, \"text-red-500\", \"dark:text-red-400\", \"mr-3\", \"mt-0.5\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-red-800\", \"dark:text-red-300\", \"mb-1\"], [1, \"text-red-700\", \"dark:text-red-400\", \"text-sm\"], [1, \"text-red-500\", \"dark:text-red-400\", \"hover:text-red-700\", \"dark:hover:text-red-300\", \"ml-3\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-12\"], [1, \"relative\"], [1, \"w-16\", \"h-16\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-full\"], [1, \"absolute\", \"top-0\", \"left-0\", \"w-16\", \"h-16\", \"border-4\", \"border-transparent\", \"border-t-[#4f5fad]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"text-center\", \"py-16\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"mb-6\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-[#7826b5]/20\", \"dark:from-[#00f7ff]/20\", \"dark:to-[#4f5fad]/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-users\", \"text-3xl\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-3\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-6\", \"max-w-md\", \"mx-auto\"], [1, \"inline-flex\", \"items-center\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", 3, \"click\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"group bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl dark:hover:shadow-[0_25px_60px_rgba(0,0,0,0.4)]\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"group\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-2xl\", \"shadow-lg\", \"dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)]\", \"border\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:scale-[1.02]\", \"hover:shadow-xl\", \"dark:hover:shadow-[0_25px_60px_rgba(0,0,0,0.4)]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"p-6\", \"pb-4\"], [1, \"flex\", \"items-start\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-colors\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"line-clamp-2\"], [1, \"ml-4\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-[#7826b5]/20\", \"dark:from-[#00f7ff]/20\", \"dark:to-[#4f5fad]/20\", \"rounded-xl\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-users\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\"], [1, \"flex\", \"items-center\", \"justify-between\", \"text-sm\"], [1, \"flex\", \"items-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-user-tie\", \"mr-2\"], [1, \"fas\", \"fa-users\", \"mr-2\"], [1, \"px-6\", \"pb-6\"], [1, \"flex\", \"items-center\", \"gap-2\"], [1, \"flex-1\", \"flex\", \"items-center\", \"justify-center\", \"px-3\", \"py-2\", \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#00f7ff]/20\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-200\", \"text-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"flex-1\", \"flex\", \"items-center\", \"justify-center\", \"px-3\", \"py-2\", \"bg-[#7826b5]/10\", \"dark:bg-[#4f5fad]/10\", \"text-[#7826b5]\", \"dark:text-[#4f5fad]\", \"hover:bg-[#7826b5]/20\", \"dark:hover:bg-[#4f5fad]/20\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-200\", \"text-sm\", 3, \"click\"], [1, \"fas\", \"fa-users-cog\", \"mr-2\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"px-3\", \"py-2\", \"bg-red-50\", \"dark:bg-red-900/20\", \"text-red-600\", \"dark:text-red-400\", \"hover:bg-red-100\", \"dark:hover:bg-red-900/30\", \"rounded-lg\", \"transition-all\", \"duration-200\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ms-2\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"input-group\", \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"ID du membre\", 1, \"form-control\"], [\"membreIdInput\", \"\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"alert\", \"alert-info\", \"mt-3\"], [1, \"list-group\"], [\"class\", \"list-group-item d-flex justify-content-between align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"]],\n      template: function EquipeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r31 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\", 9)(20, \"h1\", 10);\n          i0.ɵɵelement(21, \"i\", 11);\n          i0.ɵɵtext(22, \" Gestion des \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 12);\n          i0.ɵɵtext(24, \" Cr\\u00E9ez, g\\u00E9rez et organisez vos \\u00E9quipes de travail en toute simplicit\\u00E9 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(25, EquipeComponent_div_25_Template, 11, 1, \"div\", 13);\n          i0.ɵɵtemplate(26, EquipeComponent_div_26_Template, 4, 0, \"div\", 14);\n          i0.ɵɵelementStart(27, \"div\", 8)(28, \"div\", 15)(29, \"div\", 16)(30, \"div\", 17)(31, \"h2\", 18);\n          i0.ɵɵtext(32, \" Mes \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\", 19);\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 20)(36, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_36_listener() {\n            return ctx.loadEquipes();\n          });\n          i0.ɵɵelement(37, \"i\", 22);\n          i0.ɵɵtext(38, \" Rafra\\u00EEchir \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_39_listener() {\n            return ctx.scrollToForm();\n          });\n          i0.ɵɵelement(40, \"i\", 24);\n          i0.ɵɵtext(41, \" Nouvelle \\u00C9quipe \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"div\", 8);\n          i0.ɵɵtemplate(43, EquipeComponent_div_43_Template, 10, 0, \"div\", 25);\n          i0.ɵɵtemplate(44, EquipeComponent_div_44_Template, 2, 2, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 27)(46, \"div\", 28)(47, \"div\", 29)(48, \"div\", 30)(49, \"h3\", 31);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 32)(52, \"form\")(53, \"div\", 33)(54, \"label\", 34);\n          i0.ɵɵtext(55, \"Nom de l'\\u00E9quipe \");\n          i0.ɵɵelementStart(56, \"span\", 35);\n          i0.ɵɵtext(57, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"input\", 36, 37);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_input_input_58_listener() {\n            i0.ɵɵrestoreView(_r31);\n            const _r4 = i0.ɵɵreference(59);\n            return i0.ɵɵresetView(ctx.newEquipe.name = _r4.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 38);\n          i0.ɵɵtext(61, \"Le nom de l'\\u00E9quipe est requis\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"div\", 33)(63, \"label\", 39);\n          i0.ɵɵtext(64, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"textarea\", 40, 41);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_textarea_input_65_listener() {\n            i0.ɵɵrestoreView(_r31);\n            const _r5 = i0.ɵɵreference(66);\n            return i0.ɵɵresetView(ctx.newEquipe.description = _r5.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"small\", 42);\n          i0.ɵɵtext(68, \"Une br\\u00E8ve description de l'\\u00E9quipe et de son objectif\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 43)(70, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_70_listener() {\n            return ctx.isEditing ? ctx.updateSelectedEquipe() : ctx.addEquipe();\n          });\n          i0.ɵɵtemplate(71, EquipeComponent_span_71_Template, 1, 0, \"span\", 45);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(73, EquipeComponent_button_73_Template, 2, 0, \"button\", 46);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(74, \"div\", 47)(75, \"div\", 48)(76, \"div\", 49)(77, \"div\", 50)(78, \"h5\", 51);\n          i0.ɵɵtext(79, \"G\\u00E9rer les membres de l'\\u00E9quipe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"button\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 53);\n          i0.ɵɵtemplate(82, EquipeComponent_div_82_Template, 24, 4, \"div\", 54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 55)(84, \"button\", 56);\n          i0.ɵɵtext(85, \" Fermer \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate2(\" \", ctx.equipes.length, \" \\u00E9quipe\", ctx.equipes.length !== 1 ? \"s\" : \"\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"animate-spin\", ctx.loading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.equipes.length === 0 && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.equipes.length > 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Modifier une \\u00E9quipe\" : \"Cr\\u00E9er une \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"is-invalid\", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === \"\"));\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.name);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.description || \"\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.newEquipe.name || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipe);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.NgForm],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-radius: 8px 8px 0 0 !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 6px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n\\n.list-group-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.list-group-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeComponent_div_25_Template_button_click_9_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "error", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "EquipeComponent_div_43_Template_button_click_7_listener", "_r12", "ctx_r11", "scrollToForm", "EquipeComponent_div_44_div_1_Template_button_click_24_listener", "restoredCtx", "_r16", "equipe_r14", "$implicit", "ctx_r15", "editEquipe", "EquipeComponent_div_44_div_1_Template_button_click_27_listener", "ctx_r17", "showMembreModal", "EquipeComponent_div_44_div_1_Template_button_click_30_listener", "ctx_r18", "_id", "deleteEquipe", "ɵɵtextInterpolate1", "name", "description", "admin", "ɵɵtextInterpolate2", "members", "length", "ɵɵtemplate", "EquipeComponent_div_44_div_1_Template", "ɵɵproperty", "ctx_r3", "equipes", "trackByEquipeId", "EquipeComponent_button_73_Template_button_click_0_listener", "_r20", "ctx_r19", "cancelEdit", "EquipeComponent_div_82_div_6_li_2_Template_button_click_3_listener", "_r28", "membreId_r26", "ctx_r27", "removeMembreFromEquipe", "selectedEquipe", "getMemberId", "EquipeComponent_div_82_div_6_li_2_Template", "ctx_r21", "EquipeComponent_div_82_div_6_Template", "EquipeComponent_div_82_ng_template_7_Template", "ɵɵtemplateRefExtractor", "EquipeComponent_div_82_Template_button_click_15_listener", "_r30", "_r24", "ɵɵreference", "ctx_r29", "addMembreToEquipe", "value", "ctx_r8", "_r22", "EquipeComponent", "constructor", "equipeService", "membreService", "newEquipe", "isEditing", "membres", "loading", "membre", "id", "ngOnInit", "loadEquipes", "loadMembres", "getEquipes", "subscribe", "next", "data", "console", "log", "message", "getMembres", "addEquipe", "response", "successMessage", "alert", "equipe", "updateSelectedEquipe", "updateEquipe", "updatedEquipe", "confirm", "modalRef", "document", "getElementById", "window", "bootstrap", "modal", "Modal", "show", "teamId", "membreId", "trim", "find", "e", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "selectors", "decls", "vars", "consts", "template", "EquipeComponent_Template", "rf", "ctx", "EquipeComponent_div_25_Template", "EquipeComponent_div_26_Template", "EquipeComponent_Template_button_click_36_listener", "EquipeComponent_Template_button_click_39_listener", "EquipeComponent_div_43_Template", "EquipeComponent_div_44_Template", "EquipeComponent_Template_input_input_58_listener", "_r31", "_r4", "EquipeComponent_Template_textarea_input_65_listener", "_r5", "EquipeComponent_Template_button_click_70_listener", "EquipeComponent_span_71_Template", "EquipeComponent_button_73_Template", "EquipeComponent_div_82_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.ts", "C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { EquipeService } from 'src/app/services/equipe.service';\r\nimport { MembreService } from 'src/app/services/membre.service';\r\nimport { Equipe } from 'src/app/models/equipe.model';\r\nimport { Membre } from 'src/app/models/membre.model';\r\nimport { forkJoin } from 'rxjs';\r\n\r\n// Add Bootstrap type declaration\r\ndeclare global {\r\n  interface Window {\r\n    bootstrap: any;\r\n  }\r\n}\r\n\r\n@Component({\r\n  selector: 'app-equipe',\r\n  templateUrl: './equipe.component.html',\r\n  styleUrls: ['./equipe.component.css'],\r\n})\r\nexport class EquipeComponent implements OnInit {\r\n  equipes: Equipe[] = [];\r\n  newEquipe: Equipe = { name: '', description: '' };\r\n  selectedEquipe: Equipe | null = null;\r\n  isEditing = false;\r\n  membres: Membre[] = [];\r\n  loading = false;\r\n  error = '';\r\n\r\n  constructor(\r\n    private equipeService: EquipeService,\r\n    private membreService: MembreService\r\n  ) {}\r\n\r\n  // Méthode utilitaire pour extraire l'ID d'un membre\r\n  getMemberId(membre: string | any): string {\r\n    if (typeof membre === 'string') {\r\n      return membre;\r\n    }\r\n    return membre._id || membre.id || '';\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadEquipes();\r\n    this.loadMembres();\r\n  }\r\n\r\n  loadEquipes() {\r\n    this.loading = true;\r\n    this.equipeService.getEquipes().subscribe({\r\n      next: (data) => {\r\n        console.log('Loaded equipes:', data);\r\n        this.equipes = data;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading equipes:', error);\r\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadMembres() {\r\n    this.loading = true;\r\n    this.membreService.getMembres().subscribe({\r\n      next: (data) => {\r\n        console.log('Loaded membres:', data);\r\n        this.membres = data;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading membres:', error);\r\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  addEquipe() {\r\n    console.log('Adding equipe:', this.newEquipe);\r\n\r\n    if (!this.newEquipe.name) {\r\n      console.error('Team name is required');\r\n      this.error = 'Le nom de l\\'équipe est requis';\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.error = '';\r\n\r\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\r\n      next: (response) => {\r\n        console.log('Equipe added successfully:', response);\r\n        this.loadEquipes();\r\n        this.newEquipe = { name: '', description: '' }; // Clear input\r\n        this.loading = false;\r\n\r\n        // Afficher un message de succès temporaire\r\n        const successMessage = 'Équipe créée avec succès!';\r\n        this.error = ''; // Effacer les erreurs précédentes\r\n        alert(successMessage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding equipe:', error);\r\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  editEquipe(equipe: Equipe) {\r\n    this.isEditing = true;\r\n    // Créer une copie profonde pour éviter de modifier l'objet original\r\n    this.newEquipe = {\r\n      _id: equipe._id,\r\n      name: equipe.name || '',\r\n      description: equipe.description || '',\r\n      admin: equipe.admin,\r\n      members: equipe.members ? [...equipe.members] : []\r\n    };\r\n  }\r\n\r\n  cancelEdit() {\r\n    this.isEditing = false;\r\n    this.newEquipe = { name: '', description: '' };\r\n    this.error = ''; // Effacer les erreurs\r\n  }\r\n\r\n  updateSelectedEquipe() {\r\n    if (!this.newEquipe.name) {\r\n      console.error('Team name is required');\r\n      this.error = 'Le nom de l\\'équipe est requis';\r\n      return;\r\n    }\r\n\r\n    if (this.newEquipe._id) {\r\n      this.loading = true;\r\n      this.error = '';\r\n\r\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\r\n        next: (updatedEquipe) => {\r\n          console.log('Team updated successfully:', updatedEquipe);\r\n          this.loadEquipes();\r\n          this.isEditing = false;\r\n          this.newEquipe = { name: '', description: '' };\r\n          this.loading = false;\r\n\r\n          // Afficher un message de succès temporaire\r\n          const successMessage = 'Équipe mise à jour avec succès!';\r\n          alert(successMessage);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error updating team:', error);\r\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n          this.loading = false;\r\n        }\r\n      });\r\n    } else {\r\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\r\n    }\r\n  }\r\n\r\n  deleteEquipe(id: string) {\r\n    if (!id) {\r\n      console.error('ID is undefined');\r\n      this.error = 'ID de l\\'équipe non défini';\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\r\n      this.loading = true;\r\n      this.error = '';\r\n\r\n      this.equipeService.deleteEquipe(id).subscribe({\r\n        next: (response) => {\r\n          console.log('Team deleted successfully:', response);\r\n\r\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\r\n          if (this.isEditing && this.newEquipe._id === id) {\r\n            this.isEditing = false;\r\n            this.newEquipe = { name: '', description: '' };\r\n          }\r\n\r\n          this.loadEquipes();\r\n          this.loading = false;\r\n\r\n          // Afficher un message de succès\r\n          alert('Équipe supprimée avec succès');\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting team:', error);\r\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  showMembreModal(equipe: Equipe) {\r\n    this.selectedEquipe = equipe;\r\n    // Ouvrir le modal avec Bootstrap 5\r\n    const modalRef = document.getElementById('membreModal');\r\n    if (modalRef) {\r\n      try {\r\n        // Ensure Bootstrap is properly loaded\r\n        if (typeof window !== 'undefined' && window.bootstrap) {\r\n          const modal = new window.bootstrap.Modal(modalRef);\r\n          modal.show();\r\n        } else {\r\n          console.error('Bootstrap is not loaded properly');\r\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error showing modal:', error);\r\n      }\r\n    } else {\r\n      console.error('Modal element not found');\r\n    }\r\n  }\r\n\r\n  addMembreToEquipe(teamId: string | undefined, membreId: string) {\r\n    if (!teamId) {\r\n      console.error('Team ID is undefined');\r\n      alert('ID de l\\'équipe non défini');\r\n      return;\r\n    }\r\n\r\n    if (!membreId || membreId.trim() === '') {\r\n      console.error('Member ID is empty');\r\n      alert('L\\'ID du membre est requis');\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    // Create a proper Membre object that matches what the API expects\r\n    const membre: Membre = { id: membreId };\r\n\r\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\r\n      next: (response) => {\r\n        console.log('Member added successfully:', response);\r\n        this.loadEquipes();\r\n        this.loading = false;\r\n\r\n        // Afficher un message de succès\r\n        alert('Membre ajouté avec succès à l\\'équipe');\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding member:', error);\r\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\r\n        alert(this.error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {\r\n    if (!teamId) {\r\n      console.error('Team ID is undefined');\r\n      alert('ID de l\\'équipe non défini');\r\n      return;\r\n    }\r\n\r\n    if (!membreId) {\r\n      console.error('Member ID is undefined');\r\n      alert('ID du membre non défini');\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\r\n      this.loading = true;\r\n\r\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\r\n        next: (response) => {\r\n          console.log('Member removed successfully:', response);\r\n          this.loadEquipes();\r\n          this.loading = false;\r\n\r\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\r\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\r\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\r\n            if (updatedEquipe) {\r\n              this.selectedEquipe = updatedEquipe;\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error removing member:', error);\r\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\r\n          alert(this.error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\">\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div class=\"absolute top-[10%] left-[5%] w-96 h-96 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"></div>\r\n    <div class=\"absolute bottom-[10%] right-[5%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#7826b5]/5 to-transparent dark:from-[#4f5fad]/3 dark:to-transparent blur-3xl\"></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.02]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div class=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n\r\n    <!-- Header Section -->\r\n    <div class=\"mb-8\">\r\n      <div class=\"text-center mb-6\">\r\n        <h1 class=\"text-4xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-3 tracking-wide\">\r\n          <i class=\"fas fa-users mr-3\"></i>\r\n          Gestion des Équipes\r\n        </h1>\r\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-lg max-w-2xl mx-auto\">\r\n          Créez, gérez et organisez vos équipes de travail en toute simplicité\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error Message -->\r\n    <div *ngIf=\"error\" class=\"mb-6\">\r\n      <div class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 flex items-start\">\r\n        <div class=\"text-red-500 dark:text-red-400 mr-3 mt-0.5\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <h3 class=\"font-semibold text-red-800 dark:text-red-300 mb-1\">Erreur</h3>\r\n          <p class=\"text-red-700 dark:text-red-400 text-sm\">{{ error }}</p>\r\n        </div>\r\n        <button\r\n          (click)=\"error = ''\"\r\n          class=\"text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 ml-3\"\r\n        >\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading Indicator -->\r\n    <div *ngIf=\"loading\" class=\"flex justify-center items-center py-12\">\r\n      <div class=\"relative\">\r\n        <div class=\"w-16 h-16 border-4 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-full\"></div>\r\n        <div class=\"absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Action Bar -->\r\n    <div class=\"mb-8\">\r\n      <div class=\"bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 p-6\">\r\n        <div class=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\r\n          <div class=\"flex items-center\">\r\n            <h2 class=\"text-2xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mr-4\">\r\n              Mes Équipes\r\n            </h2>\r\n            <span class=\"bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium\">\r\n              {{ equipes.length }} équipe{{ equipes.length !== 1 ? 's' : '' }}\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"flex items-center gap-3\">\r\n            <button\r\n              (click)=\"loadEquipes()\"\r\n              [disabled]=\"loading\"\r\n              class=\"flex items-center px-4 py-2 bg-[#6d6870]/10 dark:bg-[#e0e0e0]/10 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] rounded-xl font-medium transition-all duration-300 hover:scale-105 border border-[#6d6870]/20 dark:border-[#e0e0e0]/20 hover:border-[#4f5fad]/40 dark:hover:border-[#00f7ff]/40\"\r\n            >\r\n              <i class=\"fas fa-sync-alt mr-2\" [class.animate-spin]=\"loading\"></i>\r\n              Rafraîchir\r\n            </button>\r\n\r\n            <button\r\n              (click)=\"scrollToForm()\"\r\n              class=\"flex items-center px-6 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)]\"\r\n            >\r\n              <i class=\"fas fa-plus mr-2\"></i>\r\n              Nouvelle Équipe\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Teams Grid -->\r\n    <div class=\"mb-8\">\r\n      <!-- Empty State -->\r\n      <div *ngIf=\"equipes.length === 0 && !loading\" class=\"text-center py-16\">\r\n        <div class=\"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-full flex items-center justify-center\">\r\n          <i class=\"fas fa-users text-3xl text-[#4f5fad] dark:text-[#00f7ff]\"></i>\r\n        </div>\r\n        <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-3\">\r\n          Aucune équipe trouvée\r\n        </h3>\r\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mb-6 max-w-md mx-auto\">\r\n          Commencez par créer votre première équipe pour organiser votre travail collaboratif.\r\n        </p>\r\n        <button\r\n          (click)=\"scrollToForm()\"\r\n          class=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg\"\r\n        >\r\n          <i class=\"fas fa-plus mr-2\"></i>\r\n          Créer ma première équipe\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Teams Cards -->\r\n      <div *ngIf=\"equipes.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        <div\r\n          *ngFor=\"let equipe of equipes; trackBy: trackByEquipeId\"\r\n          class=\"group bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl dark:hover:shadow-[0_25px_60px_rgba(0,0,0,0.4)]\"\r\n        >\r\n          <!-- Card Header -->\r\n          <div class=\"relative\">\r\n            <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"></div>\r\n            <div class=\"p-6 pb-4\">\r\n              <div class=\"flex items-start justify-between mb-4\">\r\n                <div class=\"flex-1\">\r\n                  <h3 class=\"text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 group-hover:text-[#7826b5] dark:group-hover:text-[#4f5fad] transition-colors\">\r\n                    {{ equipe.name }}\r\n                  </h3>\r\n                  <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm line-clamp-2\">\r\n                    {{ equipe.description || 'Aucune description disponible' }}\r\n                  </p>\r\n                </div>\r\n                <div class=\"ml-4\">\r\n                  <div class=\"w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-xl flex items-center justify-center\">\r\n                    <i class=\"fas fa-users text-[#4f5fad] dark:text-[#00f7ff]\"></i>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Team Stats -->\r\n              <div class=\"flex items-center justify-between text-sm\">\r\n                <div class=\"flex items-center text-[#6d6870] dark:text-[#a0a0a0]\">\r\n                  <i class=\"fas fa-user-tie mr-2\"></i>\r\n                  <span>Admin: {{ equipe.admin || 'Non défini' }}</span>\r\n                </div>\r\n                <div class=\"flex items-center text-[#6d6870] dark:text-[#a0a0a0]\">\r\n                  <i class=\"fas fa-users mr-2\"></i>\r\n                  <span>{{ equipe.members?.length || 0 }} membre{{ (equipe.members?.length || 0) !== 1 ? 's' : '' }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Card Actions -->\r\n          <div class=\"px-6 pb-6\">\r\n            <div class=\"flex items-center gap-2\">\r\n              <button\r\n                (click)=\"editEquipe(equipe)\"\r\n                class=\"flex-1 flex items-center justify-center px-3 py-2 bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] hover:bg-[#4f5fad]/20 dark:hover:bg-[#00f7ff]/20 rounded-lg font-medium transition-all duration-200 text-sm\"\r\n              >\r\n                <i class=\"fas fa-edit mr-2\"></i>\r\n                Modifier\r\n              </button>\r\n\r\n              <button\r\n                (click)=\"showMembreModal(equipe)\"\r\n                class=\"flex-1 flex items-center justify-center px-3 py-2 bg-[#7826b5]/10 dark:bg-[#4f5fad]/10 text-[#7826b5] dark:text-[#4f5fad] hover:bg-[#7826b5]/20 dark:hover:bg-[#4f5fad]/20 rounded-lg font-medium transition-all duration-200 text-sm\"\r\n              >\r\n                <i class=\"fas fa-users-cog mr-2\"></i>\r\n                Membres\r\n              </button>\r\n\r\n              <button\r\n                (click)=\"equipe._id && deleteEquipe(equipe._id)\"\r\n                class=\"px-3 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200\"\r\n                title=\"Supprimer l'équipe\"\r\n              >\r\n                <i class=\"fas fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  <!-- Formulaire de création d'équipe -->\r\n  <div class=\"row mb-4\">\r\n    <div class=\"col-12\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header bg-primary text-white\">\r\n          <h3 class=\"mb-0\">\r\n            {{ isEditing ? \"Modifier une équipe\" : \"Créer une équipe\" }}\r\n          </h3>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <form>\r\n            <div class=\"mb-3\">\r\n              <label for=\"name\" class=\"form-label\"\r\n                >Nom de l'équipe <span class=\"text-danger\">*</span></label\r\n              >\r\n              <input\r\n                #nameInput\r\n                type=\"text\"\r\n                class=\"form-control\"\r\n                id=\"name\"\r\n                [value]=\"newEquipe.name\"\r\n                (input)=\"newEquipe.name = nameInput.value\"\r\n                required\r\n                [class.is-invalid]=\"\r\n                  !newEquipe.name && (isEditing || newEquipe.name === '')\r\n                \"\r\n                placeholder=\"Entrez le nom de l'équipe\"\r\n              />\r\n              <div class=\"invalid-feedback\">Le nom de l'équipe est requis</div>\r\n            </div>\r\n            <div class=\"mb-3\">\r\n              <label for=\"description\" class=\"form-label\">Description</label>\r\n              <textarea\r\n                #descInput\r\n                class=\"form-control\"\r\n                id=\"description\"\r\n                [value]=\"newEquipe.description || ''\"\r\n                (input)=\"newEquipe.description = descInput.value\"\r\n                rows=\"3\"\r\n                placeholder=\"Entrez une description pour cette équipe\"\r\n              ></textarea>\r\n              <small class=\"text-muted\"\r\n                >Une brève description de l'équipe et de son objectif</small\r\n              >\r\n            </div>\r\n            <div class=\"d-flex\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary\"\r\n                [disabled]=\"!newEquipe.name || loading\"\r\n                (click)=\"isEditing ? updateSelectedEquipe() : addEquipe()\"\r\n              >\r\n                <span\r\n                  *ngIf=\"loading\"\r\n                  class=\"spinner-border spinner-border-sm me-1\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                ></span>\r\n                {{ isEditing ? \"Mettre à jour\" : \"Créer\" }}\r\n              </button>\r\n              <button\r\n                *ngIf=\"isEditing\"\r\n                type=\"button\"\r\n                class=\"btn btn-secondary ms-2\"\r\n                (click)=\"cancelEdit()\"\r\n              >\r\n                Annuler\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Modal pour gérer les membres -->\r\n  <div class=\"modal fade\" id=\"membreModal\" tabindex=\"-1\" aria-hidden=\"true\">\r\n    <div class=\"modal-dialog\">\r\n      <div class=\"modal-content\">\r\n        <div class=\"modal-header\">\r\n          <h5 class=\"modal-title\">Gérer les membres de l'équipe</h5>\r\n          <button\r\n            type=\"button\"\r\n            class=\"btn-close\"\r\n            data-bs-dismiss=\"modal\"\r\n            aria-label=\"Close\"\r\n          ></button>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div *ngIf=\"selectedEquipe\">\r\n            <h6>Équipe: {{ selectedEquipe.name }}</h6>\r\n\r\n            <!-- Liste des membres actuels -->\r\n            <div class=\"mb-3\">\r\n              <h6>Membres actuels:</h6>\r\n              <div\r\n                *ngIf=\"\r\n                  selectedEquipe.members && selectedEquipe.members.length > 0;\r\n                  else noMembers\r\n                \"\r\n              >\r\n                <ul class=\"list-group\">\r\n                  <li\r\n                    class=\"list-group-item d-flex justify-content-between align-items-center\"\r\n                    *ngFor=\"let membreId of selectedEquipe.members\"\r\n                  >\r\n                    <span>{{ membreId }}</span>\r\n                    <button\r\n                      class=\"btn btn-sm btn-danger\"\r\n                      (click)=\"\r\n                        removeMembreFromEquipe(selectedEquipe._id, getMemberId(membreId))\r\n                      \"\r\n                    >\r\n                      Retirer\r\n                    </button>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <ng-template #noMembers>\r\n                <p class=\"text-muted\">Aucun membre dans cette équipe</p>\r\n              </ng-template>\r\n            </div>\r\n\r\n            <!-- Formulaire pour ajouter un membre -->\r\n            <div class=\"mb-3\">\r\n              <h6>Ajouter un membre:</h6>\r\n              <div class=\"input-group mb-2\">\r\n                <input\r\n                  #membreIdInput\r\n                  type=\"text\"\r\n                  class=\"form-control\"\r\n                  placeholder=\"ID du membre\"\r\n                />\r\n                <button\r\n                  class=\"btn btn-primary\"\r\n                  [disabled]=\"\r\n                    !selectedEquipe ||\r\n                    !selectedEquipe._id ||\r\n                    !membreIdInput.value\r\n                  \"\r\n                  (click)=\"\r\n                    addMembreToEquipe(selectedEquipe._id, membreIdInput.value);\r\n                    membreIdInput.value = ''\r\n                  \"\r\n                >\r\n                  Ajouter\r\n                </button>\r\n              </div>\r\n              <small class=\"text-muted\"\r\n                >Entrez l'ID du membre à ajouter à l'équipe</small\r\n              >\r\n            </div>\r\n\r\n            <!-- Informations supplémentaires -->\r\n            <div class=\"alert alert-info mt-3\">\r\n              <p class=\"mb-0\">\r\n                <strong>Note:</strong> Pour ajouter un membre, vous devez\r\n                d'abord créer le membre dans la section des membres.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"modal-footer\">\r\n          <button\r\n            type=\"button\"\r\n            class=\"btn btn-secondary\"\r\n            data-bs-dismiss=\"modal\"\r\n          >\r\n            Fermer\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;ICyCIA,EAAA,CAAAC,cAAA,cAAgC;IAG1BD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAC4CD,EAAA,CAAAI,MAAA,aAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,YAAkD;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEnEH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,KAAA,GAAiB,EAAE;IAAA,EAAC;IAGpBZ,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IAP2CH,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAH,KAAA,CAAW;;;;;IAYnEZ,EAAA,CAAAC,cAAA,cAAoE;IAEhED,EAAA,CAAAE,SAAA,cAAgG;IAElGF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAyCNH,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAE,SAAA,YAAwE;IAC1EF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAoE;IAClED,EAAA,CAAAI,MAAA,4GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAW,wDAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAU,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAO,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAGxBnB,EAAA,CAAAE,SAAA,YAAgC;IAChCF,EAAA,CAAAI,MAAA,gDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAKTH,EAAA,CAAAC,cAAA,cAGC;IAGGD,EAAA,CAAAE,SAAA,cAAwI;IACxIF,EAAA,CAAAC,cAAA,cAAsB;IAIdD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAmE;IACjED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAE,SAAA,aAA+D;IACjEF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAuD;IAEnDD,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAyC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAExDH,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAA4F;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAOjHH,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAK,UAAA,mBAAAe,+DAAA;MAAA,MAAAC,WAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAc,OAAA,CAAAC,UAAA,CAAAH,UAAA,CAAkB;IAAA,EAAC;IAG5BvB,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAsB,+DAAA;MAAA,MAAAN,WAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAA5B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAiB,OAAA,CAAAC,eAAA,CAAAN,UAAA,CAAuB;IAAA,EAAC;IAGjCvB,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAyB,+DAAA;MAAA,MAAAT,WAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAO,OAAA,GAAA/B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAY,UAAA,CAAAS,GAAA,IAAcD,OAAA,CAAAE,YAAA,CAAAV,UAAA,CAAAS,GAAA,CAAwB;IAAA,EAAC;IAIhDhC,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IApDHH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkC,kBAAA,MAAAX,UAAA,CAAAY,IAAA,MACF;IAEEnC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkC,kBAAA,MAAAX,UAAA,CAAAa,WAAA,yCACF;IAaMpC,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAkC,kBAAA,YAAAX,UAAA,CAAAc,KAAA,0BAAyC;IAIzCrC,EAAA,CAAAa,SAAA,GAA4F;IAA5Fb,EAAA,CAAAsC,kBAAA,MAAAf,UAAA,CAAAgB,OAAA,kBAAAhB,UAAA,CAAAgB,OAAA,CAAAC,MAAA,qBAAAjB,UAAA,CAAAgB,OAAA,kBAAAhB,UAAA,CAAAgB,OAAA,CAAAC,MAAA,6BAA4F;;;;;IAjC9GxC,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAyC,UAAA,IAAAC,qCAAA,mBAkEM;IACR1C,EAAA,CAAAG,YAAA,EAAM;;;;IAlEiBH,EAAA,CAAAa,SAAA,GAAY;IAAZb,EAAA,CAAA2C,UAAA,YAAAC,MAAA,CAAAC,OAAA,CAAY,iBAAAD,MAAA,CAAAE,eAAA;;;;;IAyHzB9C,EAAA,CAAAE,SAAA,eAKQ;;;;;;IAGVF,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAK,UAAA,mBAAA0C,2DAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAsC,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtBlD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAmCLH,EAAA,CAAAC,cAAA,cAGC;IACOD,EAAA,CAAAI,MAAA,GAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAK,UAAA,mBAAA8C,mEAAA;MAAA,MAAA9B,WAAA,GAAArB,EAAA,CAAAO,aAAA,CAAA6C,IAAA;MAAA,MAAAC,YAAA,GAAAhC,WAAA,CAAAG,SAAA;MAAA,MAAA8B,OAAA,GAAAtD,EAAA,CAAAU,aAAA;MAAA,OAC2BV,EAAA,CAAAW,WAAA,CAAA2C,OAAA,CAAAC,sBAAA,CAAAD,OAAA,CAAAE,cAAA,CAAAxB,GAAA,EAChDsB,OAAA,CAAAG,WAAA,CAAAJ,YAAA,CAAqB,CAAC;IAAA;IAEDrD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IARHH,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAc,iBAAA,CAAAuC,YAAA,CAAc;;;;;IAX1BrD,EAAA,CAAAC,cAAA,UAKC;IAEGD,EAAA,CAAAyC,UAAA,IAAAiB,0CAAA,kBAaK;IACP1D,EAAA,CAAAG,YAAA,EAAK;;;;IAZoBH,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA2C,UAAA,YAAAgB,OAAA,CAAAH,cAAA,CAAAjB,OAAA,CAAyB;;;;;IAelDvC,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAI,MAAA,0CAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IA9B9DH,EAAA,CAAAC,cAAA,UAA4B;IACtBD,EAAA,CAAAI,MAAA,GAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAG1CH,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAyC,UAAA,IAAAmB,qCAAA,mBAsBM;IACN5D,EAAA,CAAAyC,UAAA,IAAAoB,6CAAA,kCAAA7D,EAAA,CAAA8D,sBAAA,CAEc;IAChB9D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAI,MAAA,0BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,gBAA8B;IAC5BD,EAAA,CAAAE,SAAA,uBAKE;IACFF,EAAA,CAAAC,cAAA,mBAWC;IAJCD,EAAA,CAAAK,UAAA,mBAAA0D,yDAAA;MAAA/D,EAAA,CAAAO,aAAA,CAAAyD,IAAA;MAAA,MAAAC,IAAA,GAAAjE,EAAA,CAAAkE,WAAA;MAAA,MAAAC,OAAA,GAAAnE,EAAA,CAAAU,aAAA;MACuByD,OAAA,CAAAC,iBAAA,CAAAD,OAAA,CAAAX,cAAA,CAAAxB,GAAA,EAAAiC,IAAA,CAAAI,KAAA,CACvB;MAAA,OAAsBrE,EAAA,CAAAW,WAAA,CAAAsD,IAAA,CAAAI,KAAA,GACxB,EAAE;IAAA,EAAC;IAEDrE,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,iBACG;IAAAD,EAAA,CAAAI,MAAA,iEAA0C;IAAAJ,EAAA,CAAAG,YAAA,EAC5C;IAIHH,EAAA,CAAAC,cAAA,gBAAmC;IAEvBD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAI,MAAA,sGAEzB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IApEFH,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAkC,kBAAA,kBAAAoC,MAAA,CAAAd,cAAA,CAAArB,IAAA,KAAiC;IAMhCnC,EAAA,CAAAa,SAAA,GAGf;IAHeb,EAAA,CAAA2C,UAAA,SAAA2B,MAAA,CAAAd,cAAA,CAAAjB,OAAA,IAAA+B,MAAA,CAAAd,cAAA,CAAAjB,OAAA,CAAAC,MAAA,KAGf,aAAA+B,IAAA;IAoCgBvE,EAAA,CAAAa,SAAA,GAIC;IAJDb,EAAA,CAAA2C,UAAA,cAAA2B,MAAA,CAAAd,cAAA,KAAAc,MAAA,CAAAd,cAAA,CAAAxB,GAAA,KAAAiC,IAAA,CAAAI,KAAA,CAIC;;;AD5TnB,OAAM,MAAOG,eAAe;EAS1BC,YACUC,aAA4B,EAC5BC,aAA4B;IAD5B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAVvB,KAAA9B,OAAO,GAAa,EAAE;IACtB,KAAA+B,SAAS,GAAW;MAAEzC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IACjD,KAAAoB,cAAc,GAAkB,IAAI;IACpC,KAAAqB,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAnE,KAAK,GAAG,EAAE;EAKP;EAEH;EACA6C,WAAWA,CAACuB,MAAoB;IAC9B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOA,MAAM;;IAEf,OAAOA,MAAM,CAAChD,GAAG,IAAIgD,MAAM,CAACC,EAAE,IAAI,EAAE;EACtC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,aAAa,CAACW,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAAC3C,OAAO,GAAG2C,IAAI;QACnB,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACf6E,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC+E,OAAO;QACtE,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,aAAa,CAACiB,UAAU,EAAE,CAACN,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACV,OAAO,GAAGU,IAAI;QACnB,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACf6E,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC+E,OAAO;QACtE,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAc,SAASA,CAAA;IACPJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACd,SAAS,CAAC;IAE7C,IAAI,CAAC,IAAI,CAACA,SAAS,CAACzC,IAAI,EAAE;MACxBsD,OAAO,CAAC7E,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,CAACmE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnE,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC8D,aAAa,CAACmB,SAAS,CAAC,IAAI,CAACjB,SAAS,CAAC,CAACU,SAAS,CAAC;MACrDC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACX,WAAW,EAAE;QAClB,IAAI,CAACP,SAAS,GAAG;UAAEzC,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE,CAAC,CAAC;QAChD,IAAI,CAAC2C,OAAO,GAAG,KAAK;QAEpB;QACA,MAAMgB,cAAc,GAAG,2BAA2B;QAClD,IAAI,CAACnF,KAAK,GAAG,EAAE,CAAC,CAAC;QACjBoF,KAAK,CAACD,cAAc,CAAC;MACvB,CAAC;MACDnF,KAAK,EAAGA,KAAK,IAAI;QACf6E,OAAO,CAAC7E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE+E,OAAO,IAAI/E,KAAK,CAAC+E,OAAO,IAAI,eAAe,CAAC;QACrH,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEArD,UAAUA,CAACuE,MAAc;IACvB,IAAI,CAACpB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACD,SAAS,GAAG;MACf5C,GAAG,EAAEiE,MAAM,CAACjE,GAAG;MACfG,IAAI,EAAE8D,MAAM,CAAC9D,IAAI,IAAI,EAAE;MACvBC,WAAW,EAAE6D,MAAM,CAAC7D,WAAW,IAAI,EAAE;MACrCC,KAAK,EAAE4D,MAAM,CAAC5D,KAAK;MACnBE,OAAO,EAAE0D,MAAM,CAAC1D,OAAO,GAAG,CAAC,GAAG0D,MAAM,CAAC1D,OAAO,CAAC,GAAG;KACjD;EACH;EAEAW,UAAUA,CAAA;IACR,IAAI,CAAC2B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG;MAAEzC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IAC9C,IAAI,CAACxB,KAAK,GAAG,EAAE,CAAC,CAAC;EACnB;;EAEAsF,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACtB,SAAS,CAACzC,IAAI,EAAE;MACxBsD,OAAO,CAAC7E,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACgE,SAAS,CAAC5C,GAAG,EAAE;MACtB,IAAI,CAAC+C,OAAO,GAAG,IAAI;MACnB,IAAI,CAACnE,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC8D,aAAa,CAACyB,YAAY,CAAC,IAAI,CAACvB,SAAS,CAAC5C,GAAG,EAAE,IAAI,CAAC4C,SAAS,CAAC,CAACU,SAAS,CAAC;QAC5EC,IAAI,EAAGa,aAAa,IAAI;UACtBX,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,aAAa,CAAC;UACxD,IAAI,CAACjB,WAAW,EAAE;UAClB,IAAI,CAACN,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,SAAS,GAAG;YAAEzC,IAAI,EAAE,EAAE;YAAEC,WAAW,EAAE;UAAE,CAAE;UAC9C,IAAI,CAAC2C,OAAO,GAAG,KAAK;UAEpB;UACA,MAAMgB,cAAc,GAAG,iCAAiC;UACxDC,KAAK,CAACD,cAAc,CAAC;QACvB,CAAC;QACDnF,KAAK,EAAGA,KAAK,IAAI;UACf6E,OAAO,CAAC7E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE+E,OAAO,IAAI/E,KAAK,CAAC+E,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACnE,KAAK,GAAG,8CAA8C;;EAE/D;EAEAqB,YAAYA,CAACgD,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPQ,OAAO,CAAC7E,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAIyF,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAACtB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACnE,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC8D,aAAa,CAACzC,YAAY,CAACgD,EAAE,CAAC,CAACK,SAAS,CAAC;QAC5CC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;UAEnD;UACA,IAAI,IAAI,CAACjB,SAAS,IAAI,IAAI,CAACD,SAAS,CAAC5C,GAAG,KAAKiD,EAAE,EAAE;YAC/C,IAAI,CAACJ,SAAS,GAAG,KAAK;YACtB,IAAI,CAACD,SAAS,GAAG;cAAEzC,IAAI,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;;UAGhD,IAAI,CAAC+C,WAAW,EAAE;UAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;UAEpB;UACAiB,KAAK,CAAC,8BAA8B,CAAC;QACvC,CAAC;QACDpF,KAAK,EAAGA,KAAK,IAAI;UACf6E,OAAO,CAAC7E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE+E,OAAO,IAAI/E,KAAK,CAAC+E,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAlD,eAAeA,CAACoE,MAAc;IAC5B,IAAI,CAACzC,cAAc,GAAGyC,MAAM;IAC5B;IACA,MAAMK,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IACvD,IAAIF,QAAQ,EAAE;MACZ,IAAI;QACF;QACA,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;UACrD,MAAMC,KAAK,GAAG,IAAIF,MAAM,CAACC,SAAS,CAACE,KAAK,CAACN,QAAQ,CAAC;UAClDK,KAAK,CAACE,IAAI,EAAE;SACb,MAAM;UACLpB,OAAO,CAAC7E,KAAK,CAAC,kCAAkC,CAAC;UACjDoF,KAAK,CAAC,kDAAkD,CAAC;;OAE5D,CAAC,OAAOpF,KAAK,EAAE;QACd6E,OAAO,CAAC7E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;KAE/C,MAAM;MACL6E,OAAO,CAAC7E,KAAK,CAAC,yBAAyB,CAAC;;EAE5C;EAEAwD,iBAAiBA,CAAC0C,MAA0B,EAAEC,QAAgB;IAC5D,IAAI,CAACD,MAAM,EAAE;MACXrB,OAAO,CAAC7E,KAAK,CAAC,sBAAsB,CAAC;MACrCoF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACe,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MACvCvB,OAAO,CAAC7E,KAAK,CAAC,oBAAoB,CAAC;MACnCoF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACjB,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMC,MAAM,GAAW;MAAEC,EAAE,EAAE8B;IAAQ,CAAE;IAEvC,IAAI,CAACrC,aAAa,CAACN,iBAAiB,CAAC0C,MAAM,EAAE9B,MAAM,CAAC,CAACM,SAAS,CAAC;MAC7DC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACX,WAAW,EAAE;QAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;QAEpB;QACAiB,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC;MACDpF,KAAK,EAAGA,KAAK,IAAI;QACf6E,OAAO,CAAC7E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,qCAAqC,IAAIA,KAAK,CAACA,KAAK,EAAE+E,OAAO,IAAI/E,KAAK,CAAC+E,OAAO,IAAI,eAAe,CAAC;QAC/GK,KAAK,CAAC,IAAI,CAACpF,KAAK,CAAC;QACjB,IAAI,CAACmE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAxB,sBAAsBA,CAACuD,MAA0B,EAAEC,QAAgB;IACjE,IAAI,CAACD,MAAM,EAAE;MACXrB,OAAO,CAAC7E,KAAK,CAAC,sBAAsB,CAAC;MACrCoF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACe,QAAQ,EAAE;MACbtB,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,CAAC;MACvCoF,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF,IAAIK,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAACtB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACL,aAAa,CAACnB,sBAAsB,CAACuD,MAAM,EAAEC,QAAQ,CAAC,CAACzB,SAAS,CAAC;QACpEC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,QAAQ,CAAC;UACrD,IAAI,CAACX,WAAW,EAAE;UAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,IAAI,CAACvB,cAAc,IAAI,IAAI,CAACA,cAAc,CAACxB,GAAG,KAAK8E,MAAM,EAAE;YAC7D,MAAMV,aAAa,GAAG,IAAI,CAACvD,OAAO,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClF,GAAG,KAAK8E,MAAM,CAAC;YAC9D,IAAIV,aAAa,EAAE;cACjB,IAAI,CAAC5C,cAAc,GAAG4C,aAAa;;;QAGzC,CAAC;QACDxF,KAAK,EAAGA,KAAK,IAAI;UACf6E,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE+E,OAAO,IAAI/E,KAAK,CAAC+E,OAAO,IAAI,eAAe,CAAC;UACrHK,KAAK,CAAC,IAAI,CAACpF,KAAK,CAAC;UACjB,IAAI,CAACmE,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;;;uBAnRWP,eAAe,EAAAxE,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAArH,EAAA,CAAAmH,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAf/C,eAAe;MAAAgD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnB5B9H,EAAA,CAAAC,cAAA,aAAkF;UAG9ED,EAAA,CAAAE,SAAA,aAA4K;UAI5KF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,cAAuE;UAM/DD,EAAA,CAAAE,SAAA,aAAiC;UACjCF,EAAA,CAAAI,MAAA,kCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAwE;UACtED,EAAA,CAAAI,MAAA,kGACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAyC,UAAA,KAAAuF,+BAAA,mBAgBM;UAGNhI,EAAA,CAAAyC,UAAA,KAAAwF,+BAAA,kBAKM;UAGNjI,EAAA,CAAAC,cAAA,cAAkB;UAKRD,EAAA,CAAAI,MAAA,0BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,gBAAiI;UAC/HD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAGTH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAK,UAAA,mBAAA6H,kDAAA;YAAA,OAASH,GAAA,CAAA5C,WAAA,EAAa;UAAA,EAAC;UAIvBnF,EAAA,CAAAE,SAAA,aAAmE;UACnEF,EAAA,CAAAI,MAAA,yBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAK,UAAA,mBAAA8H,kDAAA;YAAA,OAASJ,GAAA,CAAA5G,YAAA,EAAc;UAAA,EAAC;UAGxBnB,EAAA,CAAAE,SAAA,aAAgC;UAChCF,EAAA,CAAAI,MAAA,8BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,cAAkB;UAEhBD,EAAA,CAAAyC,UAAA,KAAA2F,+BAAA,mBAiBM;UAGNpI,EAAA,CAAAyC,UAAA,KAAA4F,+BAAA,kBAoEM;UACRrI,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAsB;UAKZD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEPH,EAAA,CAAAC,cAAA,eAAuB;UAIdD,EAAA,CAAAI,MAAA,6BAAgB;UAAAJ,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAErDH,EAAA,CAAAC,cAAA,qBAYE;UANAD,EAAA,CAAAK,UAAA,mBAAAiI,iDAAA;YAAAtI,EAAA,CAAAO,aAAA,CAAAgI,IAAA;YAAA,MAAAC,GAAA,GAAAxI,EAAA,CAAAkE,WAAA;YAAA,OAASlE,EAAA,CAAAW,WAAA,CAAAoH,GAAA,CAAAnD,SAAA,CAAAzC,IAAA,GAAAqG,GAAA,CAAAnE,KAAA,CAAgC;UAAA,EAAC;UAN5CrE,EAAA,CAAAG,YAAA,EAYE;UACFH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAI,MAAA,0CAA6B;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAEnEH,EAAA,CAAAC,cAAA,eAAkB;UAC4BD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,wBAQC;UAHCD,EAAA,CAAAK,UAAA,mBAAAoI,oDAAA;YAAAzI,EAAA,CAAAO,aAAA,CAAAgI,IAAA;YAAA,MAAAG,GAAA,GAAA1I,EAAA,CAAAkE,WAAA;YAAA,OAASlE,EAAA,CAAAW,WAAA,CAAAoH,GAAA,CAAAnD,SAAA,CAAAxC,WAAA,GAAAsG,GAAA,CAAArE,KAAA,CAAuC;UAAA,EAAC;UAGlDrE,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,iBACG;UAAAD,EAAA,CAAAI,MAAA,sEAAoD;UAAAJ,EAAA,CAAAG,YAAA,EACtD;UAEHH,EAAA,CAAAC,cAAA,eAAoB;UAKhBD,EAAA,CAAAK,UAAA,mBAAAsI,kDAAA;YAAA,OAAAZ,GAAA,CAAAlD,SAAA,GAAqBkD,GAAA,CAAA7B,oBAAA,EAAsB,GAAG6B,GAAA,CAAAlC,SAAA,EAAW;UAAA,EAAC;UAE1D7F,EAAA,CAAAyC,UAAA,KAAAmG,gCAAA,mBAKQ;UACR5I,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAyC,UAAA,KAAAoG,kCAAA,qBAOS;UACX7I,EAAA,CAAAG,YAAA,EAAM;UAQhBH,EAAA,CAAAC,cAAA,eAA0E;UAI1CD,EAAA,CAAAI,MAAA,+CAA6B;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC1DH,EAAA,CAAAE,SAAA,kBAKU;UACZF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAyC,UAAA,KAAAqG,+BAAA,mBAuEM;UACR9I,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UAMtBD,EAAA,CAAAI,MAAA,gBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;;;UApUTH,EAAA,CAAAa,SAAA,IAAW;UAAXb,EAAA,CAAA2C,UAAA,SAAAoF,GAAA,CAAAnH,KAAA,CAAW;UAmBXZ,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAA2C,UAAA,SAAAoF,GAAA,CAAAhD,OAAA,CAAa;UAgBT/E,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAsC,kBAAA,MAAAyF,GAAA,CAAAlF,OAAA,CAAAL,MAAA,kBAAAuF,GAAA,CAAAlF,OAAA,CAAAL,MAAA,uBACF;UAMExC,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAA2C,UAAA,aAAAoF,GAAA,CAAAhD,OAAA,CAAoB;UAGY/E,EAAA,CAAAa,SAAA,GAA8B;UAA9Bb,EAAA,CAAA+I,WAAA,iBAAAhB,GAAA,CAAAhD,OAAA,CAA8B;UAmBhE/E,EAAA,CAAAa,SAAA,GAAsC;UAAtCb,EAAA,CAAA2C,UAAA,SAAAoF,GAAA,CAAAlF,OAAA,CAAAL,MAAA,WAAAuF,GAAA,CAAAhD,OAAA,CAAsC;UAoBtC/E,EAAA,CAAAa,SAAA,GAAwB;UAAxBb,EAAA,CAAA2C,UAAA,SAAAoF,GAAA,CAAAlF,OAAA,CAAAL,MAAA,KAAwB;UA6ExBxC,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAkC,kBAAA,MAAA6F,GAAA,CAAAlD,SAAA,kEACF;UAgBM7E,EAAA,CAAAa,SAAA,GAEC;UAFDb,EAAA,CAAA+I,WAAA,gBAAAhB,GAAA,CAAAnD,SAAA,CAAAzC,IAAA,KAAA4F,GAAA,CAAAlD,SAAA,IAAAkD,GAAA,CAAAnD,SAAA,CAAAzC,IAAA,SAEC;UALDnC,EAAA,CAAA2C,UAAA,UAAAoF,GAAA,CAAAnD,SAAA,CAAAzC,IAAA,CAAwB;UAgBxBnC,EAAA,CAAAa,SAAA,GAAqC;UAArCb,EAAA,CAAA2C,UAAA,UAAAoF,GAAA,CAAAnD,SAAA,CAAAxC,WAAA,OAAqC;UAarCpC,EAAA,CAAAa,SAAA,GAAuC;UAAvCb,EAAA,CAAA2C,UAAA,cAAAoF,GAAA,CAAAnD,SAAA,CAAAzC,IAAA,IAAA4F,GAAA,CAAAhD,OAAA,CAAuC;UAIpC/E,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAA2C,UAAA,SAAAoF,GAAA,CAAAhD,OAAA,CAAa;UAKhB/E,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAkC,kBAAA,MAAA6F,GAAA,CAAAlD,SAAA,4CACF;UAEG7E,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAA2C,UAAA,SAAAoF,GAAA,CAAAlD,SAAA,CAAe;UA4BhB7E,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAA2C,UAAA,SAAAoF,GAAA,CAAAvE,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}