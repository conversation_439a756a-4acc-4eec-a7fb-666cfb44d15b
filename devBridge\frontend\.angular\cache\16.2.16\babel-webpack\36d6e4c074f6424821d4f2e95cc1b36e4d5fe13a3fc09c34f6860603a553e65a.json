{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { naturalCompare } from '../../jsutils/naturalCompare.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isAbstractType, isInterfaceType, isObjectType } from '../../type/definition.mjs';\n\n/**\n * Fields on correct type\n *\n * A GraphQL document is only valid if all fields selected are defined by the\n * parent type, or are an allowed meta field such as __typename.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selections\n */\nexport function FieldsOnCorrectTypeRule(context) {\n  return {\n    Field(node) {\n      const type = context.getParentType();\n      if (type) {\n        const fieldDef = context.getFieldDef();\n        if (!fieldDef) {\n          // This field doesn't exist, lets look for suggestions.\n          const schema = context.getSchema();\n          const fieldName = node.name.value; // First determine if there are any suggested types to condition on.\n\n          let suggestion = didYouMean('to use an inline fragment on', getSuggestedTypeNames(schema, type, fieldName)); // If there are no suggested types, then perhaps this was a typo?\n\n          if (suggestion === '') {\n            suggestion = didYouMean(getSuggestedFieldNames(type, fieldName));\n          } // Report an error, including helpful suggestions.\n\n          context.reportError(new GraphQLError(`Cannot query field \"${fieldName}\" on type \"${type.name}\".` + suggestion, {\n            nodes: node\n          }));\n        }\n      }\n    }\n  };\n}\n/**\n * Go through all of the implementations of type, as well as the interfaces that\n * they implement. If any of those types include the provided field, suggest them,\n * sorted by how often the type is referenced.\n */\n\nfunction getSuggestedTypeNames(schema, type, fieldName) {\n  if (!isAbstractType(type)) {\n    // Must be an Object type, which does not have possible fields.\n    return [];\n  }\n  const suggestedTypes = new Set();\n  const usageCount = Object.create(null);\n  for (const possibleType of schema.getPossibleTypes(type)) {\n    if (!possibleType.getFields()[fieldName]) {\n      continue;\n    } // This object type defines this field.\n\n    suggestedTypes.add(possibleType);\n    usageCount[possibleType.name] = 1;\n    for (const possibleInterface of possibleType.getInterfaces()) {\n      var _usageCount$possibleI;\n      if (!possibleInterface.getFields()[fieldName]) {\n        continue;\n      } // This interface type defines this field.\n\n      suggestedTypes.add(possibleInterface);\n      usageCount[possibleInterface.name] = ((_usageCount$possibleI = usageCount[possibleInterface.name]) !== null && _usageCount$possibleI !== void 0 ? _usageCount$possibleI : 0) + 1;\n    }\n  }\n  return [...suggestedTypes].sort((typeA, typeB) => {\n    // Suggest both interface and object types based on how common they are.\n    const usageCountDiff = usageCount[typeB.name] - usageCount[typeA.name];\n    if (usageCountDiff !== 0) {\n      return usageCountDiff;\n    } // Suggest super types first followed by subtypes\n\n    if (isInterfaceType(typeA) && schema.isSubType(typeA, typeB)) {\n      return -1;\n    }\n    if (isInterfaceType(typeB) && schema.isSubType(typeB, typeA)) {\n      return 1;\n    }\n    return naturalCompare(typeA.name, typeB.name);\n  }).map(x => x.name);\n}\n/**\n * For the field name provided, determine if there are any similar field names\n * that may be the result of a typo.\n */\n\nfunction getSuggestedFieldNames(type, fieldName) {\n  if (isObjectType(type) || isInterfaceType(type)) {\n    const possibleFieldNames = Object.keys(type.getFields());\n    return suggestionList(fieldName, possibleFieldNames);\n  } // Otherwise, must be a Union type, which does not define fields.\n\n  return [];\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "naturalCompare", "suggestionList", "GraphQLError", "isAbstractType", "isInterfaceType", "isObjectType", "FieldsOnCorrectTypeRule", "context", "Field", "node", "type", "getParentType", "fieldDef", "getFieldDef", "schema", "getSchema", "fieldName", "name", "value", "suggestion", "getSuggestedTypeNames", "getSuggestedFieldNames", "reportError", "nodes", "suggestedTypes", "Set", "usageCount", "Object", "create", "possibleType", "getPossibleTypes", "getFields", "add", "possibleInterface", "getInterfaces", "_usageCount$possibleI", "sort", "typeA", "typeB", "usageCountDiff", "isSubType", "map", "x", "possibleFieldNames", "keys"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/FieldsOnCorrectTypeRule.mjs"], "sourcesContent": ["import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { naturalCompare } from '../../jsutils/naturalCompare.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport {\n  isAbstractType,\n  isInterfaceType,\n  isObjectType,\n} from '../../type/definition.mjs';\n\n/**\n * Fields on correct type\n *\n * A GraphQL document is only valid if all fields selected are defined by the\n * parent type, or are an allowed meta field such as __typename.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selections\n */\nexport function FieldsOnCorrectTypeRule(context) {\n  return {\n    Field(node) {\n      const type = context.getParentType();\n\n      if (type) {\n        const fieldDef = context.getFieldDef();\n\n        if (!fieldDef) {\n          // This field doesn't exist, lets look for suggestions.\n          const schema = context.getSchema();\n          const fieldName = node.name.value; // First determine if there are any suggested types to condition on.\n\n          let suggestion = didYouMean(\n            'to use an inline fragment on',\n            getSuggestedTypeNames(schema, type, fieldName),\n          ); // If there are no suggested types, then perhaps this was a typo?\n\n          if (suggestion === '') {\n            suggestion = didYouMean(getSuggestedFieldNames(type, fieldName));\n          } // Report an error, including helpful suggestions.\n\n          context.reportError(\n            new GraphQLError(\n              `Cannot query field \"${fieldName}\" on type \"${type.name}\".` +\n                suggestion,\n              {\n                nodes: node,\n              },\n            ),\n          );\n        }\n      }\n    },\n  };\n}\n/**\n * Go through all of the implementations of type, as well as the interfaces that\n * they implement. If any of those types include the provided field, suggest them,\n * sorted by how often the type is referenced.\n */\n\nfunction getSuggestedTypeNames(schema, type, fieldName) {\n  if (!isAbstractType(type)) {\n    // Must be an Object type, which does not have possible fields.\n    return [];\n  }\n\n  const suggestedTypes = new Set();\n  const usageCount = Object.create(null);\n\n  for (const possibleType of schema.getPossibleTypes(type)) {\n    if (!possibleType.getFields()[fieldName]) {\n      continue;\n    } // This object type defines this field.\n\n    suggestedTypes.add(possibleType);\n    usageCount[possibleType.name] = 1;\n\n    for (const possibleInterface of possibleType.getInterfaces()) {\n      var _usageCount$possibleI;\n\n      if (!possibleInterface.getFields()[fieldName]) {\n        continue;\n      } // This interface type defines this field.\n\n      suggestedTypes.add(possibleInterface);\n      usageCount[possibleInterface.name] =\n        ((_usageCount$possibleI = usageCount[possibleInterface.name]) !==\n          null && _usageCount$possibleI !== void 0\n          ? _usageCount$possibleI\n          : 0) + 1;\n    }\n  }\n\n  return [...suggestedTypes]\n    .sort((typeA, typeB) => {\n      // Suggest both interface and object types based on how common they are.\n      const usageCountDiff = usageCount[typeB.name] - usageCount[typeA.name];\n\n      if (usageCountDiff !== 0) {\n        return usageCountDiff;\n      } // Suggest super types first followed by subtypes\n\n      if (isInterfaceType(typeA) && schema.isSubType(typeA, typeB)) {\n        return -1;\n      }\n\n      if (isInterfaceType(typeB) && schema.isSubType(typeB, typeA)) {\n        return 1;\n      }\n\n      return naturalCompare(typeA.name, typeB.name);\n    })\n    .map((x) => x.name);\n}\n/**\n * For the field name provided, determine if there are any similar field names\n * that may be the result of a typo.\n */\n\nfunction getSuggestedFieldNames(type, fieldName) {\n  if (isObjectType(type) || isInterfaceType(type)) {\n    const possibleFieldNames = Object.keys(type.getFields());\n    return suggestionList(fieldName, possibleFieldNames);\n  } // Otherwise, must be a Union type, which does not define fields.\n\n  return [];\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,8BAA8B;AACzD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SACEC,cAAc,EACdC,eAAe,EACfC,YAAY,QACP,2BAA2B;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EAC/C,OAAO;IACLC,KAAKA,CAACC,IAAI,EAAE;MACV,MAAMC,IAAI,GAAGH,OAAO,CAACI,aAAa,CAAC,CAAC;MAEpC,IAAID,IAAI,EAAE;QACR,MAAME,QAAQ,GAAGL,OAAO,CAACM,WAAW,CAAC,CAAC;QAEtC,IAAI,CAACD,QAAQ,EAAE;UACb;UACA,MAAME,MAAM,GAAGP,OAAO,CAACQ,SAAS,CAAC,CAAC;UAClC,MAAMC,SAAS,GAAGP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAAC,CAAC;;UAEnC,IAAIC,UAAU,GAAGpB,UAAU,CACzB,8BAA8B,EAC9BqB,qBAAqB,CAACN,MAAM,EAAEJ,IAAI,EAAEM,SAAS,CAC/C,CAAC,CAAC,CAAC;;UAEH,IAAIG,UAAU,KAAK,EAAE,EAAE;YACrBA,UAAU,GAAGpB,UAAU,CAACsB,sBAAsB,CAACX,IAAI,EAAEM,SAAS,CAAC,CAAC;UAClE,CAAC,CAAC;;UAEFT,OAAO,CAACe,WAAW,CACjB,IAAIpB,YAAY,CACb,uBAAsBc,SAAU,cAAaN,IAAI,CAACO,IAAK,IAAG,GACzDE,UAAU,EACZ;YACEI,KAAK,EAAEd;UACT,CACF,CACF,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASW,qBAAqBA,CAACN,MAAM,EAAEJ,IAAI,EAAEM,SAAS,EAAE;EACtD,IAAI,CAACb,cAAc,CAACO,IAAI,CAAC,EAAE;IACzB;IACA,OAAO,EAAE;EACX;EAEA,MAAMc,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAEtC,KAAK,MAAMC,YAAY,IAAIf,MAAM,CAACgB,gBAAgB,CAACpB,IAAI,CAAC,EAAE;IACxD,IAAI,CAACmB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACf,SAAS,CAAC,EAAE;MACxC;IACF,CAAC,CAAC;;IAEFQ,cAAc,CAACQ,GAAG,CAACH,YAAY,CAAC;IAChCH,UAAU,CAACG,YAAY,CAACZ,IAAI,CAAC,GAAG,CAAC;IAEjC,KAAK,MAAMgB,iBAAiB,IAAIJ,YAAY,CAACK,aAAa,CAAC,CAAC,EAAE;MAC5D,IAAIC,qBAAqB;MAEzB,IAAI,CAACF,iBAAiB,CAACF,SAAS,CAAC,CAAC,CAACf,SAAS,CAAC,EAAE;QAC7C;MACF,CAAC,CAAC;;MAEFQ,cAAc,CAACQ,GAAG,CAACC,iBAAiB,CAAC;MACrCP,UAAU,CAACO,iBAAiB,CAAChB,IAAI,CAAC,GAChC,CAAC,CAACkB,qBAAqB,GAAGT,UAAU,CAACO,iBAAiB,CAAChB,IAAI,CAAC,MAC1D,IAAI,IAAIkB,qBAAqB,KAAK,KAAK,CAAC,GACtCA,qBAAqB,GACrB,CAAC,IAAI,CAAC;IACd;EACF;EAEA,OAAO,CAAC,GAAGX,cAAc,CAAC,CACvBY,IAAI,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtB;IACA,MAAMC,cAAc,GAAGb,UAAU,CAACY,KAAK,CAACrB,IAAI,CAAC,GAAGS,UAAU,CAACW,KAAK,CAACpB,IAAI,CAAC;IAEtE,IAAIsB,cAAc,KAAK,CAAC,EAAE;MACxB,OAAOA,cAAc;IACvB,CAAC,CAAC;;IAEF,IAAInC,eAAe,CAACiC,KAAK,CAAC,IAAIvB,MAAM,CAAC0B,SAAS,CAACH,KAAK,EAAEC,KAAK,CAAC,EAAE;MAC5D,OAAO,CAAC,CAAC;IACX;IAEA,IAAIlC,eAAe,CAACkC,KAAK,CAAC,IAAIxB,MAAM,CAAC0B,SAAS,CAACF,KAAK,EAAED,KAAK,CAAC,EAAE;MAC5D,OAAO,CAAC;IACV;IAEA,OAAOrC,cAAc,CAACqC,KAAK,CAACpB,IAAI,EAAEqB,KAAK,CAACrB,IAAI,CAAC;EAC/C,CAAC,CAAC,CACDwB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACzB,IAAI,CAAC;AACvB;AACA;AACA;AACA;AACA;;AAEA,SAASI,sBAAsBA,CAACX,IAAI,EAAEM,SAAS,EAAE;EAC/C,IAAIX,YAAY,CAACK,IAAI,CAAC,IAAIN,eAAe,CAACM,IAAI,CAAC,EAAE;IAC/C,MAAMiC,kBAAkB,GAAGhB,MAAM,CAACiB,IAAI,CAAClC,IAAI,CAACqB,SAAS,CAAC,CAAC,CAAC;IACxD,OAAO9B,cAAc,CAACe,SAAS,EAAE2B,kBAAkB,CAAC;EACtD,CAAC,CAAC;;EAEF,OAAO,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}