import { User } from './user.model';

export interface Equipe {
  _id?: string;
  id?: string;
  name: string;
  description?: string;
  admin?: string | User; // Peut être un ID ou un objet User populé
  members?: (string | User)[]; // Peut être des IDs ou des objets User populés

  // Nouveaux champs
  status?: 'active' | 'inactive' | 'archived';
  maxMembers?: number;
  project?: string; // ID du projet associé
  tags?: string[];
  isPublic?: boolean;

  // Propriétés virtuelles (calculées côté backend)
  memberCount?: number;
  isFullTeam?: boolean;
  availableSlots?: number;

  // Timestamps
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface TeamMember {
  _id?: string;
  user: string | User;
  role: 'admin' | 'membre';
  userType: 'etudiant' | 'enseignant';
  team: string | Equipe;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface CreateTeamRequest {
  name: string;
  description?: string;
  maxMembers?: number;
  tags?: string[];
  isPublic?: boolean;
}

export interface UpdateTeamRequest {
  name?: string;
  description?: string;
  maxMembers?: number;
  tags?: string[];
  isPublic?: boolean;
  status?: 'active' | 'inactive' | 'archived';
}

export interface AddMemberRequest {
  userId: string;
}

export interface RemoveMemberRequest {
  userId: string;
}

export interface TeamSearchFilters {
  status?: string;
  isPublic?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface TeamListResponse {
  teams: Equipe[];
  pagination: {
    current: number;
    total: number;
    count: number;
    totalItems: number;
  };
}
