{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { <PERSON><PERSON> } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet, isNonNullObject as isObjectOrArray } from \"../../utilities/index.js\";\nimport { isArray } from \"./helpers.js\";\nfunction shallowCopy(value) {\n  if (isObjectOrArray(value)) {\n    return isArray(value) ? value.slice(0) : __assign({\n      __proto__: Object.getPrototypeOf(value)\n    }, value);\n  }\n  return value;\n}\n// When programmers talk about the \"canonical form\" of an object, they\n// usually have the following meaning in mind, which I've copied from\n// https://en.wiktionary.org/wiki/canonical_form:\n//\n// 1. A standard or normal presentation of a mathematical entity [or\n//    object]. A canonical form is an element of a set of representatives\n//    of equivalence classes of forms such that there is a function or\n//    procedure which projects every element of each equivalence class\n//    onto that one element, the canonical form of that equivalence\n//    class. The canonical form is expected to be simpler than the rest of\n//    the forms in some way.\n//\n// That's a long-winded way of saying any two objects that have the same\n// canonical form may be considered equivalent, even if they are !==,\n// which usually means the objects are structurally equivalent (deeply\n// equal), but don't necessarily use the same memory.\n//\n// Like a literary or musical canon, this ObjectCanon class represents a\n// collection of unique canonical items (JavaScript objects), with the\n// important property that canon.admit(a) === canon.admit(b) if a and b\n// are deeply equal to each other. In terms of the definition above, the\n// canon.admit method is the \"function or procedure which projects every\"\n// object \"onto that one element, the canonical form.\"\n//\n// In the worst case, the canonicalization process may involve looking at\n// every property in the provided object tree, so it takes the same order\n// of time as deep equality checking. Fortunately, already-canonicalized\n// objects are returned immediately from canon.admit, so the presence of\n// canonical subtrees tends to speed up canonicalization.\n//\n// Since consumers of canonical objects can check for deep equality in\n// constant time, canonicalizing cache results can massively improve the\n// performance of application code that skips re-rendering unchanged\n// results, such as \"pure\" UI components in a framework like React.\n//\n// Of course, since canonical objects may be shared widely between\n// unrelated consumers, it's important to think of them as immutable, even\n// though they are not actually frozen with Object.freeze in production,\n// due to the extra performance overhead that comes with frozen objects.\n//\n// Custom scalar objects whose internal class name is neither Array nor\n// Object can be included safely in the admitted tree, but they will not\n// be replaced with a canonical version (to put it another way, they are\n// assumed to be canonical already).\n//\n// If we ignore custom objects, no detection of cycles or repeated object\n// references is currently required by the StoreReader class, since\n// GraphQL result objects are JSON-serializable trees (and thus contain\n// neither cycles nor repeated subtrees), so we can avoid the complexity\n// of keeping track of objects we've already seen during the recursion of\n// the admit method.\n//\n// In the future, we may consider adding additional cases to the switch\n// statement to handle other common object types, such as \"[object Date]\"\n// objects, as needed.\nvar ObjectCanon = /** @class */function () {\n  function ObjectCanon() {\n    // Set of all canonical objects this ObjectCanon has admitted, allowing\n    // canon.admit to return previously-canonicalized objects immediately.\n    this.known = new (canUseWeakSet ? WeakSet : Set)();\n    // Efficient storage/lookup structure for canonical objects.\n    this.pool = new Trie(canUseWeakMap);\n    // Make the ObjectCanon assume this value has already been\n    // canonicalized.\n    this.passes = new WeakMap();\n    // Arrays that contain the same elements in a different order can share\n    // the same SortedKeysInfo object, to save memory.\n    this.keysByJSON = new Map();\n    // This has to come last because it depends on keysByJSON.\n    this.empty = this.admit({});\n  }\n  ObjectCanon.prototype.isKnown = function (value) {\n    return isObjectOrArray(value) && this.known.has(value);\n  };\n  ObjectCanon.prototype.pass = function (value) {\n    if (isObjectOrArray(value)) {\n      var copy = shallowCopy(value);\n      this.passes.set(copy, value);\n      return copy;\n    }\n    return value;\n  };\n  ObjectCanon.prototype.admit = function (value) {\n    var _this = this;\n    if (isObjectOrArray(value)) {\n      var original = this.passes.get(value);\n      if (original) return original;\n      var proto = Object.getPrototypeOf(value);\n      switch (proto) {\n        case Array.prototype:\n          {\n            if (this.known.has(value)) return value;\n            var array = value.map(this.admit, this);\n            // Arrays are looked up in the Trie using their recursively\n            // canonicalized elements, and the known version of the array is\n            // preserved as node.array.\n            var node = this.pool.lookupArray(array);\n            if (!node.array) {\n              this.known.add(node.array = array);\n              // Since canonical arrays may be shared widely between\n              // unrelated consumers, it's important to regard them as\n              // immutable, even if they are not frozen in production.\n              if (globalThis.__DEV__ !== false) {\n                Object.freeze(array);\n              }\n            }\n            return node.array;\n          }\n        case null:\n        case Object.prototype:\n          {\n            if (this.known.has(value)) return value;\n            var proto_1 = Object.getPrototypeOf(value);\n            var array_1 = [proto_1];\n            var keys = this.sortedKeys(value);\n            array_1.push(keys.json);\n            var firstValueIndex_1 = array_1.length;\n            keys.sorted.forEach(function (key) {\n              array_1.push(_this.admit(value[key]));\n            });\n            // Objects are looked up in the Trie by their prototype (which\n            // is *not* recursively canonicalized), followed by a JSON\n            // representation of their (sorted) keys, followed by the\n            // sequence of recursively canonicalized values corresponding to\n            // those keys. To keep the final results unambiguous with other\n            // sequences (such as arrays that just happen to contain [proto,\n            // keys.json, value1, value2, ...]), the known version of the\n            // object is stored as node.object.\n            var node = this.pool.lookupArray(array_1);\n            if (!node.object) {\n              var obj_1 = node.object = Object.create(proto_1);\n              this.known.add(obj_1);\n              keys.sorted.forEach(function (key, i) {\n                obj_1[key] = array_1[firstValueIndex_1 + i];\n              });\n              // Since canonical objects may be shared widely between\n              // unrelated consumers, it's important to regard them as\n              // immutable, even if they are not frozen in production.\n              if (globalThis.__DEV__ !== false) {\n                Object.freeze(obj_1);\n              }\n            }\n            return node.object;\n          }\n      }\n    }\n    return value;\n  };\n  // It's worthwhile to cache the sorting of arrays of strings, since the\n  // same initial unsorted arrays tend to be encountered many times.\n  // Fortunately, we can reuse the Trie machinery to look up the sorted\n  // arrays in linear time (which is faster than sorting large arrays).\n  ObjectCanon.prototype.sortedKeys = function (obj) {\n    var keys = Object.keys(obj);\n    var node = this.pool.lookupArray(keys);\n    if (!node.keys) {\n      keys.sort();\n      var json = JSON.stringify(keys);\n      if (!(node.keys = this.keysByJSON.get(json))) {\n        this.keysByJSON.set(json, node.keys = {\n          sorted: keys,\n          json: json\n        });\n      }\n    }\n    return node.keys;\n  };\n  return ObjectCanon;\n}();\nexport { ObjectCanon };", "map": {"version": 3, "names": ["__assign", "<PERSON><PERSON>", "canUseWeakMap", "canUseWeakSet", "isNonNullObject", "isObjectOrArray", "isArray", "shallowCopy", "value", "slice", "__proto__", "Object", "getPrototypeOf", "ObjectCanon", "known", "WeakSet", "Set", "pool", "passes", "WeakMap", "keysByJSON", "Map", "empty", "admit", "prototype", "isKnown", "has", "pass", "copy", "set", "_this", "original", "get", "proto", "Array", "array", "map", "node", "lookupArray", "add", "globalThis", "__DEV__", "freeze", "proto_1", "array_1", "keys", "sortedKeys", "push", "json", "firstValueIndex_1", "length", "sorted", "for<PERSON>ach", "key", "object", "obj_1", "create", "i", "obj", "sort", "JSON", "stringify"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/object-canon.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { <PERSON><PERSON> } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet, isNonNullObject as isObjectOrArray, } from \"../../utilities/index.js\";\nimport { isArray } from \"./helpers.js\";\nfunction shallowCopy(value) {\n    if (isObjectOrArray(value)) {\n        return isArray(value) ?\n            value.slice(0)\n            : __assign({ __proto__: Object.getPrototypeOf(value) }, value);\n    }\n    return value;\n}\n// When programmers talk about the \"canonical form\" of an object, they\n// usually have the following meaning in mind, which I've copied from\n// https://en.wiktionary.org/wiki/canonical_form:\n//\n// 1. A standard or normal presentation of a mathematical entity [or\n//    object]. A canonical form is an element of a set of representatives\n//    of equivalence classes of forms such that there is a function or\n//    procedure which projects every element of each equivalence class\n//    onto that one element, the canonical form of that equivalence\n//    class. The canonical form is expected to be simpler than the rest of\n//    the forms in some way.\n//\n// That's a long-winded way of saying any two objects that have the same\n// canonical form may be considered equivalent, even if they are !==,\n// which usually means the objects are structurally equivalent (deeply\n// equal), but don't necessarily use the same memory.\n//\n// Like a literary or musical canon, this ObjectCanon class represents a\n// collection of unique canonical items (JavaScript objects), with the\n// important property that canon.admit(a) === canon.admit(b) if a and b\n// are deeply equal to each other. In terms of the definition above, the\n// canon.admit method is the \"function or procedure which projects every\"\n// object \"onto that one element, the canonical form.\"\n//\n// In the worst case, the canonicalization process may involve looking at\n// every property in the provided object tree, so it takes the same order\n// of time as deep equality checking. Fortunately, already-canonicalized\n// objects are returned immediately from canon.admit, so the presence of\n// canonical subtrees tends to speed up canonicalization.\n//\n// Since consumers of canonical objects can check for deep equality in\n// constant time, canonicalizing cache results can massively improve the\n// performance of application code that skips re-rendering unchanged\n// results, such as \"pure\" UI components in a framework like React.\n//\n// Of course, since canonical objects may be shared widely between\n// unrelated consumers, it's important to think of them as immutable, even\n// though they are not actually frozen with Object.freeze in production,\n// due to the extra performance overhead that comes with frozen objects.\n//\n// Custom scalar objects whose internal class name is neither Array nor\n// Object can be included safely in the admitted tree, but they will not\n// be replaced with a canonical version (to put it another way, they are\n// assumed to be canonical already).\n//\n// If we ignore custom objects, no detection of cycles or repeated object\n// references is currently required by the StoreReader class, since\n// GraphQL result objects are JSON-serializable trees (and thus contain\n// neither cycles nor repeated subtrees), so we can avoid the complexity\n// of keeping track of objects we've already seen during the recursion of\n// the admit method.\n//\n// In the future, we may consider adding additional cases to the switch\n// statement to handle other common object types, such as \"[object Date]\"\n// objects, as needed.\nvar ObjectCanon = /** @class */ (function () {\n    function ObjectCanon() {\n        // Set of all canonical objects this ObjectCanon has admitted, allowing\n        // canon.admit to return previously-canonicalized objects immediately.\n        this.known = new (canUseWeakSet ? WeakSet : Set)();\n        // Efficient storage/lookup structure for canonical objects.\n        this.pool = new Trie(canUseWeakMap);\n        // Make the ObjectCanon assume this value has already been\n        // canonicalized.\n        this.passes = new WeakMap();\n        // Arrays that contain the same elements in a different order can share\n        // the same SortedKeysInfo object, to save memory.\n        this.keysByJSON = new Map();\n        // This has to come last because it depends on keysByJSON.\n        this.empty = this.admit({});\n    }\n    ObjectCanon.prototype.isKnown = function (value) {\n        return isObjectOrArray(value) && this.known.has(value);\n    };\n    ObjectCanon.prototype.pass = function (value) {\n        if (isObjectOrArray(value)) {\n            var copy = shallowCopy(value);\n            this.passes.set(copy, value);\n            return copy;\n        }\n        return value;\n    };\n    ObjectCanon.prototype.admit = function (value) {\n        var _this = this;\n        if (isObjectOrArray(value)) {\n            var original = this.passes.get(value);\n            if (original)\n                return original;\n            var proto = Object.getPrototypeOf(value);\n            switch (proto) {\n                case Array.prototype: {\n                    if (this.known.has(value))\n                        return value;\n                    var array = value.map(this.admit, this);\n                    // Arrays are looked up in the Trie using their recursively\n                    // canonicalized elements, and the known version of the array is\n                    // preserved as node.array.\n                    var node = this.pool.lookupArray(array);\n                    if (!node.array) {\n                        this.known.add((node.array = array));\n                        // Since canonical arrays may be shared widely between\n                        // unrelated consumers, it's important to regard them as\n                        // immutable, even if they are not frozen in production.\n                        if (globalThis.__DEV__ !== false) {\n                            Object.freeze(array);\n                        }\n                    }\n                    return node.array;\n                }\n                case null:\n                case Object.prototype: {\n                    if (this.known.has(value))\n                        return value;\n                    var proto_1 = Object.getPrototypeOf(value);\n                    var array_1 = [proto_1];\n                    var keys = this.sortedKeys(value);\n                    array_1.push(keys.json);\n                    var firstValueIndex_1 = array_1.length;\n                    keys.sorted.forEach(function (key) {\n                        array_1.push(_this.admit(value[key]));\n                    });\n                    // Objects are looked up in the Trie by their prototype (which\n                    // is *not* recursively canonicalized), followed by a JSON\n                    // representation of their (sorted) keys, followed by the\n                    // sequence of recursively canonicalized values corresponding to\n                    // those keys. To keep the final results unambiguous with other\n                    // sequences (such as arrays that just happen to contain [proto,\n                    // keys.json, value1, value2, ...]), the known version of the\n                    // object is stored as node.object.\n                    var node = this.pool.lookupArray(array_1);\n                    if (!node.object) {\n                        var obj_1 = (node.object = Object.create(proto_1));\n                        this.known.add(obj_1);\n                        keys.sorted.forEach(function (key, i) {\n                            obj_1[key] = array_1[firstValueIndex_1 + i];\n                        });\n                        // Since canonical objects may be shared widely between\n                        // unrelated consumers, it's important to regard them as\n                        // immutable, even if they are not frozen in production.\n                        if (globalThis.__DEV__ !== false) {\n                            Object.freeze(obj_1);\n                        }\n                    }\n                    return node.object;\n                }\n            }\n        }\n        return value;\n    };\n    // It's worthwhile to cache the sorting of arrays of strings, since the\n    // same initial unsorted arrays tend to be encountered many times.\n    // Fortunately, we can reuse the Trie machinery to look up the sorted\n    // arrays in linear time (which is faster than sorting large arrays).\n    ObjectCanon.prototype.sortedKeys = function (obj) {\n        var keys = Object.keys(obj);\n        var node = this.pool.lookupArray(keys);\n        if (!node.keys) {\n            keys.sort();\n            var json = JSON.stringify(keys);\n            if (!(node.keys = this.keysByJSON.get(json))) {\n                this.keysByJSON.set(json, (node.keys = { sorted: keys, json: json }));\n            }\n        }\n        return node.keys;\n    };\n    return ObjectCanon;\n}());\nexport { ObjectCanon };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,aAAa,EAAEC,aAAa,EAAEC,eAAe,IAAIC,eAAe,QAAS,0BAA0B;AAC5G,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,IAAIH,eAAe,CAACG,KAAK,CAAC,EAAE;IACxB,OAAOF,OAAO,CAACE,KAAK,CAAC,GACjBA,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,GACZT,QAAQ,CAAC;MAAEU,SAAS,EAAEC,MAAM,CAACC,cAAc,CAACJ,KAAK;IAAE,CAAC,EAAEA,KAAK,CAAC;EACtE;EACA,OAAOA,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG;IACnB;IACA;IACA,IAAI,CAACC,KAAK,GAAG,KAAKX,aAAa,GAAGY,OAAO,GAAGC,GAAG,EAAE,CAAC;IAClD;IACA,IAAI,CAACC,IAAI,GAAG,IAAIhB,IAAI,CAACC,aAAa,CAAC;IACnC;IACA;IACA,IAAI,CAACgB,MAAM,GAAG,IAAIC,OAAO,CAAC,CAAC;IAC3B;IACA;IACA,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/B;EACAV,WAAW,CAACW,SAAS,CAACC,OAAO,GAAG,UAAUjB,KAAK,EAAE;IAC7C,OAAOH,eAAe,CAACG,KAAK,CAAC,IAAI,IAAI,CAACM,KAAK,CAACY,GAAG,CAAClB,KAAK,CAAC;EAC1D,CAAC;EACDK,WAAW,CAACW,SAAS,CAACG,IAAI,GAAG,UAAUnB,KAAK,EAAE;IAC1C,IAAIH,eAAe,CAACG,KAAK,CAAC,EAAE;MACxB,IAAIoB,IAAI,GAAGrB,WAAW,CAACC,KAAK,CAAC;MAC7B,IAAI,CAACU,MAAM,CAACW,GAAG,CAACD,IAAI,EAAEpB,KAAK,CAAC;MAC5B,OAAOoB,IAAI;IACf;IACA,OAAOpB,KAAK;EAChB,CAAC;EACDK,WAAW,CAACW,SAAS,CAACD,KAAK,GAAG,UAAUf,KAAK,EAAE;IAC3C,IAAIsB,KAAK,GAAG,IAAI;IAChB,IAAIzB,eAAe,CAACG,KAAK,CAAC,EAAE;MACxB,IAAIuB,QAAQ,GAAG,IAAI,CAACb,MAAM,CAACc,GAAG,CAACxB,KAAK,CAAC;MACrC,IAAIuB,QAAQ,EACR,OAAOA,QAAQ;MACnB,IAAIE,KAAK,GAAGtB,MAAM,CAACC,cAAc,CAACJ,KAAK,CAAC;MACxC,QAAQyB,KAAK;QACT,KAAKC,KAAK,CAACV,SAAS;UAAE;YAClB,IAAI,IAAI,CAACV,KAAK,CAACY,GAAG,CAAClB,KAAK,CAAC,EACrB,OAAOA,KAAK;YAChB,IAAI2B,KAAK,GAAG3B,KAAK,CAAC4B,GAAG,CAAC,IAAI,CAACb,KAAK,EAAE,IAAI,CAAC;YACvC;YACA;YACA;YACA,IAAIc,IAAI,GAAG,IAAI,CAACpB,IAAI,CAACqB,WAAW,CAACH,KAAK,CAAC;YACvC,IAAI,CAACE,IAAI,CAACF,KAAK,EAAE;cACb,IAAI,CAACrB,KAAK,CAACyB,GAAG,CAAEF,IAAI,CAACF,KAAK,GAAGA,KAAM,CAAC;cACpC;cACA;cACA;cACA,IAAIK,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;gBAC9B9B,MAAM,CAAC+B,MAAM,CAACP,KAAK,CAAC;cACxB;YACJ;YACA,OAAOE,IAAI,CAACF,KAAK;UACrB;QACA,KAAK,IAAI;QACT,KAAKxB,MAAM,CAACa,SAAS;UAAE;YACnB,IAAI,IAAI,CAACV,KAAK,CAACY,GAAG,CAAClB,KAAK,CAAC,EACrB,OAAOA,KAAK;YAChB,IAAImC,OAAO,GAAGhC,MAAM,CAACC,cAAc,CAACJ,KAAK,CAAC;YAC1C,IAAIoC,OAAO,GAAG,CAACD,OAAO,CAAC;YACvB,IAAIE,IAAI,GAAG,IAAI,CAACC,UAAU,CAACtC,KAAK,CAAC;YACjCoC,OAAO,CAACG,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC;YACvB,IAAIC,iBAAiB,GAAGL,OAAO,CAACM,MAAM;YACtCL,IAAI,CAACM,MAAM,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;cAC/BT,OAAO,CAACG,IAAI,CAACjB,KAAK,CAACP,KAAK,CAACf,KAAK,CAAC6C,GAAG,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC;YACF;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAIhB,IAAI,GAAG,IAAI,CAACpB,IAAI,CAACqB,WAAW,CAACM,OAAO,CAAC;YACzC,IAAI,CAACP,IAAI,CAACiB,MAAM,EAAE;cACd,IAAIC,KAAK,GAAIlB,IAAI,CAACiB,MAAM,GAAG3C,MAAM,CAAC6C,MAAM,CAACb,OAAO,CAAE;cAClD,IAAI,CAAC7B,KAAK,CAACyB,GAAG,CAACgB,KAAK,CAAC;cACrBV,IAAI,CAACM,MAAM,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAEI,CAAC,EAAE;gBAClCF,KAAK,CAACF,GAAG,CAAC,GAAGT,OAAO,CAACK,iBAAiB,GAAGQ,CAAC,CAAC;cAC/C,CAAC,CAAC;cACF;cACA;cACA;cACA,IAAIjB,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;gBAC9B9B,MAAM,CAAC+B,MAAM,CAACa,KAAK,CAAC;cACxB;YACJ;YACA,OAAOlB,IAAI,CAACiB,MAAM;UACtB;MACJ;IACJ;IACA,OAAO9C,KAAK;EAChB,CAAC;EACD;EACA;EACA;EACA;EACAK,WAAW,CAACW,SAAS,CAACsB,UAAU,GAAG,UAAUY,GAAG,EAAE;IAC9C,IAAIb,IAAI,GAAGlC,MAAM,CAACkC,IAAI,CAACa,GAAG,CAAC;IAC3B,IAAIrB,IAAI,GAAG,IAAI,CAACpB,IAAI,CAACqB,WAAW,CAACO,IAAI,CAAC;IACtC,IAAI,CAACR,IAAI,CAACQ,IAAI,EAAE;MACZA,IAAI,CAACc,IAAI,CAAC,CAAC;MACX,IAAIX,IAAI,GAAGY,IAAI,CAACC,SAAS,CAAChB,IAAI,CAAC;MAC/B,IAAI,EAAER,IAAI,CAACQ,IAAI,GAAG,IAAI,CAACzB,UAAU,CAACY,GAAG,CAACgB,IAAI,CAAC,CAAC,EAAE;QAC1C,IAAI,CAAC5B,UAAU,CAACS,GAAG,CAACmB,IAAI,EAAGX,IAAI,CAACQ,IAAI,GAAG;UAAEM,MAAM,EAAEN,IAAI;UAAEG,IAAI,EAAEA;QAAK,CAAE,CAAC;MACzE;IACJ;IACA,OAAOX,IAAI,CAACQ,IAAI;EACpB,CAAC;EACD,OAAOhC,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}