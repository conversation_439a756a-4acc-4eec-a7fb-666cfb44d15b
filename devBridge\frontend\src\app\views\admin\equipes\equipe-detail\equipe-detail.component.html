<div
  class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden"
  *ngIf="equipe"
>
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto p-6 relative z-10">
    <!-- Header futuriste -->
    <div class="mb-8 relative">
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]"
      ></div>
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md"
      ></div>

      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20"
      >
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-0">
          <!-- Bannière avec titre -->
          <div
            class="lg:col-span-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] p-6"
          >
            <h1 class="text-3xl font-bold text-white mb-2 tracking-wide">
              {{ equipe.name }}
            </h1>
            <p class="text-white/80 text-sm mb-6">
              Gestion et collaboration d'équipe - Administration
            </p>

            <!-- Statistiques rapides -->
            <div class="grid grid-cols-3 gap-4">
              <div
                class="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center"
              >
                <i class="fas fa-users text-2xl mb-2 block"></i>
                <span class="text-xl font-bold block">{{
                  equipe.members?.length || 0
                }}</span>
                <small class="text-white/80">Membres</small>
              </div>
              <div
                class="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center"
              >
                <i class="fas fa-tasks text-2xl mb-2 block"></i>
                <span class="text-xl font-bold block">0</span>
                <small class="text-white/80">Tâches</small>
              </div>
              <div
                class="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center"
              >
                <i class="fas fa-calendar-check text-2xl mb-2 block"></i>
                <span class="text-sm font-bold block">{{
                  formatDate(equipe.createdAt)
                }}</span>
                <small class="text-white/80">Créée le</small>
              </div>
            </div>
          </div>

          <!-- Actions rapides -->
          <div
            class="bg-white dark:bg-[#1a1a1a] p-6 flex flex-col justify-center"
          >
            <h4
              class="text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-4 flex items-center"
            >
              <i class="fas fa-bolt mr-2"></i>Actions rapides
            </h4>
            <div class="space-y-3">
              <button
                (click)="navigateToTasks()"
                class="w-full bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <i class="fas fa-tasks mr-2"></i> Gérer les tâches
              </button>
              <button
                (click)="navigateToEditEquipe()"
                class="w-full bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 text-[#4f5fad] dark:text-[#00f7ff] px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105"
              >
                <i class="fas fa-edit mr-2"></i> Modifier l'équipe
              </button>
              <div class="grid grid-cols-2 gap-2">
                <button
                  (click)="navigateToEquipeList()"
                  class="bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105"
                >
                  <i class="fas fa-arrow-left mr-1"></i> Retour
                </button>
                <button
                  (click)="deleteEquipe()"
                  class="bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105"
                >
                  <i class="fas fa-trash mr-1"></i> Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Carte d'informations de l'équipe -->
    <div class="row mb-5">
      <div class="col-12">
        <div
          class="card border-0 shadow-sm rounded-4 overflow-hidden hover-card"
        >
          <div class="card-body p-0">
            <div class="row g-0">
              <!-- Icône et titre -->
              <div
                class="col-md-3 bg-primary text-white p-4 d-flex flex-column justify-content-center align-items-center text-center"
              >
                <div class="icon-circle bg-white text-primary mb-3">
                  <i class="bi bi-info-circle-fill fs-1"></i>
                </div>
                <h3 class="mb-2">À propos</h3>
                <p class="mb-0 text-white-50">
                  Détails et informations sur l'équipe
                </p>
              </div>

              <!-- Contenu -->
              <div class="col-md-9 p-4">
                <div
                  class="d-flex justify-content-between align-items-center mb-4"
                >
                  <h4 class="text-primary mb-0">Description</h4>
                  <span
                    class="badge bg-light text-primary rounded-pill px-3 py-2"
                  >
                    <i class="bi bi-person-fill-gear me-1"></i>
                    Admin:
                    {{
                      equipe.admin
                        ? getUserName(getAdminId(equipe.admin)) || getAdminId(equipe.admin)
                        : "Non défini"
                    }}
                  </span>
                </div>

                <div class="description-box p-3 bg-light rounded-4 mb-4">
                  <p class="lead mb-0">
                    {{
                      equipe.description ||
                        "Aucune description disponible pour cette équipe."
                    }}
                  </p>
                </div>

                <!-- Tags et informations supplémentaires -->
                <div class="d-flex flex-wrap gap-2 mt-4">
                  <span
                    class="badge bg-primary bg-opacity-10 text-primary rounded-pill px-3 py-2"
                  >
                    <i class="bi bi-people-fill me-1"></i>
                    {{ equipe.members?.length || 0 }} membres
                  </span>
                  <span
                    class="badge bg-success bg-opacity-10 text-success rounded-pill px-3 py-2"
                  >
                    <i class="bi bi-calendar-check me-1"></i>
                    Créée le {{ formatDate(equipe.createdAt) }}
                  </span>
                  <span
                    class="badge bg-info bg-opacity-10 text-info rounded-pill px-3 py-2"
                  >
                    <i class="bi bi-kanban me-1"></i>
                    Gestion de projet
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Assistant IA pour la gestion de projet (pleine largeur) -->
    <div class="row mb-5">
      <div class="col-12">
        <div
          class="card border-0 shadow-sm rounded-4 overflow-hidden hover-card"
        >
          <div
            class="card-header border-0 py-4"
            style="background: linear-gradient(45deg, #8e2de2, #4a00e0)"
          >
            <div class="d-flex justify-content-between align-items-center">
              <h3 class="mb-0 text-white d-flex align-items-center">
                <div class="icon-circle bg-white text-primary me-3">
                  <i class="bi bi-robot"></i>
                </div>
                Assistant IA Gemini
              </h3>
              <span class="badge bg-white text-primary rounded-pill px-3 py-2">
                <i class="bi bi-magic me-1"></i>
                Génération de tâches intelligente
              </span>
            </div>
          </div>
          <div class="card-body p-0">
            <app-ai-chat [team]="equipe"></app-ai-chat>
          </div>
        </div>
      </div>
    </div>

    <!-- Section des membres de l'équipe -->
    <div class="row mb-5">
      <div class="col-12">
        <div
          class="card border-0 shadow-sm rounded-4 overflow-hidden hover-card"
        >
          <div
            class="card-header border-0 py-4 d-flex justify-content-between align-items-center"
            style="background: linear-gradient(45deg, #11998e, #38ef7d)"
          >
            <h3 class="mb-0 text-white d-flex align-items-center">
              <div class="icon-circle bg-white text-success me-3">
                <i class="bi bi-people-fill"></i>
              </div>
              Membres de l'équipe
            </h3>
            <span class="badge bg-white text-success rounded-pill px-3 py-2">
              {{ teamMembers.length || 0 }} membres
            </span>
          </div>

          <div class="card-body p-0">
            <!-- Liste des membres -->
            <div
              *ngIf="teamMembers && teamMembers.length > 0; else noMembers"
              class="p-0"
            >
              <div class="row g-0">
                <div class="col-md-8">
                  <div class="member-grid p-4">
                    <div
                      *ngFor="let membre of teamMembers"
                      class="member-card mb-3 p-3 rounded-4 shadow-sm transition"
                    >
                      <div
                        class="d-flex justify-content-between align-items-start"
                      >
                        <!-- Informations du membre -->
                        <div class="d-flex align-items-center">
                          <div
                            class="member-avatar rounded-circle text-white me-3"
                            [ngClass]="{
                              'bg-primary':
                                getUserProfession(membre.user) === 'etudiant',
                              'bg-success':
                                getUserProfession(membre.user) === 'professeur',
                              'bg-secondary': !getUserProfession(membre.user)
                            }"
                          >
                            <i
                              class="bi"
                              [ngClass]="{
                                'bi-mortarboard-fill':
                                  getUserProfession(membre.user) === 'etudiant',
                                'bi-briefcase-fill':
                                  getUserProfession(membre.user) ===
                                  'professeur',
                                'bi-person-fill': !getUserProfession(
                                  membre.user
                                )
                              }"
                            ></i>
                          </div>
                          <div>
                            <h6 class="mb-0 fw-bold">
                              {{ getUserName(membre.user) }}
                            </h6>
                            <div class="d-flex align-items-center mt-1">
                              <span
                                class="badge rounded-pill me-2"
                                [ngClass]="{
                                  'bg-success bg-opacity-10 text-success':
                                    membre.role === 'admin',
                                  'bg-primary bg-opacity-10 text-primary':
                                    membre.role === 'membre'
                                }"
                              >
                                <i
                                  class="bi"
                                  [ngClass]="{
                                    'bi-person-fill-gear':
                                      membre.role === 'admin',
                                    'bi-person': membre.role === 'membre'
                                  }"
                                ></i>
                                {{
                                  membre.role === "admin"
                                    ? "Administrateur"
                                    : "Membre"
                                }}
                              </span>
                              <small class="text-muted">{{
                                getUserProfession(membre.user) === "etudiant"
                                  ? "Étudiant"
                                  : getUserProfession(membre.user) ===
                                    "professeur"
                                  ? "Professeur"
                                  : "Utilisateur"
                              }}</small>
                            </div>
                          </div>
                        </div>

                        <!-- Actions -->
                        <button
                          class="btn btn-sm btn-outline-danger rounded-circle"
                          title="Retirer de l'équipe"
                          (click)="removeMembreFromEquipe(membre._id)"
                        >
                          <i class="bi bi-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Formulaire d'ajout de membre -->
                <div class="col-md-4 bg-light p-4">
                  <h5 class="d-flex align-items-center mb-4 text-success">
                    <i class="bi bi-person-plus-fill me-2"></i>
                    Ajouter un membre
                  </h5>

                  <!-- Afficher un message si aucun utilisateur n'est disponible -->
                  <div
                    *ngIf="availableUsers.length === 0"
                    class="alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center"
                  >
                    <i
                      class="bi bi-info-circle-fill fs-4 me-3 text-primary"
                    ></i>
                    <div>
                      Aucun utilisateur disponible. Veuillez d'abord créer des
                      utilisateurs.
                    </div>
                  </div>

                  <!-- Formulaire d'ajout d'utilisateur avec rôle -->
                  <div
                    *ngIf="availableUsers.length > 0"
                    class="add-member-form"
                  >
                    <div class="mb-3">
                      <label for="userSelect" class="form-label fw-medium"
                        >Utilisateur</label
                      >
                      <select
                        #userSelect
                        id="userSelect"
                        class="form-select border-0 shadow-sm rounded-4 py-2"
                      >
                        <option value="" selected disabled>
                          Sélectionnez un utilisateur
                        </option>
                        <option
                          *ngFor="let user of availableUsers"
                          [value]="user._id || user.id"
                        >
                          {{ user.firstName || "" }}
                          {{ user.lastName || user.name || user.id }}
                          {{ user.email ? "- " + user.email : "" }}
                          {{
                            user.profession
                              ? "(" +
                                (user.profession === "etudiant"
                                  ? "Étudiant"
                                  : "Professeur") +
                                ")"
                              : ""
                          }}
                        </option>
                      </select>
                    </div>

                    <div class="mb-3">
                      <label for="roleSelect" class="form-label fw-medium"
                        >Rôle dans l'équipe</label
                      >
                      <div class="d-flex gap-2">
                        <div class="form-check flex-grow-1">
                          <input
                            class="form-check-input"
                            type="radio"
                            name="roleRadio"
                            id="roleMembre"
                            value="membre"
                            checked
                            #roleMembre
                          />
                          <label
                            class="form-check-label w-100 p-2 border rounded-4 text-center"
                            for="roleMembre"
                          >
                            <i class="bi bi-person d-block fs-4 mb-1"></i>
                            Membre
                          </label>
                        </div>
                        <div class="form-check flex-grow-1">
                          <input
                            class="form-check-input"
                            type="radio"
                            name="roleRadio"
                            id="roleAdmin"
                            value="admin"
                            #roleAdmin
                          />
                          <label
                            class="form-check-label w-100 p-2 border rounded-4 text-center"
                            for="roleAdmin"
                          >
                            <i
                              class="bi bi-person-fill-gear d-block fs-4 mb-1"
                            ></i>
                            Admin
                          </label>
                        </div>
                      </div>
                    </div>

                    <div class="d-grid">
                      <button
                        type="button"
                        class="btn btn-success rounded-4 py-2 shadow-sm"
                        [disabled]="!userSelect.value"
                        (click)="
                          addMembre(
                            userSelect.value,
                            roleMembre.checked ? 'membre' : 'admin'
                          );
                          userSelect.value = ''
                        "
                      >
                        <i class="bi bi-plus-circle me-2"></i> Ajouter à
                        l'équipe
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <ng-template #noMembers>
              <div class="row g-0">
                <div class="col-md-8">
                  <div class="text-center py-5">
                    <div class="empty-state-icon mb-4">
                      <i class="bi bi-people fs-1 text-muted"></i>
                    </div>
                    <h5 class="text-muted">Aucun membre dans cette équipe</h5>
                    <p class="text-muted">
                      Ajoutez des membres à l'équipe en utilisant le formulaire
                      ci-contre.
                    </p>
                  </div>
                </div>

                <!-- Formulaire d'ajout de membre (même code que ci-dessus) -->
                <div class="col-md-4 bg-light p-4">
                  <h5 class="d-flex align-items-center mb-4 text-success">
                    <i class="bi bi-person-plus-fill me-2"></i>
                    Ajouter un membre
                  </h5>

                  <!-- Afficher un message si aucun utilisateur n'est disponible -->
                  <div
                    *ngIf="availableUsers.length === 0"
                    class="alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center"
                  >
                    <i
                      class="bi bi-info-circle-fill fs-4 me-3 text-primary"
                    ></i>
                    <div>
                      Aucun utilisateur disponible. Veuillez d'abord créer des
                      utilisateurs.
                    </div>
                  </div>

                  <!-- Formulaire d'ajout d'utilisateur avec rôle -->
                  <div
                    *ngIf="availableUsers.length > 0"
                    class="add-member-form"
                  >
                    <div class="mb-3">
                      <label for="userSelect2" class="form-label fw-medium"
                        >Utilisateur</label
                      >
                      <select
                        #userSelect2
                        id="userSelect2"
                        class="form-select border-0 shadow-sm rounded-4 py-2"
                      >
                        <option value="" selected disabled>
                          Sélectionnez un utilisateur
                        </option>
                        <option
                          *ngFor="let user of availableUsers"
                          [value]="user._id || user.id"
                        >
                          {{ user.firstName || "" }}
                          {{ user.lastName || user.name || user.id }}
                          {{ user.email ? "- " + user.email : "" }}
                          {{
                            user.profession
                              ? "(" +
                                (user.profession === "etudiant"
                                  ? "Étudiant"
                                  : "Professeur") +
                                ")"
                              : ""
                          }}
                        </option>
                      </select>
                    </div>

                    <div class="mb-3">
                      <label for="roleSelect2" class="form-label fw-medium"
                        >Rôle dans l'équipe</label
                      >
                      <div class="d-flex gap-2">
                        <div class="form-check flex-grow-1">
                          <input
                            class="form-check-input"
                            type="radio"
                            name="roleRadio2"
                            id="roleMembre2"
                            value="membre"
                            checked
                            #roleMembre2
                          />
                          <label
                            class="form-check-label w-100 p-2 border rounded-4 text-center"
                            for="roleMembre2"
                          >
                            <i class="bi bi-person d-block fs-4 mb-1"></i>
                            Membre
                          </label>
                        </div>
                        <div class="form-check flex-grow-1">
                          <input
                            class="form-check-input"
                            type="radio"
                            name="roleRadio2"
                            id="roleAdmin2"
                            value="admin"
                            #roleAdmin2
                          />
                          <label
                            class="form-check-label w-100 p-2 border rounded-4 text-center"
                            for="roleAdmin2"
                          >
                            <i
                              class="bi bi-person-fill-gear d-block fs-4 mb-1"
                            ></i>
                            Admin
                          </label>
                        </div>
                      </div>
                    </div>

                    <div class="d-grid">
                      <button
                        type="button"
                        class="btn btn-success rounded-4 py-2 shadow-sm"
                        [disabled]="!userSelect2.value"
                        (click)="
                          addMembre(
                            userSelect2.value,
                            roleMembre2.checked ? 'membre' : 'admin'
                          );
                          userSelect2.value = ''
                        "
                      >
                        <i class="bi bi-plus-circle me-2"></i> Ajouter à
                        l'équipe
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Message de chargement ou d'erreur -->
<div class="container-fluid py-5 bg-light" *ngIf="!equipe">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-8 text-center">
        <div
          class="alert alert-warning shadow-sm border-0 rounded-3 d-flex align-items-center p-4"
        >
          <i class="bi bi-exclamation-triangle-fill fs-1 me-4 text-warning"></i>
          <div class="fs-5">
            Équipe non trouvée ou en cours de chargement...
          </div>
        </div>
        <button
          class="btn btn-outline-primary rounded-pill mt-4"
          (click)="navigateToEquipeList()"
        >
          <i class="bi bi-arrow-left me-2"></i> Retour à la liste des équipes
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Styles spécifiques pour cette page -->
<style>
  /* Fond dégradé pour l'en-tête du formulaire */
  .bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #6610f2) !important;
  }

  .bg-gradient-light {
    background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;
  }

  /* Animation au survol des éléments */
  .transition {
    transition: all 0.3s ease;
  }

  /* Effet de survol pour les cartes */
  .hover-card {
    transition: all 0.3s ease;
  }

  .hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
  }

  /* Styles pour les cartes de membres */
  .member-card {
    background-color: white;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
  }

  .member-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;
  }

  /* Avatar des membres */
  .member-avatar {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
  }

  /* Icône circulaire */
  .icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
  }

  /* Styles pour la boîte de description */
  .description-box {
    border-left: 4px solid #007bff;
  }

  /* Animation pour les boutons */
  .btn {
    transition: all 0.3s ease;
  }

  .btn:hover {
    transform: translateY(-2px);
  }

  /* Styles pour les badges */
  .badge {
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  /* Styles pour les formulaires */
  .form-select,
  .form-control {
    transition: all 0.2s ease;
  }

  .form-select:focus,
  .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  /* Styles pour les états vides */
  .empty-state-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background-color: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #adb5bd;
  }

  /* Styles pour les sélecteurs de rôle */
  .form-check-label {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .form-check-input:checked + .form-check-label {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: #007bff;
  }

  /* Styles pour les arrondis */
  .rounded-4 {
    border-radius: 0.75rem !important;
  }

  /* Styles pour la grille de membres */
  .member-grid {
    max-height: 500px;
    overflow-y: auto;
  }
</style>
