{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { getNamedType, getNullableType, isInputObjectType, isLeafType, isListType, isNonNullType, isRequiredInputField } from '../../type/definition.mjs';\n\n/**\n * Value literals of correct type\n *\n * A GraphQL document is only valid if all value literals are of the type\n * expected at their position.\n *\n * See https://spec.graphql.org/draft/#sec-Values-of-Correct-Type\n */\nexport function ValuesOfCorrectTypeRule(context) {\n  return {\n    ListValue(node) {\n      // Note: TypeInfo will traverse into a list's item type, so look to the\n      // parent input type to check if it is a list.\n      const type = getNullableType(context.getParentInputType());\n      if (!isListType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      }\n    },\n\n    ObjectValue(node) {\n      const type = getNamedType(context.getInputType());\n      if (!isInputObjectType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      } // Ensure every required field exists.\n\n      const fieldNodeMap = keyMap(node.fields, field => field.name.value);\n      for (const fieldDef of Object.values(type.getFields())) {\n        const fieldNode = fieldNodeMap[fieldDef.name];\n        if (!fieldNode && isRequiredInputField(fieldDef)) {\n          const typeStr = inspect(fieldDef.type);\n          context.reportError(new GraphQLError(`Field \"${type.name}.${fieldDef.name}\" of required type \"${typeStr}\" was not provided.`, {\n            nodes: node\n          }));\n        }\n      }\n    },\n    ObjectField(node) {\n      const parentType = getNamedType(context.getParentInputType());\n      const fieldType = context.getInputType();\n      if (!fieldType && isInputObjectType(parentType)) {\n        const suggestions = suggestionList(node.name.value, Object.keys(parentType.getFields()));\n        context.reportError(new GraphQLError(`Field \"${node.name.value}\" is not defined by type \"${parentType.name}\".` + didYouMean(suggestions), {\n          nodes: node\n        }));\n      }\n    },\n    NullValue(node) {\n      const type = context.getInputType();\n      if (isNonNullType(type)) {\n        context.reportError(new GraphQLError(`Expected value of type \"${inspect(type)}\", found ${print(node)}.`, {\n          nodes: node\n        }));\n      }\n    },\n    EnumValue: node => isValidValueNode(context, node),\n    IntValue: node => isValidValueNode(context, node),\n    FloatValue: node => isValidValueNode(context, node),\n    StringValue: node => isValidValueNode(context, node),\n    BooleanValue: node => isValidValueNode(context, node)\n  };\n}\n/**\n * Any value literal may be a valid representation of a Scalar, depending on\n * that scalar type.\n */\n\nfunction isValidValueNode(context, node) {\n  // Report any error at the full type expected by the location.\n  const locationType = context.getInputType();\n  if (!locationType) {\n    return;\n  }\n  const type = getNamedType(locationType);\n  if (!isLeafType(type)) {\n    const typeStr = inspect(locationType);\n    context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}.`, {\n      nodes: node\n    }));\n    return;\n  } // Scalars and Enums determine if a literal value is valid via parseLiteral(),\n  // which may throw or return an invalid value to indicate failure.\n\n  try {\n    const parseResult = type.parseLiteral(node, undefined\n    /* variables */);\n\n    if (parseResult === undefined) {\n      const typeStr = inspect(locationType);\n      context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}.`, {\n        nodes: node\n      }));\n    }\n  } catch (error) {\n    const typeStr = inspect(locationType);\n    if (error instanceof GraphQLError) {\n      context.reportError(error);\n    } else {\n      context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}; ` + error.message, {\n        nodes: node,\n        originalError: error\n      }));\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "inspect", "keyMap", "suggestionList", "GraphQLError", "print", "getNamedType", "getNullableType", "isInputObjectType", "isLeafType", "isListType", "isNonNullType", "isRequiredInputField", "ValuesOfCorrectTypeRule", "context", "ListValue", "node", "type", "getParentInputType", "isValidValueNode", "ObjectValue", "getInputType", "fieldNodeMap", "fields", "field", "name", "value", "fieldDef", "Object", "values", "getFields", "fieldNode", "typeStr", "reportError", "nodes", "ObjectField", "parentType", "fieldType", "suggestions", "keys", "Null<PERSON><PERSON>ue", "EnumValue", "IntValue", "FloatValue", "StringValue", "BooleanValue", "locationType", "parseResult", "parseLiteral", "undefined", "error", "message", "originalError"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/ValuesOfCorrectTypeRule.mjs"], "sourcesContent": ["import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport {\n  getNamedType,\n  getNullableType,\n  isInputObjectType,\n  isLeafType,\n  isListType,\n  isNonNullType,\n  isRequiredInputField,\n} from '../../type/definition.mjs';\n\n/**\n * Value literals of correct type\n *\n * A GraphQL document is only valid if all value literals are of the type\n * expected at their position.\n *\n * See https://spec.graphql.org/draft/#sec-Values-of-Correct-Type\n */\nexport function ValuesOfCorrectTypeRule(context) {\n  return {\n    ListValue(node) {\n      // Note: TypeInfo will traverse into a list's item type, so look to the\n      // parent input type to check if it is a list.\n      const type = getNullableType(context.getParentInputType());\n\n      if (!isListType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      }\n    },\n\n    ObjectValue(node) {\n      const type = getNamedType(context.getInputType());\n\n      if (!isInputObjectType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      } // Ensure every required field exists.\n\n      const fieldNodeMap = keyMap(node.fields, (field) => field.name.value);\n\n      for (const fieldDef of Object.values(type.getFields())) {\n        const fieldNode = fieldNodeMap[fieldDef.name];\n\n        if (!fieldNode && isRequiredInputField(fieldDef)) {\n          const typeStr = inspect(fieldDef.type);\n          context.reportError(\n            new GraphQLError(\n              `Field \"${type.name}.${fieldDef.name}\" of required type \"${typeStr}\" was not provided.`,\n              {\n                nodes: node,\n              },\n            ),\n          );\n        }\n      }\n    },\n\n    ObjectField(node) {\n      const parentType = getNamedType(context.getParentInputType());\n      const fieldType = context.getInputType();\n\n      if (!fieldType && isInputObjectType(parentType)) {\n        const suggestions = suggestionList(\n          node.name.value,\n          Object.keys(parentType.getFields()),\n        );\n        context.reportError(\n          new GraphQLError(\n            `Field \"${node.name.value}\" is not defined by type \"${parentType.name}\".` +\n              didYouMean(suggestions),\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n\n    NullValue(node) {\n      const type = context.getInputType();\n\n      if (isNonNullType(type)) {\n        context.reportError(\n          new GraphQLError(\n            `Expected value of type \"${inspect(type)}\", found ${print(node)}.`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n\n    EnumValue: (node) => isValidValueNode(context, node),\n    IntValue: (node) => isValidValueNode(context, node),\n    FloatValue: (node) => isValidValueNode(context, node),\n    StringValue: (node) => isValidValueNode(context, node),\n    BooleanValue: (node) => isValidValueNode(context, node),\n  };\n}\n/**\n * Any value literal may be a valid representation of a Scalar, depending on\n * that scalar type.\n */\n\nfunction isValidValueNode(context, node) {\n  // Report any error at the full type expected by the location.\n  const locationType = context.getInputType();\n\n  if (!locationType) {\n    return;\n  }\n\n  const type = getNamedType(locationType);\n\n  if (!isLeafType(type)) {\n    const typeStr = inspect(locationType);\n    context.reportError(\n      new GraphQLError(\n        `Expected value of type \"${typeStr}\", found ${print(node)}.`,\n        {\n          nodes: node,\n        },\n      ),\n    );\n    return;\n  } // Scalars and Enums determine if a literal value is valid via parseLiteral(),\n  // which may throw or return an invalid value to indicate failure.\n\n  try {\n    const parseResult = type.parseLiteral(\n      node,\n      undefined,\n      /* variables */\n    );\n\n    if (parseResult === undefined) {\n      const typeStr = inspect(locationType);\n      context.reportError(\n        new GraphQLError(\n          `Expected value of type \"${typeStr}\", found ${print(node)}.`,\n          {\n            nodes: node,\n          },\n        ),\n      );\n    }\n  } catch (error) {\n    const typeStr = inspect(locationType);\n\n    if (error instanceof GraphQLError) {\n      context.reportError(error);\n    } else {\n      context.reportError(\n        new GraphQLError(\n          `Expected value of type \"${typeStr}\", found ${print(node)}; ` +\n            error.message,\n          {\n            nodes: node,\n            originalError: error,\n          },\n        ),\n      );\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SACEC,YAAY,EACZC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,oBAAoB,QACf,2BAA2B;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EAC/C,OAAO;IACLC,SAASA,CAACC,IAAI,EAAE;MACd;MACA;MACA,MAAMC,IAAI,GAAGV,eAAe,CAACO,OAAO,CAACI,kBAAkB,CAAC,CAAC,CAAC;MAE1D,IAAI,CAACR,UAAU,CAACO,IAAI,CAAC,EAAE;QACrBE,gBAAgB,CAACL,OAAO,EAAEE,IAAI,CAAC;QAC/B,OAAO,KAAK,CAAC,CAAC;MAChB;IACF,CAAC;;IAEDI,WAAWA,CAACJ,IAAI,EAAE;MAChB,MAAMC,IAAI,GAAGX,YAAY,CAACQ,OAAO,CAACO,YAAY,CAAC,CAAC,CAAC;MAEjD,IAAI,CAACb,iBAAiB,CAACS,IAAI,CAAC,EAAE;QAC5BE,gBAAgB,CAACL,OAAO,EAAEE,IAAI,CAAC;QAC/B,OAAO,KAAK,CAAC,CAAC;MAChB,CAAC,CAAC;;MAEF,MAAMM,YAAY,GAAGpB,MAAM,CAACc,IAAI,CAACO,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACC,KAAK,CAAC;MAErE,KAAK,MAAMC,QAAQ,IAAIC,MAAM,CAACC,MAAM,CAACZ,IAAI,CAACa,SAAS,CAAC,CAAC,CAAC,EAAE;QACtD,MAAMC,SAAS,GAAGT,YAAY,CAACK,QAAQ,CAACF,IAAI,CAAC;QAE7C,IAAI,CAACM,SAAS,IAAInB,oBAAoB,CAACe,QAAQ,CAAC,EAAE;UAChD,MAAMK,OAAO,GAAG/B,OAAO,CAAC0B,QAAQ,CAACV,IAAI,CAAC;UACtCH,OAAO,CAACmB,WAAW,CACjB,IAAI7B,YAAY,CACb,UAASa,IAAI,CAACQ,IAAK,IAAGE,QAAQ,CAACF,IAAK,uBAAsBO,OAAQ,qBAAoB,EACvF;YACEE,KAAK,EAAElB;UACT,CACF,CACF,CAAC;QACH;MACF;IACF,CAAC;IAEDmB,WAAWA,CAACnB,IAAI,EAAE;MAChB,MAAMoB,UAAU,GAAG9B,YAAY,CAACQ,OAAO,CAACI,kBAAkB,CAAC,CAAC,CAAC;MAC7D,MAAMmB,SAAS,GAAGvB,OAAO,CAACO,YAAY,CAAC,CAAC;MAExC,IAAI,CAACgB,SAAS,IAAI7B,iBAAiB,CAAC4B,UAAU,CAAC,EAAE;QAC/C,MAAME,WAAW,GAAGnC,cAAc,CAChCa,IAAI,CAACS,IAAI,CAACC,KAAK,EACfE,MAAM,CAACW,IAAI,CAACH,UAAU,CAACN,SAAS,CAAC,CAAC,CACpC,CAAC;QACDhB,OAAO,CAACmB,WAAW,CACjB,IAAI7B,YAAY,CACb,UAASY,IAAI,CAACS,IAAI,CAACC,KAAM,6BAA4BU,UAAU,CAACX,IAAK,IAAG,GACvEzB,UAAU,CAACsC,WAAW,CAAC,EACzB;UACEJ,KAAK,EAAElB;QACT,CACF,CACF,CAAC;MACH;IACF,CAAC;IAEDwB,SAASA,CAACxB,IAAI,EAAE;MACd,MAAMC,IAAI,GAAGH,OAAO,CAACO,YAAY,CAAC,CAAC;MAEnC,IAAIV,aAAa,CAACM,IAAI,CAAC,EAAE;QACvBH,OAAO,CAACmB,WAAW,CACjB,IAAI7B,YAAY,CACb,2BAA0BH,OAAO,CAACgB,IAAI,CAAE,YAAWZ,KAAK,CAACW,IAAI,CAAE,GAAE,EAClE;UACEkB,KAAK,EAAElB;QACT,CACF,CACF,CAAC;MACH;IACF,CAAC;IAEDyB,SAAS,EAAGzB,IAAI,IAAKG,gBAAgB,CAACL,OAAO,EAAEE,IAAI,CAAC;IACpD0B,QAAQ,EAAG1B,IAAI,IAAKG,gBAAgB,CAACL,OAAO,EAAEE,IAAI,CAAC;IACnD2B,UAAU,EAAG3B,IAAI,IAAKG,gBAAgB,CAACL,OAAO,EAAEE,IAAI,CAAC;IACrD4B,WAAW,EAAG5B,IAAI,IAAKG,gBAAgB,CAACL,OAAO,EAAEE,IAAI,CAAC;IACtD6B,YAAY,EAAG7B,IAAI,IAAKG,gBAAgB,CAACL,OAAO,EAAEE,IAAI;EACxD,CAAC;AACH;AACA;AACA;AACA;AACA;;AAEA,SAASG,gBAAgBA,CAACL,OAAO,EAAEE,IAAI,EAAE;EACvC;EACA,MAAM8B,YAAY,GAAGhC,OAAO,CAACO,YAAY,CAAC,CAAC;EAE3C,IAAI,CAACyB,YAAY,EAAE;IACjB;EACF;EAEA,MAAM7B,IAAI,GAAGX,YAAY,CAACwC,YAAY,CAAC;EAEvC,IAAI,CAACrC,UAAU,CAACQ,IAAI,CAAC,EAAE;IACrB,MAAMe,OAAO,GAAG/B,OAAO,CAAC6C,YAAY,CAAC;IACrChC,OAAO,CAACmB,WAAW,CACjB,IAAI7B,YAAY,CACb,2BAA0B4B,OAAQ,YAAW3B,KAAK,CAACW,IAAI,CAAE,GAAE,EAC5D;MACEkB,KAAK,EAAElB;IACT,CACF,CACF,CAAC;IACD;EACF,CAAC,CAAC;EACF;;EAEA,IAAI;IACF,MAAM+B,WAAW,GAAG9B,IAAI,CAAC+B,YAAY,CACnChC,IAAI,EACJiC;IACA,eACF,CAAC;;IAED,IAAIF,WAAW,KAAKE,SAAS,EAAE;MAC7B,MAAMjB,OAAO,GAAG/B,OAAO,CAAC6C,YAAY,CAAC;MACrChC,OAAO,CAACmB,WAAW,CACjB,IAAI7B,YAAY,CACb,2BAA0B4B,OAAQ,YAAW3B,KAAK,CAACW,IAAI,CAAE,GAAE,EAC5D;QACEkB,KAAK,EAAElB;MACT,CACF,CACF,CAAC;IACH;EACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;IACd,MAAMlB,OAAO,GAAG/B,OAAO,CAAC6C,YAAY,CAAC;IAErC,IAAII,KAAK,YAAY9C,YAAY,EAAE;MACjCU,OAAO,CAACmB,WAAW,CAACiB,KAAK,CAAC;IAC5B,CAAC,MAAM;MACLpC,OAAO,CAACmB,WAAW,CACjB,IAAI7B,YAAY,CACb,2BAA0B4B,OAAQ,YAAW3B,KAAK,CAACW,IAAI,CAAE,IAAG,GAC3DkC,KAAK,CAACC,OAAO,EACf;QACEjB,KAAK,EAAElB,IAAI;QACXoC,aAAa,EAAEF;MACjB,CACF,CACF,CAAC;IACH;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}