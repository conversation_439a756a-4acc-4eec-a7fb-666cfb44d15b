{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { mapValue } from '../jsutils/mapValue.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isTypeDefinitionNode, isTypeExtensionNode } from '../language/predicates.mjs';\nimport { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLScalarType, GraphQLUnionType, isEnumType, isInputObjectType, isInterfaceType, isListType, isNonNullType, isObjectType, isScalarType, isUnionType } from '../type/definition.mjs';\nimport { GraphQLDeprecatedDirective, GraphQLDirective, GraphQLSpecifiedByDirective } from '../type/directives.mjs';\nimport { introspectionTypes, isIntrospectionType } from '../type/introspection.mjs';\nimport { isSpecifiedScalarType, specifiedScalarTypes } from '../type/scalars.mjs';\nimport { assertSchema, GraphQLSchema } from '../type/schema.mjs';\nimport { assertValidSDLExtension } from '../validation/validate.mjs';\nimport { getDirectiveValues } from '../execution/values.mjs';\nimport { valueFromAST } from './valueFromAST.mjs';\n\n/**\n * Produces a new schema given an existing schema and a document which may\n * contain GraphQL type extensions and definitions. The original schema will\n * remain unaltered.\n *\n * Because a schema represents a graph of references, a schema cannot be\n * extended without effectively making an entire copy. We do not know until it's\n * too late if subgraphs remain unchanged.\n *\n * This algorithm copies the provided schema, applying extensions while\n * producing the copy. The original schema remains unaltered.\n */\nexport function extendSchema(schema, documentAST, options) {\n  assertSchema(schema);\n  documentAST != null && documentAST.kind === Kind.DOCUMENT || devAssert(false, 'Must provide valid Document AST.');\n  if ((options === null || options === void 0 ? void 0 : options.assumeValid) !== true && (options === null || options === void 0 ? void 0 : options.assumeValidSDL) !== true) {\n    assertValidSDLExtension(documentAST, schema);\n  }\n  const schemaConfig = schema.toConfig();\n  const extendedConfig = extendSchemaImpl(schemaConfig, documentAST, options);\n  return schemaConfig === extendedConfig ? schema : new GraphQLSchema(extendedConfig);\n}\n/**\n * @internal\n */\n\nexport function extendSchemaImpl(schemaConfig, documentAST, options) {\n  var _schemaDef, _schemaDef$descriptio, _schemaDef2, _options$assumeValid;\n\n  // Collect the type definitions and extensions found in the document.\n  const typeDefs = [];\n  const typeExtensionsMap = Object.create(null); // New directives and types are separate because a directives and types can\n  // have the same name. For example, a type named \"skip\".\n\n  const directiveDefs = [];\n  let schemaDef; // Schema extensions are collected which may add additional operation types.\n\n  const schemaExtensions = [];\n  for (const def of documentAST.definitions) {\n    if (def.kind === Kind.SCHEMA_DEFINITION) {\n      schemaDef = def;\n    } else if (def.kind === Kind.SCHEMA_EXTENSION) {\n      schemaExtensions.push(def);\n    } else if (isTypeDefinitionNode(def)) {\n      typeDefs.push(def);\n    } else if (isTypeExtensionNode(def)) {\n      const extendedTypeName = def.name.value;\n      const existingTypeExtensions = typeExtensionsMap[extendedTypeName];\n      typeExtensionsMap[extendedTypeName] = existingTypeExtensions ? existingTypeExtensions.concat([def]) : [def];\n    } else if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      directiveDefs.push(def);\n    }\n  } // If this document contains no new types, extensions, or directives then\n  // return the same unmodified GraphQLSchema instance.\n\n  if (Object.keys(typeExtensionsMap).length === 0 && typeDefs.length === 0 && directiveDefs.length === 0 && schemaExtensions.length === 0 && schemaDef == null) {\n    return schemaConfig;\n  }\n  const typeMap = Object.create(null);\n  for (const existingType of schemaConfig.types) {\n    typeMap[existingType.name] = extendNamedType(existingType);\n  }\n  for (const typeNode of typeDefs) {\n    var _stdTypeMap$name;\n    const name = typeNode.name.value;\n    typeMap[name] = (_stdTypeMap$name = stdTypeMap[name]) !== null && _stdTypeMap$name !== void 0 ? _stdTypeMap$name : buildType(typeNode);\n  }\n  const operationTypes = {\n    // Get the extended root operation types.\n    query: schemaConfig.query && replaceNamedType(schemaConfig.query),\n    mutation: schemaConfig.mutation && replaceNamedType(schemaConfig.mutation),\n    subscription: schemaConfig.subscription && replaceNamedType(schemaConfig.subscription),\n    // Then, incorporate schema definition and all schema extensions.\n    ...(schemaDef && getOperationTypes([schemaDef])),\n    ...getOperationTypes(schemaExtensions)\n  }; // Then produce and return a Schema config with these types.\n\n  return {\n    description: (_schemaDef = schemaDef) === null || _schemaDef === void 0 ? void 0 : (_schemaDef$descriptio = _schemaDef.description) === null || _schemaDef$descriptio === void 0 ? void 0 : _schemaDef$descriptio.value,\n    ...operationTypes,\n    types: Object.values(typeMap),\n    directives: [...schemaConfig.directives.map(replaceDirective), ...directiveDefs.map(buildDirective)],\n    extensions: Object.create(null),\n    astNode: (_schemaDef2 = schemaDef) !== null && _schemaDef2 !== void 0 ? _schemaDef2 : schemaConfig.astNode,\n    extensionASTNodes: schemaConfig.extensionASTNodes.concat(schemaExtensions),\n    assumeValid: (_options$assumeValid = options === null || options === void 0 ? void 0 : options.assumeValid) !== null && _options$assumeValid !== void 0 ? _options$assumeValid : false\n  }; // Below are functions used for producing this schema that have closed over\n  // this scope and have access to the schema, cache, and newly defined types.\n\n  function replaceType(type) {\n    if (isListType(type)) {\n      // @ts-expect-error\n      return new GraphQLList(replaceType(type.ofType));\n    }\n    if (isNonNullType(type)) {\n      // @ts-expect-error\n      return new GraphQLNonNull(replaceType(type.ofType));\n    } // @ts-expect-error FIXME\n\n    return replaceNamedType(type);\n  }\n  function replaceNamedType(type) {\n    // Note: While this could make early assertions to get the correctly\n    // typed values, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    return typeMap[type.name];\n  }\n  function replaceDirective(directive) {\n    const config = directive.toConfig();\n    return new GraphQLDirective({\n      ...config,\n      args: mapValue(config.args, extendArg)\n    });\n  }\n  function extendNamedType(type) {\n    if (isIntrospectionType(type) || isSpecifiedScalarType(type)) {\n      // Builtin types are not extended.\n      return type;\n    }\n    if (isScalarType(type)) {\n      return extendScalarType(type);\n    }\n    if (isObjectType(type)) {\n      return extendObjectType(type);\n    }\n    if (isInterfaceType(type)) {\n      return extendInterfaceType(type);\n    }\n    if (isUnionType(type)) {\n      return extendUnionType(type);\n    }\n    if (isEnumType(type)) {\n      return extendEnumType(type);\n    }\n    if (isInputObjectType(type)) {\n      return extendInputObjectType(type);\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible type definition nodes have been considered.\n\n    false || invariant(false, 'Unexpected type: ' + inspect(type));\n  }\n  function extendInputObjectType(type) {\n    var _typeExtensionsMap$co;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co !== void 0 ? _typeExtensionsMap$co : [];\n    return new GraphQLInputObjectType({\n      ...config,\n      fields: () => ({\n        ...mapValue(config.fields, field => ({\n          ...field,\n          type: replaceType(field.type)\n        })),\n        ...buildInputFieldMap(extensions)\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendEnumType(type) {\n    var _typeExtensionsMap$ty;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$ty = typeExtensionsMap[type.name]) !== null && _typeExtensionsMap$ty !== void 0 ? _typeExtensionsMap$ty : [];\n    return new GraphQLEnumType({\n      ...config,\n      values: {\n        ...config.values,\n        ...buildEnumValueMap(extensions)\n      },\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendScalarType(type) {\n    var _typeExtensionsMap$co2;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co2 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co2 !== void 0 ? _typeExtensionsMap$co2 : [];\n    let specifiedByURL = config.specifiedByURL;\n    for (const extensionNode of extensions) {\n      var _getSpecifiedByURL;\n      specifiedByURL = (_getSpecifiedByURL = getSpecifiedByURL(extensionNode)) !== null && _getSpecifiedByURL !== void 0 ? _getSpecifiedByURL : specifiedByURL;\n    }\n    return new GraphQLScalarType({\n      ...config,\n      specifiedByURL,\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendObjectType(type) {\n    var _typeExtensionsMap$co3;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co3 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co3 !== void 0 ? _typeExtensionsMap$co3 : [];\n    return new GraphQLObjectType({\n      ...config,\n      interfaces: () => [...type.getInterfaces().map(replaceNamedType), ...buildInterfaces(extensions)],\n      fields: () => ({\n        ...mapValue(config.fields, extendField),\n        ...buildFieldMap(extensions)\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendInterfaceType(type) {\n    var _typeExtensionsMap$co4;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co4 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co4 !== void 0 ? _typeExtensionsMap$co4 : [];\n    return new GraphQLInterfaceType({\n      ...config,\n      interfaces: () => [...type.getInterfaces().map(replaceNamedType), ...buildInterfaces(extensions)],\n      fields: () => ({\n        ...mapValue(config.fields, extendField),\n        ...buildFieldMap(extensions)\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendUnionType(type) {\n    var _typeExtensionsMap$co5;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co5 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co5 !== void 0 ? _typeExtensionsMap$co5 : [];\n    return new GraphQLUnionType({\n      ...config,\n      types: () => [...type.getTypes().map(replaceNamedType), ...buildUnionTypes(extensions)],\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendField(field) {\n    return {\n      ...field,\n      type: replaceType(field.type),\n      args: field.args && mapValue(field.args, extendArg)\n    };\n  }\n  function extendArg(arg) {\n    return {\n      ...arg,\n      type: replaceType(arg.type)\n    };\n  }\n  function getOperationTypes(nodes) {\n    const opTypes = {};\n    for (const node of nodes) {\n      var _node$operationTypes;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const operationTypesNodes = /* c8 ignore next */\n      (_node$operationTypes = node.operationTypes) !== null && _node$operationTypes !== void 0 ? _node$operationTypes : [];\n      for (const operationType of operationTypesNodes) {\n        // Note: While this could make early assertions to get the correctly\n        // typed values below, that would throw immediately while type system\n        // validation with validateSchema() will produce more actionable results.\n        // @ts-expect-error\n        opTypes[operationType.operation] = getNamedType(operationType.type);\n      }\n    }\n    return opTypes;\n  }\n  function getNamedType(node) {\n    var _stdTypeMap$name2;\n    const name = node.name.value;\n    const type = (_stdTypeMap$name2 = stdTypeMap[name]) !== null && _stdTypeMap$name2 !== void 0 ? _stdTypeMap$name2 : typeMap[name];\n    if (type === undefined) {\n      throw new Error(`Unknown type: \"${name}\".`);\n    }\n    return type;\n  }\n  function getWrappedType(node) {\n    if (node.kind === Kind.LIST_TYPE) {\n      return new GraphQLList(getWrappedType(node.type));\n    }\n    if (node.kind === Kind.NON_NULL_TYPE) {\n      return new GraphQLNonNull(getWrappedType(node.type));\n    }\n    return getNamedType(node);\n  }\n  function buildDirective(node) {\n    var _node$description;\n    return new GraphQLDirective({\n      name: node.name.value,\n      description: (_node$description = node.description) === null || _node$description === void 0 ? void 0 : _node$description.value,\n      // @ts-expect-error\n      locations: node.locations.map(({\n        value\n      }) => value),\n      isRepeatable: node.repeatable,\n      args: buildArgumentMap(node.arguments),\n      astNode: node\n    });\n  }\n  function buildFieldMap(nodes) {\n    const fieldConfigMap = Object.create(null);\n    for (const node of nodes) {\n      var _node$fields;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const nodeFields = /* c8 ignore next */\n      (_node$fields = node.fields) !== null && _node$fields !== void 0 ? _node$fields : [];\n      for (const field of nodeFields) {\n        var _field$description;\n        fieldConfigMap[field.name.value] = {\n          // Note: While this could make assertions to get the correctly typed\n          // value, that would throw immediately while type system validation\n          // with validateSchema() will produce more actionable results.\n          type: getWrappedType(field.type),\n          description: (_field$description = field.description) === null || _field$description === void 0 ? void 0 : _field$description.value,\n          args: buildArgumentMap(field.arguments),\n          deprecationReason: getDeprecationReason(field),\n          astNode: field\n        };\n      }\n    }\n    return fieldConfigMap;\n  }\n  function buildArgumentMap(args) {\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    const argsNodes = /* c8 ignore next */\n    args !== null && args !== void 0 ? args : [];\n    const argConfigMap = Object.create(null);\n    for (const arg of argsNodes) {\n      var _arg$description;\n\n      // Note: While this could make assertions to get the correctly typed\n      // value, that would throw immediately while type system validation\n      // with validateSchema() will produce more actionable results.\n      const type = getWrappedType(arg.type);\n      argConfigMap[arg.name.value] = {\n        type,\n        description: (_arg$description = arg.description) === null || _arg$description === void 0 ? void 0 : _arg$description.value,\n        defaultValue: valueFromAST(arg.defaultValue, type),\n        deprecationReason: getDeprecationReason(arg),\n        astNode: arg\n      };\n    }\n    return argConfigMap;\n  }\n  function buildInputFieldMap(nodes) {\n    const inputFieldMap = Object.create(null);\n    for (const node of nodes) {\n      var _node$fields2;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const fieldsNodes = /* c8 ignore next */\n      (_node$fields2 = node.fields) !== null && _node$fields2 !== void 0 ? _node$fields2 : [];\n      for (const field of fieldsNodes) {\n        var _field$description2;\n\n        // Note: While this could make assertions to get the correctly typed\n        // value, that would throw immediately while type system validation\n        // with validateSchema() will produce more actionable results.\n        const type = getWrappedType(field.type);\n        inputFieldMap[field.name.value] = {\n          type,\n          description: (_field$description2 = field.description) === null || _field$description2 === void 0 ? void 0 : _field$description2.value,\n          defaultValue: valueFromAST(field.defaultValue, type),\n          deprecationReason: getDeprecationReason(field),\n          astNode: field\n        };\n      }\n    }\n    return inputFieldMap;\n  }\n  function buildEnumValueMap(nodes) {\n    const enumValueMap = Object.create(null);\n    for (const node of nodes) {\n      var _node$values;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const valuesNodes = /* c8 ignore next */\n      (_node$values = node.values) !== null && _node$values !== void 0 ? _node$values : [];\n      for (const value of valuesNodes) {\n        var _value$description;\n        enumValueMap[value.name.value] = {\n          description: (_value$description = value.description) === null || _value$description === void 0 ? void 0 : _value$description.value,\n          deprecationReason: getDeprecationReason(value),\n          astNode: value\n        };\n      }\n    }\n    return enumValueMap;\n  }\n  function buildInterfaces(nodes) {\n    // Note: While this could make assertions to get the correctly typed\n    // values below, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    // @ts-expect-error\n    return nodes.flatMap(\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    node => {\n      var _node$interfaces$map, _node$interfaces;\n      return (/* c8 ignore next */\n        (_node$interfaces$map = (_node$interfaces = node.interfaces) === null || _node$interfaces === void 0 ? void 0 : _node$interfaces.map(getNamedType)) !== null && _node$interfaces$map !== void 0 ? _node$interfaces$map : []\n      );\n    });\n  }\n  function buildUnionTypes(nodes) {\n    // Note: While this could make assertions to get the correctly typed\n    // values below, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    // @ts-expect-error\n    return nodes.flatMap(\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    node => {\n      var _node$types$map, _node$types;\n      return (/* c8 ignore next */\n        (_node$types$map = (_node$types = node.types) === null || _node$types === void 0 ? void 0 : _node$types.map(getNamedType)) !== null && _node$types$map !== void 0 ? _node$types$map : []\n      );\n    });\n  }\n  function buildType(astNode) {\n    var _typeExtensionsMap$na;\n    const name = astNode.name.value;\n    const extensionASTNodes = (_typeExtensionsMap$na = typeExtensionsMap[name]) !== null && _typeExtensionsMap$na !== void 0 ? _typeExtensionsMap$na : [];\n    switch (astNode.kind) {\n      case Kind.OBJECT_TYPE_DEFINITION:\n        {\n          var _astNode$description;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLObjectType({\n            name,\n            description: (_astNode$description = astNode.description) === null || _astNode$description === void 0 ? void 0 : _astNode$description.value,\n            interfaces: () => buildInterfaces(allNodes),\n            fields: () => buildFieldMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.INTERFACE_TYPE_DEFINITION:\n        {\n          var _astNode$description2;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLInterfaceType({\n            name,\n            description: (_astNode$description2 = astNode.description) === null || _astNode$description2 === void 0 ? void 0 : _astNode$description2.value,\n            interfaces: () => buildInterfaces(allNodes),\n            fields: () => buildFieldMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.ENUM_TYPE_DEFINITION:\n        {\n          var _astNode$description3;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLEnumType({\n            name,\n            description: (_astNode$description3 = astNode.description) === null || _astNode$description3 === void 0 ? void 0 : _astNode$description3.value,\n            values: buildEnumValueMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.UNION_TYPE_DEFINITION:\n        {\n          var _astNode$description4;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLUnionType({\n            name,\n            description: (_astNode$description4 = astNode.description) === null || _astNode$description4 === void 0 ? void 0 : _astNode$description4.value,\n            types: () => buildUnionTypes(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.SCALAR_TYPE_DEFINITION:\n        {\n          var _astNode$description5;\n          return new GraphQLScalarType({\n            name,\n            description: (_astNode$description5 = astNode.description) === null || _astNode$description5 === void 0 ? void 0 : _astNode$description5.value,\n            specifiedByURL: getSpecifiedByURL(astNode),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n        {\n          var _astNode$description6;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLInputObjectType({\n            name,\n            description: (_astNode$description6 = astNode.description) === null || _astNode$description6 === void 0 ? void 0 : _astNode$description6.value,\n            fields: () => buildInputFieldMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n    }\n  }\n}\nconst stdTypeMap = keyMap([...specifiedScalarTypes, ...introspectionTypes], type => type.name);\n/**\n * Given a field or enum value node, returns the string value for the\n * deprecation reason.\n */\n\nfunction getDeprecationReason(node) {\n  const deprecated = getDirectiveValues(GraphQLDeprecatedDirective, node); // @ts-expect-error validated by `getDirectiveValues`\n\n  return deprecated === null || deprecated === void 0 ? void 0 : deprecated.reason;\n}\n/**\n * Given a scalar node, returns the string value for the specifiedByURL.\n */\n\nfunction getSpecifiedByURL(node) {\n  const specifiedBy = getDirectiveValues(GraphQLSpecifiedByDirective, node); // @ts-expect-error validated by `getDirectiveValues`\n\n  return specifiedBy === null || specifiedBy === void 0 ? void 0 : specifiedBy.url;\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "invariant", "keyMap", "mapValue", "Kind", "isTypeDefinitionNode", "isTypeExtensionNode", "GraphQLEnumType", "GraphQLInputObjectType", "GraphQLInterfaceType", "GraphQLList", "GraphQLNonNull", "GraphQLObjectType", "GraphQLScalarType", "GraphQLUnionType", "isEnumType", "isInputObjectType", "isInterfaceType", "isListType", "isNonNullType", "isObjectType", "isScalarType", "isUnionType", "GraphQLDeprecatedDirective", "GraphQLDirective", "GraphQLSpecifiedByDirective", "introspectionTypes", "isIntrospectionType", "isSpecifiedScalarType", "specifiedScalarTypes", "assertSchema", "GraphQLSchema", "assertValidSDLExtension", "getDirectiveValues", "valueFromAST", "extendSchema", "schema", "documentAST", "options", "kind", "DOCUMENT", "<PERSON><PERSON><PERSON><PERSON>", "assumeValidSDL", "schemaConfig", "toConfig", "extendedConfig", "extendSchemaImpl", "_schemaDef", "_schemaDef$descriptio", "_schemaDef2", "_options$assumeValid", "typeDefs", "typeExtensionsMap", "Object", "create", "directiveDefs", "schemaDef", "schemaExtensions", "def", "definitions", "SCHEMA_DEFINITION", "SCHEMA_EXTENSION", "push", "extendedTypeName", "name", "value", "existingTypeExtensions", "concat", "DIRECTIVE_DEFINITION", "keys", "length", "typeMap", "existingType", "types", "extendNamedType", "typeNode", "_stdTypeMap$name", "stdTypeMap", "buildType", "operationTypes", "query", "replaceNamedType", "mutation", "subscription", "getOperationTypes", "description", "values", "directives", "map", "replaceDirective", "buildDirective", "extensions", "astNode", "extensionASTNodes", "replaceType", "type", "ofType", "directive", "config", "args", "extendArg", "extendScalarType", "extendObjectType", "extendInterfaceType", "extendUnionType", "extendEnumType", "extendInputObjectType", "_typeExtensionsMap$co", "fields", "field", "buildInputFieldMap", "_typeExtensionsMap$ty", "buildEnumValueMap", "_typeExtensionsMap$co2", "specifiedByURL", "extensionNode", "_getSpecifiedByURL", "getSpecifiedByURL", "_typeExtensionsMap$co3", "interfaces", "getInterfaces", "buildInterfaces", "extendField", "buildFieldMap", "_typeExtensionsMap$co4", "_typeExtensionsMap$co5", "getTypes", "buildUnionTypes", "arg", "nodes", "opTypes", "node", "_node$operationTypes", "operationTypesNodes", "operationType", "operation", "getNamedType", "_stdTypeMap$name2", "undefined", "Error", "getWrappedType", "LIST_TYPE", "NON_NULL_TYPE", "_node$description", "locations", "isRepeatable", "repeatable", "buildArgumentMap", "arguments", "fieldConfigMap", "_node$fields", "nodeFields", "_field$description", "deprecationReason", "getDeprecationReason", "argsNodes", "argConfigMap", "_arg$description", "defaultValue", "inputFieldMap", "_node$fields2", "fieldsNodes", "_field$description2", "enumValueMap", "_node$values", "valuesNodes", "_value$description", "flatMap", "_node$interfaces$map", "_node$interfaces", "_node$types$map", "_node$types", "_typeExtensionsMap$na", "OBJECT_TYPE_DEFINITION", "_astNode$description", "allNodes", "INTERFACE_TYPE_DEFINITION", "_astNode$description2", "ENUM_TYPE_DEFINITION", "_astNode$description3", "UNION_TYPE_DEFINITION", "_astNode$description4", "SCALAR_TYPE_DEFINITION", "_astNode$description5", "INPUT_OBJECT_TYPE_DEFINITION", "_astNode$description6", "deprecated", "reason", "specifiedBy", "url"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/utilities/extendSchema.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { mapValue } from '../jsutils/mapValue.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport {\n  isTypeDefinitionNode,\n  isTypeExtensionNode,\n} from '../language/predicates.mjs';\nimport {\n  GraphQLEnumType,\n  GraphQLInputObjectType,\n  GraphQLInterfaceType,\n  GraphQLList,\n  GraphQLNonNull,\n  GraphQLObjectType,\n  GraphQLScalarType,\n  GraphQLUnionType,\n  isEnumType,\n  isInputObjectType,\n  isInterfaceType,\n  isListType,\n  isNonNullType,\n  isObjectType,\n  isScalarType,\n  isUnionType,\n} from '../type/definition.mjs';\nimport {\n  GraphQLDeprecatedDirective,\n  GraphQLDirective,\n  GraphQLSpecifiedByDirective,\n} from '../type/directives.mjs';\nimport {\n  introspectionTypes,\n  isIntrospectionType,\n} from '../type/introspection.mjs';\nimport {\n  isSpecifiedScalarType,\n  specifiedScalarTypes,\n} from '../type/scalars.mjs';\nimport { assertSchema, GraphQLSchema } from '../type/schema.mjs';\nimport { assertValidSDLExtension } from '../validation/validate.mjs';\nimport { getDirectiveValues } from '../execution/values.mjs';\nimport { valueFromAST } from './valueFromAST.mjs';\n\n/**\n * Produces a new schema given an existing schema and a document which may\n * contain GraphQL type extensions and definitions. The original schema will\n * remain unaltered.\n *\n * Because a schema represents a graph of references, a schema cannot be\n * extended without effectively making an entire copy. We do not know until it's\n * too late if subgraphs remain unchanged.\n *\n * This algorithm copies the provided schema, applying extensions while\n * producing the copy. The original schema remains unaltered.\n */\nexport function extendSchema(schema, documentAST, options) {\n  assertSchema(schema);\n  (documentAST != null && documentAST.kind === Kind.DOCUMENT) ||\n    devAssert(false, 'Must provide valid Document AST.');\n\n  if (\n    (options === null || options === void 0 ? void 0 : options.assumeValid) !==\n      true &&\n    (options === null || options === void 0\n      ? void 0\n      : options.assumeValidSDL) !== true\n  ) {\n    assertValidSDLExtension(documentAST, schema);\n  }\n\n  const schemaConfig = schema.toConfig();\n  const extendedConfig = extendSchemaImpl(schemaConfig, documentAST, options);\n  return schemaConfig === extendedConfig\n    ? schema\n    : new GraphQLSchema(extendedConfig);\n}\n/**\n * @internal\n */\n\nexport function extendSchemaImpl(schemaConfig, documentAST, options) {\n  var _schemaDef, _schemaDef$descriptio, _schemaDef2, _options$assumeValid;\n\n  // Collect the type definitions and extensions found in the document.\n  const typeDefs = [];\n  const typeExtensionsMap = Object.create(null); // New directives and types are separate because a directives and types can\n  // have the same name. For example, a type named \"skip\".\n\n  const directiveDefs = [];\n  let schemaDef; // Schema extensions are collected which may add additional operation types.\n\n  const schemaExtensions = [];\n\n  for (const def of documentAST.definitions) {\n    if (def.kind === Kind.SCHEMA_DEFINITION) {\n      schemaDef = def;\n    } else if (def.kind === Kind.SCHEMA_EXTENSION) {\n      schemaExtensions.push(def);\n    } else if (isTypeDefinitionNode(def)) {\n      typeDefs.push(def);\n    } else if (isTypeExtensionNode(def)) {\n      const extendedTypeName = def.name.value;\n      const existingTypeExtensions = typeExtensionsMap[extendedTypeName];\n      typeExtensionsMap[extendedTypeName] = existingTypeExtensions\n        ? existingTypeExtensions.concat([def])\n        : [def];\n    } else if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      directiveDefs.push(def);\n    }\n  } // If this document contains no new types, extensions, or directives then\n  // return the same unmodified GraphQLSchema instance.\n\n  if (\n    Object.keys(typeExtensionsMap).length === 0 &&\n    typeDefs.length === 0 &&\n    directiveDefs.length === 0 &&\n    schemaExtensions.length === 0 &&\n    schemaDef == null\n  ) {\n    return schemaConfig;\n  }\n\n  const typeMap = Object.create(null);\n\n  for (const existingType of schemaConfig.types) {\n    typeMap[existingType.name] = extendNamedType(existingType);\n  }\n\n  for (const typeNode of typeDefs) {\n    var _stdTypeMap$name;\n\n    const name = typeNode.name.value;\n    typeMap[name] =\n      (_stdTypeMap$name = stdTypeMap[name]) !== null &&\n      _stdTypeMap$name !== void 0\n        ? _stdTypeMap$name\n        : buildType(typeNode);\n  }\n\n  const operationTypes = {\n    // Get the extended root operation types.\n    query: schemaConfig.query && replaceNamedType(schemaConfig.query),\n    mutation: schemaConfig.mutation && replaceNamedType(schemaConfig.mutation),\n    subscription:\n      schemaConfig.subscription && replaceNamedType(schemaConfig.subscription),\n    // Then, incorporate schema definition and all schema extensions.\n    ...(schemaDef && getOperationTypes([schemaDef])),\n    ...getOperationTypes(schemaExtensions),\n  }; // Then produce and return a Schema config with these types.\n\n  return {\n    description:\n      (_schemaDef = schemaDef) === null || _schemaDef === void 0\n        ? void 0\n        : (_schemaDef$descriptio = _schemaDef.description) === null ||\n          _schemaDef$descriptio === void 0\n        ? void 0\n        : _schemaDef$descriptio.value,\n    ...operationTypes,\n    types: Object.values(typeMap),\n    directives: [\n      ...schemaConfig.directives.map(replaceDirective),\n      ...directiveDefs.map(buildDirective),\n    ],\n    extensions: Object.create(null),\n    astNode:\n      (_schemaDef2 = schemaDef) !== null && _schemaDef2 !== void 0\n        ? _schemaDef2\n        : schemaConfig.astNode,\n    extensionASTNodes: schemaConfig.extensionASTNodes.concat(schemaExtensions),\n    assumeValid:\n      (_options$assumeValid =\n        options === null || options === void 0\n          ? void 0\n          : options.assumeValid) !== null && _options$assumeValid !== void 0\n        ? _options$assumeValid\n        : false,\n  }; // Below are functions used for producing this schema that have closed over\n  // this scope and have access to the schema, cache, and newly defined types.\n\n  function replaceType(type) {\n    if (isListType(type)) {\n      // @ts-expect-error\n      return new GraphQLList(replaceType(type.ofType));\n    }\n\n    if (isNonNullType(type)) {\n      // @ts-expect-error\n      return new GraphQLNonNull(replaceType(type.ofType));\n    } // @ts-expect-error FIXME\n\n    return replaceNamedType(type);\n  }\n\n  function replaceNamedType(type) {\n    // Note: While this could make early assertions to get the correctly\n    // typed values, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    return typeMap[type.name];\n  }\n\n  function replaceDirective(directive) {\n    const config = directive.toConfig();\n    return new GraphQLDirective({\n      ...config,\n      args: mapValue(config.args, extendArg),\n    });\n  }\n\n  function extendNamedType(type) {\n    if (isIntrospectionType(type) || isSpecifiedScalarType(type)) {\n      // Builtin types are not extended.\n      return type;\n    }\n\n    if (isScalarType(type)) {\n      return extendScalarType(type);\n    }\n\n    if (isObjectType(type)) {\n      return extendObjectType(type);\n    }\n\n    if (isInterfaceType(type)) {\n      return extendInterfaceType(type);\n    }\n\n    if (isUnionType(type)) {\n      return extendUnionType(type);\n    }\n\n    if (isEnumType(type)) {\n      return extendEnumType(type);\n    }\n\n    if (isInputObjectType(type)) {\n      return extendInputObjectType(type);\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible type definition nodes have been considered.\n\n    false || invariant(false, 'Unexpected type: ' + inspect(type));\n  }\n\n  function extendInputObjectType(type) {\n    var _typeExtensionsMap$co;\n\n    const config = type.toConfig();\n    const extensions =\n      (_typeExtensionsMap$co = typeExtensionsMap[config.name]) !== null &&\n      _typeExtensionsMap$co !== void 0\n        ? _typeExtensionsMap$co\n        : [];\n    return new GraphQLInputObjectType({\n      ...config,\n      fields: () => ({\n        ...mapValue(config.fields, (field) => ({\n          ...field,\n          type: replaceType(field.type),\n        })),\n        ...buildInputFieldMap(extensions),\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions),\n    });\n  }\n\n  function extendEnumType(type) {\n    var _typeExtensionsMap$ty;\n\n    const config = type.toConfig();\n    const extensions =\n      (_typeExtensionsMap$ty = typeExtensionsMap[type.name]) !== null &&\n      _typeExtensionsMap$ty !== void 0\n        ? _typeExtensionsMap$ty\n        : [];\n    return new GraphQLEnumType({\n      ...config,\n      values: { ...config.values, ...buildEnumValueMap(extensions) },\n      extensionASTNodes: config.extensionASTNodes.concat(extensions),\n    });\n  }\n\n  function extendScalarType(type) {\n    var _typeExtensionsMap$co2;\n\n    const config = type.toConfig();\n    const extensions =\n      (_typeExtensionsMap$co2 = typeExtensionsMap[config.name]) !== null &&\n      _typeExtensionsMap$co2 !== void 0\n        ? _typeExtensionsMap$co2\n        : [];\n    let specifiedByURL = config.specifiedByURL;\n\n    for (const extensionNode of extensions) {\n      var _getSpecifiedByURL;\n\n      specifiedByURL =\n        (_getSpecifiedByURL = getSpecifiedByURL(extensionNode)) !== null &&\n        _getSpecifiedByURL !== void 0\n          ? _getSpecifiedByURL\n          : specifiedByURL;\n    }\n\n    return new GraphQLScalarType({\n      ...config,\n      specifiedByURL,\n      extensionASTNodes: config.extensionASTNodes.concat(extensions),\n    });\n  }\n\n  function extendObjectType(type) {\n    var _typeExtensionsMap$co3;\n\n    const config = type.toConfig();\n    const extensions =\n      (_typeExtensionsMap$co3 = typeExtensionsMap[config.name]) !== null &&\n      _typeExtensionsMap$co3 !== void 0\n        ? _typeExtensionsMap$co3\n        : [];\n    return new GraphQLObjectType({\n      ...config,\n      interfaces: () => [\n        ...type.getInterfaces().map(replaceNamedType),\n        ...buildInterfaces(extensions),\n      ],\n      fields: () => ({\n        ...mapValue(config.fields, extendField),\n        ...buildFieldMap(extensions),\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions),\n    });\n  }\n\n  function extendInterfaceType(type) {\n    var _typeExtensionsMap$co4;\n\n    const config = type.toConfig();\n    const extensions =\n      (_typeExtensionsMap$co4 = typeExtensionsMap[config.name]) !== null &&\n      _typeExtensionsMap$co4 !== void 0\n        ? _typeExtensionsMap$co4\n        : [];\n    return new GraphQLInterfaceType({\n      ...config,\n      interfaces: () => [\n        ...type.getInterfaces().map(replaceNamedType),\n        ...buildInterfaces(extensions),\n      ],\n      fields: () => ({\n        ...mapValue(config.fields, extendField),\n        ...buildFieldMap(extensions),\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions),\n    });\n  }\n\n  function extendUnionType(type) {\n    var _typeExtensionsMap$co5;\n\n    const config = type.toConfig();\n    const extensions =\n      (_typeExtensionsMap$co5 = typeExtensionsMap[config.name]) !== null &&\n      _typeExtensionsMap$co5 !== void 0\n        ? _typeExtensionsMap$co5\n        : [];\n    return new GraphQLUnionType({\n      ...config,\n      types: () => [\n        ...type.getTypes().map(replaceNamedType),\n        ...buildUnionTypes(extensions),\n      ],\n      extensionASTNodes: config.extensionASTNodes.concat(extensions),\n    });\n  }\n\n  function extendField(field) {\n    return {\n      ...field,\n      type: replaceType(field.type),\n      args: field.args && mapValue(field.args, extendArg),\n    };\n  }\n\n  function extendArg(arg) {\n    return { ...arg, type: replaceType(arg.type) };\n  }\n\n  function getOperationTypes(nodes) {\n    const opTypes = {};\n\n    for (const node of nodes) {\n      var _node$operationTypes;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const operationTypesNodes =\n        /* c8 ignore next */\n        (_node$operationTypes = node.operationTypes) !== null &&\n        _node$operationTypes !== void 0\n          ? _node$operationTypes\n          : [];\n\n      for (const operationType of operationTypesNodes) {\n        // Note: While this could make early assertions to get the correctly\n        // typed values below, that would throw immediately while type system\n        // validation with validateSchema() will produce more actionable results.\n        // @ts-expect-error\n        opTypes[operationType.operation] = getNamedType(operationType.type);\n      }\n    }\n\n    return opTypes;\n  }\n\n  function getNamedType(node) {\n    var _stdTypeMap$name2;\n\n    const name = node.name.value;\n    const type =\n      (_stdTypeMap$name2 = stdTypeMap[name]) !== null &&\n      _stdTypeMap$name2 !== void 0\n        ? _stdTypeMap$name2\n        : typeMap[name];\n\n    if (type === undefined) {\n      throw new Error(`Unknown type: \"${name}\".`);\n    }\n\n    return type;\n  }\n\n  function getWrappedType(node) {\n    if (node.kind === Kind.LIST_TYPE) {\n      return new GraphQLList(getWrappedType(node.type));\n    }\n\n    if (node.kind === Kind.NON_NULL_TYPE) {\n      return new GraphQLNonNull(getWrappedType(node.type));\n    }\n\n    return getNamedType(node);\n  }\n\n  function buildDirective(node) {\n    var _node$description;\n\n    return new GraphQLDirective({\n      name: node.name.value,\n      description:\n        (_node$description = node.description) === null ||\n        _node$description === void 0\n          ? void 0\n          : _node$description.value,\n      // @ts-expect-error\n      locations: node.locations.map(({ value }) => value),\n      isRepeatable: node.repeatable,\n      args: buildArgumentMap(node.arguments),\n      astNode: node,\n    });\n  }\n\n  function buildFieldMap(nodes) {\n    const fieldConfigMap = Object.create(null);\n\n    for (const node of nodes) {\n      var _node$fields;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const nodeFields =\n        /* c8 ignore next */\n        (_node$fields = node.fields) !== null && _node$fields !== void 0\n          ? _node$fields\n          : [];\n\n      for (const field of nodeFields) {\n        var _field$description;\n\n        fieldConfigMap[field.name.value] = {\n          // Note: While this could make assertions to get the correctly typed\n          // value, that would throw immediately while type system validation\n          // with validateSchema() will produce more actionable results.\n          type: getWrappedType(field.type),\n          description:\n            (_field$description = field.description) === null ||\n            _field$description === void 0\n              ? void 0\n              : _field$description.value,\n          args: buildArgumentMap(field.arguments),\n          deprecationReason: getDeprecationReason(field),\n          astNode: field,\n        };\n      }\n    }\n\n    return fieldConfigMap;\n  }\n\n  function buildArgumentMap(args) {\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    const argsNodes =\n      /* c8 ignore next */\n      args !== null && args !== void 0 ? args : [];\n    const argConfigMap = Object.create(null);\n\n    for (const arg of argsNodes) {\n      var _arg$description;\n\n      // Note: While this could make assertions to get the correctly typed\n      // value, that would throw immediately while type system validation\n      // with validateSchema() will produce more actionable results.\n      const type = getWrappedType(arg.type);\n      argConfigMap[arg.name.value] = {\n        type,\n        description:\n          (_arg$description = arg.description) === null ||\n          _arg$description === void 0\n            ? void 0\n            : _arg$description.value,\n        defaultValue: valueFromAST(arg.defaultValue, type),\n        deprecationReason: getDeprecationReason(arg),\n        astNode: arg,\n      };\n    }\n\n    return argConfigMap;\n  }\n\n  function buildInputFieldMap(nodes) {\n    const inputFieldMap = Object.create(null);\n\n    for (const node of nodes) {\n      var _node$fields2;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const fieldsNodes =\n        /* c8 ignore next */\n        (_node$fields2 = node.fields) !== null && _node$fields2 !== void 0\n          ? _node$fields2\n          : [];\n\n      for (const field of fieldsNodes) {\n        var _field$description2;\n\n        // Note: While this could make assertions to get the correctly typed\n        // value, that would throw immediately while type system validation\n        // with validateSchema() will produce more actionable results.\n        const type = getWrappedType(field.type);\n        inputFieldMap[field.name.value] = {\n          type,\n          description:\n            (_field$description2 = field.description) === null ||\n            _field$description2 === void 0\n              ? void 0\n              : _field$description2.value,\n          defaultValue: valueFromAST(field.defaultValue, type),\n          deprecationReason: getDeprecationReason(field),\n          astNode: field,\n        };\n      }\n    }\n\n    return inputFieldMap;\n  }\n\n  function buildEnumValueMap(nodes) {\n    const enumValueMap = Object.create(null);\n\n    for (const node of nodes) {\n      var _node$values;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const valuesNodes =\n        /* c8 ignore next */\n        (_node$values = node.values) !== null && _node$values !== void 0\n          ? _node$values\n          : [];\n\n      for (const value of valuesNodes) {\n        var _value$description;\n\n        enumValueMap[value.name.value] = {\n          description:\n            (_value$description = value.description) === null ||\n            _value$description === void 0\n              ? void 0\n              : _value$description.value,\n          deprecationReason: getDeprecationReason(value),\n          astNode: value,\n        };\n      }\n    }\n\n    return enumValueMap;\n  }\n\n  function buildInterfaces(nodes) {\n    // Note: While this could make assertions to get the correctly typed\n    // values below, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    // @ts-expect-error\n    return nodes.flatMap(\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      (node) => {\n        var _node$interfaces$map, _node$interfaces;\n\n        return (\n          /* c8 ignore next */\n          (_node$interfaces$map =\n            (_node$interfaces = node.interfaces) === null ||\n            _node$interfaces === void 0\n              ? void 0\n              : _node$interfaces.map(getNamedType)) !== null &&\n            _node$interfaces$map !== void 0\n            ? _node$interfaces$map\n            : []\n        );\n      },\n    );\n  }\n\n  function buildUnionTypes(nodes) {\n    // Note: While this could make assertions to get the correctly typed\n    // values below, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    // @ts-expect-error\n    return nodes.flatMap(\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      (node) => {\n        var _node$types$map, _node$types;\n\n        return (\n          /* c8 ignore next */\n          (_node$types$map =\n            (_node$types = node.types) === null || _node$types === void 0\n              ? void 0\n              : _node$types.map(getNamedType)) !== null &&\n            _node$types$map !== void 0\n            ? _node$types$map\n            : []\n        );\n      },\n    );\n  }\n\n  function buildType(astNode) {\n    var _typeExtensionsMap$na;\n\n    const name = astNode.name.value;\n    const extensionASTNodes =\n      (_typeExtensionsMap$na = typeExtensionsMap[name]) !== null &&\n      _typeExtensionsMap$na !== void 0\n        ? _typeExtensionsMap$na\n        : [];\n\n    switch (astNode.kind) {\n      case Kind.OBJECT_TYPE_DEFINITION: {\n        var _astNode$description;\n\n        const allNodes = [astNode, ...extensionASTNodes];\n        return new GraphQLObjectType({\n          name,\n          description:\n            (_astNode$description = astNode.description) === null ||\n            _astNode$description === void 0\n              ? void 0\n              : _astNode$description.value,\n          interfaces: () => buildInterfaces(allNodes),\n          fields: () => buildFieldMap(allNodes),\n          astNode,\n          extensionASTNodes,\n        });\n      }\n\n      case Kind.INTERFACE_TYPE_DEFINITION: {\n        var _astNode$description2;\n\n        const allNodes = [astNode, ...extensionASTNodes];\n        return new GraphQLInterfaceType({\n          name,\n          description:\n            (_astNode$description2 = astNode.description) === null ||\n            _astNode$description2 === void 0\n              ? void 0\n              : _astNode$description2.value,\n          interfaces: () => buildInterfaces(allNodes),\n          fields: () => buildFieldMap(allNodes),\n          astNode,\n          extensionASTNodes,\n        });\n      }\n\n      case Kind.ENUM_TYPE_DEFINITION: {\n        var _astNode$description3;\n\n        const allNodes = [astNode, ...extensionASTNodes];\n        return new GraphQLEnumType({\n          name,\n          description:\n            (_astNode$description3 = astNode.description) === null ||\n            _astNode$description3 === void 0\n              ? void 0\n              : _astNode$description3.value,\n          values: buildEnumValueMap(allNodes),\n          astNode,\n          extensionASTNodes,\n        });\n      }\n\n      case Kind.UNION_TYPE_DEFINITION: {\n        var _astNode$description4;\n\n        const allNodes = [astNode, ...extensionASTNodes];\n        return new GraphQLUnionType({\n          name,\n          description:\n            (_astNode$description4 = astNode.description) === null ||\n            _astNode$description4 === void 0\n              ? void 0\n              : _astNode$description4.value,\n          types: () => buildUnionTypes(allNodes),\n          astNode,\n          extensionASTNodes,\n        });\n      }\n\n      case Kind.SCALAR_TYPE_DEFINITION: {\n        var _astNode$description5;\n\n        return new GraphQLScalarType({\n          name,\n          description:\n            (_astNode$description5 = astNode.description) === null ||\n            _astNode$description5 === void 0\n              ? void 0\n              : _astNode$description5.value,\n          specifiedByURL: getSpecifiedByURL(astNode),\n          astNode,\n          extensionASTNodes,\n        });\n      }\n\n      case Kind.INPUT_OBJECT_TYPE_DEFINITION: {\n        var _astNode$description6;\n\n        const allNodes = [astNode, ...extensionASTNodes];\n        return new GraphQLInputObjectType({\n          name,\n          description:\n            (_astNode$description6 = astNode.description) === null ||\n            _astNode$description6 === void 0\n              ? void 0\n              : _astNode$description6.value,\n          fields: () => buildInputFieldMap(allNodes),\n          astNode,\n          extensionASTNodes,\n        });\n      }\n    }\n  }\n}\nconst stdTypeMap = keyMap(\n  [...specifiedScalarTypes, ...introspectionTypes],\n  (type) => type.name,\n);\n/**\n * Given a field or enum value node, returns the string value for the\n * deprecation reason.\n */\n\nfunction getDeprecationReason(node) {\n  const deprecated = getDirectiveValues(GraphQLDeprecatedDirective, node); // @ts-expect-error validated by `getDirectiveValues`\n\n  return deprecated === null || deprecated === void 0\n    ? void 0\n    : deprecated.reason;\n}\n/**\n * Given a scalar node, returns the string value for the specifiedByURL.\n */\n\nfunction getSpecifiedByURL(node) {\n  const specifiedBy = getDirectiveValues(GraphQLSpecifiedByDirective, node); // @ts-expect-error validated by `getDirectiveValues`\n\n  return specifiedBy === null || specifiedBy === void 0\n    ? void 0\n    : specifiedBy.url;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SACEC,oBAAoB,EACpBC,mBAAmB,QACd,4BAA4B;AACnC,SACEC,eAAe,EACfC,sBAAsB,EACtBC,oBAAoB,EACpBC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EACjBC,iBAAiB,EACjBC,gBAAgB,EAChBC,UAAU,EACVC,iBAAiB,EACjBC,eAAe,EACfC,UAAU,EACVC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,WAAW,QACN,wBAAwB;AAC/B,SACEC,0BAA0B,EAC1BC,gBAAgB,EAChBC,2BAA2B,QACtB,wBAAwB;AAC/B,SACEC,kBAAkB,EAClBC,mBAAmB,QACd,2BAA2B;AAClC,SACEC,qBAAqB,EACrBC,oBAAoB,QACf,qBAAqB;AAC5B,SAASC,YAAY,EAAEC,aAAa,QAAQ,oBAAoB;AAChE,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,YAAY,QAAQ,oBAAoB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACzDR,YAAY,CAACM,MAAM,CAAC;EACnBC,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,IAAI,KAAKnC,IAAI,CAACoC,QAAQ,IACxDzC,SAAS,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAEtD,IACE,CAACuC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,WAAW,MACpE,IAAI,IACN,CAACH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GACnC,KAAK,CAAC,GACNA,OAAO,CAACI,cAAc,MAAM,IAAI,EACpC;IACAV,uBAAuB,CAACK,WAAW,EAAED,MAAM,CAAC;EAC9C;EAEA,MAAMO,YAAY,GAAGP,MAAM,CAACQ,QAAQ,CAAC,CAAC;EACtC,MAAMC,cAAc,GAAGC,gBAAgB,CAACH,YAAY,EAAEN,WAAW,EAAEC,OAAO,CAAC;EAC3E,OAAOK,YAAY,KAAKE,cAAc,GAClCT,MAAM,GACN,IAAIL,aAAa,CAACc,cAAc,CAAC;AACvC;AACA;AACA;AACA;;AAEA,OAAO,SAASC,gBAAgBA,CAACH,YAAY,EAAEN,WAAW,EAAEC,OAAO,EAAE;EACnE,IAAIS,UAAU,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,oBAAoB;;EAExE;EACA,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/C;;EAEA,MAAMC,aAAa,GAAG,EAAE;EACxB,IAAIC,SAAS,CAAC,CAAC;;EAEf,MAAMC,gBAAgB,GAAG,EAAE;EAE3B,KAAK,MAAMC,GAAG,IAAIrB,WAAW,CAACsB,WAAW,EAAE;IACzC,IAAID,GAAG,CAACnB,IAAI,KAAKnC,IAAI,CAACwD,iBAAiB,EAAE;MACvCJ,SAAS,GAAGE,GAAG;IACjB,CAAC,MAAM,IAAIA,GAAG,CAACnB,IAAI,KAAKnC,IAAI,CAACyD,gBAAgB,EAAE;MAC7CJ,gBAAgB,CAACK,IAAI,CAACJ,GAAG,CAAC;IAC5B,CAAC,MAAM,IAAIrD,oBAAoB,CAACqD,GAAG,CAAC,EAAE;MACpCP,QAAQ,CAACW,IAAI,CAACJ,GAAG,CAAC;IACpB,CAAC,MAAM,IAAIpD,mBAAmB,CAACoD,GAAG,CAAC,EAAE;MACnC,MAAMK,gBAAgB,GAAGL,GAAG,CAACM,IAAI,CAACC,KAAK;MACvC,MAAMC,sBAAsB,GAAGd,iBAAiB,CAACW,gBAAgB,CAAC;MAClEX,iBAAiB,CAACW,gBAAgB,CAAC,GAAGG,sBAAsB,GACxDA,sBAAsB,CAACC,MAAM,CAAC,CAACT,GAAG,CAAC,CAAC,GACpC,CAACA,GAAG,CAAC;IACX,CAAC,MAAM,IAAIA,GAAG,CAACnB,IAAI,KAAKnC,IAAI,CAACgE,oBAAoB,EAAE;MACjDb,aAAa,CAACO,IAAI,CAACJ,GAAG,CAAC;IACzB;EACF,CAAC,CAAC;EACF;;EAEA,IACEL,MAAM,CAACgB,IAAI,CAACjB,iBAAiB,CAAC,CAACkB,MAAM,KAAK,CAAC,IAC3CnB,QAAQ,CAACmB,MAAM,KAAK,CAAC,IACrBf,aAAa,CAACe,MAAM,KAAK,CAAC,IAC1Bb,gBAAgB,CAACa,MAAM,KAAK,CAAC,IAC7Bd,SAAS,IAAI,IAAI,EACjB;IACA,OAAOb,YAAY;EACrB;EAEA,MAAM4B,OAAO,GAAGlB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAEnC,KAAK,MAAMkB,YAAY,IAAI7B,YAAY,CAAC8B,KAAK,EAAE;IAC7CF,OAAO,CAACC,YAAY,CAACR,IAAI,CAAC,GAAGU,eAAe,CAACF,YAAY,CAAC;EAC5D;EAEA,KAAK,MAAMG,QAAQ,IAAIxB,QAAQ,EAAE;IAC/B,IAAIyB,gBAAgB;IAEpB,MAAMZ,IAAI,GAAGW,QAAQ,CAACX,IAAI,CAACC,KAAK;IAChCM,OAAO,CAACP,IAAI,CAAC,GACX,CAACY,gBAAgB,GAAGC,UAAU,CAACb,IAAI,CAAC,MAAM,IAAI,IAC9CY,gBAAgB,KAAK,KAAK,CAAC,GACvBA,gBAAgB,GAChBE,SAAS,CAACH,QAAQ,CAAC;EAC3B;EAEA,MAAMI,cAAc,GAAG;IACrB;IACAC,KAAK,EAAErC,YAAY,CAACqC,KAAK,IAAIC,gBAAgB,CAACtC,YAAY,CAACqC,KAAK,CAAC;IACjEE,QAAQ,EAAEvC,YAAY,CAACuC,QAAQ,IAAID,gBAAgB,CAACtC,YAAY,CAACuC,QAAQ,CAAC;IAC1EC,YAAY,EACVxC,YAAY,CAACwC,YAAY,IAAIF,gBAAgB,CAACtC,YAAY,CAACwC,YAAY,CAAC;IAC1E;IACA,IAAI3B,SAAS,IAAI4B,iBAAiB,CAAC,CAAC5B,SAAS,CAAC,CAAC,CAAC;IAChD,GAAG4B,iBAAiB,CAAC3B,gBAAgB;EACvC,CAAC,CAAC,CAAC;;EAEH,OAAO;IACL4B,WAAW,EACT,CAACtC,UAAU,GAAGS,SAAS,MAAM,IAAI,IAAIT,UAAU,KAAK,KAAK,CAAC,GACtD,KAAK,CAAC,GACN,CAACC,qBAAqB,GAAGD,UAAU,CAACsC,WAAW,MAAM,IAAI,IACzDrC,qBAAqB,KAAK,KAAK,CAAC,GAChC,KAAK,CAAC,GACNA,qBAAqB,CAACiB,KAAK;IACjC,GAAGc,cAAc;IACjBN,KAAK,EAAEpB,MAAM,CAACiC,MAAM,CAACf,OAAO,CAAC;IAC7BgB,UAAU,EAAE,CACV,GAAG5C,YAAY,CAAC4C,UAAU,CAACC,GAAG,CAACC,gBAAgB,CAAC,EAChD,GAAGlC,aAAa,CAACiC,GAAG,CAACE,cAAc,CAAC,CACrC;IACDC,UAAU,EAAEtC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/BsC,OAAO,EACL,CAAC3C,WAAW,GAAGO,SAAS,MAAM,IAAI,IAAIP,WAAW,KAAK,KAAK,CAAC,GACxDA,WAAW,GACXN,YAAY,CAACiD,OAAO;IAC1BC,iBAAiB,EAAElD,YAAY,CAACkD,iBAAiB,CAAC1B,MAAM,CAACV,gBAAgB,CAAC;IAC1EhB,WAAW,EACT,CAACS,oBAAoB,GACnBZ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAClC,KAAK,CAAC,GACNA,OAAO,CAACG,WAAW,MAAM,IAAI,IAAIS,oBAAoB,KAAK,KAAK,CAAC,GAClEA,oBAAoB,GACpB;EACR,CAAC,CAAC,CAAC;EACH;;EAEA,SAAS4C,WAAWA,CAACC,IAAI,EAAE;IACzB,IAAI7E,UAAU,CAAC6E,IAAI,CAAC,EAAE;MACpB;MACA,OAAO,IAAIrF,WAAW,CAACoF,WAAW,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC;IAClD;IAEA,IAAI7E,aAAa,CAAC4E,IAAI,CAAC,EAAE;MACvB;MACA,OAAO,IAAIpF,cAAc,CAACmF,WAAW,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC;;IAEF,OAAOf,gBAAgB,CAACc,IAAI,CAAC;EAC/B;EAEA,SAASd,gBAAgBA,CAACc,IAAI,EAAE;IAC9B;IACA;IACA;IACA,OAAOxB,OAAO,CAACwB,IAAI,CAAC/B,IAAI,CAAC;EAC3B;EAEA,SAASyB,gBAAgBA,CAACQ,SAAS,EAAE;IACnC,MAAMC,MAAM,GAAGD,SAAS,CAACrD,QAAQ,CAAC,CAAC;IACnC,OAAO,IAAIpB,gBAAgB,CAAC;MAC1B,GAAG0E,MAAM;MACTC,IAAI,EAAEhG,QAAQ,CAAC+F,MAAM,CAACC,IAAI,EAAEC,SAAS;IACvC,CAAC,CAAC;EACJ;EAEA,SAAS1B,eAAeA,CAACqB,IAAI,EAAE;IAC7B,IAAIpE,mBAAmB,CAACoE,IAAI,CAAC,IAAInE,qBAAqB,CAACmE,IAAI,CAAC,EAAE;MAC5D;MACA,OAAOA,IAAI;IACb;IAEA,IAAI1E,YAAY,CAAC0E,IAAI,CAAC,EAAE;MACtB,OAAOM,gBAAgB,CAACN,IAAI,CAAC;IAC/B;IAEA,IAAI3E,YAAY,CAAC2E,IAAI,CAAC,EAAE;MACtB,OAAOO,gBAAgB,CAACP,IAAI,CAAC;IAC/B;IAEA,IAAI9E,eAAe,CAAC8E,IAAI,CAAC,EAAE;MACzB,OAAOQ,mBAAmB,CAACR,IAAI,CAAC;IAClC;IAEA,IAAIzE,WAAW,CAACyE,IAAI,CAAC,EAAE;MACrB,OAAOS,eAAe,CAACT,IAAI,CAAC;IAC9B;IAEA,IAAIhF,UAAU,CAACgF,IAAI,CAAC,EAAE;MACpB,OAAOU,cAAc,CAACV,IAAI,CAAC;IAC7B;IAEA,IAAI/E,iBAAiB,CAAC+E,IAAI,CAAC,EAAE;MAC3B,OAAOW,qBAAqB,CAACX,IAAI,CAAC;IACpC;IACA;IACA;;IAEA,KAAK,IAAI9F,SAAS,CAAC,KAAK,EAAE,mBAAmB,GAAGD,OAAO,CAAC+F,IAAI,CAAC,CAAC;EAChE;EAEA,SAASW,qBAAqBA,CAACX,IAAI,EAAE;IACnC,IAAIY,qBAAqB;IAEzB,MAAMT,MAAM,GAAGH,IAAI,CAACnD,QAAQ,CAAC,CAAC;IAC9B,MAAM+C,UAAU,GACd,CAACgB,qBAAqB,GAAGvD,iBAAiB,CAAC8C,MAAM,CAAClC,IAAI,CAAC,MAAM,IAAI,IACjE2C,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;IACR,OAAO,IAAInG,sBAAsB,CAAC;MAChC,GAAG0F,MAAM;MACTU,MAAM,EAAEA,CAAA,MAAO;QACb,GAAGzG,QAAQ,CAAC+F,MAAM,CAACU,MAAM,EAAGC,KAAK,KAAM;UACrC,GAAGA,KAAK;UACRd,IAAI,EAAED,WAAW,CAACe,KAAK,CAACd,IAAI;QAC9B,CAAC,CAAC,CAAC;QACH,GAAGe,kBAAkB,CAACnB,UAAU;MAClC,CAAC,CAAC;MACFE,iBAAiB,EAAEK,MAAM,CAACL,iBAAiB,CAAC1B,MAAM,CAACwB,UAAU;IAC/D,CAAC,CAAC;EACJ;EAEA,SAASc,cAAcA,CAACV,IAAI,EAAE;IAC5B,IAAIgB,qBAAqB;IAEzB,MAAMb,MAAM,GAAGH,IAAI,CAACnD,QAAQ,CAAC,CAAC;IAC9B,MAAM+C,UAAU,GACd,CAACoB,qBAAqB,GAAG3D,iBAAiB,CAAC2C,IAAI,CAAC/B,IAAI,CAAC,MAAM,IAAI,IAC/D+C,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;IACR,OAAO,IAAIxG,eAAe,CAAC;MACzB,GAAG2F,MAAM;MACTZ,MAAM,EAAE;QAAE,GAAGY,MAAM,CAACZ,MAAM;QAAE,GAAG0B,iBAAiB,CAACrB,UAAU;MAAE,CAAC;MAC9DE,iBAAiB,EAAEK,MAAM,CAACL,iBAAiB,CAAC1B,MAAM,CAACwB,UAAU;IAC/D,CAAC,CAAC;EACJ;EAEA,SAASU,gBAAgBA,CAACN,IAAI,EAAE;IAC9B,IAAIkB,sBAAsB;IAE1B,MAAMf,MAAM,GAAGH,IAAI,CAACnD,QAAQ,CAAC,CAAC;IAC9B,MAAM+C,UAAU,GACd,CAACsB,sBAAsB,GAAG7D,iBAAiB,CAAC8C,MAAM,CAAClC,IAAI,CAAC,MAAM,IAAI,IAClEiD,sBAAsB,KAAK,KAAK,CAAC,GAC7BA,sBAAsB,GACtB,EAAE;IACR,IAAIC,cAAc,GAAGhB,MAAM,CAACgB,cAAc;IAE1C,KAAK,MAAMC,aAAa,IAAIxB,UAAU,EAAE;MACtC,IAAIyB,kBAAkB;MAEtBF,cAAc,GACZ,CAACE,kBAAkB,GAAGC,iBAAiB,CAACF,aAAa,CAAC,MAAM,IAAI,IAChEC,kBAAkB,KAAK,KAAK,CAAC,GACzBA,kBAAkB,GAClBF,cAAc;IACtB;IAEA,OAAO,IAAIrG,iBAAiB,CAAC;MAC3B,GAAGqF,MAAM;MACTgB,cAAc;MACdrB,iBAAiB,EAAEK,MAAM,CAACL,iBAAiB,CAAC1B,MAAM,CAACwB,UAAU;IAC/D,CAAC,CAAC;EACJ;EAEA,SAASW,gBAAgBA,CAACP,IAAI,EAAE;IAC9B,IAAIuB,sBAAsB;IAE1B,MAAMpB,MAAM,GAAGH,IAAI,CAACnD,QAAQ,CAAC,CAAC;IAC9B,MAAM+C,UAAU,GACd,CAAC2B,sBAAsB,GAAGlE,iBAAiB,CAAC8C,MAAM,CAAClC,IAAI,CAAC,MAAM,IAAI,IAClEsD,sBAAsB,KAAK,KAAK,CAAC,GAC7BA,sBAAsB,GACtB,EAAE;IACR,OAAO,IAAI1G,iBAAiB,CAAC;MAC3B,GAAGsF,MAAM;MACTqB,UAAU,EAAEA,CAAA,KAAM,CAChB,GAAGxB,IAAI,CAACyB,aAAa,CAAC,CAAC,CAAChC,GAAG,CAACP,gBAAgB,CAAC,EAC7C,GAAGwC,eAAe,CAAC9B,UAAU,CAAC,CAC/B;MACDiB,MAAM,EAAEA,CAAA,MAAO;QACb,GAAGzG,QAAQ,CAAC+F,MAAM,CAACU,MAAM,EAAEc,WAAW,CAAC;QACvC,GAAGC,aAAa,CAAChC,UAAU;MAC7B,CAAC,CAAC;MACFE,iBAAiB,EAAEK,MAAM,CAACL,iBAAiB,CAAC1B,MAAM,CAACwB,UAAU;IAC/D,CAAC,CAAC;EACJ;EAEA,SAASY,mBAAmBA,CAACR,IAAI,EAAE;IACjC,IAAI6B,sBAAsB;IAE1B,MAAM1B,MAAM,GAAGH,IAAI,CAACnD,QAAQ,CAAC,CAAC;IAC9B,MAAM+C,UAAU,GACd,CAACiC,sBAAsB,GAAGxE,iBAAiB,CAAC8C,MAAM,CAAClC,IAAI,CAAC,MAAM,IAAI,IAClE4D,sBAAsB,KAAK,KAAK,CAAC,GAC7BA,sBAAsB,GACtB,EAAE;IACR,OAAO,IAAInH,oBAAoB,CAAC;MAC9B,GAAGyF,MAAM;MACTqB,UAAU,EAAEA,CAAA,KAAM,CAChB,GAAGxB,IAAI,CAACyB,aAAa,CAAC,CAAC,CAAChC,GAAG,CAACP,gBAAgB,CAAC,EAC7C,GAAGwC,eAAe,CAAC9B,UAAU,CAAC,CAC/B;MACDiB,MAAM,EAAEA,CAAA,MAAO;QACb,GAAGzG,QAAQ,CAAC+F,MAAM,CAACU,MAAM,EAAEc,WAAW,CAAC;QACvC,GAAGC,aAAa,CAAChC,UAAU;MAC7B,CAAC,CAAC;MACFE,iBAAiB,EAAEK,MAAM,CAACL,iBAAiB,CAAC1B,MAAM,CAACwB,UAAU;IAC/D,CAAC,CAAC;EACJ;EAEA,SAASa,eAAeA,CAACT,IAAI,EAAE;IAC7B,IAAI8B,sBAAsB;IAE1B,MAAM3B,MAAM,GAAGH,IAAI,CAACnD,QAAQ,CAAC,CAAC;IAC9B,MAAM+C,UAAU,GACd,CAACkC,sBAAsB,GAAGzE,iBAAiB,CAAC8C,MAAM,CAAClC,IAAI,CAAC,MAAM,IAAI,IAClE6D,sBAAsB,KAAK,KAAK,CAAC,GAC7BA,sBAAsB,GACtB,EAAE;IACR,OAAO,IAAI/G,gBAAgB,CAAC;MAC1B,GAAGoF,MAAM;MACTzB,KAAK,EAAEA,CAAA,KAAM,CACX,GAAGsB,IAAI,CAAC+B,QAAQ,CAAC,CAAC,CAACtC,GAAG,CAACP,gBAAgB,CAAC,EACxC,GAAG8C,eAAe,CAACpC,UAAU,CAAC,CAC/B;MACDE,iBAAiB,EAAEK,MAAM,CAACL,iBAAiB,CAAC1B,MAAM,CAACwB,UAAU;IAC/D,CAAC,CAAC;EACJ;EAEA,SAAS+B,WAAWA,CAACb,KAAK,EAAE;IAC1B,OAAO;MACL,GAAGA,KAAK;MACRd,IAAI,EAAED,WAAW,CAACe,KAAK,CAACd,IAAI,CAAC;MAC7BI,IAAI,EAAEU,KAAK,CAACV,IAAI,IAAIhG,QAAQ,CAAC0G,KAAK,CAACV,IAAI,EAAEC,SAAS;IACpD,CAAC;EACH;EAEA,SAASA,SAASA,CAAC4B,GAAG,EAAE;IACtB,OAAO;MAAE,GAAGA,GAAG;MAAEjC,IAAI,EAAED,WAAW,CAACkC,GAAG,CAACjC,IAAI;IAAE,CAAC;EAChD;EAEA,SAASX,iBAAiBA,CAAC6C,KAAK,EAAE;IAChC,MAAMC,OAAO,GAAG,CAAC,CAAC;IAElB,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAIG,oBAAoB;;MAExB;MACA,MAAMC,mBAAmB,GACvB;MACA,CAACD,oBAAoB,GAAGD,IAAI,CAACpD,cAAc,MAAM,IAAI,IACrDqD,oBAAoB,KAAK,KAAK,CAAC,GAC3BA,oBAAoB,GACpB,EAAE;MAER,KAAK,MAAME,aAAa,IAAID,mBAAmB,EAAE;QAC/C;QACA;QACA;QACA;QACAH,OAAO,CAACI,aAAa,CAACC,SAAS,CAAC,GAAGC,YAAY,CAACF,aAAa,CAACvC,IAAI,CAAC;MACrE;IACF;IAEA,OAAOmC,OAAO;EAChB;EAEA,SAASM,YAAYA,CAACL,IAAI,EAAE;IAC1B,IAAIM,iBAAiB;IAErB,MAAMzE,IAAI,GAAGmE,IAAI,CAACnE,IAAI,CAACC,KAAK;IAC5B,MAAM8B,IAAI,GACR,CAAC0C,iBAAiB,GAAG5D,UAAU,CAACb,IAAI,CAAC,MAAM,IAAI,IAC/CyE,iBAAiB,KAAK,KAAK,CAAC,GACxBA,iBAAiB,GACjBlE,OAAO,CAACP,IAAI,CAAC;IAEnB,IAAI+B,IAAI,KAAK2C,SAAS,EAAE;MACtB,MAAM,IAAIC,KAAK,CAAE,kBAAiB3E,IAAK,IAAG,CAAC;IAC7C;IAEA,OAAO+B,IAAI;EACb;EAEA,SAAS6C,cAAcA,CAACT,IAAI,EAAE;IAC5B,IAAIA,IAAI,CAAC5F,IAAI,KAAKnC,IAAI,CAACyI,SAAS,EAAE;MAChC,OAAO,IAAInI,WAAW,CAACkI,cAAc,CAACT,IAAI,CAACpC,IAAI,CAAC,CAAC;IACnD;IAEA,IAAIoC,IAAI,CAAC5F,IAAI,KAAKnC,IAAI,CAAC0I,aAAa,EAAE;MACpC,OAAO,IAAInI,cAAc,CAACiI,cAAc,CAACT,IAAI,CAACpC,IAAI,CAAC,CAAC;IACtD;IAEA,OAAOyC,YAAY,CAACL,IAAI,CAAC;EAC3B;EAEA,SAASzC,cAAcA,CAACyC,IAAI,EAAE;IAC5B,IAAIY,iBAAiB;IAErB,OAAO,IAAIvH,gBAAgB,CAAC;MAC1BwC,IAAI,EAAEmE,IAAI,CAACnE,IAAI,CAACC,KAAK;MACrBoB,WAAW,EACT,CAAC0D,iBAAiB,GAAGZ,IAAI,CAAC9C,WAAW,MAAM,IAAI,IAC/C0D,iBAAiB,KAAK,KAAK,CAAC,GACxB,KAAK,CAAC,GACNA,iBAAiB,CAAC9E,KAAK;MAC7B;MACA+E,SAAS,EAAEb,IAAI,CAACa,SAAS,CAACxD,GAAG,CAAC,CAAC;QAAEvB;MAAM,CAAC,KAAKA,KAAK,CAAC;MACnDgF,YAAY,EAAEd,IAAI,CAACe,UAAU;MAC7B/C,IAAI,EAAEgD,gBAAgB,CAAChB,IAAI,CAACiB,SAAS,CAAC;MACtCxD,OAAO,EAAEuC;IACX,CAAC,CAAC;EACJ;EAEA,SAASR,aAAaA,CAACM,KAAK,EAAE;IAC5B,MAAMoB,cAAc,GAAGhG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE1C,KAAK,MAAM6E,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAIqB,YAAY;;MAEhB;MACA,MAAMC,UAAU,GACd;MACA,CAACD,YAAY,GAAGnB,IAAI,CAACvB,MAAM,MAAM,IAAI,IAAI0C,YAAY,KAAK,KAAK,CAAC,GAC5DA,YAAY,GACZ,EAAE;MAER,KAAK,MAAMzC,KAAK,IAAI0C,UAAU,EAAE;QAC9B,IAAIC,kBAAkB;QAEtBH,cAAc,CAACxC,KAAK,CAAC7C,IAAI,CAACC,KAAK,CAAC,GAAG;UACjC;UACA;UACA;UACA8B,IAAI,EAAE6C,cAAc,CAAC/B,KAAK,CAACd,IAAI,CAAC;UAChCV,WAAW,EACT,CAACmE,kBAAkB,GAAG3C,KAAK,CAACxB,WAAW,MAAM,IAAI,IACjDmE,kBAAkB,KAAK,KAAK,CAAC,GACzB,KAAK,CAAC,GACNA,kBAAkB,CAACvF,KAAK;UAC9BkC,IAAI,EAAEgD,gBAAgB,CAACtC,KAAK,CAACuC,SAAS,CAAC;UACvCK,iBAAiB,EAAEC,oBAAoB,CAAC7C,KAAK,CAAC;UAC9CjB,OAAO,EAAEiB;QACX,CAAC;MACH;IACF;IAEA,OAAOwC,cAAc;EACvB;EAEA,SAASF,gBAAgBA,CAAChD,IAAI,EAAE;IAC9B;IACA,MAAMwD,SAAS,GACb;IACAxD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,EAAE;IAC9C,MAAMyD,YAAY,GAAGvG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAExC,KAAK,MAAM0E,GAAG,IAAI2B,SAAS,EAAE;MAC3B,IAAIE,gBAAgB;;MAEpB;MACA;MACA;MACA,MAAM9D,IAAI,GAAG6C,cAAc,CAACZ,GAAG,CAACjC,IAAI,CAAC;MACrC6D,YAAY,CAAC5B,GAAG,CAAChE,IAAI,CAACC,KAAK,CAAC,GAAG;QAC7B8B,IAAI;QACJV,WAAW,EACT,CAACwE,gBAAgB,GAAG7B,GAAG,CAAC3C,WAAW,MAAM,IAAI,IAC7CwE,gBAAgB,KAAK,KAAK,CAAC,GACvB,KAAK,CAAC,GACNA,gBAAgB,CAAC5F,KAAK;QAC5B6F,YAAY,EAAE5H,YAAY,CAAC8F,GAAG,CAAC8B,YAAY,EAAE/D,IAAI,CAAC;QAClD0D,iBAAiB,EAAEC,oBAAoB,CAAC1B,GAAG,CAAC;QAC5CpC,OAAO,EAAEoC;MACX,CAAC;IACH;IAEA,OAAO4B,YAAY;EACrB;EAEA,SAAS9C,kBAAkBA,CAACmB,KAAK,EAAE;IACjC,MAAM8B,aAAa,GAAG1G,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAEzC,KAAK,MAAM6E,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAI+B,aAAa;;MAEjB;MACA,MAAMC,WAAW,GACf;MACA,CAACD,aAAa,GAAG7B,IAAI,CAACvB,MAAM,MAAM,IAAI,IAAIoD,aAAa,KAAK,KAAK,CAAC,GAC9DA,aAAa,GACb,EAAE;MAER,KAAK,MAAMnD,KAAK,IAAIoD,WAAW,EAAE;QAC/B,IAAIC,mBAAmB;;QAEvB;QACA;QACA;QACA,MAAMnE,IAAI,GAAG6C,cAAc,CAAC/B,KAAK,CAACd,IAAI,CAAC;QACvCgE,aAAa,CAAClD,KAAK,CAAC7C,IAAI,CAACC,KAAK,CAAC,GAAG;UAChC8B,IAAI;UACJV,WAAW,EACT,CAAC6E,mBAAmB,GAAGrD,KAAK,CAACxB,WAAW,MAAM,IAAI,IAClD6E,mBAAmB,KAAK,KAAK,CAAC,GAC1B,KAAK,CAAC,GACNA,mBAAmB,CAACjG,KAAK;UAC/B6F,YAAY,EAAE5H,YAAY,CAAC2E,KAAK,CAACiD,YAAY,EAAE/D,IAAI,CAAC;UACpD0D,iBAAiB,EAAEC,oBAAoB,CAAC7C,KAAK,CAAC;UAC9CjB,OAAO,EAAEiB;QACX,CAAC;MACH;IACF;IAEA,OAAOkD,aAAa;EACtB;EAEA,SAAS/C,iBAAiBA,CAACiB,KAAK,EAAE;IAChC,MAAMkC,YAAY,GAAG9G,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAExC,KAAK,MAAM6E,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAImC,YAAY;;MAEhB;MACA,MAAMC,WAAW,GACf;MACA,CAACD,YAAY,GAAGjC,IAAI,CAAC7C,MAAM,MAAM,IAAI,IAAI8E,YAAY,KAAK,KAAK,CAAC,GAC5DA,YAAY,GACZ,EAAE;MAER,KAAK,MAAMnG,KAAK,IAAIoG,WAAW,EAAE;QAC/B,IAAIC,kBAAkB;QAEtBH,YAAY,CAAClG,KAAK,CAACD,IAAI,CAACC,KAAK,CAAC,GAAG;UAC/BoB,WAAW,EACT,CAACiF,kBAAkB,GAAGrG,KAAK,CAACoB,WAAW,MAAM,IAAI,IACjDiF,kBAAkB,KAAK,KAAK,CAAC,GACzB,KAAK,CAAC,GACNA,kBAAkB,CAACrG,KAAK;UAC9BwF,iBAAiB,EAAEC,oBAAoB,CAACzF,KAAK,CAAC;UAC9C2B,OAAO,EAAE3B;QACX,CAAC;MACH;IACF;IAEA,OAAOkG,YAAY;EACrB;EAEA,SAAS1C,eAAeA,CAACQ,KAAK,EAAE;IAC9B;IACA;IACA;IACA;IACA,OAAOA,KAAK,CAACsC,OAAO;IAClB;IACCpC,IAAI,IAAK;MACR,IAAIqC,oBAAoB,EAAEC,gBAAgB;MAE1C,OACE;QACA,CAACD,oBAAoB,GACnB,CAACC,gBAAgB,GAAGtC,IAAI,CAACZ,UAAU,MAAM,IAAI,IAC7CkD,gBAAgB,KAAK,KAAK,CAAC,GACvB,KAAK,CAAC,GACNA,gBAAgB,CAACjF,GAAG,CAACgD,YAAY,CAAC,MAAM,IAAI,IAChDgC,oBAAoB,KAAK,KAAK,CAAC,GAC7BA,oBAAoB,GACpB;MAAE;IAEV,CACF,CAAC;EACH;EAEA,SAASzC,eAAeA,CAACE,KAAK,EAAE;IAC9B;IACA;IACA;IACA;IACA,OAAOA,KAAK,CAACsC,OAAO;IAClB;IACCpC,IAAI,IAAK;MACR,IAAIuC,eAAe,EAAEC,WAAW;MAEhC,OACE;QACA,CAACD,eAAe,GACd,CAACC,WAAW,GAAGxC,IAAI,CAAC1D,KAAK,MAAM,IAAI,IAAIkG,WAAW,KAAK,KAAK,CAAC,GACzD,KAAK,CAAC,GACNA,WAAW,CAACnF,GAAG,CAACgD,YAAY,CAAC,MAAM,IAAI,IAC3CkC,eAAe,KAAK,KAAK,CAAC,GACxBA,eAAe,GACf;MAAE;IAEV,CACF,CAAC;EACH;EAEA,SAAS5F,SAASA,CAACc,OAAO,EAAE;IAC1B,IAAIgF,qBAAqB;IAEzB,MAAM5G,IAAI,GAAG4B,OAAO,CAAC5B,IAAI,CAACC,KAAK;IAC/B,MAAM4B,iBAAiB,GACrB,CAAC+E,qBAAqB,GAAGxH,iBAAiB,CAACY,IAAI,CAAC,MAAM,IAAI,IAC1D4G,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;IAER,QAAQhF,OAAO,CAACrD,IAAI;MAClB,KAAKnC,IAAI,CAACyK,sBAAsB;QAAE;UAChC,IAAIC,oBAAoB;UAExB,MAAMC,QAAQ,GAAG,CAACnF,OAAO,EAAE,GAAGC,iBAAiB,CAAC;UAChD,OAAO,IAAIjF,iBAAiB,CAAC;YAC3BoD,IAAI;YACJqB,WAAW,EACT,CAACyF,oBAAoB,GAAGlF,OAAO,CAACP,WAAW,MAAM,IAAI,IACrDyF,oBAAoB,KAAK,KAAK,CAAC,GAC3B,KAAK,CAAC,GACNA,oBAAoB,CAAC7G,KAAK;YAChCsD,UAAU,EAAEA,CAAA,KAAME,eAAe,CAACsD,QAAQ,CAAC;YAC3CnE,MAAM,EAAEA,CAAA,KAAMe,aAAa,CAACoD,QAAQ,CAAC;YACrCnF,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;MAEA,KAAKzF,IAAI,CAAC4K,yBAAyB;QAAE;UACnC,IAAIC,qBAAqB;UAEzB,MAAMF,QAAQ,GAAG,CAACnF,OAAO,EAAE,GAAGC,iBAAiB,CAAC;UAChD,OAAO,IAAIpF,oBAAoB,CAAC;YAC9BuD,IAAI;YACJqB,WAAW,EACT,CAAC4F,qBAAqB,GAAGrF,OAAO,CAACP,WAAW,MAAM,IAAI,IACtD4F,qBAAqB,KAAK,KAAK,CAAC,GAC5B,KAAK,CAAC,GACNA,qBAAqB,CAAChH,KAAK;YACjCsD,UAAU,EAAEA,CAAA,KAAME,eAAe,CAACsD,QAAQ,CAAC;YAC3CnE,MAAM,EAAEA,CAAA,KAAMe,aAAa,CAACoD,QAAQ,CAAC;YACrCnF,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;MAEA,KAAKzF,IAAI,CAAC8K,oBAAoB;QAAE;UAC9B,IAAIC,qBAAqB;UAEzB,MAAMJ,QAAQ,GAAG,CAACnF,OAAO,EAAE,GAAGC,iBAAiB,CAAC;UAChD,OAAO,IAAItF,eAAe,CAAC;YACzByD,IAAI;YACJqB,WAAW,EACT,CAAC8F,qBAAqB,GAAGvF,OAAO,CAACP,WAAW,MAAM,IAAI,IACtD8F,qBAAqB,KAAK,KAAK,CAAC,GAC5B,KAAK,CAAC,GACNA,qBAAqB,CAAClH,KAAK;YACjCqB,MAAM,EAAE0B,iBAAiB,CAAC+D,QAAQ,CAAC;YACnCnF,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;MAEA,KAAKzF,IAAI,CAACgL,qBAAqB;QAAE;UAC/B,IAAIC,qBAAqB;UAEzB,MAAMN,QAAQ,GAAG,CAACnF,OAAO,EAAE,GAAGC,iBAAiB,CAAC;UAChD,OAAO,IAAI/E,gBAAgB,CAAC;YAC1BkD,IAAI;YACJqB,WAAW,EACT,CAACgG,qBAAqB,GAAGzF,OAAO,CAACP,WAAW,MAAM,IAAI,IACtDgG,qBAAqB,KAAK,KAAK,CAAC,GAC5B,KAAK,CAAC,GACNA,qBAAqB,CAACpH,KAAK;YACjCQ,KAAK,EAAEA,CAAA,KAAMsD,eAAe,CAACgD,QAAQ,CAAC;YACtCnF,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;MAEA,KAAKzF,IAAI,CAACkL,sBAAsB;QAAE;UAChC,IAAIC,qBAAqB;UAEzB,OAAO,IAAI1K,iBAAiB,CAAC;YAC3BmD,IAAI;YACJqB,WAAW,EACT,CAACkG,qBAAqB,GAAG3F,OAAO,CAACP,WAAW,MAAM,IAAI,IACtDkG,qBAAqB,KAAK,KAAK,CAAC,GAC5B,KAAK,CAAC,GACNA,qBAAqB,CAACtH,KAAK;YACjCiD,cAAc,EAAEG,iBAAiB,CAACzB,OAAO,CAAC;YAC1CA,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;MAEA,KAAKzF,IAAI,CAACoL,4BAA4B;QAAE;UACtC,IAAIC,qBAAqB;UAEzB,MAAMV,QAAQ,GAAG,CAACnF,OAAO,EAAE,GAAGC,iBAAiB,CAAC;UAChD,OAAO,IAAIrF,sBAAsB,CAAC;YAChCwD,IAAI;YACJqB,WAAW,EACT,CAACoG,qBAAqB,GAAG7F,OAAO,CAACP,WAAW,MAAM,IAAI,IACtDoG,qBAAqB,KAAK,KAAK,CAAC,GAC5B,KAAK,CAAC,GACNA,qBAAqB,CAACxH,KAAK;YACjC2C,MAAM,EAAEA,CAAA,KAAME,kBAAkB,CAACiE,QAAQ,CAAC;YAC1CnF,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;IACF;EACF;AACF;AACA,MAAMhB,UAAU,GAAG3E,MAAM,CACvB,CAAC,GAAG2B,oBAAoB,EAAE,GAAGH,kBAAkB,CAAC,EAC/CqE,IAAI,IAAKA,IAAI,CAAC/B,IACjB,CAAC;AACD;AACA;AACA;AACA;;AAEA,SAAS0F,oBAAoBA,CAACvB,IAAI,EAAE;EAClC,MAAMuD,UAAU,GAAGzJ,kBAAkB,CAACV,0BAA0B,EAAE4G,IAAI,CAAC,CAAC,CAAC;;EAEzE,OAAOuD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAC/C,KAAK,CAAC,GACNA,UAAU,CAACC,MAAM;AACvB;AACA;AACA;AACA;;AAEA,SAAStE,iBAAiBA,CAACc,IAAI,EAAE;EAC/B,MAAMyD,WAAW,GAAG3J,kBAAkB,CAACR,2BAA2B,EAAE0G,IAAI,CAAC,CAAC,CAAC;;EAE3E,OAAOyD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GACjD,KAAK,CAAC,GACNA,WAAW,CAACC,GAAG;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}