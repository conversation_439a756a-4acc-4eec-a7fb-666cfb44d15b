{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isPast\n * @category Common Helpers\n * @summary Is the given date in the past?\n * @pure false\n *\n * @description\n * Is the given date in the past?\n *\n * @param date - The date to check\n *\n * @returns The date is in the past\n *\n * @example\n * // If today is 6 October 2014, is 2 July 2014 in the past?\n * const result = isPast(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isPast(date) {\n  return +toDate(date) < Date.now();\n}\n\n// Fallback for modularized imports:\nexport default isPast;", "map": {"version": 3, "names": ["toDate", "isPast", "date", "Date", "now"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/isPast.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name isPast\n * @category Common Helpers\n * @summary Is the given date in the past?\n * @pure false\n *\n * @description\n * Is the given date in the past?\n *\n * @param date - The date to check\n *\n * @returns The date is in the past\n *\n * @example\n * // If today is 6 October 2014, is 2 July 2014 in the past?\n * const result = isPast(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isPast(date) {\n  return +toDate(date) < Date.now();\n}\n\n// Fallback for modularized imports:\nexport default isPast;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAO,CAACF,MAAM,CAACE,IAAI,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;AACnC;;AAEA;AACA,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}