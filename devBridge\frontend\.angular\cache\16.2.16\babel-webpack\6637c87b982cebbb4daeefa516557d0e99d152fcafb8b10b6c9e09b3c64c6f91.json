{"ast": null, "code": "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfWeek} function options.\n */\n\n/**\n * @name endOfWeek\n * @category Week Helpers\n * @summary Return the end of a week for the given date.\n *\n * @description\n * Return the end of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a week\n *\n * @example\n * // The end of a week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sat Sep 06 2014 23:59:59.999\n *\n * @example\n * // If the week starts on Monday, the end of the week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions.weekStartsOn ?? defaultOptions.locale?.options?.weekStartsOn ?? 0;\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n  _date.setDate(_date.getDate() + diff);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfWeek;", "map": {"version": 3, "names": ["getDefaultOptions", "toDate", "endOfWeek", "date", "options", "defaultOptions", "weekStartsOn", "locale", "_date", "in", "day", "getDay", "diff", "setDate", "getDate", "setHours"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/endOfWeek.js"], "sourcesContent": ["import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfWeek} function options.\n */\n\n/**\n * @name endOfWeek\n * @category Week Helpers\n * @summary Return the end of a week for the given date.\n *\n * @description\n * Return the end of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a week\n *\n * @example\n * // The end of a week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sat Sep 06 2014 23:59:59.999\n *\n * @example\n * // If the week starts on Monday, the end of the week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n\n  _date.setDate(_date.getDate() + diff);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfWeek;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,MAAMC,cAAc,GAAGL,iBAAiB,CAAC,CAAC;EAC1C,MAAMM,YAAY,GAChBF,OAAO,EAAEE,YAAY,IACrBF,OAAO,EAAEG,MAAM,EAAEH,OAAO,EAAEE,YAAY,IACtCD,cAAc,CAACC,YAAY,IAC3BD,cAAc,CAACE,MAAM,EAAEH,OAAO,EAAEE,YAAY,IAC5C,CAAC;EAEH,MAAME,KAAK,GAAGP,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEK,EAAE,CAAC;EACvC,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC;EAC1B,MAAMC,IAAI,GAAG,CAACF,GAAG,GAAGJ,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAII,GAAG,GAAGJ,YAAY,CAAC;EAErEE,KAAK,CAACK,OAAO,CAACL,KAAK,CAACM,OAAO,CAAC,CAAC,GAAGF,IAAI,CAAC;EACrCJ,KAAK,CAACO,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC/B,OAAOP,KAAK;AACd;;AAEA;AACA,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}