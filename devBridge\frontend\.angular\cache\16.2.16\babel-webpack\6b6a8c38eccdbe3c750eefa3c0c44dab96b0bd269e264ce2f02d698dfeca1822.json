{"ast": null, "code": "import { <PERSON><PERSON> } from \"@wry/trie\";\nimport { StrongCache } from \"@wry/caches\";\nimport { Entry } from \"./entry.js\";\nimport { parentEntrySlot } from \"./context.js\";\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\nexport { bindContext, noContext, nonReactive, setTimeout, asyncFromGen, Slot } from \"./context.js\";\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\nexport { dep } from \"./dep.js\";\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nlet defaultKeyTrie;\nexport function defaultMakeCacheKey(...args) {\n  const trie = defaultKeyTrie || (defaultKeyTrie = new Trie(typeof WeakMap === \"function\"));\n  return trie.lookupArray(args);\n}\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\nexport { Trie as KeyTrie };\n;\nconst caches = new Set();\nexport function wrap(originalFunction, {\n  max = Math.pow(2, 16),\n  keyArgs,\n  makeCacheKey = defaultMakeCacheKey,\n  normalizeResult,\n  subscribe,\n  cache: cacheOption = StrongCache\n} = Object.create(null)) {\n  const cache = typeof cacheOption === \"function\" ? new cacheOption(max, entry => entry.dispose()) : cacheOption;\n  const optimistic = function () {\n    const key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n    if (key === void 0) {\n      return originalFunction.apply(null, arguments);\n    }\n    let entry = cache.get(key);\n    if (!entry) {\n      cache.set(key, entry = new Entry(originalFunction));\n      entry.normalizeResult = normalizeResult;\n      entry.subscribe = subscribe;\n      // Give the Entry the ability to trigger cache.delete(key), even though\n      // the Entry itself does not know about key or cache.\n      entry.forget = () => cache.delete(key);\n    }\n    const value = entry.recompute(Array.prototype.slice.call(arguments));\n    // Move this entry to the front of the least-recently used queue,\n    // since we just finished computing its value.\n    cache.set(key, entry);\n    caches.add(cache);\n    // Clean up any excess entries in the cache, but only if there is no\n    // active parent entry, meaning we're not in the middle of a larger\n    // computation that might be flummoxed by the cleaning.\n    if (!parentEntrySlot.hasValue()) {\n      caches.forEach(cache => cache.clean());\n      caches.clear();\n    }\n    return value;\n  };\n  Object.defineProperty(optimistic, \"size\", {\n    get: () => cache.size,\n    configurable: false,\n    enumerable: false\n  });\n  Object.freeze(optimistic.options = {\n    max,\n    keyArgs,\n    makeCacheKey,\n    normalizeResult,\n    subscribe,\n    cache\n  });\n  function dirtyKey(key) {\n    const entry = key && cache.get(key);\n    if (entry) {\n      entry.setDirty();\n    }\n  }\n  optimistic.dirtyKey = dirtyKey;\n  optimistic.dirty = function dirty() {\n    dirtyKey(makeCacheKey.apply(null, arguments));\n  };\n  function peekKey(key) {\n    const entry = key && cache.get(key);\n    if (entry) {\n      return entry.peek();\n    }\n  }\n  optimistic.peekKey = peekKey;\n  optimistic.peek = function peek() {\n    return peekKey(makeCacheKey.apply(null, arguments));\n  };\n  function forgetKey(key) {\n    return key ? cache.delete(key) : false;\n  }\n  optimistic.forgetKey = forgetKey;\n  optimistic.forget = function forget() {\n    return forgetKey(makeCacheKey.apply(null, arguments));\n  };\n  optimistic.makeCacheKey = makeCacheKey;\n  optimistic.getKey = keyArgs ? function getKey() {\n    return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n  } : makeCacheKey;\n  return Object.freeze(optimistic);\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "StrongCache", "Entry", "parentEntrySlot", "bindContext", "noContext", "nonReactive", "setTimeout", "asyncFromGen", "Slot", "dep", "defaultKeyTrie", "defaultMakeCacheKey", "args", "trie", "WeakMap", "lookupArray", "KeyTrie", "caches", "Set", "wrap", "originalFunction", "max", "Math", "pow", "keyArgs", "make<PERSON><PERSON><PERSON><PERSON>", "normalizeResult", "subscribe", "cache", "cacheOption", "Object", "create", "entry", "dispose", "optimistic", "key", "apply", "arguments", "get", "set", "forget", "delete", "value", "recompute", "Array", "prototype", "slice", "call", "add", "hasValue", "for<PERSON>ach", "clean", "clear", "defineProperty", "size", "configurable", "enumerable", "freeze", "options", "<PERSON><PERSON><PERSON>", "set<PERSON>irty", "dirty", "<PERSON><PERSON><PERSON>", "peek", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/optimism/lib/index.js"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@wry/trie\";\nimport { StrongCache } from \"@wry/caches\";\nimport { Entry } from \"./entry.js\";\nimport { parentEntrySlot } from \"./context.js\";\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\nexport { bindContext, noContext, nonReactive, setTimeout, asyncFromGen, Slot, } from \"./context.js\";\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\nexport { dep } from \"./dep.js\";\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCache<PERSON>ey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nlet defaultKeyTrie;\nexport function defaultMakeCacheKey(...args) {\n    const trie = defaultKeyTrie || (defaultKeyTrie = new Trie(typeof WeakMap === \"function\"));\n    return trie.lookupArray(args);\n}\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\nexport { Trie as KeyTrie };\n;\nconst caches = new Set();\nexport function wrap(originalFunction, { max = Math.pow(2, 16), keyArgs, makeCacheKey = defaultMakeCacheKey, normalizeResult, subscribe, cache: cacheOption = StrongCache, } = Object.create(null)) {\n    const cache = typeof cacheOption === \"function\"\n        ? new cacheOption(max, entry => entry.dispose())\n        : cacheOption;\n    const optimistic = function () {\n        const key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n        if (key === void 0) {\n            return originalFunction.apply(null, arguments);\n        }\n        let entry = cache.get(key);\n        if (!entry) {\n            cache.set(key, entry = new Entry(originalFunction));\n            entry.normalizeResult = normalizeResult;\n            entry.subscribe = subscribe;\n            // Give the Entry the ability to trigger cache.delete(key), even though\n            // the Entry itself does not know about key or cache.\n            entry.forget = () => cache.delete(key);\n        }\n        const value = entry.recompute(Array.prototype.slice.call(arguments));\n        // Move this entry to the front of the least-recently used queue,\n        // since we just finished computing its value.\n        cache.set(key, entry);\n        caches.add(cache);\n        // Clean up any excess entries in the cache, but only if there is no\n        // active parent entry, meaning we're not in the middle of a larger\n        // computation that might be flummoxed by the cleaning.\n        if (!parentEntrySlot.hasValue()) {\n            caches.forEach(cache => cache.clean());\n            caches.clear();\n        }\n        return value;\n    };\n    Object.defineProperty(optimistic, \"size\", {\n        get: () => cache.size,\n        configurable: false,\n        enumerable: false,\n    });\n    Object.freeze(optimistic.options = {\n        max,\n        keyArgs,\n        makeCacheKey,\n        normalizeResult,\n        subscribe,\n        cache,\n    });\n    function dirtyKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            entry.setDirty();\n        }\n    }\n    optimistic.dirtyKey = dirtyKey;\n    optimistic.dirty = function dirty() {\n        dirtyKey(makeCacheKey.apply(null, arguments));\n    };\n    function peekKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            return entry.peek();\n        }\n    }\n    optimistic.peekKey = peekKey;\n    optimistic.peek = function peek() {\n        return peekKey(makeCacheKey.apply(null, arguments));\n    };\n    function forgetKey(key) {\n        return key ? cache.delete(key) : false;\n    }\n    optimistic.forgetKey = forgetKey;\n    optimistic.forget = function forget() {\n        return forgetKey(makeCacheKey.apply(null, arguments));\n    };\n    optimistic.makeCacheKey = makeCacheKey;\n    optimistic.getKey = keyArgs ? function getKey() {\n        return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n    } : makeCacheKey;\n    return Object.freeze(optimistic);\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,eAAe,QAAQ,cAAc;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,YAAY,EAAEC,IAAI,QAAS,cAAc;AACnG;AACA;AACA;AACA;AACA,SAASC,GAAG,QAAQ,UAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB,OAAO,SAASC,mBAAmBA,CAAC,GAAGC,IAAI,EAAE;EACzC,MAAMC,IAAI,GAAGH,cAAc,KAAKA,cAAc,GAAG,IAAIX,IAAI,CAAC,OAAOe,OAAO,KAAK,UAAU,CAAC,CAAC;EACzF,OAAOD,IAAI,CAACE,WAAW,CAACH,IAAI,CAAC;AACjC;AACA;AACA;AACA;AACA,SAASb,IAAI,IAAIiB,OAAO;AACxB;AACA,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;AACxB,OAAO,SAASC,IAAIA,CAACC,gBAAgB,EAAE;EAAEC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EAAEC,OAAO;EAAEC,YAAY,GAAGd,mBAAmB;EAAEe,eAAe;EAAEC,SAAS;EAAEC,KAAK,EAAEC,WAAW,GAAG7B;AAAa,CAAC,GAAG8B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,EAAE;EAChM,MAAMH,KAAK,GAAG,OAAOC,WAAW,KAAK,UAAU,GACzC,IAAIA,WAAW,CAACR,GAAG,EAAEW,KAAK,IAAIA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,GAC9CJ,WAAW;EACjB,MAAMK,UAAU,GAAG,SAAAA,CAAA,EAAY;IAC3B,MAAMC,GAAG,GAAGV,YAAY,CAACW,KAAK,CAAC,IAAI,EAAEZ,OAAO,GAAGA,OAAO,CAACY,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAGA,SAAS,CAAC;IAC1F,IAAIF,GAAG,KAAK,KAAK,CAAC,EAAE;MAChB,OAAOf,gBAAgB,CAACgB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAClD;IACA,IAAIL,KAAK,GAAGJ,KAAK,CAACU,GAAG,CAACH,GAAG,CAAC;IAC1B,IAAI,CAACH,KAAK,EAAE;MACRJ,KAAK,CAACW,GAAG,CAACJ,GAAG,EAAEH,KAAK,GAAG,IAAI/B,KAAK,CAACmB,gBAAgB,CAAC,CAAC;MACnDY,KAAK,CAACN,eAAe,GAAGA,eAAe;MACvCM,KAAK,CAACL,SAAS,GAAGA,SAAS;MAC3B;MACA;MACAK,KAAK,CAACQ,MAAM,GAAG,MAAMZ,KAAK,CAACa,MAAM,CAACN,GAAG,CAAC;IAC1C;IACA,MAAMO,KAAK,GAAGV,KAAK,CAACW,SAAS,CAACC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACV,SAAS,CAAC,CAAC;IACpE;IACA;IACAT,KAAK,CAACW,GAAG,CAACJ,GAAG,EAAEH,KAAK,CAAC;IACrBf,MAAM,CAAC+B,GAAG,CAACpB,KAAK,CAAC;IACjB;IACA;IACA;IACA,IAAI,CAAC1B,eAAe,CAAC+C,QAAQ,CAAC,CAAC,EAAE;MAC7BhC,MAAM,CAACiC,OAAO,CAACtB,KAAK,IAAIA,KAAK,CAACuB,KAAK,CAAC,CAAC,CAAC;MACtClC,MAAM,CAACmC,KAAK,CAAC,CAAC;IAClB;IACA,OAAOV,KAAK;EAChB,CAAC;EACDZ,MAAM,CAACuB,cAAc,CAACnB,UAAU,EAAE,MAAM,EAAE;IACtCI,GAAG,EAAEA,CAAA,KAAMV,KAAK,CAAC0B,IAAI;IACrBC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;EAChB,CAAC,CAAC;EACF1B,MAAM,CAAC2B,MAAM,CAACvB,UAAU,CAACwB,OAAO,GAAG;IAC/BrC,GAAG;IACHG,OAAO;IACPC,YAAY;IACZC,eAAe;IACfC,SAAS;IACTC;EACJ,CAAC,CAAC;EACF,SAAS+B,QAAQA,CAACxB,GAAG,EAAE;IACnB,MAAMH,KAAK,GAAGG,GAAG,IAAIP,KAAK,CAACU,GAAG,CAACH,GAAG,CAAC;IACnC,IAAIH,KAAK,EAAE;MACPA,KAAK,CAAC4B,QAAQ,CAAC,CAAC;IACpB;EACJ;EACA1B,UAAU,CAACyB,QAAQ,GAAGA,QAAQ;EAC9BzB,UAAU,CAAC2B,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAChCF,QAAQ,CAAClC,YAAY,CAACW,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EACjD,CAAC;EACD,SAASyB,OAAOA,CAAC3B,GAAG,EAAE;IAClB,MAAMH,KAAK,GAAGG,GAAG,IAAIP,KAAK,CAACU,GAAG,CAACH,GAAG,CAAC;IACnC,IAAIH,KAAK,EAAE;MACP,OAAOA,KAAK,CAAC+B,IAAI,CAAC,CAAC;IACvB;EACJ;EACA7B,UAAU,CAAC4B,OAAO,GAAGA,OAAO;EAC5B5B,UAAU,CAAC6B,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;IAC9B,OAAOD,OAAO,CAACrC,YAAY,CAACW,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EACvD,CAAC;EACD,SAAS2B,SAASA,CAAC7B,GAAG,EAAE;IACpB,OAAOA,GAAG,GAAGP,KAAK,CAACa,MAAM,CAACN,GAAG,CAAC,GAAG,KAAK;EAC1C;EACAD,UAAU,CAAC8B,SAAS,GAAGA,SAAS;EAChC9B,UAAU,CAACM,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAClC,OAAOwB,SAAS,CAACvC,YAAY,CAACW,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EACzD,CAAC;EACDH,UAAU,CAACT,YAAY,GAAGA,YAAY;EACtCS,UAAU,CAAC+B,MAAM,GAAGzC,OAAO,GAAG,SAASyC,MAAMA,CAAA,EAAG;IAC5C,OAAOxC,YAAY,CAACW,KAAK,CAAC,IAAI,EAAEZ,OAAO,CAACY,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EACnE,CAAC,GAAGZ,YAAY;EAChB,OAAOK,MAAM,CAAC2B,MAAM,CAACvB,UAAU,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}