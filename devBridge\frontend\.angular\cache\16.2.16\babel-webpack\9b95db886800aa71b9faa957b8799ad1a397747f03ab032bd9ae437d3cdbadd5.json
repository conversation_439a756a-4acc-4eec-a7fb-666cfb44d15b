{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No unused fragments\n *\n * A GraphQL document is only valid if all fragment definitions are spread\n * within operations, or spread within other fragments spread within operations.\n *\n * See https://spec.graphql.org/draft/#sec-Fragments-Must-Be-Used\n */\nexport function NoUnusedFragmentsRule(context) {\n  const operationDefs = [];\n  const fragmentDefs = [];\n  return {\n    OperationDefinition(node) {\n      operationDefs.push(node);\n      return false;\n    },\n    FragmentDefinition(node) {\n      fragmentDefs.push(node);\n      return false;\n    },\n    Document: {\n      leave() {\n        const fragmentNameUsed = Object.create(null);\n        for (const operation of operationDefs) {\n          for (const fragment of context.getRecursivelyReferencedFragments(operation)) {\n            fragmentNameUsed[fragment.name.value] = true;\n          }\n        }\n        for (const fragmentDef of fragmentDefs) {\n          const fragName = fragmentDef.name.value;\n          if (fragmentNameUsed[fragName] !== true) {\n            context.reportError(new GraphQLError(`Fragment \"${fragName}\" is never used.`, {\n              nodes: fragmentDef\n            }));\n          }\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "NoUnusedFragmentsRule", "context", "operationDefs", "fragmentDefs", "OperationDefinition", "node", "push", "FragmentDefinition", "Document", "leave", "fragmentNameUsed", "Object", "create", "operation", "fragment", "getRecursivelyReferencedFragments", "name", "value", "fragmentDef", "fragName", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/NoUnusedFragmentsRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No unused fragments\n *\n * A GraphQL document is only valid if all fragment definitions are spread\n * within operations, or spread within other fragments spread within operations.\n *\n * See https://spec.graphql.org/draft/#sec-Fragments-Must-Be-Used\n */\nexport function NoUnusedFragmentsRule(context) {\n  const operationDefs = [];\n  const fragmentDefs = [];\n  return {\n    OperationDefinition(node) {\n      operationDefs.push(node);\n      return false;\n    },\n\n    FragmentDefinition(node) {\n      fragmentDefs.push(node);\n      return false;\n    },\n\n    Document: {\n      leave() {\n        const fragmentNameUsed = Object.create(null);\n\n        for (const operation of operationDefs) {\n          for (const fragment of context.getRecursivelyReferencedFragments(\n            operation,\n          )) {\n            fragmentNameUsed[fragment.name.value] = true;\n          }\n        }\n\n        for (const fragmentDef of fragmentDefs) {\n          const fragName = fragmentDef.name.value;\n\n          if (fragmentNameUsed[fragName] !== true) {\n            context.reportError(\n              new GraphQLError(`Fragment \"${fragName}\" is never used.`, {\n                nodes: fragmentDef,\n              }),\n            );\n          }\n        }\n      },\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,YAAY,GAAG,EAAE;EACvB,OAAO;IACLC,mBAAmBA,CAACC,IAAI,EAAE;MACxBH,aAAa,CAACI,IAAI,CAACD,IAAI,CAAC;MACxB,OAAO,KAAK;IACd,CAAC;IAEDE,kBAAkBA,CAACF,IAAI,EAAE;MACvBF,YAAY,CAACG,IAAI,CAACD,IAAI,CAAC;MACvB,OAAO,KAAK;IACd,CAAC;IAEDG,QAAQ,EAAE;MACRC,KAAKA,CAAA,EAAG;QACN,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QAE5C,KAAK,MAAMC,SAAS,IAAIX,aAAa,EAAE;UACrC,KAAK,MAAMY,QAAQ,IAAIb,OAAO,CAACc,iCAAiC,CAC9DF,SACF,CAAC,EAAE;YACDH,gBAAgB,CAACI,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC,GAAG,IAAI;UAC9C;QACF;QAEA,KAAK,MAAMC,WAAW,IAAIf,YAAY,EAAE;UACtC,MAAMgB,QAAQ,GAAGD,WAAW,CAACF,IAAI,CAACC,KAAK;UAEvC,IAAIP,gBAAgB,CAACS,QAAQ,CAAC,KAAK,IAAI,EAAE;YACvClB,OAAO,CAACmB,WAAW,CACjB,IAAIrB,YAAY,CAAE,aAAYoB,QAAS,kBAAiB,EAAE;cACxDE,KAAK,EAAEH;YACT,CAAC,CACH,CAAC;UACH;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}