{"version": 3, "file": "angular-draggable-droppable.mjs", "sources": ["../../../projects/angular-draggable-droppable/src/lib/util.ts", "../../../projects/angular-draggable-droppable/src/lib/draggable-helper.provider.ts", "../../../projects/angular-draggable-droppable/src/lib/draggable-scroll-container.directive.ts", "../../../projects/angular-draggable-droppable/src/lib/draggable.directive.ts", "../../../projects/angular-draggable-droppable/src/lib/droppable.directive.ts", "../../../projects/angular-draggable-droppable/src/lib/drag-and-drop.module.ts", "../../../projects/angular-draggable-droppable/src/public_api.ts", "../../../projects/angular-draggable-droppable/src/angular-draggable-droppable.ts"], "sourcesContent": ["import { ElementRef, Renderer2 } from '@angular/core';\n\nexport function addClass(\n  renderer: Renderer2,\n  element: ElementRef<HTMLElement>,\n  classToAdd: string\n) {\n  if (classToAdd) {\n    classToAdd\n      .split(' ')\n      .forEach((className) =>\n        renderer.addClass(element.nativeElement, className)\n      );\n  }\n}\n\nexport function removeClass(\n  renderer: Renderer2,\n  element: ElementRef<HTMLElement>,\n  classToRemove: string\n) {\n  if (classToRemove) {\n    classToRemove\n      .split(' ')\n      .forEach((className) =>\n        renderer.removeClass(element.nativeElement, className)\n      );\n  }\n}\n", "import { Subject } from 'rxjs';\nimport { Injectable } from '@angular/core';\n\nexport interface CurrentDragData {\n  clientX: number;\n  clientY: number;\n  dropData: any;\n  target: EventTarget;\n}\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class DraggableHelper {\n  currentDrag = new Subject<Subject<CurrentDragData>>();\n}\n", "import { Directive, ElementRef } from '@angular/core';\n\n/**\n * If the window isn't scrollable, then place this on the scrollable container that draggable elements are inside. e.g.\n * ```html\n  <div style=\"overflow: scroll\" mwlDraggableScrollContainer>\n    <div mwlDraggable>Drag me!</div>\n  </div>\n  ```\n */\n@Directive({\n  selector: '[mwlDraggableScrollContainer]',\n})\nexport class DraggableScrollContainerDirective {\n  /**\n   * @hidden\n   */\n  constructor(public elementRef: ElementRef<HTMLElement>) {}\n}\n", "import {\n  Directive,\n  OnInit,\n  ElementRef,\n  Renderer2,\n  Output,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  OnChanges,\n  NgZone,\n  SimpleChanges,\n  Inject,\n  TemplateRef,\n  ViewContainerRef,\n  Optional,\n} from '@angular/core';\nimport {\n  Subject,\n  Observable,\n  merge,\n  ReplaySubject,\n  combineLatest,\n  fromEvent,\n} from 'rxjs';\nimport {\n  map,\n  mergeMap,\n  takeUntil,\n  take,\n  takeLast,\n  pairwise,\n  share,\n  filter,\n  count,\n  startWith,\n} from 'rxjs/operators';\nimport { CurrentDragData, DraggableHelper } from './draggable-helper.provider';\nimport { DOCUMENT } from '@angular/common';\nimport autoScroll from '@mattlewis92/dom-autoscroller';\nimport { DraggableScrollContainerDirective } from './draggable-scroll-container.directive';\nimport { addClass, removeClass } from './util';\n\nexport interface Coordinates {\n  x: number;\n  y: number;\n}\n\nexport interface DragAxis {\n  x: boolean;\n  y: boolean;\n}\n\nexport interface SnapGrid {\n  x?: number;\n  y?: number;\n}\n\nexport interface DragPointerDownEvent extends Coordinates {}\n\nexport interface DragStartEvent {\n  cancelDrag$: ReplaySubject<void>;\n}\n\nexport interface DragMoveEvent extends Coordinates {}\n\nexport interface DragEndEvent extends Coordinates {\n  dragCancelled: boolean;\n}\n\nexport interface ValidateDragParams extends Coordinates {\n  transform: {\n    x: number;\n    y: number;\n  };\n}\n\nexport type ValidateDrag = (params: ValidateDragParams) => boolean;\n\nexport interface PointerEvent {\n  clientX: number;\n  clientY: number;\n  event: MouseEvent | TouchEvent;\n}\n\nexport interface TimeLongPress {\n  timerBegin: number;\n  timerEnd: number;\n}\n\nexport interface GhostElementCreatedEvent {\n  clientX: number;\n  clientY: number;\n  element: HTMLElement;\n}\n\n@Directive({\n  selector: '[mwlDraggable]',\n})\nexport class DraggableDirective implements OnInit, OnChanges, OnDestroy {\n  /**\n   * an object of data you can pass to the drop event\n   */\n  @Input() dropData: any;\n\n  /**\n   * The axis along which the element is draggable\n   */\n  @Input() dragAxis: DragAxis = { x: true, y: true };\n\n  /**\n   * Snap all drags to an x / y grid\n   */\n  @Input() dragSnapGrid: SnapGrid = {};\n\n  /**\n   * Show a ghost element that shows the drag when dragging\n   */\n  @Input() ghostDragEnabled: boolean = true;\n\n  /**\n   * Show the original element when ghostDragEnabled is true\n   */\n  @Input() showOriginalElementWhileDragging: boolean = false;\n\n  /**\n   * Allow custom behaviour to control when the element is dragged\n   */\n  @Input() validateDrag: ValidateDrag;\n\n  /**\n   * The cursor to use when hovering over a draggable element\n   */\n  @Input() dragCursor: string = '';\n\n  /**\n   * The css class to apply when the element is being dragged\n   */\n  @Input() dragActiveClass: string;\n\n  /**\n   * The element the ghost element will be appended to. Default is next to the dragged element\n   */\n  @Input() ghostElementAppendTo: HTMLElement;\n\n  /**\n   * An ng-template to be inserted into the parent element of the ghost element. It will overwrite any child nodes.\n   */\n  @Input() ghostElementTemplate: TemplateRef<any>;\n\n  /**\n   * Amount of milliseconds to wait on touch devices before starting to drag the element (so that you can scroll the page by touching a draggable element)\n   */\n  @Input() touchStartLongPress: { delay: number; delta: number };\n\n  /*\n   * Options used to control the behaviour of auto scrolling: https://www.npmjs.com/package/dom-autoscroller\n   */\n  @Input() autoScroll: {\n    margin:\n      | number\n      | { top?: number; left?: number; right?: number; bottom?: number };\n    maxSpeed?:\n      | number\n      | { top?: number; left?: number; right?: number; bottom?: number };\n    scrollWhenOutside?: boolean;\n  } = {\n    margin: 20,\n  };\n\n  /**\n   * Called when the element can be dragged along one axis and has the mouse or pointer device pressed on it\n   */\n  @Output() dragPointerDown = new EventEmitter<DragPointerDownEvent>();\n\n  /**\n   * Called when the element has started to be dragged.\n   * Only called after at least one mouse or touch move event.\n   * If you call $event.cancelDrag$.emit() it will cancel the current drag\n   */\n  @Output() dragStart = new EventEmitter<DragStartEvent>();\n\n  /**\n   * Called after the ghost element has been created\n   */\n  @Output() ghostElementCreated = new EventEmitter<GhostElementCreatedEvent>();\n\n  /**\n   * Called when the element is being dragged\n   */\n  @Output() dragging = new EventEmitter<DragMoveEvent>();\n\n  /**\n   * Called after the element is dragged\n   */\n  @Output() dragEnd = new EventEmitter<DragEndEvent>();\n\n  /**\n   * @hidden\n   */\n  pointerDown$ = new Subject<PointerEvent>();\n\n  /**\n   * @hidden\n   */\n  pointerMove$ = new Subject<PointerEvent>();\n\n  /**\n   * @hidden\n   */\n  pointerUp$ = new Subject<PointerEvent>();\n\n  private eventListenerSubscriptions: {\n    mousemove?: () => void;\n    mousedown?: () => void;\n    mouseup?: () => void;\n    mouseenter?: () => void;\n    mouseleave?: () => void;\n    touchstart?: () => void;\n    touchmove?: () => void;\n    touchend?: () => void;\n    touchcancel?: () => void;\n  } = {};\n\n  private ghostElement: HTMLElement | null;\n\n  private destroy$ = new Subject<void>();\n\n  private timeLongPress: TimeLongPress = { timerBegin: 0, timerEnd: 0 };\n\n  private scroller: { destroy: () => void };\n\n  /**\n   * @hidden\n   */\n  constructor(\n    private element: ElementRef<HTMLElement>,\n    private renderer: Renderer2,\n    private draggableHelper: DraggableHelper,\n    private zone: NgZone,\n    private vcr: ViewContainerRef,\n    @Optional() private scrollContainer: DraggableScrollContainerDirective,\n    @Inject(DOCUMENT) private document: any\n  ) {}\n\n  ngOnInit(): void {\n    this.checkEventListeners();\n\n    const pointerDragged$: Observable<any> = this.pointerDown$.pipe(\n      filter(() => this.canDrag()),\n      mergeMap((pointerDownEvent: PointerEvent) => {\n        // fix for https://github.com/mattlewis92/angular-draggable-droppable/issues/61\n        // stop mouse events propagating up the chain\n        if (pointerDownEvent.event.stopPropagation && !this.scrollContainer) {\n          pointerDownEvent.event.stopPropagation();\n        }\n\n        // hack to prevent text getting selected in safari while dragging\n        const globalDragStyle: HTMLStyleElement =\n          this.renderer.createElement('style');\n        this.renderer.setAttribute(globalDragStyle, 'type', 'text/css');\n        this.renderer.appendChild(\n          globalDragStyle,\n          this.renderer.createText(`\n          body * {\n           -moz-user-select: none;\n           -ms-user-select: none;\n           -webkit-user-select: none;\n           user-select: none;\n          }\n        `)\n        );\n        requestAnimationFrame(() => {\n          this.document.head.appendChild(globalDragStyle);\n        });\n\n        const startScrollPosition = this.getScrollPosition();\n\n        const scrollContainerScroll$ = new Observable((observer) => {\n          const scrollContainer = this.scrollContainer\n            ? this.scrollContainer.elementRef.nativeElement\n            : 'window';\n          return this.renderer.listen(scrollContainer, 'scroll', (e) =>\n            observer.next(e)\n          );\n        }).pipe(\n          startWith(startScrollPosition),\n          map(() => this.getScrollPosition())\n        );\n\n        const currentDrag$ = new Subject<CurrentDragData>();\n        const cancelDrag$ = new ReplaySubject<void>();\n\n        if (this.dragPointerDown.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragPointerDown.next({ x: 0, y: 0 });\n          });\n        }\n\n        const dragComplete$ = merge(\n          this.pointerUp$,\n          this.pointerDown$,\n          cancelDrag$,\n          this.destroy$\n        ).pipe(share());\n\n        const pointerMove = combineLatest([\n          this.pointerMove$,\n          scrollContainerScroll$,\n        ]).pipe(\n          map(([pointerMoveEvent, scroll]) => {\n            return {\n              currentDrag$,\n              transformX: pointerMoveEvent.clientX - pointerDownEvent.clientX,\n              transformY: pointerMoveEvent.clientY - pointerDownEvent.clientY,\n              clientX: pointerMoveEvent.clientX,\n              clientY: pointerMoveEvent.clientY,\n              scrollLeft: scroll.left,\n              scrollTop: scroll.top,\n              target: pointerMoveEvent.event.target,\n            };\n          }),\n          map((moveData) => {\n            if (this.dragSnapGrid.x) {\n              moveData.transformX =\n                Math.round(moveData.transformX / this.dragSnapGrid.x) *\n                this.dragSnapGrid.x;\n            }\n\n            if (this.dragSnapGrid.y) {\n              moveData.transformY =\n                Math.round(moveData.transformY / this.dragSnapGrid.y) *\n                this.dragSnapGrid.y;\n            }\n\n            return moveData;\n          }),\n          map((moveData) => {\n            if (!this.dragAxis.x) {\n              moveData.transformX = 0;\n            }\n\n            if (!this.dragAxis.y) {\n              moveData.transformY = 0;\n            }\n\n            return moveData;\n          }),\n          map((moveData) => {\n            const scrollX = moveData.scrollLeft - startScrollPosition.left;\n            const scrollY = moveData.scrollTop - startScrollPosition.top;\n            return {\n              ...moveData,\n              x: moveData.transformX + scrollX,\n              y: moveData.transformY + scrollY,\n            };\n          }),\n          filter(\n            ({ x, y, transformX, transformY }) =>\n              !this.validateDrag ||\n              this.validateDrag({\n                x,\n                y,\n                transform: { x: transformX, y: transformY },\n              })\n          ),\n          takeUntil(dragComplete$),\n          share()\n        );\n\n        const dragStarted$ = pointerMove.pipe(take(1), share());\n        const dragEnded$ = pointerMove.pipe(takeLast(1), share());\n\n        dragStarted$.subscribe(({ clientX, clientY, x, y }) => {\n          if (this.dragStart.observers.length > 0) {\n            this.zone.run(() => {\n              this.dragStart.next({ cancelDrag$ });\n            });\n          }\n\n          this.scroller = autoScroll(\n            [\n              this.scrollContainer\n                ? this.scrollContainer.elementRef.nativeElement\n                : this.document.defaultView,\n            ],\n            {\n              ...this.autoScroll,\n              autoScroll() {\n                return true;\n              },\n            }\n          );\n          addClass(this.renderer, this.element, this.dragActiveClass);\n\n          if (this.ghostDragEnabled) {\n            const rect = this.element.nativeElement.getBoundingClientRect();\n            const clone = this.element.nativeElement.cloneNode(\n              true\n            ) as HTMLElement;\n            if (!this.showOriginalElementWhileDragging) {\n              this.renderer.setStyle(\n                this.element.nativeElement,\n                'visibility',\n                'hidden'\n              );\n            }\n\n            if (this.ghostElementAppendTo) {\n              this.ghostElementAppendTo.appendChild(clone);\n            } else {\n              this.element.nativeElement.parentNode!.insertBefore(\n                clone,\n                this.element.nativeElement.nextSibling\n              );\n            }\n\n            this.ghostElement = clone;\n\n            this.document.body.style.cursor = this.dragCursor;\n\n            this.setElementStyles(clone, {\n              position: 'fixed',\n              top: `${rect.top}px`,\n              left: `${rect.left}px`,\n              width: `${rect.width}px`,\n              height: `${rect.height}px`,\n              cursor: this.dragCursor,\n              margin: '0',\n              willChange: 'transform',\n              pointerEvents: 'none',\n            });\n\n            if (this.ghostElementTemplate) {\n              const viewRef = this.vcr.createEmbeddedView(\n                this.ghostElementTemplate\n              );\n              clone.innerHTML = '';\n              viewRef.rootNodes\n                .filter((node) => node instanceof Node)\n                .forEach((node) => {\n                  clone.appendChild(node);\n                });\n              dragEnded$.subscribe(() => {\n                this.vcr.remove(this.vcr.indexOf(viewRef));\n              });\n            }\n\n            if (this.ghostElementCreated.observers.length > 0) {\n              this.zone.run(() => {\n                this.ghostElementCreated.emit({\n                  clientX: clientX - x,\n                  clientY: clientY - y,\n                  element: clone,\n                });\n              });\n            }\n\n            dragEnded$.subscribe(() => {\n              clone.parentElement!.removeChild(clone);\n              this.ghostElement = null;\n              this.renderer.setStyle(\n                this.element.nativeElement,\n                'visibility',\n                ''\n              );\n            });\n          }\n\n          this.draggableHelper.currentDrag.next(currentDrag$);\n        });\n\n        dragEnded$\n          .pipe(\n            mergeMap((dragEndData) => {\n              const dragEndData$ = cancelDrag$.pipe(\n                count(),\n                take(1),\n                map((calledCount) => ({\n                  ...dragEndData,\n                  dragCancelled: calledCount > 0,\n                }))\n              );\n              cancelDrag$.complete();\n              return dragEndData$;\n            })\n          )\n          .subscribe(({ x, y, dragCancelled }) => {\n            this.scroller.destroy();\n            if (this.dragEnd.observers.length > 0) {\n              this.zone.run(() => {\n                this.dragEnd.next({ x, y, dragCancelled });\n              });\n            }\n            removeClass(this.renderer, this.element, this.dragActiveClass);\n            currentDrag$.complete();\n          });\n\n        merge(dragComplete$, dragEnded$)\n          .pipe(take(1))\n          .subscribe(() => {\n            requestAnimationFrame(() => {\n              this.document.head.removeChild(globalDragStyle);\n            });\n          });\n\n        return pointerMove;\n      }),\n      share()\n    );\n\n    merge(\n      pointerDragged$.pipe(\n        take(1),\n        map((value) => [, value])\n      ),\n      pointerDragged$.pipe(pairwise())\n    )\n      .pipe(\n        filter(([previous, next]) => {\n          if (!previous) {\n            return true;\n          }\n          return previous.x !== next.x || previous.y !== next.y;\n        }),\n        map(([previous, next]) => next)\n      )\n      .subscribe(\n        ({\n          x,\n          y,\n          currentDrag$,\n          clientX,\n          clientY,\n          transformX,\n          transformY,\n          target,\n        }) => {\n          if (this.dragging.observers.length > 0) {\n            this.zone.run(() => {\n              this.dragging.next({ x, y });\n            });\n          }\n          requestAnimationFrame(() => {\n            if (this.ghostElement) {\n              const transform = `translate3d(${transformX}px, ${transformY}px, 0px)`;\n              this.setElementStyles(this.ghostElement, {\n                transform,\n                '-webkit-transform': transform,\n                '-ms-transform': transform,\n                '-moz-transform': transform,\n                '-o-transform': transform,\n              });\n            }\n          });\n          currentDrag$.next({\n            clientX,\n            clientY,\n            dropData: this.dropData,\n            target,\n          });\n        }\n      );\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.dragAxis) {\n      this.checkEventListeners();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.unsubscribeEventListeners();\n    this.pointerDown$.complete();\n    this.pointerMove$.complete();\n    this.pointerUp$.complete();\n    this.destroy$.next();\n  }\n\n  private checkEventListeners(): void {\n    const canDrag: boolean = this.canDrag();\n    const hasEventListeners: boolean =\n      Object.keys(this.eventListenerSubscriptions).length > 0;\n\n    if (canDrag && !hasEventListeners) {\n      this.zone.runOutsideAngular(() => {\n        this.eventListenerSubscriptions.mousedown = this.renderer.listen(\n          this.element.nativeElement,\n          'mousedown',\n          (event: MouseEvent) => {\n            this.onMouseDown(event);\n          }\n        );\n\n        this.eventListenerSubscriptions.mouseup = this.renderer.listen(\n          'document',\n          'mouseup',\n          (event: MouseEvent) => {\n            this.onMouseUp(event);\n          }\n        );\n\n        this.eventListenerSubscriptions.touchstart = this.renderer.listen(\n          this.element.nativeElement,\n          'touchstart',\n          (event: TouchEvent) => {\n            this.onTouchStart(event);\n          }\n        );\n\n        this.eventListenerSubscriptions.touchend = this.renderer.listen(\n          'document',\n          'touchend',\n          (event: TouchEvent) => {\n            this.onTouchEnd(event);\n          }\n        );\n\n        this.eventListenerSubscriptions.touchcancel = this.renderer.listen(\n          'document',\n          'touchcancel',\n          (event: TouchEvent) => {\n            this.onTouchEnd(event);\n          }\n        );\n\n        this.eventListenerSubscriptions.mouseenter = this.renderer.listen(\n          this.element.nativeElement,\n          'mouseenter',\n          () => {\n            this.onMouseEnter();\n          }\n        );\n\n        this.eventListenerSubscriptions.mouseleave = this.renderer.listen(\n          this.element.nativeElement,\n          'mouseleave',\n          () => {\n            this.onMouseLeave();\n          }\n        );\n      });\n    } else if (!canDrag && hasEventListeners) {\n      this.unsubscribeEventListeners();\n    }\n  }\n\n  private onMouseDown(event: MouseEvent): void {\n    if (event.button === 0) {\n      if (!this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove = this.renderer.listen(\n          'document',\n          'mousemove',\n          (mouseMoveEvent: MouseEvent) => {\n            this.pointerMove$.next({\n              event: mouseMoveEvent,\n              clientX: mouseMoveEvent.clientX,\n              clientY: mouseMoveEvent.clientY,\n            });\n          }\n        );\n      }\n      this.pointerDown$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY,\n      });\n    }\n  }\n\n  private onMouseUp(event: MouseEvent): void {\n    if (event.button === 0) {\n      if (this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove();\n        delete this.eventListenerSubscriptions.mousemove;\n      }\n      this.pointerUp$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY,\n      });\n    }\n  }\n\n  private onTouchStart(event: TouchEvent): void {\n    let startScrollPosition: any;\n    let isDragActivated: boolean;\n    let hasContainerScrollbar: boolean;\n    if (this.touchStartLongPress) {\n      this.timeLongPress.timerBegin = Date.now();\n      isDragActivated = false;\n      hasContainerScrollbar = this.hasScrollbar();\n      startScrollPosition = this.getScrollPosition();\n    }\n\n    if (!this.eventListenerSubscriptions.touchmove) {\n      const contextMenuListener = fromEvent<Event>(\n        this.document,\n        'contextmenu'\n      ).subscribe((e) => {\n        e.preventDefault();\n      });\n\n      const touchMoveListener = fromEvent<TouchEvent>(\n        this.document,\n        'touchmove',\n        {\n          passive: false,\n        }\n      ).subscribe((touchMoveEvent) => {\n        if (\n          this.touchStartLongPress &&\n          !isDragActivated &&\n          hasContainerScrollbar\n        ) {\n          isDragActivated = this.shouldBeginDrag(\n            event,\n            touchMoveEvent,\n            startScrollPosition\n          );\n        }\n        if (\n          !this.touchStartLongPress ||\n          !hasContainerScrollbar ||\n          isDragActivated\n        ) {\n          touchMoveEvent.preventDefault();\n          this.pointerMove$.next({\n            event: touchMoveEvent,\n            clientX: touchMoveEvent.targetTouches[0].clientX,\n            clientY: touchMoveEvent.targetTouches[0].clientY,\n          });\n        }\n      });\n\n      this.eventListenerSubscriptions.touchmove = () => {\n        contextMenuListener.unsubscribe();\n        touchMoveListener.unsubscribe();\n      };\n    }\n    this.pointerDown$.next({\n      event,\n      clientX: event.touches[0].clientX,\n      clientY: event.touches[0].clientY,\n    });\n  }\n\n  private onTouchEnd(event: TouchEvent): void {\n    if (this.eventListenerSubscriptions.touchmove) {\n      this.eventListenerSubscriptions.touchmove();\n      delete this.eventListenerSubscriptions.touchmove;\n\n      if (this.touchStartLongPress) {\n        this.enableScroll();\n      }\n    }\n    this.pointerUp$.next({\n      event,\n      clientX: event.changedTouches[0].clientX,\n      clientY: event.changedTouches[0].clientY,\n    });\n  }\n\n  private onMouseEnter(): void {\n    this.setCursor(this.dragCursor);\n  }\n\n  private onMouseLeave(): void {\n    this.setCursor('');\n  }\n\n  private canDrag(): boolean {\n    return this.dragAxis.x || this.dragAxis.y;\n  }\n\n  private setCursor(value: string): void {\n    if (!this.eventListenerSubscriptions.mousemove) {\n      this.renderer.setStyle(this.element.nativeElement, 'cursor', value);\n    }\n  }\n\n  private unsubscribeEventListeners(): void {\n    Object.keys(this.eventListenerSubscriptions).forEach((type) => {\n      (this as any).eventListenerSubscriptions[type]();\n      delete (this as any).eventListenerSubscriptions[type];\n    });\n  }\n\n  private setElementStyles(\n    element: HTMLElement,\n    styles: { [key: string]: string }\n  ) {\n    Object.keys(styles).forEach((key) => {\n      this.renderer.setStyle(element, key, styles[key]);\n    });\n  }\n\n  private getScrollElement() {\n    if (this.scrollContainer) {\n      return this.scrollContainer.elementRef.nativeElement;\n    } else {\n      return this.document.body;\n    }\n  }\n\n  private getScrollPosition() {\n    if (this.scrollContainer) {\n      return {\n        top: this.scrollContainer.elementRef.nativeElement.scrollTop,\n        left: this.scrollContainer.elementRef.nativeElement.scrollLeft,\n      };\n    } else {\n      return {\n        top: window.pageYOffset || this.document.documentElement.scrollTop,\n        left: window.pageXOffset || this.document.documentElement.scrollLeft,\n      };\n    }\n  }\n\n  private shouldBeginDrag(\n    event: TouchEvent,\n    touchMoveEvent: TouchEvent,\n    startScrollPosition: { top: number; left: number }\n  ): boolean {\n    const moveScrollPosition = this.getScrollPosition();\n    const deltaScroll = {\n      top: Math.abs(moveScrollPosition.top - startScrollPosition.top),\n      left: Math.abs(moveScrollPosition.left - startScrollPosition.left),\n    };\n    const deltaX =\n      Math.abs(\n        touchMoveEvent.targetTouches[0].clientX - event.touches[0].clientX\n      ) - deltaScroll.left;\n    const deltaY =\n      Math.abs(\n        touchMoveEvent.targetTouches[0].clientY - event.touches[0].clientY\n      ) - deltaScroll.top;\n    const deltaTotal = deltaX + deltaY;\n    const longPressConfig = this.touchStartLongPress;\n    if (\n      deltaTotal > longPressConfig.delta ||\n      deltaScroll.top > 0 ||\n      deltaScroll.left > 0\n    ) {\n      this.timeLongPress.timerBegin = Date.now();\n    }\n    this.timeLongPress.timerEnd = Date.now();\n    const duration =\n      this.timeLongPress.timerEnd - this.timeLongPress.timerBegin;\n    if (duration >= longPressConfig.delay) {\n      this.disableScroll();\n      return true;\n    }\n    return false;\n  }\n\n  private enableScroll() {\n    if (this.scrollContainer) {\n      this.renderer.setStyle(\n        this.scrollContainer.elementRef.nativeElement,\n        'overflow',\n        ''\n      );\n    }\n    this.renderer.setStyle(this.document.body, 'overflow', '');\n  }\n\n  private disableScroll() {\n    /* istanbul ignore next */\n    if (this.scrollContainer) {\n      this.renderer.setStyle(\n        this.scrollContainer.elementRef.nativeElement,\n        'overflow',\n        'hidden'\n      );\n    }\n    this.renderer.setStyle(this.document.body, 'overflow', 'hidden');\n  }\n\n  private hasScrollbar(): boolean {\n    const scrollContainer = this.getScrollElement();\n    const containerHasHorizontalScroll =\n      scrollContainer.scrollWidth > scrollContainer.clientWidth;\n    const containerHasVerticalScroll =\n      scrollContainer.scrollHeight > scrollContainer.clientHeight;\n    return containerHasHorizontalScroll || containerHasVerticalScroll;\n  }\n}\n", "import {\n  Directive,\n  OnInit,\n  ElementRef,\n  OnDestroy,\n  Output,\n  EventEmitter,\n  NgZone,\n  Input,\n  Renderer2,\n  Optional,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { distinctUntilChanged, pairwise, filter, map } from 'rxjs/operators';\nimport { DraggableHelper } from './draggable-helper.provider';\nimport { DraggableScrollContainerDirective } from './draggable-scroll-container.directive';\nimport { addClass, removeClass } from './util';\n\nfunction isCoordinateWithinRectangle(\n  clientX: number,\n  clientY: number,\n  rect: ClientRect\n): boolean {\n  return (\n    clientX >= rect.left &&\n    clientX <= rect.right &&\n    clientY >= rect.top &&\n    clientY <= rect.bottom\n  );\n}\n\nexport interface DropEvent<T = any> {\n  dropData: T;\n  /**\n   * ClientX value of the mouse location where the drop occurred\n   */\n  clientX: number;\n  /**\n   * ClientY value of the mouse location where the drop occurred\n   */\n  clientY: number;\n  /**\n   * The target of the event where the drop occurred\n   */\n  target: EventTarget;\n}\n\nexport interface DragEvent<T = any> extends DropEvent<T> {}\n\nexport interface ValidateDropParams extends DropEvent {}\n\nexport type ValidateDrop = (params: ValidateDropParams) => boolean;\n\n@Directive({\n  selector: '[mwlDroppable]',\n})\nexport class DroppableDirective implements OnInit, OnDestroy {\n  /**\n   * Added to the element when an element is dragged over it\n   */\n  @Input() dragOverClass: string;\n\n  /**\n   * Added to the element any time a draggable element is being dragged\n   */\n  @Input() dragActiveClass: string;\n\n  /**\n   * Allow custom behaviour to control when the element is dropped\n   */\n  @Input() validateDrop: ValidateDrop;\n\n  /**\n   * Called when a draggable element starts overlapping the element\n   */\n  @Output() dragEnter = new EventEmitter<DropEvent>();\n\n  /**\n   * Called when a draggable element stops overlapping the element\n   */\n  @Output() dragLeave = new EventEmitter<DropEvent>();\n\n  /**\n   * Called when a draggable element is moved over the element\n   */\n  @Output() dragOver = new EventEmitter<DropEvent>();\n\n  /**\n   * Called when a draggable element is dropped on this element\n   */\n  @Output() drop = new EventEmitter<DropEvent>(); // eslint-disable-line  @angular-eslint/no-output-native\n\n  currentDragSubscription: Subscription;\n\n  constructor(\n    private element: ElementRef<HTMLElement>,\n    private draggableHelper: DraggableHelper,\n    private zone: NgZone,\n    private renderer: Renderer2,\n    @Optional() private scrollContainer: DraggableScrollContainerDirective\n  ) {}\n\n  ngOnInit() {\n    this.currentDragSubscription = this.draggableHelper.currentDrag.subscribe(\n      (drag$) => {\n        addClass(this.renderer, this.element, this.dragActiveClass);\n        const droppableElement: {\n          rect?: ClientRect;\n          updateCache: boolean;\n          scrollContainerRect?: ClientRect;\n        } = {\n          updateCache: true,\n        };\n\n        const deregisterScrollListener = this.renderer.listen(\n          this.scrollContainer\n            ? this.scrollContainer.elementRef.nativeElement\n            : 'window',\n          'scroll',\n          () => {\n            droppableElement.updateCache = true;\n          }\n        );\n\n        let currentDragEvent: DragEvent;\n        const overlaps$ = drag$.pipe(\n          map(({ clientX, clientY, dropData, target }) => {\n            currentDragEvent = { clientX, clientY, dropData, target };\n            if (droppableElement.updateCache) {\n              droppableElement.rect =\n                this.element.nativeElement.getBoundingClientRect();\n              if (this.scrollContainer) {\n                droppableElement.scrollContainerRect =\n                  this.scrollContainer.elementRef.nativeElement.getBoundingClientRect();\n              }\n              droppableElement.updateCache = false;\n            }\n            const isWithinElement = isCoordinateWithinRectangle(\n              clientX,\n              clientY,\n              droppableElement.rect as ClientRect\n            );\n\n            const isDropAllowed =\n              !this.validateDrop ||\n              this.validateDrop({ clientX, clientY, target, dropData });\n\n            if (droppableElement.scrollContainerRect) {\n              return (\n                isWithinElement &&\n                isDropAllowed &&\n                isCoordinateWithinRectangle(\n                  clientX,\n                  clientY,\n                  droppableElement.scrollContainerRect as ClientRect\n                )\n              );\n            } else {\n              return isWithinElement && isDropAllowed;\n            }\n          })\n        );\n\n        const overlapsChanged$ = overlaps$.pipe(distinctUntilChanged());\n\n        let dragOverActive: boolean; // TODO - see if there's a way of doing this via rxjs\n\n        overlapsChanged$\n          .pipe(filter((overlapsNow) => overlapsNow))\n          .subscribe(() => {\n            dragOverActive = true;\n            addClass(this.renderer, this.element, this.dragOverClass);\n            if (this.dragEnter.observers.length > 0) {\n              this.zone.run(() => {\n                this.dragEnter.next(currentDragEvent);\n              });\n            }\n          });\n\n        overlaps$.pipe(filter((overlapsNow) => overlapsNow)).subscribe(() => {\n          if (this.dragOver.observers.length > 0) {\n            this.zone.run(() => {\n              this.dragOver.next(currentDragEvent);\n            });\n          }\n        });\n\n        overlapsChanged$\n          .pipe(\n            pairwise(),\n            filter(([didOverlap, overlapsNow]) => didOverlap && !overlapsNow)\n          )\n          .subscribe(() => {\n            dragOverActive = false;\n            removeClass(this.renderer, this.element, this.dragOverClass);\n            if (this.dragLeave.observers.length > 0) {\n              this.zone.run(() => {\n                this.dragLeave.next(currentDragEvent);\n              });\n            }\n          });\n\n        drag$.subscribe({\n          complete: () => {\n            deregisterScrollListener();\n            removeClass(this.renderer, this.element, this.dragActiveClass);\n            if (dragOverActive) {\n              removeClass(this.renderer, this.element, this.dragOverClass);\n              if (this.drop.observers.length > 0) {\n                this.zone.run(() => {\n                  this.drop.next(currentDragEvent);\n                });\n              }\n            }\n          },\n        });\n      }\n    );\n  }\n\n  ngOnDestroy() {\n    if (this.currentDragSubscription) {\n      this.currentDragSubscription.unsubscribe();\n    }\n  }\n}\n", "import { NgModule } from '@angular/core';\nimport { DraggableDirective } from './draggable.directive';\nimport { DroppableDirective } from './droppable.directive';\nimport { DraggableScrollContainerDirective } from './draggable-scroll-container.directive';\n\n@NgModule({\n  declarations: [\n    DraggableDirective,\n    DroppableDirective,\n    DraggableScrollContainerDirective,\n  ],\n  exports: [\n    DraggableDirective,\n    DroppableDirective,\n    DraggableScrollContainerDirective,\n  ],\n})\nexport class DragAndDropModule {}\n", "/*\n * Public API Surface of angular-draggable-droppable\n */\n\nexport * from './lib/drag-and-drop.module';\nexport {\n  DropEvent,\n  ValidateDrop,\n  ValidateDropParams,\n  DroppableDirective,\n} from './lib/droppable.directive';\nexport {\n  DragPointerDownEvent,\n  DragStartEvent,\n  DragMoveEvent,\n  DragEndEvent,\n  GhostElementCreatedEvent,\n  ValidateDrag,\n  ValidateDragParams,\n  DraggableDirective,\n} from './lib/draggable.directive';\nexport { DraggableScrollContainerDirective } from './lib/draggable-scroll-container.directive';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": ["i1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i2.DraggableScrollContainerDirective"], "mappings": ";;;;;;;SAEgB,QAAQ,CACtB,QAAmB,EACnB,OAAgC,EAChC,UAAkB,EAAA;AAElB,IAAA,IAAI,UAAU,EAAE;QACd,UAAU;aACP,KAAK,CAAC,GAAG,CAAC;AACV,aAAA,OAAO,CAAC,CAAC,SAAS,KACjB,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CACpD,CAAC;AACL,KAAA;AACH,CAAC;SAEe,WAAW,CACzB,QAAmB,EACnB,OAAgC,EAChC,aAAqB,EAAA;AAErB,IAAA,IAAI,aAAa,EAAE;QACjB,aAAa;aACV,KAAK,CAAC,GAAG,CAAC;AACV,aAAA,OAAO,CAAC,CAAC,SAAS,KACjB,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CACvD,CAAC;AACL,KAAA;AACH;;MCfa,eAAe,CAAA;AAH5B,IAAA,WAAA,GAAA;AAIE,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,OAAO,EAA4B,CAAC;KACvD;;4GAFY,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAf,eAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cAFd,MAAM,EAAA,CAAA,CAAA;2FAEP,eAAe,EAAA,UAAA,EAAA,CAAA;kBAH3B,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;iBACnB,CAAA;;;ACVD;;;;;;;AAOG;MAIU,iCAAiC,CAAA;AAC5C;;AAEG;AACH,IAAA,WAAA,CAAmB,UAAmC,EAAA;AAAnC,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAyB;KAAI;;8HAJ/C,iCAAiC,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;kHAAjC,iCAAiC,EAAA,QAAA,EAAA,+BAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;2FAAjC,iCAAiC,EAAA,UAAA,EAAA,CAAA;kBAH7C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;iBAC1C,CAAA;;;MCuFY,kBAAkB,CAAA;AAqI7B;;AAEG;AACH,IAAA,WAAA,CACU,OAAgC,EAChC,QAAmB,EACnB,eAAgC,EAChC,IAAY,EACZ,GAAqB,EACT,eAAkD,EAC5C,QAAa,EAAA;AAN/B,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAyB;AAChC,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;AACnB,QAAA,IAAe,CAAA,eAAA,GAAf,eAAe,CAAiB;AAChC,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AACZ,QAAA,IAAG,CAAA,GAAA,GAAH,GAAG,CAAkB;AACT,QAAA,IAAe,CAAA,eAAA,GAAf,eAAe,CAAmC;AAC5C,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAK;AAzIzC;;AAEG;AACM,QAAA,IAAQ,CAAA,QAAA,GAAa,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;AAEnD;;AAEG;AACM,QAAA,IAAY,CAAA,YAAA,GAAa,EAAE,CAAC;AAErC;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAY,IAAI,CAAC;AAE1C;;AAEG;AACM,QAAA,IAAgC,CAAA,gCAAA,GAAY,KAAK,CAAC;AAO3D;;AAEG;AACM,QAAA,IAAU,CAAA,UAAA,GAAW,EAAE,CAAC;AAsBjC;;AAEG;QACM,IAAA,CAAA,UAAU,GAQf;AACF,YAAA,MAAM,EAAE,EAAE;SACX,CAAC;AAEF;;AAEG;AACO,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAwB,CAAC;AAErE;;;;AAIG;AACO,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,YAAY,EAAkB,CAAC;AAEzD;;AAEG;AACO,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAA4B,CAAC;AAE7E;;AAEG;AACO,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAAiB,CAAC;AAEvD;;AAEG;AACO,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,YAAY,EAAgB,CAAC;AAErD;;AAEG;AACH,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAgB,CAAC;AAE3C;;AAEG;AACH,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAgB,CAAC;AAE3C;;AAEG;AACH,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAgB,CAAC;AAEjC,QAAA,IAA0B,CAAA,0BAAA,GAU9B,EAAE,CAAC;AAIC,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE/B,QAAA,IAAa,CAAA,aAAA,GAAkB,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;KAelE;IAEJ,QAAQ,GAAA;QACN,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,MAAM,eAAe,GAAoB,IAAI,CAAC,YAAY,CAAC,IAAI,CAC7D,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,EAC5B,QAAQ,CAAC,CAAC,gBAA8B,KAAI;;;YAG1C,IAAI,gBAAgB,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACnE,gBAAA,gBAAgB,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;AAC1C,aAAA;;YAGD,MAAM,eAAe,GACnB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAChE,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CACvB,eAAe,EACf,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;;;;;;;AAO1B,QAAA,CAAA,CAAC,CACD,CAAC;YACF,qBAAqB,CAAC,MAAK;gBACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAClD,aAAC,CAAC,CAAC;AAEH,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAErD,MAAM,sBAAsB,GAAG,IAAI,UAAU,CAAC,CAAC,QAAQ,KAAI;AACzD,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe;AAC1C,sBAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa;sBAC7C,QAAQ,CAAC;gBACb,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC,CAAC,KACvD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CACjB,CAAC;aACH,CAAC,CAAC,IAAI,CACL,SAAS,CAAC,mBAAmB,CAAC,EAC9B,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC,CACpC,CAAC;AAEF,YAAA,MAAM,YAAY,GAAG,IAAI,OAAO,EAAmB,CAAC;AACpD,YAAA,MAAM,WAAW,GAAG,IAAI,aAAa,EAAQ,CAAC;YAE9C,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7C,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,oBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5C,iBAAC,CAAC,CAAC;AACJ,aAAA;YAED,MAAM,aAAa,GAAG,KAAK,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,YAAY,EACjB,WAAW,EACX,IAAI,CAAC,QAAQ,CACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAEhB,MAAM,WAAW,GAAG,aAAa,CAAC;AAChC,gBAAA,IAAI,CAAC,YAAY;gBACjB,sBAAsB;AACvB,aAAA,CAAC,CAAC,IAAI,CACL,GAAG,CAAC,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,KAAI;gBACjC,OAAO;oBACL,YAAY;AACZ,oBAAA,UAAU,EAAE,gBAAgB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO;AAC/D,oBAAA,UAAU,EAAE,gBAAgB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO;oBAC/D,OAAO,EAAE,gBAAgB,CAAC,OAAO;oBACjC,OAAO,EAAE,gBAAgB,CAAC,OAAO;oBACjC,UAAU,EAAE,MAAM,CAAC,IAAI;oBACvB,SAAS,EAAE,MAAM,CAAC,GAAG;AACrB,oBAAA,MAAM,EAAE,gBAAgB,CAAC,KAAK,CAAC,MAAM;iBACtC,CAAC;AACJ,aAAC,CAAC,EACF,GAAG,CAAC,CAAC,QAAQ,KAAI;AACf,gBAAA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE;AACvB,oBAAA,QAAQ,CAAC,UAAU;AACjB,wBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACrD,4BAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACvB,iBAAA;AAED,gBAAA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE;AACvB,oBAAA,QAAQ,CAAC,UAAU;AACjB,wBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACrD,4BAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACvB,iBAAA;AAED,gBAAA,OAAO,QAAQ,CAAC;AAClB,aAAC,CAAC,EACF,GAAG,CAAC,CAAC,QAAQ,KAAI;AACf,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;AACpB,oBAAA,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;AACzB,iBAAA;AAED,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;AACpB,oBAAA,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;AACzB,iBAAA;AAED,gBAAA,OAAO,QAAQ,CAAC;AAClB,aAAC,CAAC,EACF,GAAG,CAAC,CAAC,QAAQ,KAAI;gBACf,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,GAAG,mBAAmB,CAAC,IAAI,CAAC;gBAC/D,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC;AAC7D,gBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,QAAQ,CACX,EAAA,EAAA,CAAC,EAAE,QAAQ,CAAC,UAAU,GAAG,OAAO,EAChC,CAAC,EAAE,QAAQ,CAAC,UAAU,GAAG,OAAO,EAChC,CAAA,CAAA;aACH,CAAC,EACF,MAAM,CACJ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,KAC/B,CAAC,IAAI,CAAC,YAAY;gBAClB,IAAI,CAAC,YAAY,CAAC;oBAChB,CAAC;oBACD,CAAC;oBACD,SAAS,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE;iBAC5C,CAAC,CACL,EACD,SAAS,CAAC,aAAa,CAAC,EACxB,KAAK,EAAE,CACR,CAAC;AAEF,YAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACxD,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AAE1D,YAAA,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,KAAI;gBACpD,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;wBACjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;AACvC,qBAAC,CAAC,CAAC;AACJ,iBAAA;AAED,gBAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CACxB;AACE,oBAAA,IAAI,CAAC,eAAe;AAClB,0BAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa;AAC/C,0BAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;AAC9B,iBAAA,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAEI,IAAI,CAAC,UAAU,CAAA,EAAA,EAClB,UAAU,GAAA;AACR,wBAAA,OAAO,IAAI,CAAC;AACd,qBAAC,IAEJ,CAAC;AACF,gBAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAE5D,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACzB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;AAChE,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAChD,IAAI,CACU,CAAC;AACjB,oBAAA,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;AAC1C,wBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,YAAY,EACZ,QAAQ,CACT,CAAC;AACH,qBAAA;oBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,wBAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC9C,qBAAA;AAAM,yBAAA;AACL,wBAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAW,CAAC,YAAY,CACjD,KAAK,EACL,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CACvC,CAAC;AACH,qBAAA;AAED,oBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAE1B,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;AAElD,oBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AAC3B,wBAAA,QAAQ,EAAE,OAAO;AACjB,wBAAA,GAAG,EAAE,CAAA,EAAG,IAAI,CAAC,GAAG,CAAI,EAAA,CAAA;AACpB,wBAAA,IAAI,EAAE,CAAA,EAAG,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA;AACtB,wBAAA,KAAK,EAAE,CAAA,EAAG,IAAI,CAAC,KAAK,CAAI,EAAA,CAAA;AACxB,wBAAA,MAAM,EAAE,CAAA,EAAG,IAAI,CAAC,MAAM,CAAI,EAAA,CAAA;wBAC1B,MAAM,EAAE,IAAI,CAAC,UAAU;AACvB,wBAAA,MAAM,EAAE,GAAG;AACX,wBAAA,UAAU,EAAE,WAAW;AACvB,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA,CAAC,CAAC;oBAEH,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,wBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CACzC,IAAI,CAAC,oBAAoB,CAC1B,CAAC;AACF,wBAAA,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;AACrB,wBAAA,OAAO,CAAC,SAAS;6BACd,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,YAAY,IAAI,CAAC;AACtC,6BAAA,OAAO,CAAC,CAAC,IAAI,KAAI;AAChB,4BAAA,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,yBAAC,CAAC,CAAC;AACL,wBAAA,UAAU,CAAC,SAAS,CAAC,MAAK;AACxB,4BAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7C,yBAAC,CAAC,CAAC;AACJ,qBAAA;oBAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACjD,wBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,4BAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;gCAC5B,OAAO,EAAE,OAAO,GAAG,CAAC;gCACpB,OAAO,EAAE,OAAO,GAAG,CAAC;AACpB,gCAAA,OAAO,EAAE,KAAK;AACf,6BAAA,CAAC,CAAC;AACL,yBAAC,CAAC,CAAC;AACJ,qBAAA;AAED,oBAAA,UAAU,CAAC,SAAS,CAAC,MAAK;AACxB,wBAAA,KAAK,CAAC,aAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxC,wBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,wBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,YAAY,EACZ,EAAE,CACH,CAAC;AACJ,qBAAC,CAAC,CAAC;AACJ,iBAAA;gBAED,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACtD,aAAC,CAAC,CAAC;YAEH,UAAU;AACP,iBAAA,IAAI,CACH,QAAQ,CAAC,CAAC,WAAW,KAAI;AACvB,gBAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CACnC,KAAK,EAAE,EACP,IAAI,CAAC,CAAC,CAAC,EACP,GAAG,CAAC,CAAC,WAAW,MAAK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAChB,WAAW,CACd,EAAA,EAAA,aAAa,EAAE,WAAW,GAAG,CAAC,EAC9B,CAAA,CAAA,CAAC,CACJ,CAAC;gBACF,WAAW,CAAC,QAAQ,EAAE,CAAC;AACvB,gBAAA,OAAO,YAAY,CAAC;AACtB,aAAC,CAAC,CACH;iBACA,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,KAAI;AACrC,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,wBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC;AAC7C,qBAAC,CAAC,CAAC;AACJ,iBAAA;AACD,gBAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC/D,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;AAEL,YAAA,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC;AAC7B,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACb,SAAS,CAAC,MAAK;gBACd,qBAAqB,CAAC,MAAK;oBACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAClD,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;AAEL,YAAA,OAAO,WAAW,CAAC;AACrB,SAAC,CAAC,EACF,KAAK,EAAE,CACR,CAAC;AAEF,QAAA,KAAK,CACH,eAAe,CAAC,IAAI,CAClB,IAAI,CAAC,CAAC,CAAC,EACP,GAAG,CAAC,CAAC,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAC1B,EACD,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CACjC;aACE,IAAI,CACH,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAI;YAC1B,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACD,YAAA,OAAO,QAAQ,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;AACxD,SAAC,CAAC,EACF,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,CAChC;aACA,SAAS,CACR,CAAC,EACC,CAAC,EACD,CAAC,EACD,YAAY,EACZ,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,MAAM,GACP,KAAI;YACH,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;oBACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/B,iBAAC,CAAC,CAAC;AACJ,aAAA;YACD,qBAAqB,CAAC,MAAK;gBACzB,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,oBAAA,MAAM,SAAS,GAAG,CAAA,YAAA,EAAe,UAAU,CAAO,IAAA,EAAA,UAAU,UAAU,CAAC;AACvE,oBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE;wBACvC,SAAS;AACT,wBAAA,mBAAmB,EAAE,SAAS;AAC9B,wBAAA,eAAe,EAAE,SAAS;AAC1B,wBAAA,gBAAgB,EAAE,SAAS;AAC3B,wBAAA,cAAc,EAAE,SAAS;AAC1B,qBAAA,CAAC,CAAC;AACJ,iBAAA;AACH,aAAC,CAAC,CAAC;YACH,YAAY,CAAC,IAAI,CAAC;gBAChB,OAAO;gBACP,OAAO;gBACP,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM;AACP,aAAA,CAAC,CAAC;AACL,SAAC,CACF,CAAC;KACL;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC5B,SAAA;KACF;IAED,WAAW,GAAA;QACT,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IAEO,mBAAmB,GAAA;AACzB,QAAA,MAAM,OAAO,GAAY,IAAI,CAAC,OAAO,EAAE,CAAC;AACxC,QAAA,MAAM,iBAAiB,GACrB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAE1D,QAAA,IAAI,OAAO,IAAI,CAAC,iBAAiB,EAAE;AACjC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC/B,IAAI,CAAC,0BAA0B,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC9D,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,WAAW,EACX,CAAC,KAAiB,KAAI;AACpB,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1B,iBAAC,CACF,CAAC;AAEF,gBAAA,IAAI,CAAC,0BAA0B,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC5D,UAAU,EACV,SAAS,EACT,CAAC,KAAiB,KAAI;AACpB,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACxB,iBAAC,CACF,CAAC;gBAEF,IAAI,CAAC,0BAA0B,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC/D,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,YAAY,EACZ,CAAC,KAAiB,KAAI;AACpB,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC3B,iBAAC,CACF,CAAC;AAEF,gBAAA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC7D,UAAU,EACV,UAAU,EACV,CAAC,KAAiB,KAAI;AACpB,oBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACzB,iBAAC,CACF,CAAC;AAEF,gBAAA,IAAI,CAAC,0BAA0B,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAChE,UAAU,EACV,aAAa,EACb,CAAC,KAAiB,KAAI;AACpB,oBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACzB,iBAAC,CACF,CAAC;gBAEF,IAAI,CAAC,0BAA0B,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC/D,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,YAAY,EACZ,MAAK;oBACH,IAAI,CAAC,YAAY,EAAE,CAAC;AACtB,iBAAC,CACF,CAAC;gBAEF,IAAI,CAAC,0BAA0B,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC/D,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,YAAY,EACZ,MAAK;oBACH,IAAI,CAAC,YAAY,EAAE,CAAC;AACtB,iBAAC,CACF,CAAC;AACJ,aAAC,CAAC,CAAC;AACJ,SAAA;AAAM,aAAA,IAAI,CAAC,OAAO,IAAI,iBAAiB,EAAE;YACxC,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAClC,SAAA;KACF;AAEO,IAAA,WAAW,CAAC,KAAiB,EAAA;AACnC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE;AAC9C,gBAAA,IAAI,CAAC,0BAA0B,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC9D,UAAU,EACV,WAAW,EACX,CAAC,cAA0B,KAAI;AAC7B,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACrB,wBAAA,KAAK,EAAE,cAAc;wBACrB,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,OAAO,EAAE,cAAc,CAAC,OAAO;AAChC,qBAAA,CAAC,CAAC;AACL,iBAAC,CACF,CAAC;AACH,aAAA;AACD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB,KAAK;gBACL,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,OAAO,EAAE,KAAK,CAAC,OAAO;AACvB,aAAA,CAAC,CAAC;AACJ,SAAA;KACF;AAEO,IAAA,SAAS,CAAC,KAAiB,EAAA;AACjC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,YAAA,IAAI,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE;AAC7C,gBAAA,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,CAAC;AAC5C,gBAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC;AAClD,aAAA;AACD,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;gBACnB,KAAK;gBACL,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,OAAO,EAAE,KAAK,CAAC,OAAO;AACvB,aAAA,CAAC,CAAC;AACJ,SAAA;KACF;AAEO,IAAA,YAAY,CAAC,KAAiB,EAAA;AACpC,QAAA,IAAI,mBAAwB,CAAC;AAC7B,QAAA,IAAI,eAAwB,CAAC;AAC7B,QAAA,IAAI,qBAA8B,CAAC;QACnC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3C,eAAe,GAAG,KAAK,CAAC;AACxB,YAAA,qBAAqB,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5C,YAAA,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAChD,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE;AAC9C,YAAA,MAAM,mBAAmB,GAAG,SAAS,CACnC,IAAI,CAAC,QAAQ,EACb,aAAa,CACd,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;gBAChB,CAAC,CAAC,cAAc,EAAE,CAAC;AACrB,aAAC,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,SAAS,CACjC,IAAI,CAAC,QAAQ,EACb,WAAW,EACX;AACE,gBAAA,OAAO,EAAE,KAAK;AACf,aAAA,CACF,CAAC,SAAS,CAAC,CAAC,cAAc,KAAI;gBAC7B,IACE,IAAI,CAAC,mBAAmB;AACxB,oBAAA,CAAC,eAAe;AAChB,oBAAA,qBAAqB,EACrB;oBACA,eAAe,GAAG,IAAI,CAAC,eAAe,CACpC,KAAK,EACL,cAAc,EACd,mBAAmB,CACpB,CAAC;AACH,iBAAA;gBACD,IACE,CAAC,IAAI,CAAC,mBAAmB;AACzB,oBAAA,CAAC,qBAAqB;AACtB,oBAAA,eAAe,EACf;oBACA,cAAc,CAAC,cAAc,EAAE,CAAC;AAChC,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACrB,wBAAA,KAAK,EAAE,cAAc;wBACrB,OAAO,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO;wBAChD,OAAO,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO;AACjD,qBAAA,CAAC,CAAC;AACJ,iBAAA;AACH,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,0BAA0B,CAAC,SAAS,GAAG,MAAK;gBAC/C,mBAAmB,CAAC,WAAW,EAAE,CAAC;gBAClC,iBAAiB,CAAC,WAAW,EAAE,CAAC;AAClC,aAAC,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACrB,KAAK;YACL,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;YACjC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;AAClC,SAAA,CAAC,CAAC;KACJ;AAEO,IAAA,UAAU,CAAC,KAAiB,EAAA;AAClC,QAAA,IAAI,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE;AAC7C,YAAA,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,CAAC;AAC5C,YAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC;YAEjD,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,YAAY,EAAE,CAAC;AACrB,aAAA;AACF,SAAA;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnB,KAAK;YACL,OAAO,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO;YACxC,OAAO,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO;AACzC,SAAA,CAAC,CAAC;KACJ;IAEO,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACjC;IAEO,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KACpB;IAEO,OAAO,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC3C;AAEO,IAAA,SAAS,CAAC,KAAa,EAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE;AAC9C,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACrE,SAAA;KACF;IAEO,yBAAyB,GAAA;AAC/B,QAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC3D,YAAA,IAAY,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC;AACjD,YAAA,OAAQ,IAAY,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;AACxD,SAAC,CAAC,CAAC;KACJ;IAEO,gBAAgB,CACtB,OAAoB,EACpB,MAAiC,EAAA;QAEjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACpD,SAAC,CAAC,CAAC;KACJ;IAEO,gBAAgB,GAAA;QACtB,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC;AACtD,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC3B,SAAA;KACF;IAEO,iBAAiB,GAAA;QACvB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,OAAO;gBACL,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;gBAC5D,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU;aAC/D,CAAC;AACH,SAAA;AAAM,aAAA;YACL,OAAO;gBACL,GAAG,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS;gBAClE,IAAI,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU;aACrE,CAAC;AACH,SAAA;KACF;AAEO,IAAA,eAAe,CACrB,KAAiB,EACjB,cAA0B,EAC1B,mBAAkD,EAAA;AAElD,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACpD,QAAA,MAAM,WAAW,GAAG;AAClB,YAAA,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,GAAG,mBAAmB,CAAC,GAAG,CAAC;AAC/D,YAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;SACnE,CAAC;AACF,QAAA,MAAM,MAAM,GACV,IAAI,CAAC,GAAG,CACN,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CACnE,GAAG,WAAW,CAAC,IAAI,CAAC;AACvB,QAAA,MAAM,MAAM,GACV,IAAI,CAAC,GAAG,CACN,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CACnE,GAAG,WAAW,CAAC,GAAG,CAAC;AACtB,QAAA,MAAM,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC;AACnC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACjD,QAAA,IACE,UAAU,GAAG,eAAe,CAAC,KAAK;YAClC,WAAW,CAAC,GAAG,GAAG,CAAC;AACnB,YAAA,WAAW,CAAC,IAAI,GAAG,CAAC,EACpB;YACA,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC5C,SAAA;QACD,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACzC,QAAA,MAAM,QAAQ,GACZ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;AAC9D,QAAA,IAAI,QAAQ,IAAI,eAAe,CAAC,KAAK,EAAE;YACrC,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KACd;IAEO,YAAY,GAAA;QAClB,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,EAC7C,UAAU,EACV,EAAE,CACH,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;KAC5D;IAEO,aAAa,GAAA;;QAEnB,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,EAC7C,UAAU,EACV,QAAQ,CACT,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KAClE;IAEO,YAAY,GAAA;AAClB,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAChD,MAAM,4BAA4B,GAChC,eAAe,CAAC,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;QAC5D,MAAM,0BAA0B,GAC9B,eAAe,CAAC,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC;QAC9D,OAAO,4BAA4B,IAAI,0BAA0B,CAAC;KACnE;;AAnxBU,kBAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,qNA+InB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;mGA/IP,kBAAkB,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,gCAAA,EAAA,kCAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAH9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;iBAC3B,CAAA;;;8BA+II,QAAQ;;8BACR,MAAM;+BAAC,QAAQ,CAAA;;yBA3IT,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,gCAAgC,EAAA,CAAA;sBAAxC,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAeI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAOG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAKG,mBAAmB,EAAA,CAAA;sBAA5B,MAAM;gBAKG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;;;ACjLT,SAAS,2BAA2B,CAClC,OAAe,EACf,OAAe,EACf,IAAgB,EAAA;AAEhB,IAAA,QACE,OAAO,IAAI,IAAI,CAAC,IAAI;QACpB,OAAO,IAAI,IAAI,CAAC,KAAK;QACrB,OAAO,IAAI,IAAI,CAAC,GAAG;AACnB,QAAA,OAAO,IAAI,IAAI,CAAC,MAAM,EACtB;AACJ,CAAC;MA2BY,kBAAkB,CAAA;IAsC7B,WACU,CAAA,OAAgC,EAChC,eAAgC,EAChC,IAAY,EACZ,QAAmB,EACP,eAAkD,EAAA;AAJ9D,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAyB;AAChC,QAAA,IAAe,CAAA,eAAA,GAAf,eAAe,CAAiB;AAChC,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AACZ,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;AACP,QAAA,IAAe,CAAA,eAAA,GAAf,eAAe,CAAmC;AA3BxE;;AAEG;AACO,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,YAAY,EAAa,CAAC;AAEpD;;AAEG;AACO,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,YAAY,EAAa,CAAC;AAEpD;;AAEG;AACO,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAAa,CAAC;AAEnD;;AAEG;QACO,IAAA,CAAA,IAAI,GAAG,IAAI,YAAY,EAAa,CAAC;KAU3C;IAEJ,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CACvE,CAAC,KAAK,KAAI;AACR,YAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;AAC5D,YAAA,MAAM,gBAAgB,GAIlB;AACF,gBAAA,WAAW,EAAE,IAAI;aAClB,CAAC;YAEF,MAAM,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CACnD,IAAI,CAAC,eAAe;AAClB,kBAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa;AAC/C,kBAAE,QAAQ,EACZ,QAAQ,EACR,MAAK;AACH,gBAAA,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC,aAAC,CACF,CAAC;AAEF,YAAA,IAAI,gBAA2B,CAAC;AAChC,YAAA,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAC1B,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAI;gBAC7C,gBAAgB,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC1D,IAAI,gBAAgB,CAAC,WAAW,EAAE;AAChC,oBAAA,gBAAgB,CAAC,IAAI;AACnB,wBAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;oBACrD,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,wBAAA,gBAAgB,CAAC,mBAAmB;4BAClC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;AACzE,qBAAA;AACD,oBAAA,gBAAgB,CAAC,WAAW,GAAG,KAAK,CAAC;AACtC,iBAAA;AACD,gBAAA,MAAM,eAAe,GAAG,2BAA2B,CACjD,OAAO,EACP,OAAO,EACP,gBAAgB,CAAC,IAAkB,CACpC,CAAC;AAEF,gBAAA,MAAM,aAAa,GACjB,CAAC,IAAI,CAAC,YAAY;AAClB,oBAAA,IAAI,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAE5D,IAAI,gBAAgB,CAAC,mBAAmB,EAAE;AACxC,oBAAA,QACE,eAAe;wBACf,aAAa;wBACb,2BAA2B,CACzB,OAAO,EACP,OAAO,EACP,gBAAgB,CAAC,mBAAiC,CACnD,EACD;AACH,iBAAA;AAAM,qBAAA;oBACL,OAAO,eAAe,IAAI,aAAa,CAAC;AACzC,iBAAA;aACF,CAAC,CACH,CAAC;YAEF,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;YAEhE,IAAI,cAAuB,CAAC;YAE5B,gBAAgB;iBACb,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;iBAC1C,SAAS,CAAC,MAAK;gBACd,cAAc,GAAG,IAAI,CAAC;AACtB,gBAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC1D,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACxC,qBAAC,CAAC,CAAC;AACJ,iBAAA;AACH,aAAC,CAAC,CAAC;AAEL,YAAA,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBAClE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,wBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACvC,qBAAC,CAAC,CAAC;AACJ,iBAAA;AACH,aAAC,CAAC,CAAC;YAEH,gBAAgB;iBACb,IAAI,CACH,QAAQ,EAAE,EACV,MAAM,CAAC,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,UAAU,IAAI,CAAC,WAAW,CAAC,CAClE;iBACA,SAAS,CAAC,MAAK;gBACd,cAAc,GAAG,KAAK,CAAC;AACvB,gBAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACxC,qBAAC,CAAC,CAAC;AACJ,iBAAA;AACH,aAAC,CAAC,CAAC;YAEL,KAAK,CAAC,SAAS,CAAC;gBACd,QAAQ,EAAE,MAAK;AACb,oBAAA,wBAAwB,EAAE,CAAC;AAC3B,oBAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;AAC/D,oBAAA,IAAI,cAAc,EAAE;AAClB,wBAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,4BAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,gCAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACnC,6BAAC,CAAC,CAAC;AACJ,yBAAA;AACF,qBAAA;iBACF;AACF,aAAA,CAAC,CAAC;AACL,SAAC,CACF,CAAC;KACH;IAED,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC5C,SAAA;KACF;;+GAxKU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,iCAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;mGAAlB,kBAAkB,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAH9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;iBAC3B,CAAA;;;8BA4CI,QAAQ;;yBAvCF,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKI,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAKG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAKG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAKG,IAAI,EAAA,CAAA;sBAAb,MAAM;;;MCzEI,iBAAiB,CAAA;;8GAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,iBAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,iBAV1B,kBAAkB;QAClB,kBAAkB;AAClB,QAAA,iCAAiC,aAGjC,kBAAkB;QAClB,kBAAkB;QAClB,iCAAiC,CAAA,EAAA,CAAA,CAAA;+GAGxB,iBAAiB,EAAA,CAAA,CAAA;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAZ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,YAAY,EAAE;wBACZ,kBAAkB;wBAClB,kBAAkB;wBAClB,iCAAiC;AAClC,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,kBAAkB;wBAClB,kBAAkB;wBAClB,iCAAiC;AAClC,qBAAA;iBACF,CAAA;;;AChBD;;AAEG;;ACFH;;AAEG;;;;"}