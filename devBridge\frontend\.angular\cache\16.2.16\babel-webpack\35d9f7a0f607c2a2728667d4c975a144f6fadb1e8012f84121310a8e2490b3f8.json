{"ast": null, "code": "import { ValueSetter } from \"./Setter.js\";\nexport class Parser {\n  run(dateString, token, match, options) {\n    const result = this.parse(dateString, token, match, options);\n    if (!result) {\n      return null;\n    }\n    return {\n      setter: new ValueSetter(result.value, this.validate, this.set, this.priority, this.subPriority),\n      rest: result.rest\n    };\n  }\n  validate(_utcDate, _value, _options) {\n    return true;\n  }\n}", "map": {"version": 3, "names": ["ValueSetter", "<PERSON><PERSON><PERSON>", "run", "dateString", "token", "match", "options", "result", "parse", "setter", "value", "validate", "set", "priority", "subPriority", "rest", "_utcDate", "_value", "_options"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/parse/_lib/Parser.js"], "sourcesContent": ["import { ValueSetter } from \"./Setter.js\";\n\nexport class Parser {\n  run(dateString, token, match, options) {\n    const result = this.parse(dateString, token, match, options);\n    if (!result) {\n      return null;\n    }\n\n    return {\n      setter: new ValueSetter(\n        result.value,\n        this.validate,\n        this.set,\n        this.priority,\n        this.subPriority,\n      ),\n      rest: result.rest,\n    };\n  }\n\n  validate(_utcDate, _value, _options) {\n    return true;\n  }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,aAAa;AAEzC,OAAO,MAAMC,MAAM,CAAC;EAClBC,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACrC,MAAMC,MAAM,GAAG,IAAI,CAACC,KAAK,CAACL,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,CAAC;IAC5D,IAAI,CAACC,MAAM,EAAE;MACX,OAAO,IAAI;IACb;IAEA,OAAO;MACLE,MAAM,EAAE,IAAIT,WAAW,CACrBO,MAAM,CAACG,KAAK,EACZ,IAAI,CAACC,QAAQ,EACb,IAAI,CAACC,GAAG,EACR,IAAI,CAACC,QAAQ,EACb,IAAI,CAACC,WACP,CAAC;MACDC,IAAI,EAAER,MAAM,CAACQ;IACf,CAAC;EACH;EAEAJ,QAAQA,CAACK,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IACnC,OAAO,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}