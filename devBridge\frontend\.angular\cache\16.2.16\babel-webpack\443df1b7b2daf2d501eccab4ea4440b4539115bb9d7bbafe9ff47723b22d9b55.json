{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { add } from \"./add.js\";\nimport { differenceInDays } from \"./differenceInDays.js\";\nimport { differenceInHours } from \"./differenceInHours.js\";\nimport { differenceInMinutes } from \"./differenceInMinutes.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\nimport { differenceInYears } from \"./differenceInYears.js\";\n\n/**\n * The {@link intervalToDuration} function options.\n */\n\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert an interval object to a duration object.\n *\n * @param interval - The interval to convert to duration\n * @param options - The context options\n *\n * @returns The duration object\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * });\n * //=> { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\nexport function intervalToDuration(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  const duration = {};\n  const years = differenceInYears(end, start);\n  if (years) duration.years = years;\n  const remainingMonths = add(start, {\n    years: duration.years\n  });\n  const months = differenceInMonths(end, remainingMonths);\n  if (months) duration.months = months;\n  const remainingDays = add(remainingMonths, {\n    months: duration.months\n  });\n  const days = differenceInDays(end, remainingDays);\n  if (days) duration.days = days;\n  const remainingHours = add(remainingDays, {\n    days: duration.days\n  });\n  const hours = differenceInHours(end, remainingHours);\n  if (hours) duration.hours = hours;\n  const remainingMinutes = add(remainingHours, {\n    hours: duration.hours\n  });\n  const minutes = differenceInMinutes(end, remainingMinutes);\n  if (minutes) duration.minutes = minutes;\n  const remainingSeconds = add(remainingMinutes, {\n    minutes: duration.minutes\n  });\n  const seconds = differenceInSeconds(end, remainingSeconds);\n  if (seconds) duration.seconds = seconds;\n  return duration;\n}\n\n// Fallback for modularized imports:\nexport default intervalToDuration;", "map": {"version": 3, "names": ["normalizeInterval", "add", "differenceInDays", "differenceInHours", "differenceInMinutes", "differenceInMonths", "differenceInSeconds", "differenceInYears", "intervalToDuration", "interval", "options", "start", "end", "in", "duration", "years", "remainingMonths", "months", "remainingDays", "days", "remainingHours", "hours", "remainingMinutes", "minutes", "remainingSeconds", "seconds"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/intervalToDuration.js"], "sourcesContent": ["import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { add } from \"./add.js\";\nimport { differenceInDays } from \"./differenceInDays.js\";\nimport { differenceInHours } from \"./differenceInHours.js\";\nimport { differenceInMinutes } from \"./differenceInMinutes.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\nimport { differenceInYears } from \"./differenceInYears.js\";\n\n/**\n * The {@link intervalToDuration} function options.\n */\n\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert an interval object to a duration object.\n *\n * @param interval - The interval to convert to duration\n * @param options - The context options\n *\n * @returns The duration object\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * });\n * //=> { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\nexport function intervalToDuration(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n  const duration = {};\n\n  const years = differenceInYears(end, start);\n  if (years) duration.years = years;\n\n  const remainingMonths = add(start, { years: duration.years });\n  const months = differenceInMonths(end, remainingMonths);\n  if (months) duration.months = months;\n\n  const remainingDays = add(remainingMonths, { months: duration.months });\n  const days = differenceInDays(end, remainingDays);\n  if (days) duration.days = days;\n\n  const remainingHours = add(remainingDays, { days: duration.days });\n  const hours = differenceInHours(end, remainingHours);\n  if (hours) duration.hours = hours;\n\n  const remainingMinutes = add(remainingHours, { hours: duration.hours });\n  const minutes = differenceInMinutes(end, remainingMinutes);\n  if (minutes) duration.minutes = minutes;\n\n  const remainingSeconds = add(remainingMinutes, { minutes: duration.minutes });\n  const seconds = differenceInSeconds(end, remainingSeconds);\n  if (seconds) duration.seconds = seconds;\n\n  return duration;\n}\n\n// Fallback for modularized imports:\nexport default intervalToDuration;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,GAAG,QAAQ,UAAU;AAC9B,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,wBAAwB;;AAE1D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACpD,MAAM;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAGZ,iBAAiB,CAACU,OAAO,EAAEG,EAAE,EAAEJ,QAAQ,CAAC;EAC/D,MAAMK,QAAQ,GAAG,CAAC,CAAC;EAEnB,MAAMC,KAAK,GAAGR,iBAAiB,CAACK,GAAG,EAAED,KAAK,CAAC;EAC3C,IAAII,KAAK,EAAED,QAAQ,CAACC,KAAK,GAAGA,KAAK;EAEjC,MAAMC,eAAe,GAAGf,GAAG,CAACU,KAAK,EAAE;IAAEI,KAAK,EAAED,QAAQ,CAACC;EAAM,CAAC,CAAC;EAC7D,MAAME,MAAM,GAAGZ,kBAAkB,CAACO,GAAG,EAAEI,eAAe,CAAC;EACvD,IAAIC,MAAM,EAAEH,QAAQ,CAACG,MAAM,GAAGA,MAAM;EAEpC,MAAMC,aAAa,GAAGjB,GAAG,CAACe,eAAe,EAAE;IAAEC,MAAM,EAAEH,QAAQ,CAACG;EAAO,CAAC,CAAC;EACvE,MAAME,IAAI,GAAGjB,gBAAgB,CAACU,GAAG,EAAEM,aAAa,CAAC;EACjD,IAAIC,IAAI,EAAEL,QAAQ,CAACK,IAAI,GAAGA,IAAI;EAE9B,MAAMC,cAAc,GAAGnB,GAAG,CAACiB,aAAa,EAAE;IAAEC,IAAI,EAAEL,QAAQ,CAACK;EAAK,CAAC,CAAC;EAClE,MAAME,KAAK,GAAGlB,iBAAiB,CAACS,GAAG,EAAEQ,cAAc,CAAC;EACpD,IAAIC,KAAK,EAAEP,QAAQ,CAACO,KAAK,GAAGA,KAAK;EAEjC,MAAMC,gBAAgB,GAAGrB,GAAG,CAACmB,cAAc,EAAE;IAAEC,KAAK,EAAEP,QAAQ,CAACO;EAAM,CAAC,CAAC;EACvE,MAAME,OAAO,GAAGnB,mBAAmB,CAACQ,GAAG,EAAEU,gBAAgB,CAAC;EAC1D,IAAIC,OAAO,EAAET,QAAQ,CAACS,OAAO,GAAGA,OAAO;EAEvC,MAAMC,gBAAgB,GAAGvB,GAAG,CAACqB,gBAAgB,EAAE;IAAEC,OAAO,EAAET,QAAQ,CAACS;EAAQ,CAAC,CAAC;EAC7E,MAAME,OAAO,GAAGnB,mBAAmB,CAACM,GAAG,EAAEY,gBAAgB,CAAC;EAC1D,IAAIC,OAAO,EAAEX,QAAQ,CAACW,OAAO,GAAGA,OAAO;EAEvC,OAAOX,QAAQ;AACjB;;AAEA;AACA,eAAeN,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}