//
// Copyright 2021 Google Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
//

@use '@material/feature-targeting/feature-targeting';
@use '@material/rtl/rtl';
@use '@material/dom/dom';

$ring-radius-default: 8px !default;
$inner-ring-width-default: 2px !default;
$inner-ring-color-default: transparent !default;
$outer-ring-width-default: 2px !default;
$outer-ring-color-default: transparent !default;
$container-outer-padding-default: 2px !default;

/// Styles applied to the component's inner focus ring element.
///
/// @param $ring-radius [$ring-radius-default] - Focus ring radius.
/// @param $inner-ring-width [$inner-ring-width-default] - Inner focus ring width.
/// @param $inner-ring-color [$inner-ring-color-default] - Inner focus ring color.
/// @param $outer-ring-width [$outer-ring-width-default] - Outer focus ring width.
/// @param $outer-ring-color [$outer-ring-color-default] - Outer focus ring color.
/// @param $container-outer-padding [$container-outer-padding-default] - The
///     distance between the focus ring and the container.
@mixin focus-ring(
  $query: feature-targeting.all(),
  $ring-radius: $ring-radius-default,
  $inner-ring-width: $inner-ring-width-default,
  $inner-ring-color: $inner-ring-color-default,
  $outer-ring-width: $outer-ring-width-default,
  $outer-ring-color: $outer-ring-color-default,
  $container-outer-padding-vertical: $container-outer-padding-default,
  $container-outer-padding-horizontal: $container-outer-padding-default
) {
  $feat-structure: feature-targeting.create-target($query, structure);
  $container-size-vertical: 100%;
  @if $container-outer-padding-vertical != 0 {
    $container-size-vertical: calc(
      100% + #{$container-outer-padding-vertical * 2}
    );
  }
  $container-size-horizontal: 100%;
  @if $container-outer-padding-horizontal != 0 {
    $container-size-horizontal: calc(
      100% + #{$container-outer-padding-horizontal * 2}
    );
  }
  $outer-ring-size: 100%;
  @if $outer-ring-width > 0 {
    $outer-ring-size: calc(100% + #{$outer-ring-width * 2});
  }
  @include feature-targeting.targets($feat-structure) {
    pointer-events: none;
    border: $inner-ring-width solid $inner-ring-color;
    border-radius: $ring-radius - $outer-ring-width;
    box-sizing: content-box;
    position: absolute;
    top: 50%;
    @include rtl.ignore-next-line();
    left: 50%;
    @include rtl.ignore-next-line();
    transform: translate(-50%, -50%);
    height: $container-size-vertical;
    width: $container-size-horizontal;

    @include dom.forced-colors-mode($exclude-ie11: true) {
      border-color: CanvasText;
    }

    &::after {
      content: '';
      border: $outer-ring-width solid $outer-ring-color;
      border-radius: $ring-radius;
      display: block;
      position: absolute;
      top: 50%;
      @include rtl.ignore-next-line();
      left: 50%;
      @include rtl.ignore-next-line();
      transform: translate(-50%, -50%);
      height: $outer-ring-size;
      width: $outer-ring-size;

      @include dom.forced-colors-mode($exclude-ie11: true) {
        border-color: CanvasText;
      }
    }
  }
}
/// Customizes the color of the button focus ring.
///
/// @param $inner-ring-color [$inner-ring-color-default] - Inner focus ring color.
/// @param $outer-ring-width [$outer-ring-width-default] - Outer focus ring width.
@mixin focus-ring-color(
  $inner-ring-color: $inner-ring-color-default,
  $outer-ring-color: $outer-ring-color-default
) {
  border-color: $inner-ring-color;

  &::after {
    border-color: $outer-ring-color;
  }
}

/// Customizes the border radius of the button focus ring.
///
/// @param {Number} $ring-radius - The border radius of the focus ring.
/// @param {Number} $outer-ring-width [$outer-ring-width] - Width of the outer
///     ring, required to compute the radius for the inner ring.
@mixin focus-ring-radius(
  $ring-radius,
  $outer-ring-width: $outer-ring-width-default,
  $query: feature-targeting.all()
) {
  $feat-structure: feature-targeting.create-target($query, structure);

  @include feature-targeting.targets($feat-structure) {
    border-radius: $ring-radius - $outer-ring-width;

    &::after {
      border-radius: $ring-radius;
    }
  }
}
