{"ast": null, "code": "import { Slot } from \"optimism\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { canUseWeakMap, canUseWeakSet } from \"../utilities/index.js\";\nexport var MapImpl = canUseWeakMap ? WeakMap : Map;\nexport var SetImpl = canUseWeakSet ? WeakSet : Set;\n// Contextual slot that allows us to disable accessor warnings on fields when in\n// migrate mode.\n/** @internal */\nexport var disableWarningsSlot = new Slot();\nvar issuedWarning = false;\nexport function warnOnImproperCacheImplementation() {\n  if (!issuedWarning) {\n    issuedWarning = true;\n    globalThis.__DEV__ !== false && invariant.warn(52);\n  }\n}", "map": {"version": 3, "names": ["Slot", "invariant", "canUseWeakMap", "canUseWeakSet", "MapImpl", "WeakMap", "Map", "SetImpl", "WeakSet", "Set", "disableWarningsSlot", "issuedWarning", "warnOnImproperCacheImplementation", "globalThis", "__DEV__", "warn"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/masking/utils.js"], "sourcesContent": ["import { Slot } from \"optimism\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { canUseWeakMap, canUseWeakSet } from \"../utilities/index.js\";\nexport var MapImpl = canUseWeakMap ? WeakMap : Map;\nexport var SetImpl = canUseWeakSet ? WeakSet : Set;\n// Contextual slot that allows us to disable accessor warnings on fields when in\n// migrate mode.\n/** @internal */\nexport var disableWarningsSlot = new Slot();\nvar issuedWarning = false;\nexport function warnOnImproperCacheImplementation() {\n    if (!issuedWarning) {\n        issuedWarning = true;\n        globalThis.__DEV__ !== false && invariant.warn(52);\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,UAAU;AAC/B,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,EAAEC,aAAa,QAAQ,uBAAuB;AACpE,OAAO,IAAIC,OAAO,GAAGF,aAAa,GAAGG,OAAO,GAAGC,GAAG;AAClD,OAAO,IAAIC,OAAO,GAAGJ,aAAa,GAAGK,OAAO,GAAGC,GAAG;AAClD;AACA;AACA;AACA,OAAO,IAAIC,mBAAmB,GAAG,IAAIV,IAAI,CAAC,CAAC;AAC3C,IAAIW,aAAa,GAAG,KAAK;AACzB,OAAO,SAASC,iCAAiCA,CAAA,EAAG;EAChD,IAAI,CAACD,aAAa,EAAE;IAChBA,aAAa,GAAG,IAAI;IACpBE,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIb,SAAS,CAACc,IAAI,CAAC,EAAE,CAAC;EACtD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}