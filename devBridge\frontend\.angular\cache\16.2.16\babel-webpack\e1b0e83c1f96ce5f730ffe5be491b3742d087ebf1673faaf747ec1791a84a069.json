{"ast": null, "code": "const MAX_SUGGESTIONS = 5;\n/**\n * Given [ A, B, C ] return ' Did you mean A, B, or C?'.\n */\n\nexport function didYouMean(firstArg, secondArg) {\n  const [subMessage, suggestionsArg] = secondArg ? [firstArg, secondArg] : [undefined, firstArg];\n  let message = ' Did you mean ';\n  if (subMessage) {\n    message += subMessage + ' ';\n  }\n  const suggestions = suggestionsArg.map(x => `\"${x}\"`);\n  switch (suggestions.length) {\n    case 0:\n      return '';\n    case 1:\n      return message + suggestions[0] + '?';\n    case 2:\n      return message + suggestions[0] + ' or ' + suggestions[1] + '?';\n  }\n  const selected = suggestions.slice(0, MAX_SUGGESTIONS);\n  const lastItem = selected.pop();\n  return message + selected.join(', ') + ', or ' + lastItem + '?';\n}", "map": {"version": 3, "names": ["MAX_SUGGESTIONS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstArg", "secondArg", "subMessage", "suggestionsArg", "undefined", "message", "suggestions", "map", "x", "length", "selected", "slice", "lastItem", "pop", "join"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/jsutils/didYouMean.mjs"], "sourcesContent": ["const MAX_SUGGESTIONS = 5;\n/**\n * Given [ A, B, C ] return ' Did you mean A, B, or C?'.\n */\n\nexport function didYouMean(firstArg, secondArg) {\n  const [subMessage, suggestionsArg] = secondArg\n    ? [firstArg, secondArg]\n    : [undefined, firstArg];\n  let message = ' Did you mean ';\n\n  if (subMessage) {\n    message += subMessage + ' ';\n  }\n\n  const suggestions = suggestionsArg.map((x) => `\"${x}\"`);\n\n  switch (suggestions.length) {\n    case 0:\n      return '';\n\n    case 1:\n      return message + suggestions[0] + '?';\n\n    case 2:\n      return message + suggestions[0] + ' or ' + suggestions[1] + '?';\n  }\n\n  const selected = suggestions.slice(0, MAX_SUGGESTIONS);\n  const lastItem = selected.pop();\n  return message + selected.join(', ') + ', or ' + lastItem + '?';\n}\n"], "mappings": "AAAA,MAAMA,eAAe,GAAG,CAAC;AACzB;AACA;AACA;;AAEA,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC9C,MAAM,CAACC,UAAU,EAAEC,cAAc,CAAC,GAAGF,SAAS,GAC1C,CAACD,QAAQ,EAAEC,SAAS,CAAC,GACrB,CAACG,SAAS,EAAEJ,QAAQ,CAAC;EACzB,IAAIK,OAAO,GAAG,gBAAgB;EAE9B,IAAIH,UAAU,EAAE;IACdG,OAAO,IAAIH,UAAU,GAAG,GAAG;EAC7B;EAEA,MAAMI,WAAW,GAAGH,cAAc,CAACI,GAAG,CAAEC,CAAC,IAAM,IAAGA,CAAE,GAAE,CAAC;EAEvD,QAAQF,WAAW,CAACG,MAAM;IACxB,KAAK,CAAC;MACJ,OAAO,EAAE;IAEX,KAAK,CAAC;MACJ,OAAOJ,OAAO,GAAGC,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG;IAEvC,KAAK,CAAC;MACJ,OAAOD,OAAO,GAAGC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG;EACnE;EAEA,MAAMI,QAAQ,GAAGJ,WAAW,CAACK,KAAK,CAAC,CAAC,EAAEb,eAAe,CAAC;EACtD,MAAMc,QAAQ,GAAGF,QAAQ,CAACG,GAAG,CAAC,CAAC;EAC/B,OAAOR,OAAO,GAAGK,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,GAAGF,QAAQ,GAAG,GAAG;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}