{"ast": null, "code": "import { toError } from '../jsutils/toError.mjs';\nimport { GraphQLError } from './GraphQLError.mjs';\n/**\n * Given an arbitrary value, presumably thrown while attempting to execute a\n * GraphQL operation, produce a new GraphQLError aware of the location in the\n * document responsible for the original Error.\n */\n\nexport function locatedError(rawOriginalError, nodes, path) {\n  var _nodes;\n  const originalError = toError(rawOriginalError); // Note: this uses a brand-check to support GraphQL errors originating from other contexts.\n\n  if (isLocatedGraphQLError(originalError)) {\n    return originalError;\n  }\n  return new GraphQLError(originalError.message, {\n    nodes: (_nodes = originalError.nodes) !== null && _nodes !== void 0 ? _nodes : nodes,\n    source: originalError.source,\n    positions: originalError.positions,\n    path,\n    originalError\n  });\n}\nfunction isLocatedGraphQLError(error) {\n  return Array.isArray(error.path);\n}", "map": {"version": 3, "names": ["to<PERSON><PERSON><PERSON>", "GraphQLError", "locatedError", "rawOriginalError", "nodes", "path", "_nodes", "originalError", "isLocatedGraphQLError", "message", "source", "positions", "error", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/error/locatedError.mjs"], "sourcesContent": ["import { toError } from '../jsutils/toError.mjs';\nimport { GraphQLError } from './GraphQLError.mjs';\n/**\n * Given an arbitrary value, presumably thrown while attempting to execute a\n * GraphQL operation, produce a new GraphQLError aware of the location in the\n * document responsible for the original Error.\n */\n\nexport function locatedError(rawOriginalError, nodes, path) {\n  var _nodes;\n\n  const originalError = toError(rawOriginalError); // Note: this uses a brand-check to support GraphQL errors originating from other contexts.\n\n  if (isLocatedGraphQLError(originalError)) {\n    return originalError;\n  }\n\n  return new GraphQLError(originalError.message, {\n    nodes:\n      (_nodes = originalError.nodes) !== null && _nodes !== void 0\n        ? _nodes\n        : nodes,\n    source: originalError.source,\n    positions: originalError.positions,\n    path,\n    originalError,\n  });\n}\n\nfunction isLocatedGraphQLError(error) {\n  return Array.isArray(error.path);\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,oBAAoB;AACjD;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,gBAAgB,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC1D,IAAIC,MAAM;EAEV,MAAMC,aAAa,GAAGP,OAAO,CAACG,gBAAgB,CAAC,CAAC,CAAC;;EAEjD,IAAIK,qBAAqB,CAACD,aAAa,CAAC,EAAE;IACxC,OAAOA,aAAa;EACtB;EAEA,OAAO,IAAIN,YAAY,CAACM,aAAa,CAACE,OAAO,EAAE;IAC7CL,KAAK,EACH,CAACE,MAAM,GAAGC,aAAa,CAACH,KAAK,MAAM,IAAI,IAAIE,MAAM,KAAK,KAAK,CAAC,GACxDA,MAAM,GACNF,KAAK;IACXM,MAAM,EAAEH,aAAa,CAACG,MAAM;IAC5BC,SAAS,EAAEJ,aAAa,CAACI,SAAS;IAClCN,IAAI;IACJE;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,qBAAqBA,CAACI,KAAK,EAAE;EACpC,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAACP,IAAI,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}