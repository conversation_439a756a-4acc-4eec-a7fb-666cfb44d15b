{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isRequiredArgument, isType } from '../../type/definition.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Provided required arguments\n *\n * A field or directive is only valid if all required (non-null without a\n * default value) field arguments have been provided.\n */\nexport function ProvidedRequiredArgumentsRule(context) {\n  return {\n    // eslint-disable-next-line new-cap\n    ...ProvidedRequiredArgumentsOnDirectivesRule(context),\n    Field: {\n      // Validate on leave to allow for deeper errors to appear first.\n      leave(fieldNode) {\n        var _fieldNode$arguments;\n        const fieldDef = context.getFieldDef();\n        if (!fieldDef) {\n          return false;\n        }\n        const providedArgs = new Set(\n        // FIXME: https://github.com/graphql/graphql-js/issues/2203\n        /* c8 ignore next */\n        (_fieldNode$arguments = fieldNode.arguments) === null || _fieldNode$arguments === void 0 ? void 0 : _fieldNode$arguments.map(arg => arg.name.value));\n        for (const argDef of fieldDef.args) {\n          if (!providedArgs.has(argDef.name) && isRequiredArgument(argDef)) {\n            const argTypeStr = inspect(argDef.type);\n            context.reportError(new GraphQLError(`Field \"${fieldDef.name}\" argument \"${argDef.name}\" of type \"${argTypeStr}\" is required, but it was not provided.`, {\n              nodes: fieldNode\n            }));\n          }\n        }\n      }\n    }\n  };\n}\n/**\n * @internal\n */\n\nexport function ProvidedRequiredArgumentsOnDirectivesRule(context) {\n  var _schema$getDirectives;\n  const requiredArgsMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = (_schema$getDirectives = schema === null || schema === void 0 ? void 0 : schema.getDirectives()) !== null && _schema$getDirectives !== void 0 ? _schema$getDirectives : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    requiredArgsMap[directive.name] = keyMap(directive.args.filter(isRequiredArgument), arg => arg.name);\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      var _def$arguments;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argNodes = (_def$arguments = def.arguments) !== null && _def$arguments !== void 0 ? _def$arguments : [];\n      requiredArgsMap[def.name.value] = keyMap(argNodes.filter(isRequiredArgumentNode), arg => arg.name.value);\n    }\n  }\n  return {\n    Directive: {\n      // Validate on leave to allow for deeper errors to appear first.\n      leave(directiveNode) {\n        const directiveName = directiveNode.name.value;\n        const requiredArgs = requiredArgsMap[directiveName];\n        if (requiredArgs) {\n          var _directiveNode$argume;\n\n          // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n          /* c8 ignore next */\n          const argNodes = (_directiveNode$argume = directiveNode.arguments) !== null && _directiveNode$argume !== void 0 ? _directiveNode$argume : [];\n          const argNodeMap = new Set(argNodes.map(arg => arg.name.value));\n          for (const [argName, argDef] of Object.entries(requiredArgs)) {\n            if (!argNodeMap.has(argName)) {\n              const argType = isType(argDef.type) ? inspect(argDef.type) : print(argDef.type);\n              context.reportError(new GraphQLError(`Directive \"@${directiveName}\" argument \"${argName}\" of type \"${argType}\" is required, but it was not provided.`, {\n                nodes: directiveNode\n              }));\n            }\n          }\n        }\n      }\n    }\n  };\n}\nfunction isRequiredArgumentNode(arg) {\n  return arg.type.kind === Kind.NON_NULL_TYPE && arg.defaultValue == null;\n}", "map": {"version": 3, "names": ["inspect", "keyMap", "GraphQLError", "Kind", "print", "isRequiredArgument", "isType", "specifiedDirectives", "ProvidedRequiredArgumentsRule", "context", "ProvidedRequiredArgumentsOnDirectivesRule", "Field", "leave", "fieldNode", "_fieldNode$arguments", "fieldDef", "getFieldDef", "provided<PERSON><PERSON><PERSON>", "Set", "arguments", "map", "arg", "name", "value", "argDef", "args", "has", "argTypeStr", "type", "reportError", "nodes", "_schema$getDirectives", "requiredArgsMap", "Object", "create", "schema", "getSchema", "definedDirectives", "getDirectives", "directive", "filter", "astDefinitions", "getDocument", "definitions", "def", "kind", "DIRECTIVE_DEFINITION", "_def$arguments", "argNodes", "isRequiredArgumentNode", "Directive", "directiveNode", "directiveName", "requiredArgs", "_directiveNode$argume", "argNodeMap", "argName", "entries", "argType", "NON_NULL_TYPE", "defaultValue"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/ProvidedRequiredArgumentsRule.mjs"], "sourcesContent": ["import { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isRequiredArgument, isType } from '../../type/definition.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Provided required arguments\n *\n * A field or directive is only valid if all required (non-null without a\n * default value) field arguments have been provided.\n */\nexport function ProvidedRequiredArgumentsRule(context) {\n  return {\n    // eslint-disable-next-line new-cap\n    ...ProvidedRequiredArgumentsOnDirectivesRule(context),\n    Field: {\n      // Validate on leave to allow for deeper errors to appear first.\n      leave(fieldNode) {\n        var _fieldNode$arguments;\n\n        const fieldDef = context.getFieldDef();\n\n        if (!fieldDef) {\n          return false;\n        }\n\n        const providedArgs = new Set( // FIXME: https://github.com/graphql/graphql-js/issues/2203\n          /* c8 ignore next */\n          (_fieldNode$arguments = fieldNode.arguments) === null ||\n          _fieldNode$arguments === void 0\n            ? void 0\n            : _fieldNode$arguments.map((arg) => arg.name.value),\n        );\n\n        for (const argDef of fieldDef.args) {\n          if (!providedArgs.has(argDef.name) && isRequiredArgument(argDef)) {\n            const argTypeStr = inspect(argDef.type);\n            context.reportError(\n              new GraphQLError(\n                `Field \"${fieldDef.name}\" argument \"${argDef.name}\" of type \"${argTypeStr}\" is required, but it was not provided.`,\n                {\n                  nodes: fieldNode,\n                },\n              ),\n            );\n          }\n        }\n      },\n    },\n  };\n}\n/**\n * @internal\n */\n\nexport function ProvidedRequiredArgumentsOnDirectivesRule(context) {\n  var _schema$getDirectives;\n\n  const requiredArgsMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives =\n    (_schema$getDirectives =\n      schema === null || schema === void 0\n        ? void 0\n        : schema.getDirectives()) !== null && _schema$getDirectives !== void 0\n      ? _schema$getDirectives\n      : specifiedDirectives;\n\n  for (const directive of definedDirectives) {\n    requiredArgsMap[directive.name] = keyMap(\n      directive.args.filter(isRequiredArgument),\n      (arg) => arg.name,\n    );\n  }\n\n  const astDefinitions = context.getDocument().definitions;\n\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      var _def$arguments;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argNodes =\n        (_def$arguments = def.arguments) !== null && _def$arguments !== void 0\n          ? _def$arguments\n          : [];\n      requiredArgsMap[def.name.value] = keyMap(\n        argNodes.filter(isRequiredArgumentNode),\n        (arg) => arg.name.value,\n      );\n    }\n  }\n\n  return {\n    Directive: {\n      // Validate on leave to allow for deeper errors to appear first.\n      leave(directiveNode) {\n        const directiveName = directiveNode.name.value;\n        const requiredArgs = requiredArgsMap[directiveName];\n\n        if (requiredArgs) {\n          var _directiveNode$argume;\n\n          // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n          /* c8 ignore next */\n          const argNodes =\n            (_directiveNode$argume = directiveNode.arguments) !== null &&\n            _directiveNode$argume !== void 0\n              ? _directiveNode$argume\n              : [];\n          const argNodeMap = new Set(argNodes.map((arg) => arg.name.value));\n\n          for (const [argName, argDef] of Object.entries(requiredArgs)) {\n            if (!argNodeMap.has(argName)) {\n              const argType = isType(argDef.type)\n                ? inspect(argDef.type)\n                : print(argDef.type);\n              context.reportError(\n                new GraphQLError(\n                  `Directive \"@${directiveName}\" argument \"${argName}\" of type \"${argType}\" is required, but it was not provided.`,\n                  {\n                    nodes: directiveNode,\n                  },\n                ),\n              );\n            }\n          }\n        }\n      },\n    },\n  };\n}\n\nfunction isRequiredArgumentNode(arg) {\n  return arg.type.kind === Kind.NON_NULL_TYPE && arg.defaultValue == null;\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SAASC,kBAAkB,EAAEC,MAAM,QAAQ,2BAA2B;AACtE,SAASC,mBAAmB,QAAQ,2BAA2B;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,6BAA6BA,CAACC,OAAO,EAAE;EACrD,OAAO;IACL;IACA,GAAGC,yCAAyC,CAACD,OAAO,CAAC;IACrDE,KAAK,EAAE;MACL;MACAC,KAAKA,CAACC,SAAS,EAAE;QACf,IAAIC,oBAAoB;QAExB,MAAMC,QAAQ,GAAGN,OAAO,CAACO,WAAW,CAAC,CAAC;QAEtC,IAAI,CAACD,QAAQ,EAAE;UACb,OAAO,KAAK;QACd;QAEA,MAAME,YAAY,GAAG,IAAIC,GAAG;QAAE;QAC5B;QACA,CAACJ,oBAAoB,GAAGD,SAAS,CAACM,SAAS,MAAM,IAAI,IACrDL,oBAAoB,KAAK,KAAK,CAAC,GAC3B,KAAK,CAAC,GACNA,oBAAoB,CAACM,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAACC,KAAK,CACtD,CAAC;QAED,KAAK,MAAMC,MAAM,IAAIT,QAAQ,CAACU,IAAI,EAAE;UAClC,IAAI,CAACR,YAAY,CAACS,GAAG,CAACF,MAAM,CAACF,IAAI,CAAC,IAAIjB,kBAAkB,CAACmB,MAAM,CAAC,EAAE;YAChE,MAAMG,UAAU,GAAG3B,OAAO,CAACwB,MAAM,CAACI,IAAI,CAAC;YACvCnB,OAAO,CAACoB,WAAW,CACjB,IAAI3B,YAAY,CACb,UAASa,QAAQ,CAACO,IAAK,eAAcE,MAAM,CAACF,IAAK,cAAaK,UAAW,yCAAwC,EAClH;cACEG,KAAK,EAAEjB;YACT,CACF,CACF,CAAC;UACH;QACF;MACF;IACF;EACF,CAAC;AACH;AACA;AACA;AACA;;AAEA,OAAO,SAASH,yCAAyCA,CAACD,OAAO,EAAE;EACjE,IAAIsB,qBAAqB;EAEzB,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMC,MAAM,GAAG1B,OAAO,CAAC2B,SAAS,CAAC,CAAC;EAClC,MAAMC,iBAAiB,GACrB,CAACN,qBAAqB,GACpBI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAChC,KAAK,CAAC,GACNA,MAAM,CAACG,aAAa,CAAC,CAAC,MAAM,IAAI,IAAIP,qBAAqB,KAAK,KAAK,CAAC,GACtEA,qBAAqB,GACrBxB,mBAAmB;EAEzB,KAAK,MAAMgC,SAAS,IAAIF,iBAAiB,EAAE;IACzCL,eAAe,CAACO,SAAS,CAACjB,IAAI,CAAC,GAAGrB,MAAM,CACtCsC,SAAS,CAACd,IAAI,CAACe,MAAM,CAACnC,kBAAkB,CAAC,EACxCgB,GAAG,IAAKA,GAAG,CAACC,IACf,CAAC;EACH;EAEA,MAAMmB,cAAc,GAAGhC,OAAO,CAACiC,WAAW,CAAC,CAAC,CAACC,WAAW;EAExD,KAAK,MAAMC,GAAG,IAAIH,cAAc,EAAE;IAChC,IAAIG,GAAG,CAACC,IAAI,KAAK1C,IAAI,CAAC2C,oBAAoB,EAAE;MAC1C,IAAIC,cAAc;;MAElB;;MAEA;MACA,MAAMC,QAAQ,GACZ,CAACD,cAAc,GAAGH,GAAG,CAACzB,SAAS,MAAM,IAAI,IAAI4B,cAAc,KAAK,KAAK,CAAC,GAClEA,cAAc,GACd,EAAE;MACRf,eAAe,CAACY,GAAG,CAACtB,IAAI,CAACC,KAAK,CAAC,GAAGtB,MAAM,CACtC+C,QAAQ,CAACR,MAAM,CAACS,sBAAsB,CAAC,EACtC5B,GAAG,IAAKA,GAAG,CAACC,IAAI,CAACC,KACpB,CAAC;IACH;EACF;EAEA,OAAO;IACL2B,SAAS,EAAE;MACT;MACAtC,KAAKA,CAACuC,aAAa,EAAE;QACnB,MAAMC,aAAa,GAAGD,aAAa,CAAC7B,IAAI,CAACC,KAAK;QAC9C,MAAM8B,YAAY,GAAGrB,eAAe,CAACoB,aAAa,CAAC;QAEnD,IAAIC,YAAY,EAAE;UAChB,IAAIC,qBAAqB;;UAEzB;;UAEA;UACA,MAAMN,QAAQ,GACZ,CAACM,qBAAqB,GAAGH,aAAa,CAAChC,SAAS,MAAM,IAAI,IAC1DmC,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;UACR,MAAMC,UAAU,GAAG,IAAIrC,GAAG,CAAC8B,QAAQ,CAAC5B,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;UAEjE,KAAK,MAAM,CAACiC,OAAO,EAAEhC,MAAM,CAAC,IAAIS,MAAM,CAACwB,OAAO,CAACJ,YAAY,CAAC,EAAE;YAC5D,IAAI,CAACE,UAAU,CAAC7B,GAAG,CAAC8B,OAAO,CAAC,EAAE;cAC5B,MAAME,OAAO,GAAGpD,MAAM,CAACkB,MAAM,CAACI,IAAI,CAAC,GAC/B5B,OAAO,CAACwB,MAAM,CAACI,IAAI,CAAC,GACpBxB,KAAK,CAACoB,MAAM,CAACI,IAAI,CAAC;cACtBnB,OAAO,CAACoB,WAAW,CACjB,IAAI3B,YAAY,CACb,eAAckD,aAAc,eAAcI,OAAQ,cAAaE,OAAQ,yCAAwC,EAChH;gBACE5B,KAAK,EAAEqB;cACT,CACF,CACF,CAAC;YACH;UACF;QACF;MACF;IACF;EACF,CAAC;AACH;AAEA,SAASF,sBAAsBA,CAAC5B,GAAG,EAAE;EACnC,OAAOA,GAAG,CAACO,IAAI,CAACiB,IAAI,KAAK1C,IAAI,CAACwD,aAAa,IAAItC,GAAG,CAACuC,YAAY,IAAI,IAAI;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}