{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { isPromise } from '../jsutils/isPromise.mjs';\nimport { memoize3 } from '../jsutils/memoize3.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { promiseForObject } from '../jsutils/promiseForObject.mjs';\nimport { promiseReduce } from '../jsutils/promiseReduce.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isAbstractType, isLeafType, isListType, isNonNullType, isObjectType } from '../type/definition.mjs';\nimport { SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef } from '../type/introspection.mjs';\nimport { assertValidSchema } from '../type/validate.mjs';\nimport { collectFields, collectSubfields as _collectSubfields } from './collectFields.mjs';\nimport { getArgumentValues, getVariableValues } from './values.mjs';\n/**\n * A memoized collection of relevant subfields with regard to the return\n * type. Memoizing ensures the subfields are not repeatedly calculated, which\n * saves overhead when resolving lists of values.\n */\n\nconst collectSubfields = memoize3((exeContext, returnType, fieldNodes) => _collectSubfields(exeContext.schema, exeContext.fragments, exeContext.variableValues, returnType, fieldNodes));\n/**\n * Terminology\n *\n * \"Definitions\" are the generic name for top-level statements in the document.\n * Examples of this include:\n * 1) Operations (such as a query)\n * 2) Fragments\n *\n * \"Operations\" are a generic name for requests in the document.\n * Examples of this include:\n * 1) query,\n * 2) mutation\n *\n * \"Selections\" are the definitions that can appear legally and at\n * single level of the query. These include:\n * 1) field references e.g `a`\n * 2) fragment \"spreads\" e.g. `...c`\n * 3) inline fragment \"spreads\" e.g. `...on Type { a }`\n */\n\n/**\n * Data that must be available at all points during query execution.\n *\n * Namely, schema of the type system that is currently executing,\n * and the fragments defined in the query document\n */\n\n/**\n * Implements the \"Executing requests\" section of the GraphQL specification.\n *\n * Returns either a synchronous ExecutionResult (if all encountered resolvers\n * are synchronous), or a Promise of an ExecutionResult that will eventually be\n * resolved and never rejected.\n *\n * If the arguments to this function do not result in a legal execution context,\n * a GraphQLError will be thrown immediately explaining the invalid input.\n */\nexport function execute(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 || devAssert(false, 'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.');\n  const {\n    schema,\n    document,\n    variableValues,\n    rootValue\n  } = args; // If arguments are missing or incorrect, throw an error.\n\n  assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n  // a \"Response\" with only errors is returned.\n\n  const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n  if (!('schema' in exeContext)) {\n    return {\n      errors: exeContext\n    };\n  } // Return a Promise that will eventually resolve to the data described by\n  // The \"Response\" section of the GraphQL specification.\n  //\n  // If errors are encountered while executing a GraphQL field, only that\n  // field and its descendants will be omitted, and sibling fields will still\n  // be executed. An execution which encounters errors will still result in a\n  // resolved Promise.\n  //\n  // Errors from sub-fields of a NonNull type may propagate to the top level,\n  // at which point we still log the error and null the parent field, which\n  // in this case is the entire response.\n\n  try {\n    const {\n      operation\n    } = exeContext;\n    const result = executeOperation(exeContext, operation, rootValue);\n    if (isPromise(result)) {\n      return result.then(data => buildResponse(data, exeContext.errors), error => {\n        exeContext.errors.push(error);\n        return buildResponse(null, exeContext.errors);\n      });\n    }\n    return buildResponse(result, exeContext.errors);\n  } catch (error) {\n    exeContext.errors.push(error);\n    return buildResponse(null, exeContext.errors);\n  }\n}\n/**\n * Also implements the \"Executing requests\" section of the GraphQL specification.\n * However, it guarantees to complete synchronously (or throw an error) assuming\n * that all field resolvers are also synchronous.\n */\n\nexport function executeSync(args) {\n  const result = execute(args); // Assert that the execution was synchronous.\n\n  if (isPromise(result)) {\n    throw new Error('GraphQL execution failed to complete synchronously.');\n  }\n  return result;\n}\n/**\n * Given a completed execution context and data, build the `{ errors, data }`\n * response defined by the \"Response\" section of the GraphQL specification.\n */\n\nfunction buildResponse(data, errors) {\n  return errors.length === 0 ? {\n    data\n  } : {\n    errors,\n    data\n  };\n}\n/**\n * Essential assertions before executing to provide developer feedback for\n * improper use of the GraphQL library.\n *\n * @internal\n */\n\nexport function assertValidExecutionArguments(schema, document, rawVariableValues) {\n  document || devAssert(false, 'Must provide document.'); // If the schema used for execution is invalid, throw an error.\n\n  assertValidSchema(schema); // Variables, if provided, must be an object.\n\n  rawVariableValues == null || isObjectLike(rawVariableValues) || devAssert(false, 'Variables must be provided as an Object where each property is a variable value. Perhaps look to see if an unparsed JSON string was provided.');\n}\n/**\n * Constructs a ExecutionContext object from the arguments passed to\n * execute, which we will pass throughout the other execution methods.\n *\n * Throws a GraphQLError if a valid execution context cannot be created.\n *\n * @internal\n */\n\nexport function buildExecutionContext(args) {\n  var _definition$name, _operation$variableDe;\n  const {\n    schema,\n    document,\n    rootValue,\n    contextValue,\n    variableValues: rawVariableValues,\n    operationName,\n    fieldResolver,\n    typeResolver,\n    subscribeFieldResolver\n  } = args;\n  let operation;\n  const fragments = Object.create(null);\n  for (const definition of document.definitions) {\n    switch (definition.kind) {\n      case Kind.OPERATION_DEFINITION:\n        if (operationName == null) {\n          if (operation !== undefined) {\n            return [new GraphQLError('Must provide operation name if query contains multiple operations.')];\n          }\n          operation = definition;\n        } else if (((_definition$name = definition.name) === null || _definition$name === void 0 ? void 0 : _definition$name.value) === operationName) {\n          operation = definition;\n        }\n        break;\n      case Kind.FRAGMENT_DEFINITION:\n        fragments[definition.name.value] = definition;\n        break;\n      default: // ignore non-executable definitions\n    }\n  }\n\n  if (!operation) {\n    if (operationName != null) {\n      return [new GraphQLError(`Unknown operation named \"${operationName}\".`)];\n    }\n    return [new GraphQLError('Must provide an operation.')];\n  } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n\n  const variableDefinitions = (_operation$variableDe = operation.variableDefinitions) !== null && _operation$variableDe !== void 0 ? _operation$variableDe : [];\n  const coercedVariableValues = getVariableValues(schema, variableDefinitions, rawVariableValues !== null && rawVariableValues !== void 0 ? rawVariableValues : {}, {\n    maxErrors: 50\n  });\n  if (coercedVariableValues.errors) {\n    return coercedVariableValues.errors;\n  }\n  return {\n    schema,\n    fragments,\n    rootValue,\n    contextValue,\n    operation,\n    variableValues: coercedVariableValues.coerced,\n    fieldResolver: fieldResolver !== null && fieldResolver !== void 0 ? fieldResolver : defaultFieldResolver,\n    typeResolver: typeResolver !== null && typeResolver !== void 0 ? typeResolver : defaultTypeResolver,\n    subscribeFieldResolver: subscribeFieldResolver !== null && subscribeFieldResolver !== void 0 ? subscribeFieldResolver : defaultFieldResolver,\n    errors: []\n  };\n}\n/**\n * Implements the \"Executing operations\" section of the spec.\n */\n\nfunction executeOperation(exeContext, operation, rootValue) {\n  const rootType = exeContext.schema.getRootType(operation.operation);\n  if (rootType == null) {\n    throw new GraphQLError(`Schema is not configured to execute ${operation.operation} operation.`, {\n      nodes: operation\n    });\n  }\n  const rootFields = collectFields(exeContext.schema, exeContext.fragments, exeContext.variableValues, rootType, operation.selectionSet);\n  const path = undefined;\n  switch (operation.operation) {\n    case OperationTypeNode.QUERY:\n      return executeFields(exeContext, rootType, rootValue, path, rootFields);\n    case OperationTypeNode.MUTATION:\n      return executeFieldsSerially(exeContext, rootType, rootValue, path, rootFields);\n    case OperationTypeNode.SUBSCRIPTION:\n      // TODO: deprecate `subscribe` and move all logic here\n      // Temporary solution until we finish merging execute and subscribe together\n      return executeFields(exeContext, rootType, rootValue, path, rootFields);\n  }\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that must be executed serially.\n */\n\nfunction executeFieldsSerially(exeContext, parentType, sourceValue, path, fields) {\n  return promiseReduce(fields.entries(), (results, [responseName, fieldNodes]) => {\n    const fieldPath = addPath(path, responseName, parentType.name);\n    const result = executeField(exeContext, parentType, sourceValue, fieldNodes, fieldPath);\n    if (result === undefined) {\n      return results;\n    }\n    if (isPromise(result)) {\n      return result.then(resolvedResult => {\n        results[responseName] = resolvedResult;\n        return results;\n      });\n    }\n    results[responseName] = result;\n    return results;\n  }, Object.create(null));\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that may be executed in parallel.\n */\n\nfunction executeFields(exeContext, parentType, sourceValue, path, fields) {\n  const results = Object.create(null);\n  let containsPromise = false;\n  try {\n    for (const [responseName, fieldNodes] of fields.entries()) {\n      const fieldPath = addPath(path, responseName, parentType.name);\n      const result = executeField(exeContext, parentType, sourceValue, fieldNodes, fieldPath);\n      if (result !== undefined) {\n        results[responseName] = result;\n        if (isPromise(result)) {\n          containsPromise = true;\n        }\n      }\n    }\n  } catch (error) {\n    if (containsPromise) {\n      // Ensure that any promises returned by other fields are handled, as they may also reject.\n      return promiseForObject(results).finally(() => {\n        throw error;\n      });\n    }\n    throw error;\n  } // If there are no promises, we can just return the object\n\n  if (!containsPromise) {\n    return results;\n  } // Otherwise, results is a map from field name to the result of resolving that\n  // field, which is possibly a promise. Return a promise that will return this\n  // same map, but with any promises replaced with the values they resolved to.\n\n  return promiseForObject(results);\n}\n/**\n * Implements the \"Executing fields\" section of the spec\n * In particular, this function figures out the value that the field returns by\n * calling its resolve function, then calls completeValue to complete promises,\n * serialize scalars, or execute the sub-selection-set for objects.\n */\n\nfunction executeField(exeContext, parentType, source, fieldNodes, path) {\n  var _fieldDef$resolve;\n  const fieldDef = getFieldDef(exeContext.schema, parentType, fieldNodes[0]);\n  if (!fieldDef) {\n    return;\n  }\n  const returnType = fieldDef.type;\n  const resolveFn = (_fieldDef$resolve = fieldDef.resolve) !== null && _fieldDef$resolve !== void 0 ? _fieldDef$resolve : exeContext.fieldResolver;\n  const info = buildResolveInfo(exeContext, fieldDef, fieldNodes, parentType, path); // Get the resolve function, regardless of if its result is normal or abrupt (error).\n\n  try {\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    // TODO: find a way to memoize, in case this field is within a List type.\n    const args = getArgumentValues(fieldDef, fieldNodes[0], exeContext.variableValues); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    const contextValue = exeContext.contextValue;\n    const result = resolveFn(source, args, contextValue, info);\n    let completed;\n    if (isPromise(result)) {\n      completed = result.then(resolved => completeValue(exeContext, returnType, fieldNodes, info, path, resolved));\n    } else {\n      completed = completeValue(exeContext, returnType, fieldNodes, info, path, result);\n    }\n    if (isPromise(completed)) {\n      // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n      // to take a second callback for the error case.\n      return completed.then(undefined, rawError => {\n        const error = locatedError(rawError, fieldNodes, pathToArray(path));\n        return handleFieldError(error, returnType, exeContext);\n      });\n    }\n    return completed;\n  } catch (rawError) {\n    const error = locatedError(rawError, fieldNodes, pathToArray(path));\n    return handleFieldError(error, returnType, exeContext);\n  }\n}\n/**\n * @internal\n */\n\nexport function buildResolveInfo(exeContext, fieldDef, fieldNodes, parentType, path) {\n  // The resolve function's optional fourth argument is a collection of\n  // information about the current execution state.\n  return {\n    fieldName: fieldDef.name,\n    fieldNodes,\n    returnType: fieldDef.type,\n    parentType,\n    path,\n    schema: exeContext.schema,\n    fragments: exeContext.fragments,\n    rootValue: exeContext.rootValue,\n    operation: exeContext.operation,\n    variableValues: exeContext.variableValues\n  };\n}\nfunction handleFieldError(error, returnType, exeContext) {\n  // If the field type is non-nullable, then it is resolved without any\n  // protection from errors, however it still properly locates the error.\n  if (isNonNullType(returnType)) {\n    throw error;\n  } // Otherwise, error protection is applied, logging the error and resolving\n  // a null value for this field if one is encountered.\n\n  exeContext.errors.push(error);\n  return null;\n}\n/**\n * Implements the instructions for completeValue as defined in the\n * \"Value Completion\" section of the spec.\n *\n * If the field type is Non-Null, then this recursively completes the value\n * for the inner type. It throws a field error if that completion returns null,\n * as per the \"Nullability\" section of the spec.\n *\n * If the field type is a List, then this recursively completes the value\n * for the inner type on each item in the list.\n *\n * If the field type is a Scalar or Enum, ensures the completed value is a legal\n * value of the type by calling the `serialize` method of GraphQL type\n * definition.\n *\n * If the field is an abstract type, determine the runtime type of the value\n * and then complete based on that type\n *\n * Otherwise, the field type expects a sub-selection set, and will complete the\n * value by executing all sub-selections.\n */\n\nfunction completeValue(exeContext, returnType, fieldNodes, info, path, result) {\n  // If result is an Error, throw a located error.\n  if (result instanceof Error) {\n    throw result;\n  } // If field type is NonNull, complete for inner type, and throw field error\n  // if result is null.\n\n  if (isNonNullType(returnType)) {\n    const completed = completeValue(exeContext, returnType.ofType, fieldNodes, info, path, result);\n    if (completed === null) {\n      throw new Error(`Cannot return null for non-nullable field ${info.parentType.name}.${info.fieldName}.`);\n    }\n    return completed;\n  } // If result value is null or undefined then return null.\n\n  if (result == null) {\n    return null;\n  } // If field type is List, complete each item in the list with the inner type\n\n  if (isListType(returnType)) {\n    return completeListValue(exeContext, returnType, fieldNodes, info, path, result);\n  } // If field type is a leaf type, Scalar or Enum, serialize to a valid value,\n  // returning null if serialization is not possible.\n\n  if (isLeafType(returnType)) {\n    return completeLeafValue(returnType, result);\n  } // If field type is an abstract type, Interface or Union, determine the\n  // runtime Object type and complete for that type.\n\n  if (isAbstractType(returnType)) {\n    return completeAbstractValue(exeContext, returnType, fieldNodes, info, path, result);\n  } // If field type is Object, execute and complete all sub-selections.\n\n  if (isObjectType(returnType)) {\n    return completeObjectValue(exeContext, returnType, fieldNodes, info, path, result);\n  }\n  /* c8 ignore next 6 */\n  // Not reachable, all possible output types have been considered.\n\n  false || invariant(false, 'Cannot complete value of unexpected output type: ' + inspect(returnType));\n}\n/**\n * Complete a list value by completing each item in the list with the\n * inner type\n */\n\nfunction completeListValue(exeContext, returnType, fieldNodes, info, path, result) {\n  if (!isIterableObject(result)) {\n    throw new GraphQLError(`Expected Iterable, but did not find one for field \"${info.parentType.name}.${info.fieldName}\".`);\n  } // This is specified as a simple map, however we're optimizing the path\n  // where the list contains no Promises by avoiding creating another Promise.\n\n  const itemType = returnType.ofType;\n  let containsPromise = false;\n  const completedResults = Array.from(result, (item, index) => {\n    // No need to modify the info object containing the path,\n    // since from here on it is not ever accessed by resolver functions.\n    const itemPath = addPath(path, index, undefined);\n    try {\n      let completedItem;\n      if (isPromise(item)) {\n        completedItem = item.then(resolved => completeValue(exeContext, itemType, fieldNodes, info, itemPath, resolved));\n      } else {\n        completedItem = completeValue(exeContext, itemType, fieldNodes, info, itemPath, item);\n      }\n      if (isPromise(completedItem)) {\n        containsPromise = true; // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n        // to take a second callback for the error case.\n\n        return completedItem.then(undefined, rawError => {\n          const error = locatedError(rawError, fieldNodes, pathToArray(itemPath));\n          return handleFieldError(error, itemType, exeContext);\n        });\n      }\n      return completedItem;\n    } catch (rawError) {\n      const error = locatedError(rawError, fieldNodes, pathToArray(itemPath));\n      return handleFieldError(error, itemType, exeContext);\n    }\n  });\n  return containsPromise ? Promise.all(completedResults) : completedResults;\n}\n/**\n * Complete a Scalar or Enum by serializing to a valid value, returning\n * null if serialization is not possible.\n */\n\nfunction completeLeafValue(returnType, result) {\n  const serializedResult = returnType.serialize(result);\n  if (serializedResult == null) {\n    throw new Error(`Expected \\`${inspect(returnType)}.serialize(${inspect(result)})\\` to ` + `return non-nullable value, returned: ${inspect(serializedResult)}`);\n  }\n  return serializedResult;\n}\n/**\n * Complete a value of an abstract type by determining the runtime object type\n * of that value, then complete the value for that type.\n */\n\nfunction completeAbstractValue(exeContext, returnType, fieldNodes, info, path, result) {\n  var _returnType$resolveTy;\n  const resolveTypeFn = (_returnType$resolveTy = returnType.resolveType) !== null && _returnType$resolveTy !== void 0 ? _returnType$resolveTy : exeContext.typeResolver;\n  const contextValue = exeContext.contextValue;\n  const runtimeType = resolveTypeFn(result, contextValue, info, returnType);\n  if (isPromise(runtimeType)) {\n    return runtimeType.then(resolvedRuntimeType => completeObjectValue(exeContext, ensureValidRuntimeType(resolvedRuntimeType, exeContext, returnType, fieldNodes, info, result), fieldNodes, info, path, result));\n  }\n  return completeObjectValue(exeContext, ensureValidRuntimeType(runtimeType, exeContext, returnType, fieldNodes, info, result), fieldNodes, info, path, result);\n}\nfunction ensureValidRuntimeType(runtimeTypeName, exeContext, returnType, fieldNodes, info, result) {\n  if (runtimeTypeName == null) {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\". Either the \"${returnType.name}\" type should provide a \"resolveType\" function or each possible type should provide an \"isTypeOf\" function.`, fieldNodes);\n  } // releases before 16.0.0 supported returning `GraphQLObjectType` from `resolveType`\n  // TODO: remove in 17.0.0 release\n\n  if (isObjectType(runtimeTypeName)) {\n    throw new GraphQLError('Support for returning GraphQLObjectType from resolveType was removed in graphql-js@16.0.0 please return type name instead.');\n  }\n  if (typeof runtimeTypeName !== 'string') {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\" with ` + `value ${inspect(result)}, received \"${inspect(runtimeTypeName)}\".`);\n  }\n  const runtimeType = exeContext.schema.getType(runtimeTypeName);\n  if (runtimeType == null) {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" was resolved to a type \"${runtimeTypeName}\" that does not exist inside the schema.`, {\n      nodes: fieldNodes\n    });\n  }\n  if (!isObjectType(runtimeType)) {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" was resolved to a non-object type \"${runtimeTypeName}\".`, {\n      nodes: fieldNodes\n    });\n  }\n  if (!exeContext.schema.isSubType(returnType, runtimeType)) {\n    throw new GraphQLError(`Runtime Object type \"${runtimeType.name}\" is not a possible type for \"${returnType.name}\".`, {\n      nodes: fieldNodes\n    });\n  }\n  return runtimeType;\n}\n/**\n * Complete an Object value by executing all sub-selections.\n */\n\nfunction completeObjectValue(exeContext, returnType, fieldNodes, info, path, result) {\n  // Collect sub-fields to execute to complete this value.\n  const subFieldNodes = collectSubfields(exeContext, returnType, fieldNodes); // If there is an isTypeOf predicate function, call it with the\n  // current result. If isTypeOf returns false, then raise an error rather\n  // than continuing execution.\n\n  if (returnType.isTypeOf) {\n    const isTypeOf = returnType.isTypeOf(result, exeContext.contextValue, info);\n    if (isPromise(isTypeOf)) {\n      return isTypeOf.then(resolvedIsTypeOf => {\n        if (!resolvedIsTypeOf) {\n          throw invalidReturnTypeError(returnType, result, fieldNodes);\n        }\n        return executeFields(exeContext, returnType, result, path, subFieldNodes);\n      });\n    }\n    if (!isTypeOf) {\n      throw invalidReturnTypeError(returnType, result, fieldNodes);\n    }\n  }\n  return executeFields(exeContext, returnType, result, path, subFieldNodes);\n}\nfunction invalidReturnTypeError(returnType, result, fieldNodes) {\n  return new GraphQLError(`Expected value of type \"${returnType.name}\" but got: ${inspect(result)}.`, {\n    nodes: fieldNodes\n  });\n}\n/**\n * If a resolveType function is not given, then a default resolve behavior is\n * used which attempts two strategies:\n *\n * First, See if the provided value has a `__typename` field defined, if so, use\n * that value as name of the resolved type.\n *\n * Otherwise, test each possible type for the abstract type by calling\n * isTypeOf for the object being coerced, returning the first type that matches.\n */\n\nexport const defaultTypeResolver = function (value, contextValue, info, abstractType) {\n  // First, look for `__typename`.\n  if (isObjectLike(value) && typeof value.__typename === 'string') {\n    return value.__typename;\n  } // Otherwise, test each possible type.\n\n  const possibleTypes = info.schema.getPossibleTypes(abstractType);\n  const promisedIsTypeOfResults = [];\n  for (let i = 0; i < possibleTypes.length; i++) {\n    const type = possibleTypes[i];\n    if (type.isTypeOf) {\n      const isTypeOfResult = type.isTypeOf(value, contextValue, info);\n      if (isPromise(isTypeOfResult)) {\n        promisedIsTypeOfResults[i] = isTypeOfResult;\n      } else if (isTypeOfResult) {\n        return type.name;\n      }\n    }\n  }\n  if (promisedIsTypeOfResults.length) {\n    return Promise.all(promisedIsTypeOfResults).then(isTypeOfResults => {\n      for (let i = 0; i < isTypeOfResults.length; i++) {\n        if (isTypeOfResults[i]) {\n          return possibleTypes[i].name;\n        }\n      }\n    });\n  }\n};\n/**\n * If a resolve function is not given, then a default resolve behavior is used\n * which takes the property of the source object of the same name as the field\n * and returns it as the result, or if it's a function, returns the result\n * of calling that function while passing along args and context value.\n */\n\nexport const defaultFieldResolver = function (source, args, contextValue, info) {\n  // ensure source is a value for which property access is acceptable.\n  if (isObjectLike(source) || typeof source === 'function') {\n    const property = source[info.fieldName];\n    if (typeof property === 'function') {\n      return source[info.fieldName](args, contextValue, info);\n    }\n    return property;\n  }\n};\n/**\n * This method looks up the field on the given type definition.\n * It has special casing for the three introspection fields,\n * __schema, __type and __typename. __typename is special because\n * it can always be queried as a field, even in situations where no\n * other fields are allowed, like on a Union. __schema and __type\n * could get automatically added to the query type, but that would\n * require mutating type definitions, which would cause issues.\n *\n * @internal\n */\n\nexport function getFieldDef(schema, parentType, fieldNode) {\n  const fieldName = fieldNode.name.value;\n  if (fieldName === SchemaMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return SchemaMetaFieldDef;\n  } else if (fieldName === TypeMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return TypeMetaFieldDef;\n  } else if (fieldName === TypeNameMetaFieldDef.name) {\n    return TypeNameMetaFieldDef;\n  }\n  return parentType.getFields()[fieldName];\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "invariant", "isIterableObject", "isObjectLike", "isPromise", "memoize3", "addPath", "pathToArray", "promiseForObject", "promiseReduce", "GraphQLError", "locatedError", "OperationTypeNode", "Kind", "isAbstractType", "isLeafType", "isListType", "isNonNullType", "isObjectType", "SchemaMetaFieldDef", "TypeMetaFieldDef", "TypeNameMetaFieldDef", "assertValidSchema", "collectFields", "collectSubfields", "_collectSubfields", "getArgumentValues", "getVariableValues", "exeContext", "returnType", "fieldNodes", "schema", "fragments", "variableValues", "execute", "args", "arguments", "length", "document", "rootValue", "assertValidExecutionArguments", "buildExecutionContext", "errors", "operation", "result", "executeOperation", "then", "data", "buildResponse", "error", "push", "executeSync", "Error", "rawVariableValues", "_definition$name", "_operation$variableDe", "contextValue", "operationName", "fieldResolver", "typeResolver", "subscribeFieldResolver", "Object", "create", "definition", "definitions", "kind", "OPERATION_DEFINITION", "undefined", "name", "value", "FRAGMENT_DEFINITION", "variableDefinitions", "coercedVariable<PERSON><PERSON>ues", "maxErrors", "coerced", "defaultFieldResolver", "defaultTypeResolver", "rootType", "getRootType", "nodes", "rootFields", "selectionSet", "path", "QUERY", "executeFields", "MUTATION", "executeFieldsSerially", "SUBSCRIPTION", "parentType", "sourceValue", "fields", "entries", "results", "responseName", "fieldPath", "executeField", "resolvedResult", "containsPromise", "finally", "source", "_fieldDef$resolve", "fieldDef", "getFieldDef", "type", "resolveFn", "resolve", "info", "buildResolveInfo", "completed", "resolved", "completeValue", "rawError", "handleFieldError", "fieldName", "ofType", "completeListValue", "completeLeafValue", "completeAbstractValue", "completeObjectValue", "itemType", "completedResults", "Array", "from", "item", "index", "itemPath", "completedItem", "Promise", "all", "serializedResult", "serialize", "_returnType$resolveTy", "resolveTypeFn", "resolveType", "runtimeType", "resolvedRuntimeType", "ensureValidRuntimeType", "runtimeTypeName", "getType", "isSubType", "subFieldNodes", "isTypeOf", "resolvedIsTypeOf", "invalidReturnTypeError", "abstractType", "__typename", "possibleTypes", "getPossibleTypes", "promisedIsTypeOfResults", "i", "isTypeOfResult", "isTypeOfResults", "property", "fieldNode", "getQueryType", "getFields"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/execution/execute.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { isPromise } from '../jsutils/isPromise.mjs';\nimport { memoize3 } from '../jsutils/memoize3.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { promiseForObject } from '../jsutils/promiseForObject.mjs';\nimport { promiseReduce } from '../jsutils/promiseReduce.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport {\n  isAbstractType,\n  isLeafType,\n  isListType,\n  isNonNullType,\n  isObjectType,\n} from '../type/definition.mjs';\nimport {\n  SchemaMetaFieldDef,\n  TypeMetaFieldDef,\n  TypeNameMetaFieldDef,\n} from '../type/introspection.mjs';\nimport { assertValidSchema } from '../type/validate.mjs';\nimport {\n  collectFields,\n  collectSubfields as _collectSubfields,\n} from './collectFields.mjs';\nimport { getArgumentValues, getVariableValues } from './values.mjs';\n/**\n * A memoized collection of relevant subfields with regard to the return\n * type. Memoizing ensures the subfields are not repeatedly calculated, which\n * saves overhead when resolving lists of values.\n */\n\nconst collectSubfields = memoize3((exeContext, returnType, fieldNodes) =>\n  _collectSubfields(\n    exeContext.schema,\n    exeContext.fragments,\n    exeContext.variableValues,\n    returnType,\n    fieldNodes,\n  ),\n);\n/**\n * Terminology\n *\n * \"Definitions\" are the generic name for top-level statements in the document.\n * Examples of this include:\n * 1) Operations (such as a query)\n * 2) Fragments\n *\n * \"Operations\" are a generic name for requests in the document.\n * Examples of this include:\n * 1) query,\n * 2) mutation\n *\n * \"Selections\" are the definitions that can appear legally and at\n * single level of the query. These include:\n * 1) field references e.g `a`\n * 2) fragment \"spreads\" e.g. `...c`\n * 3) inline fragment \"spreads\" e.g. `...on Type { a }`\n */\n\n/**\n * Data that must be available at all points during query execution.\n *\n * Namely, schema of the type system that is currently executing,\n * and the fragments defined in the query document\n */\n\n/**\n * Implements the \"Executing requests\" section of the GraphQL specification.\n *\n * Returns either a synchronous ExecutionResult (if all encountered resolvers\n * are synchronous), or a Promise of an ExecutionResult that will eventually be\n * resolved and never rejected.\n *\n * If the arguments to this function do not result in a legal execution context,\n * a GraphQLError will be thrown immediately explaining the invalid input.\n */\nexport function execute(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 ||\n    devAssert(\n      false,\n      'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.',\n    );\n  const { schema, document, variableValues, rootValue } = args; // If arguments are missing or incorrect, throw an error.\n\n  assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n  // a \"Response\" with only errors is returned.\n\n  const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n  if (!('schema' in exeContext)) {\n    return {\n      errors: exeContext,\n    };\n  } // Return a Promise that will eventually resolve to the data described by\n  // The \"Response\" section of the GraphQL specification.\n  //\n  // If errors are encountered while executing a GraphQL field, only that\n  // field and its descendants will be omitted, and sibling fields will still\n  // be executed. An execution which encounters errors will still result in a\n  // resolved Promise.\n  //\n  // Errors from sub-fields of a NonNull type may propagate to the top level,\n  // at which point we still log the error and null the parent field, which\n  // in this case is the entire response.\n\n  try {\n    const { operation } = exeContext;\n    const result = executeOperation(exeContext, operation, rootValue);\n\n    if (isPromise(result)) {\n      return result.then(\n        (data) => buildResponse(data, exeContext.errors),\n        (error) => {\n          exeContext.errors.push(error);\n          return buildResponse(null, exeContext.errors);\n        },\n      );\n    }\n\n    return buildResponse(result, exeContext.errors);\n  } catch (error) {\n    exeContext.errors.push(error);\n    return buildResponse(null, exeContext.errors);\n  }\n}\n/**\n * Also implements the \"Executing requests\" section of the GraphQL specification.\n * However, it guarantees to complete synchronously (or throw an error) assuming\n * that all field resolvers are also synchronous.\n */\n\nexport function executeSync(args) {\n  const result = execute(args); // Assert that the execution was synchronous.\n\n  if (isPromise(result)) {\n    throw new Error('GraphQL execution failed to complete synchronously.');\n  }\n\n  return result;\n}\n/**\n * Given a completed execution context and data, build the `{ errors, data }`\n * response defined by the \"Response\" section of the GraphQL specification.\n */\n\nfunction buildResponse(data, errors) {\n  return errors.length === 0\n    ? {\n        data,\n      }\n    : {\n        errors,\n        data,\n      };\n}\n/**\n * Essential assertions before executing to provide developer feedback for\n * improper use of the GraphQL library.\n *\n * @internal\n */\n\nexport function assertValidExecutionArguments(\n  schema,\n  document,\n  rawVariableValues,\n) {\n  document || devAssert(false, 'Must provide document.'); // If the schema used for execution is invalid, throw an error.\n\n  assertValidSchema(schema); // Variables, if provided, must be an object.\n\n  rawVariableValues == null ||\n    isObjectLike(rawVariableValues) ||\n    devAssert(\n      false,\n      'Variables must be provided as an Object where each property is a variable value. Perhaps look to see if an unparsed JSON string was provided.',\n    );\n}\n/**\n * Constructs a ExecutionContext object from the arguments passed to\n * execute, which we will pass throughout the other execution methods.\n *\n * Throws a GraphQLError if a valid execution context cannot be created.\n *\n * @internal\n */\n\nexport function buildExecutionContext(args) {\n  var _definition$name, _operation$variableDe;\n\n  const {\n    schema,\n    document,\n    rootValue,\n    contextValue,\n    variableValues: rawVariableValues,\n    operationName,\n    fieldResolver,\n    typeResolver,\n    subscribeFieldResolver,\n  } = args;\n  let operation;\n  const fragments = Object.create(null);\n\n  for (const definition of document.definitions) {\n    switch (definition.kind) {\n      case Kind.OPERATION_DEFINITION:\n        if (operationName == null) {\n          if (operation !== undefined) {\n            return [\n              new GraphQLError(\n                'Must provide operation name if query contains multiple operations.',\n              ),\n            ];\n          }\n\n          operation = definition;\n        } else if (\n          ((_definition$name = definition.name) === null ||\n          _definition$name === void 0\n            ? void 0\n            : _definition$name.value) === operationName\n        ) {\n          operation = definition;\n        }\n\n        break;\n\n      case Kind.FRAGMENT_DEFINITION:\n        fragments[definition.name.value] = definition;\n        break;\n\n      default: // ignore non-executable definitions\n    }\n  }\n\n  if (!operation) {\n    if (operationName != null) {\n      return [new GraphQLError(`Unknown operation named \"${operationName}\".`)];\n    }\n\n    return [new GraphQLError('Must provide an operation.')];\n  } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n\n  const variableDefinitions =\n    (_operation$variableDe = operation.variableDefinitions) !== null &&\n    _operation$variableDe !== void 0\n      ? _operation$variableDe\n      : [];\n  const coercedVariableValues = getVariableValues(\n    schema,\n    variableDefinitions,\n    rawVariableValues !== null && rawVariableValues !== void 0\n      ? rawVariableValues\n      : {},\n    {\n      maxErrors: 50,\n    },\n  );\n\n  if (coercedVariableValues.errors) {\n    return coercedVariableValues.errors;\n  }\n\n  return {\n    schema,\n    fragments,\n    rootValue,\n    contextValue,\n    operation,\n    variableValues: coercedVariableValues.coerced,\n    fieldResolver:\n      fieldResolver !== null && fieldResolver !== void 0\n        ? fieldResolver\n        : defaultFieldResolver,\n    typeResolver:\n      typeResolver !== null && typeResolver !== void 0\n        ? typeResolver\n        : defaultTypeResolver,\n    subscribeFieldResolver:\n      subscribeFieldResolver !== null && subscribeFieldResolver !== void 0\n        ? subscribeFieldResolver\n        : defaultFieldResolver,\n    errors: [],\n  };\n}\n/**\n * Implements the \"Executing operations\" section of the spec.\n */\n\nfunction executeOperation(exeContext, operation, rootValue) {\n  const rootType = exeContext.schema.getRootType(operation.operation);\n\n  if (rootType == null) {\n    throw new GraphQLError(\n      `Schema is not configured to execute ${operation.operation} operation.`,\n      {\n        nodes: operation,\n      },\n    );\n  }\n\n  const rootFields = collectFields(\n    exeContext.schema,\n    exeContext.fragments,\n    exeContext.variableValues,\n    rootType,\n    operation.selectionSet,\n  );\n  const path = undefined;\n\n  switch (operation.operation) {\n    case OperationTypeNode.QUERY:\n      return executeFields(exeContext, rootType, rootValue, path, rootFields);\n\n    case OperationTypeNode.MUTATION:\n      return executeFieldsSerially(\n        exeContext,\n        rootType,\n        rootValue,\n        path,\n        rootFields,\n      );\n\n    case OperationTypeNode.SUBSCRIPTION:\n      // TODO: deprecate `subscribe` and move all logic here\n      // Temporary solution until we finish merging execute and subscribe together\n      return executeFields(exeContext, rootType, rootValue, path, rootFields);\n  }\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that must be executed serially.\n */\n\nfunction executeFieldsSerially(\n  exeContext,\n  parentType,\n  sourceValue,\n  path,\n  fields,\n) {\n  return promiseReduce(\n    fields.entries(),\n    (results, [responseName, fieldNodes]) => {\n      const fieldPath = addPath(path, responseName, parentType.name);\n      const result = executeField(\n        exeContext,\n        parentType,\n        sourceValue,\n        fieldNodes,\n        fieldPath,\n      );\n\n      if (result === undefined) {\n        return results;\n      }\n\n      if (isPromise(result)) {\n        return result.then((resolvedResult) => {\n          results[responseName] = resolvedResult;\n          return results;\n        });\n      }\n\n      results[responseName] = result;\n      return results;\n    },\n    Object.create(null),\n  );\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that may be executed in parallel.\n */\n\nfunction executeFields(exeContext, parentType, sourceValue, path, fields) {\n  const results = Object.create(null);\n  let containsPromise = false;\n\n  try {\n    for (const [responseName, fieldNodes] of fields.entries()) {\n      const fieldPath = addPath(path, responseName, parentType.name);\n      const result = executeField(\n        exeContext,\n        parentType,\n        sourceValue,\n        fieldNodes,\n        fieldPath,\n      );\n\n      if (result !== undefined) {\n        results[responseName] = result;\n\n        if (isPromise(result)) {\n          containsPromise = true;\n        }\n      }\n    }\n  } catch (error) {\n    if (containsPromise) {\n      // Ensure that any promises returned by other fields are handled, as they may also reject.\n      return promiseForObject(results).finally(() => {\n        throw error;\n      });\n    }\n\n    throw error;\n  } // If there are no promises, we can just return the object\n\n  if (!containsPromise) {\n    return results;\n  } // Otherwise, results is a map from field name to the result of resolving that\n  // field, which is possibly a promise. Return a promise that will return this\n  // same map, but with any promises replaced with the values they resolved to.\n\n  return promiseForObject(results);\n}\n/**\n * Implements the \"Executing fields\" section of the spec\n * In particular, this function figures out the value that the field returns by\n * calling its resolve function, then calls completeValue to complete promises,\n * serialize scalars, or execute the sub-selection-set for objects.\n */\n\nfunction executeField(exeContext, parentType, source, fieldNodes, path) {\n  var _fieldDef$resolve;\n\n  const fieldDef = getFieldDef(exeContext.schema, parentType, fieldNodes[0]);\n\n  if (!fieldDef) {\n    return;\n  }\n\n  const returnType = fieldDef.type;\n  const resolveFn =\n    (_fieldDef$resolve = fieldDef.resolve) !== null &&\n    _fieldDef$resolve !== void 0\n      ? _fieldDef$resolve\n      : exeContext.fieldResolver;\n  const info = buildResolveInfo(\n    exeContext,\n    fieldDef,\n    fieldNodes,\n    parentType,\n    path,\n  ); // Get the resolve function, regardless of if its result is normal or abrupt (error).\n\n  try {\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    // TODO: find a way to memoize, in case this field is within a List type.\n    const args = getArgumentValues(\n      fieldDef,\n      fieldNodes[0],\n      exeContext.variableValues,\n    ); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    const contextValue = exeContext.contextValue;\n    const result = resolveFn(source, args, contextValue, info);\n    let completed;\n\n    if (isPromise(result)) {\n      completed = result.then((resolved) =>\n        completeValue(exeContext, returnType, fieldNodes, info, path, resolved),\n      );\n    } else {\n      completed = completeValue(\n        exeContext,\n        returnType,\n        fieldNodes,\n        info,\n        path,\n        result,\n      );\n    }\n\n    if (isPromise(completed)) {\n      // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n      // to take a second callback for the error case.\n      return completed.then(undefined, (rawError) => {\n        const error = locatedError(rawError, fieldNodes, pathToArray(path));\n        return handleFieldError(error, returnType, exeContext);\n      });\n    }\n\n    return completed;\n  } catch (rawError) {\n    const error = locatedError(rawError, fieldNodes, pathToArray(path));\n    return handleFieldError(error, returnType, exeContext);\n  }\n}\n/**\n * @internal\n */\n\nexport function buildResolveInfo(\n  exeContext,\n  fieldDef,\n  fieldNodes,\n  parentType,\n  path,\n) {\n  // The resolve function's optional fourth argument is a collection of\n  // information about the current execution state.\n  return {\n    fieldName: fieldDef.name,\n    fieldNodes,\n    returnType: fieldDef.type,\n    parentType,\n    path,\n    schema: exeContext.schema,\n    fragments: exeContext.fragments,\n    rootValue: exeContext.rootValue,\n    operation: exeContext.operation,\n    variableValues: exeContext.variableValues,\n  };\n}\n\nfunction handleFieldError(error, returnType, exeContext) {\n  // If the field type is non-nullable, then it is resolved without any\n  // protection from errors, however it still properly locates the error.\n  if (isNonNullType(returnType)) {\n    throw error;\n  } // Otherwise, error protection is applied, logging the error and resolving\n  // a null value for this field if one is encountered.\n\n  exeContext.errors.push(error);\n  return null;\n}\n/**\n * Implements the instructions for completeValue as defined in the\n * \"Value Completion\" section of the spec.\n *\n * If the field type is Non-Null, then this recursively completes the value\n * for the inner type. It throws a field error if that completion returns null,\n * as per the \"Nullability\" section of the spec.\n *\n * If the field type is a List, then this recursively completes the value\n * for the inner type on each item in the list.\n *\n * If the field type is a Scalar or Enum, ensures the completed value is a legal\n * value of the type by calling the `serialize` method of GraphQL type\n * definition.\n *\n * If the field is an abstract type, determine the runtime type of the value\n * and then complete based on that type\n *\n * Otherwise, the field type expects a sub-selection set, and will complete the\n * value by executing all sub-selections.\n */\n\nfunction completeValue(exeContext, returnType, fieldNodes, info, path, result) {\n  // If result is an Error, throw a located error.\n  if (result instanceof Error) {\n    throw result;\n  } // If field type is NonNull, complete for inner type, and throw field error\n  // if result is null.\n\n  if (isNonNullType(returnType)) {\n    const completed = completeValue(\n      exeContext,\n      returnType.ofType,\n      fieldNodes,\n      info,\n      path,\n      result,\n    );\n\n    if (completed === null) {\n      throw new Error(\n        `Cannot return null for non-nullable field ${info.parentType.name}.${info.fieldName}.`,\n      );\n    }\n\n    return completed;\n  } // If result value is null or undefined then return null.\n\n  if (result == null) {\n    return null;\n  } // If field type is List, complete each item in the list with the inner type\n\n  if (isListType(returnType)) {\n    return completeListValue(\n      exeContext,\n      returnType,\n      fieldNodes,\n      info,\n      path,\n      result,\n    );\n  } // If field type is a leaf type, Scalar or Enum, serialize to a valid value,\n  // returning null if serialization is not possible.\n\n  if (isLeafType(returnType)) {\n    return completeLeafValue(returnType, result);\n  } // If field type is an abstract type, Interface or Union, determine the\n  // runtime Object type and complete for that type.\n\n  if (isAbstractType(returnType)) {\n    return completeAbstractValue(\n      exeContext,\n      returnType,\n      fieldNodes,\n      info,\n      path,\n      result,\n    );\n  } // If field type is Object, execute and complete all sub-selections.\n\n  if (isObjectType(returnType)) {\n    return completeObjectValue(\n      exeContext,\n      returnType,\n      fieldNodes,\n      info,\n      path,\n      result,\n    );\n  }\n  /* c8 ignore next 6 */\n  // Not reachable, all possible output types have been considered.\n\n  false ||\n    invariant(\n      false,\n      'Cannot complete value of unexpected output type: ' + inspect(returnType),\n    );\n}\n/**\n * Complete a list value by completing each item in the list with the\n * inner type\n */\n\nfunction completeListValue(\n  exeContext,\n  returnType,\n  fieldNodes,\n  info,\n  path,\n  result,\n) {\n  if (!isIterableObject(result)) {\n    throw new GraphQLError(\n      `Expected Iterable, but did not find one for field \"${info.parentType.name}.${info.fieldName}\".`,\n    );\n  } // This is specified as a simple map, however we're optimizing the path\n  // where the list contains no Promises by avoiding creating another Promise.\n\n  const itemType = returnType.ofType;\n  let containsPromise = false;\n  const completedResults = Array.from(result, (item, index) => {\n    // No need to modify the info object containing the path,\n    // since from here on it is not ever accessed by resolver functions.\n    const itemPath = addPath(path, index, undefined);\n\n    try {\n      let completedItem;\n\n      if (isPromise(item)) {\n        completedItem = item.then((resolved) =>\n          completeValue(\n            exeContext,\n            itemType,\n            fieldNodes,\n            info,\n            itemPath,\n            resolved,\n          ),\n        );\n      } else {\n        completedItem = completeValue(\n          exeContext,\n          itemType,\n          fieldNodes,\n          info,\n          itemPath,\n          item,\n        );\n      }\n\n      if (isPromise(completedItem)) {\n        containsPromise = true; // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n        // to take a second callback for the error case.\n\n        return completedItem.then(undefined, (rawError) => {\n          const error = locatedError(\n            rawError,\n            fieldNodes,\n            pathToArray(itemPath),\n          );\n          return handleFieldError(error, itemType, exeContext);\n        });\n      }\n\n      return completedItem;\n    } catch (rawError) {\n      const error = locatedError(rawError, fieldNodes, pathToArray(itemPath));\n      return handleFieldError(error, itemType, exeContext);\n    }\n  });\n  return containsPromise ? Promise.all(completedResults) : completedResults;\n}\n/**\n * Complete a Scalar or Enum by serializing to a valid value, returning\n * null if serialization is not possible.\n */\n\nfunction completeLeafValue(returnType, result) {\n  const serializedResult = returnType.serialize(result);\n\n  if (serializedResult == null) {\n    throw new Error(\n      `Expected \\`${inspect(returnType)}.serialize(${inspect(result)})\\` to ` +\n        `return non-nullable value, returned: ${inspect(serializedResult)}`,\n    );\n  }\n\n  return serializedResult;\n}\n/**\n * Complete a value of an abstract type by determining the runtime object type\n * of that value, then complete the value for that type.\n */\n\nfunction completeAbstractValue(\n  exeContext,\n  returnType,\n  fieldNodes,\n  info,\n  path,\n  result,\n) {\n  var _returnType$resolveTy;\n\n  const resolveTypeFn =\n    (_returnType$resolveTy = returnType.resolveType) !== null &&\n    _returnType$resolveTy !== void 0\n      ? _returnType$resolveTy\n      : exeContext.typeResolver;\n  const contextValue = exeContext.contextValue;\n  const runtimeType = resolveTypeFn(result, contextValue, info, returnType);\n\n  if (isPromise(runtimeType)) {\n    return runtimeType.then((resolvedRuntimeType) =>\n      completeObjectValue(\n        exeContext,\n        ensureValidRuntimeType(\n          resolvedRuntimeType,\n          exeContext,\n          returnType,\n          fieldNodes,\n          info,\n          result,\n        ),\n        fieldNodes,\n        info,\n        path,\n        result,\n      ),\n    );\n  }\n\n  return completeObjectValue(\n    exeContext,\n    ensureValidRuntimeType(\n      runtimeType,\n      exeContext,\n      returnType,\n      fieldNodes,\n      info,\n      result,\n    ),\n    fieldNodes,\n    info,\n    path,\n    result,\n  );\n}\n\nfunction ensureValidRuntimeType(\n  runtimeTypeName,\n  exeContext,\n  returnType,\n  fieldNodes,\n  info,\n  result,\n) {\n  if (runtimeTypeName == null) {\n    throw new GraphQLError(\n      `Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\". Either the \"${returnType.name}\" type should provide a \"resolveType\" function or each possible type should provide an \"isTypeOf\" function.`,\n      fieldNodes,\n    );\n  } // releases before 16.0.0 supported returning `GraphQLObjectType` from `resolveType`\n  // TODO: remove in 17.0.0 release\n\n  if (isObjectType(runtimeTypeName)) {\n    throw new GraphQLError(\n      'Support for returning GraphQLObjectType from resolveType was removed in graphql-js@16.0.0 please return type name instead.',\n    );\n  }\n\n  if (typeof runtimeTypeName !== 'string') {\n    throw new GraphQLError(\n      `Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\" with ` +\n        `value ${inspect(result)}, received \"${inspect(runtimeTypeName)}\".`,\n    );\n  }\n\n  const runtimeType = exeContext.schema.getType(runtimeTypeName);\n\n  if (runtimeType == null) {\n    throw new GraphQLError(\n      `Abstract type \"${returnType.name}\" was resolved to a type \"${runtimeTypeName}\" that does not exist inside the schema.`,\n      {\n        nodes: fieldNodes,\n      },\n    );\n  }\n\n  if (!isObjectType(runtimeType)) {\n    throw new GraphQLError(\n      `Abstract type \"${returnType.name}\" was resolved to a non-object type \"${runtimeTypeName}\".`,\n      {\n        nodes: fieldNodes,\n      },\n    );\n  }\n\n  if (!exeContext.schema.isSubType(returnType, runtimeType)) {\n    throw new GraphQLError(\n      `Runtime Object type \"${runtimeType.name}\" is not a possible type for \"${returnType.name}\".`,\n      {\n        nodes: fieldNodes,\n      },\n    );\n  }\n\n  return runtimeType;\n}\n/**\n * Complete an Object value by executing all sub-selections.\n */\n\nfunction completeObjectValue(\n  exeContext,\n  returnType,\n  fieldNodes,\n  info,\n  path,\n  result,\n) {\n  // Collect sub-fields to execute to complete this value.\n  const subFieldNodes = collectSubfields(exeContext, returnType, fieldNodes); // If there is an isTypeOf predicate function, call it with the\n  // current result. If isTypeOf returns false, then raise an error rather\n  // than continuing execution.\n\n  if (returnType.isTypeOf) {\n    const isTypeOf = returnType.isTypeOf(result, exeContext.contextValue, info);\n\n    if (isPromise(isTypeOf)) {\n      return isTypeOf.then((resolvedIsTypeOf) => {\n        if (!resolvedIsTypeOf) {\n          throw invalidReturnTypeError(returnType, result, fieldNodes);\n        }\n\n        return executeFields(\n          exeContext,\n          returnType,\n          result,\n          path,\n          subFieldNodes,\n        );\n      });\n    }\n\n    if (!isTypeOf) {\n      throw invalidReturnTypeError(returnType, result, fieldNodes);\n    }\n  }\n\n  return executeFields(exeContext, returnType, result, path, subFieldNodes);\n}\n\nfunction invalidReturnTypeError(returnType, result, fieldNodes) {\n  return new GraphQLError(\n    `Expected value of type \"${returnType.name}\" but got: ${inspect(result)}.`,\n    {\n      nodes: fieldNodes,\n    },\n  );\n}\n/**\n * If a resolveType function is not given, then a default resolve behavior is\n * used which attempts two strategies:\n *\n * First, See if the provided value has a `__typename` field defined, if so, use\n * that value as name of the resolved type.\n *\n * Otherwise, test each possible type for the abstract type by calling\n * isTypeOf for the object being coerced, returning the first type that matches.\n */\n\nexport const defaultTypeResolver = function (\n  value,\n  contextValue,\n  info,\n  abstractType,\n) {\n  // First, look for `__typename`.\n  if (isObjectLike(value) && typeof value.__typename === 'string') {\n    return value.__typename;\n  } // Otherwise, test each possible type.\n\n  const possibleTypes = info.schema.getPossibleTypes(abstractType);\n  const promisedIsTypeOfResults = [];\n\n  for (let i = 0; i < possibleTypes.length; i++) {\n    const type = possibleTypes[i];\n\n    if (type.isTypeOf) {\n      const isTypeOfResult = type.isTypeOf(value, contextValue, info);\n\n      if (isPromise(isTypeOfResult)) {\n        promisedIsTypeOfResults[i] = isTypeOfResult;\n      } else if (isTypeOfResult) {\n        return type.name;\n      }\n    }\n  }\n\n  if (promisedIsTypeOfResults.length) {\n    return Promise.all(promisedIsTypeOfResults).then((isTypeOfResults) => {\n      for (let i = 0; i < isTypeOfResults.length; i++) {\n        if (isTypeOfResults[i]) {\n          return possibleTypes[i].name;\n        }\n      }\n    });\n  }\n};\n/**\n * If a resolve function is not given, then a default resolve behavior is used\n * which takes the property of the source object of the same name as the field\n * and returns it as the result, or if it's a function, returns the result\n * of calling that function while passing along args and context value.\n */\n\nexport const defaultFieldResolver = function (\n  source,\n  args,\n  contextValue,\n  info,\n) {\n  // ensure source is a value for which property access is acceptable.\n  if (isObjectLike(source) || typeof source === 'function') {\n    const property = source[info.fieldName];\n\n    if (typeof property === 'function') {\n      return source[info.fieldName](args, contextValue, info);\n    }\n\n    return property;\n  }\n};\n/**\n * This method looks up the field on the given type definition.\n * It has special casing for the three introspection fields,\n * __schema, __type and __typename. __typename is special because\n * it can always be queried as a field, even in situations where no\n * other fields are allowed, like on a Union. __schema and __type\n * could get automatically added to the query type, but that would\n * require mutating type definitions, which would cause issues.\n *\n * @internal\n */\n\nexport function getFieldDef(schema, parentType, fieldNode) {\n  const fieldName = fieldNode.name.value;\n\n  if (\n    fieldName === SchemaMetaFieldDef.name &&\n    schema.getQueryType() === parentType\n  ) {\n    return SchemaMetaFieldDef;\n  } else if (\n    fieldName === TypeMetaFieldDef.name &&\n    schema.getQueryType() === parentType\n  ) {\n    return TypeMetaFieldDef;\n  } else if (fieldName === TypeNameMetaFieldDef.name) {\n    return TypeNameMetaFieldDef;\n  }\n\n  return parentType.getFields()[fieldName];\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAC1D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SACEC,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,wBAAwB;AAC/B,SACEC,kBAAkB,EAClBC,gBAAgB,EAChBC,oBAAoB,QACf,2BAA2B;AAClC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SACEC,aAAa,EACbC,gBAAgB,IAAIC,iBAAiB,QAChC,qBAAqB;AAC5B,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,cAAc;AACnE;AACA;AACA;AACA;AACA;;AAEA,MAAMH,gBAAgB,GAAGnB,QAAQ,CAAC,CAACuB,UAAU,EAAEC,UAAU,EAAEC,UAAU,KACnEL,iBAAiB,CACfG,UAAU,CAACG,MAAM,EACjBH,UAAU,CAACI,SAAS,EACpBJ,UAAU,CAACK,cAAc,EACzBJ,UAAU,EACVC,UACF,CACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B;EACAC,SAAS,CAACC,MAAM,GAAG,CAAC,IAClBtC,SAAS,CACP,KAAK,EACL,qGACF,CAAC;EACH,MAAM;IAAEgC,MAAM;IAAEO,QAAQ;IAAEL,cAAc;IAAEM;EAAU,CAAC,GAAGJ,IAAI,CAAC,CAAC;;EAE9DK,6BAA6B,CAACT,MAAM,EAAEO,QAAQ,EAAEL,cAAc,CAAC,CAAC,CAAC;EACjE;;EAEA,MAAML,UAAU,GAAGa,qBAAqB,CAACN,IAAI,CAAC,CAAC,CAAC;;EAEhD,IAAI,EAAE,QAAQ,IAAIP,UAAU,CAAC,EAAE;IAC7B,OAAO;MACLc,MAAM,EAAEd;IACV,CAAC;EACH,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAI;IACF,MAAM;MAAEe;IAAU,CAAC,GAAGf,UAAU;IAChC,MAAMgB,MAAM,GAAGC,gBAAgB,CAACjB,UAAU,EAAEe,SAAS,EAAEJ,SAAS,CAAC;IAEjE,IAAInC,SAAS,CAACwC,MAAM,CAAC,EAAE;MACrB,OAAOA,MAAM,CAACE,IAAI,CACfC,IAAI,IAAKC,aAAa,CAACD,IAAI,EAAEnB,UAAU,CAACc,MAAM,CAAC,EAC/CO,KAAK,IAAK;QACTrB,UAAU,CAACc,MAAM,CAACQ,IAAI,CAACD,KAAK,CAAC;QAC7B,OAAOD,aAAa,CAAC,IAAI,EAAEpB,UAAU,CAACc,MAAM,CAAC;MAC/C,CACF,CAAC;IACH;IAEA,OAAOM,aAAa,CAACJ,MAAM,EAAEhB,UAAU,CAACc,MAAM,CAAC;EACjD,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdrB,UAAU,CAACc,MAAM,CAACQ,IAAI,CAACD,KAAK,CAAC;IAC7B,OAAOD,aAAa,CAAC,IAAI,EAAEpB,UAAU,CAACc,MAAM,CAAC;EAC/C;AACF;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASS,WAAWA,CAAChB,IAAI,EAAE;EAChC,MAAMS,MAAM,GAAGV,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;;EAE9B,IAAI/B,SAAS,CAACwC,MAAM,CAAC,EAAE;IACrB,MAAM,IAAIQ,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,OAAOR,MAAM;AACf;AACA;AACA;AACA;AACA;;AAEA,SAASI,aAAaA,CAACD,IAAI,EAAEL,MAAM,EAAE;EACnC,OAAOA,MAAM,CAACL,MAAM,KAAK,CAAC,GACtB;IACEU;EACF,CAAC,GACD;IACEL,MAAM;IACNK;EACF,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASP,6BAA6BA,CAC3CT,MAAM,EACNO,QAAQ,EACRe,iBAAiB,EACjB;EACAf,QAAQ,IAAIvC,SAAS,CAAC,KAAK,EAAE,wBAAwB,CAAC,CAAC,CAAC;;EAExDuB,iBAAiB,CAACS,MAAM,CAAC,CAAC,CAAC;;EAE3BsB,iBAAiB,IAAI,IAAI,IACvBlD,YAAY,CAACkD,iBAAiB,CAAC,IAC/BtD,SAAS,CACP,KAAK,EACL,+IACF,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAAS0C,qBAAqBA,CAACN,IAAI,EAAE;EAC1C,IAAImB,gBAAgB,EAAEC,qBAAqB;EAE3C,MAAM;IACJxB,MAAM;IACNO,QAAQ;IACRC,SAAS;IACTiB,YAAY;IACZvB,cAAc,EAAEoB,iBAAiB;IACjCI,aAAa;IACbC,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,GAAGzB,IAAI;EACR,IAAIQ,SAAS;EACb,MAAMX,SAAS,GAAG6B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAErC,KAAK,MAAMC,UAAU,IAAIzB,QAAQ,CAAC0B,WAAW,EAAE;IAC7C,QAAQD,UAAU,CAACE,IAAI;MACrB,KAAKpD,IAAI,CAACqD,oBAAoB;QAC5B,IAAIT,aAAa,IAAI,IAAI,EAAE;UACzB,IAAId,SAAS,KAAKwB,SAAS,EAAE;YAC3B,OAAO,CACL,IAAIzD,YAAY,CACd,oEACF,CAAC,CACF;UACH;UAEAiC,SAAS,GAAGoB,UAAU;QACxB,CAAC,MAAM,IACL,CAAC,CAACT,gBAAgB,GAAGS,UAAU,CAACK,IAAI,MAAM,IAAI,IAC9Cd,gBAAgB,KAAK,KAAK,CAAC,GACvB,KAAK,CAAC,GACNA,gBAAgB,CAACe,KAAK,MAAMZ,aAAa,EAC7C;UACAd,SAAS,GAAGoB,UAAU;QACxB;QAEA;MAEF,KAAKlD,IAAI,CAACyD,mBAAmB;QAC3BtC,SAAS,CAAC+B,UAAU,CAACK,IAAI,CAACC,KAAK,CAAC,GAAGN,UAAU;QAC7C;MAEF,QAAQ,CAAC;IACX;EACF;;EAEA,IAAI,CAACpB,SAAS,EAAE;IACd,IAAIc,aAAa,IAAI,IAAI,EAAE;MACzB,OAAO,CAAC,IAAI/C,YAAY,CAAE,4BAA2B+C,aAAc,IAAG,CAAC,CAAC;IAC1E;IAEA,OAAO,CAAC,IAAI/C,YAAY,CAAC,4BAA4B,CAAC,CAAC;EACzD,CAAC,CAAC;;EAEF;;EAEA,MAAM6D,mBAAmB,GACvB,CAAChB,qBAAqB,GAAGZ,SAAS,CAAC4B,mBAAmB,MAAM,IAAI,IAChEhB,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;EACR,MAAMiB,qBAAqB,GAAG7C,iBAAiB,CAC7CI,MAAM,EACNwC,mBAAmB,EACnBlB,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GACtDA,iBAAiB,GACjB,CAAC,CAAC,EACN;IACEoB,SAAS,EAAE;EACb,CACF,CAAC;EAED,IAAID,qBAAqB,CAAC9B,MAAM,EAAE;IAChC,OAAO8B,qBAAqB,CAAC9B,MAAM;EACrC;EAEA,OAAO;IACLX,MAAM;IACNC,SAAS;IACTO,SAAS;IACTiB,YAAY;IACZb,SAAS;IACTV,cAAc,EAAEuC,qBAAqB,CAACE,OAAO;IAC7ChB,aAAa,EACXA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAC9CA,aAAa,GACbiB,oBAAoB;IAC1BhB,YAAY,EACVA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAC5CA,YAAY,GACZiB,mBAAmB;IACzBhB,sBAAsB,EACpBA,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,KAAK,KAAK,CAAC,GAChEA,sBAAsB,GACtBe,oBAAoB;IAC1BjC,MAAM,EAAE;EACV,CAAC;AACH;AACA;AACA;AACA;;AAEA,SAASG,gBAAgBA,CAACjB,UAAU,EAAEe,SAAS,EAAEJ,SAAS,EAAE;EAC1D,MAAMsC,QAAQ,GAAGjD,UAAU,CAACG,MAAM,CAAC+C,WAAW,CAACnC,SAAS,CAACA,SAAS,CAAC;EAEnE,IAAIkC,QAAQ,IAAI,IAAI,EAAE;IACpB,MAAM,IAAInE,YAAY,CACnB,uCAAsCiC,SAAS,CAACA,SAAU,aAAY,EACvE;MACEoC,KAAK,EAAEpC;IACT,CACF,CAAC;EACH;EAEA,MAAMqC,UAAU,GAAGzD,aAAa,CAC9BK,UAAU,CAACG,MAAM,EACjBH,UAAU,CAACI,SAAS,EACpBJ,UAAU,CAACK,cAAc,EACzB4C,QAAQ,EACRlC,SAAS,CAACsC,YACZ,CAAC;EACD,MAAMC,IAAI,GAAGf,SAAS;EAEtB,QAAQxB,SAAS,CAACA,SAAS;IACzB,KAAK/B,iBAAiB,CAACuE,KAAK;MAC1B,OAAOC,aAAa,CAACxD,UAAU,EAAEiD,QAAQ,EAAEtC,SAAS,EAAE2C,IAAI,EAAEF,UAAU,CAAC;IAEzE,KAAKpE,iBAAiB,CAACyE,QAAQ;MAC7B,OAAOC,qBAAqB,CAC1B1D,UAAU,EACViD,QAAQ,EACRtC,SAAS,EACT2C,IAAI,EACJF,UACF,CAAC;IAEH,KAAKpE,iBAAiB,CAAC2E,YAAY;MACjC;MACA;MACA,OAAOH,aAAa,CAACxD,UAAU,EAAEiD,QAAQ,EAAEtC,SAAS,EAAE2C,IAAI,EAAEF,UAAU,CAAC;EAC3E;AACF;AACA;AACA;AACA;AACA;;AAEA,SAASM,qBAAqBA,CAC5B1D,UAAU,EACV4D,UAAU,EACVC,WAAW,EACXP,IAAI,EACJQ,MAAM,EACN;EACA,OAAOjF,aAAa,CAClBiF,MAAM,CAACC,OAAO,CAAC,CAAC,EAChB,CAACC,OAAO,EAAE,CAACC,YAAY,EAAE/D,UAAU,CAAC,KAAK;IACvC,MAAMgE,SAAS,GAAGxF,OAAO,CAAC4E,IAAI,EAAEW,YAAY,EAAEL,UAAU,CAACpB,IAAI,CAAC;IAC9D,MAAMxB,MAAM,GAAGmD,YAAY,CACzBnE,UAAU,EACV4D,UAAU,EACVC,WAAW,EACX3D,UAAU,EACVgE,SACF,CAAC;IAED,IAAIlD,MAAM,KAAKuB,SAAS,EAAE;MACxB,OAAOyB,OAAO;IAChB;IAEA,IAAIxF,SAAS,CAACwC,MAAM,CAAC,EAAE;MACrB,OAAOA,MAAM,CAACE,IAAI,CAAEkD,cAAc,IAAK;QACrCJ,OAAO,CAACC,YAAY,CAAC,GAAGG,cAAc;QACtC,OAAOJ,OAAO;MAChB,CAAC,CAAC;IACJ;IAEAA,OAAO,CAACC,YAAY,CAAC,GAAGjD,MAAM;IAC9B,OAAOgD,OAAO;EAChB,CAAC,EACD/B,MAAM,CAACC,MAAM,CAAC,IAAI,CACpB,CAAC;AACH;AACA;AACA;AACA;AACA;;AAEA,SAASsB,aAAaA,CAACxD,UAAU,EAAE4D,UAAU,EAAEC,WAAW,EAAEP,IAAI,EAAEQ,MAAM,EAAE;EACxE,MAAME,OAAO,GAAG/B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACnC,IAAImC,eAAe,GAAG,KAAK;EAE3B,IAAI;IACF,KAAK,MAAM,CAACJ,YAAY,EAAE/D,UAAU,CAAC,IAAI4D,MAAM,CAACC,OAAO,CAAC,CAAC,EAAE;MACzD,MAAMG,SAAS,GAAGxF,OAAO,CAAC4E,IAAI,EAAEW,YAAY,EAAEL,UAAU,CAACpB,IAAI,CAAC;MAC9D,MAAMxB,MAAM,GAAGmD,YAAY,CACzBnE,UAAU,EACV4D,UAAU,EACVC,WAAW,EACX3D,UAAU,EACVgE,SACF,CAAC;MAED,IAAIlD,MAAM,KAAKuB,SAAS,EAAE;QACxByB,OAAO,CAACC,YAAY,CAAC,GAAGjD,MAAM;QAE9B,IAAIxC,SAAS,CAACwC,MAAM,CAAC,EAAE;UACrBqD,eAAe,GAAG,IAAI;QACxB;MACF;IACF;EACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;IACd,IAAIgD,eAAe,EAAE;MACnB;MACA,OAAOzF,gBAAgB,CAACoF,OAAO,CAAC,CAACM,OAAO,CAAC,MAAM;QAC7C,MAAMjD,KAAK;MACb,CAAC,CAAC;IACJ;IAEA,MAAMA,KAAK;EACb,CAAC,CAAC;;EAEF,IAAI,CAACgD,eAAe,EAAE;IACpB,OAAOL,OAAO;EAChB,CAAC,CAAC;EACF;EACA;;EAEA,OAAOpF,gBAAgB,CAACoF,OAAO,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASG,YAAYA,CAACnE,UAAU,EAAE4D,UAAU,EAAEW,MAAM,EAAErE,UAAU,EAAEoD,IAAI,EAAE;EACtE,IAAIkB,iBAAiB;EAErB,MAAMC,QAAQ,GAAGC,WAAW,CAAC1E,UAAU,CAACG,MAAM,EAAEyD,UAAU,EAAE1D,UAAU,CAAC,CAAC,CAAC,CAAC;EAE1E,IAAI,CAACuE,QAAQ,EAAE;IACb;EACF;EAEA,MAAMxE,UAAU,GAAGwE,QAAQ,CAACE,IAAI;EAChC,MAAMC,SAAS,GACb,CAACJ,iBAAiB,GAAGC,QAAQ,CAACI,OAAO,MAAM,IAAI,IAC/CL,iBAAiB,KAAK,KAAK,CAAC,GACxBA,iBAAiB,GACjBxE,UAAU,CAAC8B,aAAa;EAC9B,MAAMgD,IAAI,GAAGC,gBAAgB,CAC3B/E,UAAU,EACVyE,QAAQ,EACRvE,UAAU,EACV0D,UAAU,EACVN,IACF,CAAC,CAAC,CAAC;;EAEH,IAAI;IACF;IACA;IACA;IACA,MAAM/C,IAAI,GAAGT,iBAAiB,CAC5B2E,QAAQ,EACRvE,UAAU,CAAC,CAAC,CAAC,EACbF,UAAU,CAACK,cACb,CAAC,CAAC,CAAC;IACH;IACA;;IAEA,MAAMuB,YAAY,GAAG5B,UAAU,CAAC4B,YAAY;IAC5C,MAAMZ,MAAM,GAAG4D,SAAS,CAACL,MAAM,EAAEhE,IAAI,EAAEqB,YAAY,EAAEkD,IAAI,CAAC;IAC1D,IAAIE,SAAS;IAEb,IAAIxG,SAAS,CAACwC,MAAM,CAAC,EAAE;MACrBgE,SAAS,GAAGhE,MAAM,CAACE,IAAI,CAAE+D,QAAQ,IAC/BC,aAAa,CAAClF,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAE4E,IAAI,EAAExB,IAAI,EAAE2B,QAAQ,CACxE,CAAC;IACH,CAAC,MAAM;MACLD,SAAS,GAAGE,aAAa,CACvBlF,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MACF,CAAC;IACH;IAEA,IAAIxC,SAAS,CAACwG,SAAS,CAAC,EAAE;MACxB;MACA;MACA,OAAOA,SAAS,CAAC9D,IAAI,CAACqB,SAAS,EAAG4C,QAAQ,IAAK;QAC7C,MAAM9D,KAAK,GAAGtC,YAAY,CAACoG,QAAQ,EAAEjF,UAAU,EAAEvB,WAAW,CAAC2E,IAAI,CAAC,CAAC;QACnE,OAAO8B,gBAAgB,CAAC/D,KAAK,EAAEpB,UAAU,EAAED,UAAU,CAAC;MACxD,CAAC,CAAC;IACJ;IAEA,OAAOgF,SAAS;EAClB,CAAC,CAAC,OAAOG,QAAQ,EAAE;IACjB,MAAM9D,KAAK,GAAGtC,YAAY,CAACoG,QAAQ,EAAEjF,UAAU,EAAEvB,WAAW,CAAC2E,IAAI,CAAC,CAAC;IACnE,OAAO8B,gBAAgB,CAAC/D,KAAK,EAAEpB,UAAU,EAAED,UAAU,CAAC;EACxD;AACF;AACA;AACA;AACA;;AAEA,OAAO,SAAS+E,gBAAgBA,CAC9B/E,UAAU,EACVyE,QAAQ,EACRvE,UAAU,EACV0D,UAAU,EACVN,IAAI,EACJ;EACA;EACA;EACA,OAAO;IACL+B,SAAS,EAAEZ,QAAQ,CAACjC,IAAI;IACxBtC,UAAU;IACVD,UAAU,EAAEwE,QAAQ,CAACE,IAAI;IACzBf,UAAU;IACVN,IAAI;IACJnD,MAAM,EAAEH,UAAU,CAACG,MAAM;IACzBC,SAAS,EAAEJ,UAAU,CAACI,SAAS;IAC/BO,SAAS,EAAEX,UAAU,CAACW,SAAS;IAC/BI,SAAS,EAAEf,UAAU,CAACe,SAAS;IAC/BV,cAAc,EAAEL,UAAU,CAACK;EAC7B,CAAC;AACH;AAEA,SAAS+E,gBAAgBA,CAAC/D,KAAK,EAAEpB,UAAU,EAAED,UAAU,EAAE;EACvD;EACA;EACA,IAAIX,aAAa,CAACY,UAAU,CAAC,EAAE;IAC7B,MAAMoB,KAAK;EACb,CAAC,CAAC;EACF;;EAEArB,UAAU,CAACc,MAAM,CAACQ,IAAI,CAACD,KAAK,CAAC;EAC7B,OAAO,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS6D,aAAaA,CAAClF,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAE4E,IAAI,EAAExB,IAAI,EAAEtC,MAAM,EAAE;EAC7E;EACA,IAAIA,MAAM,YAAYQ,KAAK,EAAE;IAC3B,MAAMR,MAAM;EACd,CAAC,CAAC;EACF;;EAEA,IAAI3B,aAAa,CAACY,UAAU,CAAC,EAAE;IAC7B,MAAM+E,SAAS,GAAGE,aAAa,CAC7BlF,UAAU,EACVC,UAAU,CAACqF,MAAM,EACjBpF,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MACF,CAAC;IAED,IAAIgE,SAAS,KAAK,IAAI,EAAE;MACtB,MAAM,IAAIxD,KAAK,CACZ,6CAA4CsD,IAAI,CAAClB,UAAU,CAACpB,IAAK,IAAGsC,IAAI,CAACO,SAAU,GACtF,CAAC;IACH;IAEA,OAAOL,SAAS;EAClB,CAAC,CAAC;;EAEF,IAAIhE,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF,IAAI5B,UAAU,CAACa,UAAU,CAAC,EAAE;IAC1B,OAAOsF,iBAAiB,CACtBvF,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MACF,CAAC;EACH,CAAC,CAAC;EACF;;EAEA,IAAI7B,UAAU,CAACc,UAAU,CAAC,EAAE;IAC1B,OAAOuF,iBAAiB,CAACvF,UAAU,EAAEe,MAAM,CAAC;EAC9C,CAAC,CAAC;EACF;;EAEA,IAAI9B,cAAc,CAACe,UAAU,CAAC,EAAE;IAC9B,OAAOwF,qBAAqB,CAC1BzF,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MACF,CAAC;EACH,CAAC,CAAC;;EAEF,IAAI1B,YAAY,CAACW,UAAU,CAAC,EAAE;IAC5B,OAAOyF,mBAAmB,CACxB1F,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MACF,CAAC;EACH;EACA;EACA;;EAEA,KAAK,IACH3C,SAAS,CACP,KAAK,EACL,mDAAmD,GAAGD,OAAO,CAAC6B,UAAU,CAC1E,CAAC;AACL;AACA;AACA;AACA;AACA;;AAEA,SAASsF,iBAAiBA,CACxBvF,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MAAM,EACN;EACA,IAAI,CAAC1C,gBAAgB,CAAC0C,MAAM,CAAC,EAAE;IAC7B,MAAM,IAAIlC,YAAY,CACnB,sDAAqDgG,IAAI,CAAClB,UAAU,CAACpB,IAAK,IAAGsC,IAAI,CAACO,SAAU,IAC/F,CAAC;EACH,CAAC,CAAC;EACF;;EAEA,MAAMM,QAAQ,GAAG1F,UAAU,CAACqF,MAAM;EAClC,IAAIjB,eAAe,GAAG,KAAK;EAC3B,MAAMuB,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAAC9E,MAAM,EAAE,CAAC+E,IAAI,EAAEC,KAAK,KAAK;IAC3D;IACA;IACA,MAAMC,QAAQ,GAAGvH,OAAO,CAAC4E,IAAI,EAAE0C,KAAK,EAAEzD,SAAS,CAAC;IAEhD,IAAI;MACF,IAAI2D,aAAa;MAEjB,IAAI1H,SAAS,CAACuH,IAAI,CAAC,EAAE;QACnBG,aAAa,GAAGH,IAAI,CAAC7E,IAAI,CAAE+D,QAAQ,IACjCC,aAAa,CACXlF,UAAU,EACV2F,QAAQ,EACRzF,UAAU,EACV4E,IAAI,EACJmB,QAAQ,EACRhB,QACF,CACF,CAAC;MACH,CAAC,MAAM;QACLiB,aAAa,GAAGhB,aAAa,CAC3BlF,UAAU,EACV2F,QAAQ,EACRzF,UAAU,EACV4E,IAAI,EACJmB,QAAQ,EACRF,IACF,CAAC;MACH;MAEA,IAAIvH,SAAS,CAAC0H,aAAa,CAAC,EAAE;QAC5B7B,eAAe,GAAG,IAAI,CAAC,CAAC;QACxB;;QAEA,OAAO6B,aAAa,CAAChF,IAAI,CAACqB,SAAS,EAAG4C,QAAQ,IAAK;UACjD,MAAM9D,KAAK,GAAGtC,YAAY,CACxBoG,QAAQ,EACRjF,UAAU,EACVvB,WAAW,CAACsH,QAAQ,CACtB,CAAC;UACD,OAAOb,gBAAgB,CAAC/D,KAAK,EAAEsE,QAAQ,EAAE3F,UAAU,CAAC;QACtD,CAAC,CAAC;MACJ;MAEA,OAAOkG,aAAa;IACtB,CAAC,CAAC,OAAOf,QAAQ,EAAE;MACjB,MAAM9D,KAAK,GAAGtC,YAAY,CAACoG,QAAQ,EAAEjF,UAAU,EAAEvB,WAAW,CAACsH,QAAQ,CAAC,CAAC;MACvE,OAAOb,gBAAgB,CAAC/D,KAAK,EAAEsE,QAAQ,EAAE3F,UAAU,CAAC;IACtD;EACF,CAAC,CAAC;EACF,OAAOqE,eAAe,GAAG8B,OAAO,CAACC,GAAG,CAACR,gBAAgB,CAAC,GAAGA,gBAAgB;AAC3E;AACA;AACA;AACA;AACA;;AAEA,SAASJ,iBAAiBA,CAACvF,UAAU,EAAEe,MAAM,EAAE;EAC7C,MAAMqF,gBAAgB,GAAGpG,UAAU,CAACqG,SAAS,CAACtF,MAAM,CAAC;EAErD,IAAIqF,gBAAgB,IAAI,IAAI,EAAE;IAC5B,MAAM,IAAI7E,KAAK,CACZ,cAAapD,OAAO,CAAC6B,UAAU,CAAE,cAAa7B,OAAO,CAAC4C,MAAM,CAAE,SAAQ,GACpE,wCAAuC5C,OAAO,CAACiI,gBAAgB,CAAE,EACtE,CAAC;EACH;EAEA,OAAOA,gBAAgB;AACzB;AACA;AACA;AACA;AACA;;AAEA,SAASZ,qBAAqBA,CAC5BzF,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MAAM,EACN;EACA,IAAIuF,qBAAqB;EAEzB,MAAMC,aAAa,GACjB,CAACD,qBAAqB,GAAGtG,UAAU,CAACwG,WAAW,MAAM,IAAI,IACzDF,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrBvG,UAAU,CAAC+B,YAAY;EAC7B,MAAMH,YAAY,GAAG5B,UAAU,CAAC4B,YAAY;EAC5C,MAAM8E,WAAW,GAAGF,aAAa,CAACxF,MAAM,EAAEY,YAAY,EAAEkD,IAAI,EAAE7E,UAAU,CAAC;EAEzE,IAAIzB,SAAS,CAACkI,WAAW,CAAC,EAAE;IAC1B,OAAOA,WAAW,CAACxF,IAAI,CAAEyF,mBAAmB,IAC1CjB,mBAAmB,CACjB1F,UAAU,EACV4G,sBAAsB,CACpBD,mBAAmB,EACnB3G,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJ9D,MACF,CAAC,EACDd,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MACF,CACF,CAAC;EACH;EAEA,OAAO0E,mBAAmB,CACxB1F,UAAU,EACV4G,sBAAsB,CACpBF,WAAW,EACX1G,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJ9D,MACF,CAAC,EACDd,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MACF,CAAC;AACH;AAEA,SAAS4F,sBAAsBA,CAC7BC,eAAe,EACf7G,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJ9D,MAAM,EACN;EACA,IAAI6F,eAAe,IAAI,IAAI,EAAE;IAC3B,MAAM,IAAI/H,YAAY,CACnB,kBAAiBmB,UAAU,CAACuC,IAAK,0DAAyDsC,IAAI,CAAClB,UAAU,CAACpB,IAAK,IAAGsC,IAAI,CAACO,SAAU,kBAAiBpF,UAAU,CAACuC,IAAK,6GAA4G,EAC/QtC,UACF,CAAC;EACH,CAAC,CAAC;EACF;;EAEA,IAAIZ,YAAY,CAACuH,eAAe,CAAC,EAAE;IACjC,MAAM,IAAI/H,YAAY,CACpB,4HACF,CAAC;EACH;EAEA,IAAI,OAAO+H,eAAe,KAAK,QAAQ,EAAE;IACvC,MAAM,IAAI/H,YAAY,CACnB,kBAAiBmB,UAAU,CAACuC,IAAK,0DAAyDsC,IAAI,CAAClB,UAAU,CAACpB,IAAK,IAAGsC,IAAI,CAACO,SAAU,SAAQ,GACvI,SAAQjH,OAAO,CAAC4C,MAAM,CAAE,eAAc5C,OAAO,CAACyI,eAAe,CAAE,IACpE,CAAC;EACH;EAEA,MAAMH,WAAW,GAAG1G,UAAU,CAACG,MAAM,CAAC2G,OAAO,CAACD,eAAe,CAAC;EAE9D,IAAIH,WAAW,IAAI,IAAI,EAAE;IACvB,MAAM,IAAI5H,YAAY,CACnB,kBAAiBmB,UAAU,CAACuC,IAAK,6BAA4BqE,eAAgB,0CAAyC,EACvH;MACE1D,KAAK,EAAEjD;IACT,CACF,CAAC;EACH;EAEA,IAAI,CAACZ,YAAY,CAACoH,WAAW,CAAC,EAAE;IAC9B,MAAM,IAAI5H,YAAY,CACnB,kBAAiBmB,UAAU,CAACuC,IAAK,wCAAuCqE,eAAgB,IAAG,EAC5F;MACE1D,KAAK,EAAEjD;IACT,CACF,CAAC;EACH;EAEA,IAAI,CAACF,UAAU,CAACG,MAAM,CAAC4G,SAAS,CAAC9G,UAAU,EAAEyG,WAAW,CAAC,EAAE;IACzD,MAAM,IAAI5H,YAAY,CACnB,wBAAuB4H,WAAW,CAAClE,IAAK,iCAAgCvC,UAAU,CAACuC,IAAK,IAAG,EAC5F;MACEW,KAAK,EAAEjD;IACT,CACF,CAAC;EACH;EAEA,OAAOwG,WAAW;AACpB;AACA;AACA;AACA;;AAEA,SAAShB,mBAAmBA,CAC1B1F,UAAU,EACVC,UAAU,EACVC,UAAU,EACV4E,IAAI,EACJxB,IAAI,EACJtC,MAAM,EACN;EACA;EACA,MAAMgG,aAAa,GAAGpH,gBAAgB,CAACI,UAAU,EAAEC,UAAU,EAAEC,UAAU,CAAC,CAAC,CAAC;EAC5E;EACA;;EAEA,IAAID,UAAU,CAACgH,QAAQ,EAAE;IACvB,MAAMA,QAAQ,GAAGhH,UAAU,CAACgH,QAAQ,CAACjG,MAAM,EAAEhB,UAAU,CAAC4B,YAAY,EAAEkD,IAAI,CAAC;IAE3E,IAAItG,SAAS,CAACyI,QAAQ,CAAC,EAAE;MACvB,OAAOA,QAAQ,CAAC/F,IAAI,CAAEgG,gBAAgB,IAAK;QACzC,IAAI,CAACA,gBAAgB,EAAE;UACrB,MAAMC,sBAAsB,CAAClH,UAAU,EAAEe,MAAM,EAAEd,UAAU,CAAC;QAC9D;QAEA,OAAOsD,aAAa,CAClBxD,UAAU,EACVC,UAAU,EACVe,MAAM,EACNsC,IAAI,EACJ0D,aACF,CAAC;MACH,CAAC,CAAC;IACJ;IAEA,IAAI,CAACC,QAAQ,EAAE;MACb,MAAME,sBAAsB,CAAClH,UAAU,EAAEe,MAAM,EAAEd,UAAU,CAAC;IAC9D;EACF;EAEA,OAAOsD,aAAa,CAACxD,UAAU,EAAEC,UAAU,EAAEe,MAAM,EAAEsC,IAAI,EAAE0D,aAAa,CAAC;AAC3E;AAEA,SAASG,sBAAsBA,CAAClH,UAAU,EAAEe,MAAM,EAAEd,UAAU,EAAE;EAC9D,OAAO,IAAIpB,YAAY,CACpB,2BAA0BmB,UAAU,CAACuC,IAAK,cAAapE,OAAO,CAAC4C,MAAM,CAAE,GAAE,EAC1E;IACEmC,KAAK,EAAEjD;EACT,CACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAM8C,mBAAmB,GAAG,SAAAA,CACjCP,KAAK,EACLb,YAAY,EACZkD,IAAI,EACJsC,YAAY,EACZ;EACA;EACA,IAAI7I,YAAY,CAACkE,KAAK,CAAC,IAAI,OAAOA,KAAK,CAAC4E,UAAU,KAAK,QAAQ,EAAE;IAC/D,OAAO5E,KAAK,CAAC4E,UAAU;EACzB,CAAC,CAAC;;EAEF,MAAMC,aAAa,GAAGxC,IAAI,CAAC3E,MAAM,CAACoH,gBAAgB,CAACH,YAAY,CAAC;EAChE,MAAMI,uBAAuB,GAAG,EAAE;EAElC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,aAAa,CAAC7G,MAAM,EAAEgH,CAAC,EAAE,EAAE;IAC7C,MAAM9C,IAAI,GAAG2C,aAAa,CAACG,CAAC,CAAC;IAE7B,IAAI9C,IAAI,CAACsC,QAAQ,EAAE;MACjB,MAAMS,cAAc,GAAG/C,IAAI,CAACsC,QAAQ,CAACxE,KAAK,EAAEb,YAAY,EAAEkD,IAAI,CAAC;MAE/D,IAAItG,SAAS,CAACkJ,cAAc,CAAC,EAAE;QAC7BF,uBAAuB,CAACC,CAAC,CAAC,GAAGC,cAAc;MAC7C,CAAC,MAAM,IAAIA,cAAc,EAAE;QACzB,OAAO/C,IAAI,CAACnC,IAAI;MAClB;IACF;EACF;EAEA,IAAIgF,uBAAuB,CAAC/G,MAAM,EAAE;IAClC,OAAO0F,OAAO,CAACC,GAAG,CAACoB,uBAAuB,CAAC,CAACtG,IAAI,CAAEyG,eAAe,IAAK;MACpE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,eAAe,CAAClH,MAAM,EAAEgH,CAAC,EAAE,EAAE;QAC/C,IAAIE,eAAe,CAACF,CAAC,CAAC,EAAE;UACtB,OAAOH,aAAa,CAACG,CAAC,CAAC,CAACjF,IAAI;QAC9B;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMO,oBAAoB,GAAG,SAAAA,CAClCwB,MAAM,EACNhE,IAAI,EACJqB,YAAY,EACZkD,IAAI,EACJ;EACA;EACA,IAAIvG,YAAY,CAACgG,MAAM,CAAC,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IACxD,MAAMqD,QAAQ,GAAGrD,MAAM,CAACO,IAAI,CAACO,SAAS,CAAC;IAEvC,IAAI,OAAOuC,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAOrD,MAAM,CAACO,IAAI,CAACO,SAAS,CAAC,CAAC9E,IAAI,EAAEqB,YAAY,EAAEkD,IAAI,CAAC;IACzD;IAEA,OAAO8C,QAAQ;EACjB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASlD,WAAWA,CAACvE,MAAM,EAAEyD,UAAU,EAAEiE,SAAS,EAAE;EACzD,MAAMxC,SAAS,GAAGwC,SAAS,CAACrF,IAAI,CAACC,KAAK;EAEtC,IACE4C,SAAS,KAAK9F,kBAAkB,CAACiD,IAAI,IACrCrC,MAAM,CAAC2H,YAAY,CAAC,CAAC,KAAKlE,UAAU,EACpC;IACA,OAAOrE,kBAAkB;EAC3B,CAAC,MAAM,IACL8F,SAAS,KAAK7F,gBAAgB,CAACgD,IAAI,IACnCrC,MAAM,CAAC2H,YAAY,CAAC,CAAC,KAAKlE,UAAU,EACpC;IACA,OAAOpE,gBAAgB;EACzB,CAAC,MAAM,IAAI6F,SAAS,KAAK5F,oBAAoB,CAAC+C,IAAI,EAAE;IAClD,OAAO/C,oBAAoB;EAC7B;EAEA,OAAOmE,UAAU,CAACmE,SAAS,CAAC,CAAC,CAAC1C,SAAS,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}