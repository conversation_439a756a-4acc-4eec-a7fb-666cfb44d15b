{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { GraphQLList, GraphQLNonNull } from '../type/definition.mjs';\nexport function typeFromAST(schema, typeNode) {\n  switch (typeNode.kind) {\n    case Kind.LIST_TYPE:\n      {\n        const innerType = typeFromAST(schema, typeNode.type);\n        return innerType && new GraphQLList(innerType);\n      }\n    case Kind.NON_NULL_TYPE:\n      {\n        const innerType = typeFromAST(schema, typeNode.type);\n        return innerType && new GraphQLNonNull(innerType);\n      }\n    case Kind.NAMED_TYPE:\n      return schema.getType(typeNode.name.value);\n  }\n}", "map": {"version": 3, "names": ["Kind", "GraphQLList", "GraphQLNonNull", "typeFromAST", "schema", "typeNode", "kind", "LIST_TYPE", "innerType", "type", "NON_NULL_TYPE", "NAMED_TYPE", "getType", "name", "value"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/utilities/typeFromAST.mjs"], "sourcesContent": ["import { Kind } from '../language/kinds.mjs';\nimport { GraphQLList, GraphQLNonNull } from '../type/definition.mjs';\nexport function typeFromAST(schema, typeNode) {\n  switch (typeNode.kind) {\n    case Kind.LIST_TYPE: {\n      const innerType = typeFromAST(schema, typeNode.type);\n      return innerType && new GraphQLList(innerType);\n    }\n\n    case Kind.NON_NULL_TYPE: {\n      const innerType = typeFromAST(schema, typeNode.type);\n      return innerType && new GraphQLNonNull(innerType);\n    }\n\n    case Kind.NAMED_TYPE:\n      return schema.getType(typeNode.name.value);\n  }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,WAAW,EAAEC,cAAc,QAAQ,wBAAwB;AACpE,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,QAAQA,QAAQ,CAACC,IAAI;IACnB,KAAKN,IAAI,CAACO,SAAS;MAAE;QACnB,MAAMC,SAAS,GAAGL,WAAW,CAACC,MAAM,EAAEC,QAAQ,CAACI,IAAI,CAAC;QACpD,OAAOD,SAAS,IAAI,IAAIP,WAAW,CAACO,SAAS,CAAC;MAChD;IAEA,KAAKR,IAAI,CAACU,aAAa;MAAE;QACvB,MAAMF,SAAS,GAAGL,WAAW,CAACC,MAAM,EAAEC,QAAQ,CAACI,IAAI,CAAC;QACpD,OAAOD,SAAS,IAAI,IAAIN,cAAc,CAACM,SAAS,CAAC;MACnD;IAEA,KAAKR,IAAI,CAACW,UAAU;MAClB,OAAOP,MAAM,CAACQ,OAAO,CAACP,QAAQ,CAACQ,IAAI,CAACC,KAAK,CAAC;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}