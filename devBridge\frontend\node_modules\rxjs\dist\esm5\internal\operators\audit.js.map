{"version": 3, "file": "audit.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/audit.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AA+ChE,MAAM,UAAU,KAAK,CAAI,gBAAoD;IAC3E,OAAO,OAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,SAAS,GAAa,IAAI,CAAC;QAC/B,IAAI,kBAAkB,GAA2B,IAAI,CAAC;QACtD,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,IAAM,WAAW,GAAG;YAClB,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,WAAW,EAAE,CAAC;YAClC,kBAAkB,GAAG,IAAI,CAAC;YAC1B,IAAI,QAAQ,EAAE;gBACZ,QAAQ,GAAG,KAAK,CAAC;gBACjB,IAAM,KAAK,GAAG,SAAU,CAAC;gBACzB,SAAS,GAAG,IAAI,CAAC;gBACjB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACxB;YACD,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QACtC,CAAC,CAAC;QAEF,IAAM,eAAe,GAAG;YACtB,kBAAkB,GAAG,IAAI,CAAC;YAC1B,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QACtC,CAAC,CAAC;QAEF,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,UAAC,KAAK;YACJ,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,kBAAkB,EAAE;gBACvB,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAC1C,CAAC,kBAAkB,GAAG,wBAAwB,CAAC,UAAU,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC,CAC1F,CAAC;aACH;QACH,CAAC,EACD;YACE,UAAU,GAAG,IAAI,CAAC;YAClB,CAAC,CAAC,QAAQ,IAAI,CAAC,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC3F,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}