{"name": "@npmcli/promise-spawn", "version": "6.0.2", "files": ["bin/", "lib/"], "main": "./lib/index.js", "description": "spawn processes the way the npm cli likes to do", "repository": {"type": "git", "url": "https://github.com/npm/promise-spawn.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "minipass": "^4.0.0", "spawk": "^1.7.1", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0"}, "dependencies": {"which": "^3.0.0"}}