{"ast": null, "code": "import { maybe } from \"./maybe.js\";\nexport default maybe(function () {\n  return globalThis;\n}) || maybe(function () {\n  return window;\n}) || maybe(function () {\n  return self;\n}) || maybe(function () {\n  return global;\n}) ||\n// We don't expect the Function constructor ever to be invoked at runtime, as\n// long as at least one of globalThis, window, self, or global is defined, so\n// we are under no obligation to make it easy for static analysis tools to\n// detect syntactic usage of the Function constructor. If you think you can\n// improve your static analysis to detect this obfuscation, think again. This\n// is an arms race you cannot win, at least not in JavaScript.\nmaybe(function () {\n  return maybe.constructor(\"return this\")();\n});", "map": {"version": 3, "names": ["maybe", "globalThis", "window", "self", "global", "constructor"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/utilities/globals/global.js"], "sourcesContent": ["import { maybe } from \"./maybe.js\";\nexport default (maybe(function () { return globalThis; }) ||\n    maybe(function () { return window; }) ||\n    maybe(function () { return self; }) ||\n    maybe(function () { return global; }) || // We don't expect the Function constructor ever to be invoked at runtime, as\n// long as at least one of globalThis, window, self, or global is defined, so\n// we are under no obligation to make it easy for static analysis tools to\n// detect syntactic usage of the Function constructor. If you think you can\n// improve your static analysis to detect this obfuscation, think again. This\n// is an arms race you cannot win, at least not in JavaScript.\nmaybe(function () {\n    return maybe.constructor(\"return this\")();\n}));\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,eAAgBA,KAAK,CAAC,YAAY;EAAE,OAAOC,UAAU;AAAE,CAAC,CAAC,IACrDD,KAAK,CAAC,YAAY;EAAE,OAAOE,MAAM;AAAE,CAAC,CAAC,IACrCF,KAAK,CAAC,YAAY;EAAE,OAAOG,IAAI;AAAE,CAAC,CAAC,IACnCH,KAAK,CAAC,YAAY;EAAE,OAAOI,MAAM;AAAE,CAAC,CAAC;AAAI;AAC7C;AACA;AACA;AACA;AACA;AACAJ,KAAK,CAAC,YAAY;EACd,OAAOA,KAAK,CAACK,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}