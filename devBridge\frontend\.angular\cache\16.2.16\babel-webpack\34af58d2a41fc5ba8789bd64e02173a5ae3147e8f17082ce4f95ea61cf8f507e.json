{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nfunction EquipeComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"h3\", 33);\n    i0.ɵɵtext(6, \"Erreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 34);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_25_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.error = \"\");\n    });\n    i0.ɵɵelement(10, \"i\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.error);\n  }\n}\nfunction EquipeComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"div\", 39)(3, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"i\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 44);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 45);\n    i0.ɵɵtext(6, \" Commencez par cr\\u00E9er votre premi\\u00E8re \\u00E9quipe pour organiser votre travail collaboratif. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 46);\n    i0.ɵɵelement(8, \"i\", 24);\n    i0.ɵɵtext(9, \" Cr\\u00E9er ma premi\\u00E8re \\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_44_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"div\", 50);\n    i0.ɵɵelementStart(3, \"div\", 51)(4, \"div\", 52)(5, \"div\", 32)(6, \"h3\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 54);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 55)(11, \"div\", 56);\n    i0.ɵɵelement(12, \"i\", 57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 58)(14, \"div\", 59);\n    i0.ɵɵelement(15, \"i\", 60);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 59);\n    i0.ɵɵelement(19, \"i\", 61);\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\", 62)(23, \"div\", 63)(24, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_44_div_1_Template_button_click_24_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const equipe_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.editEquipe(equipe_r8));\n    });\n    i0.ɵɵelement(25, \"i\", 65);\n    i0.ɵɵtext(26, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_44_div_1_Template_button_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const equipe_r8 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.showMembreModal(equipe_r8));\n    });\n    i0.ɵɵelement(28, \"i\", 67);\n    i0.ɵɵtext(29, \" Membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_44_div_1_Template_button_click_30_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const equipe_r8 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r8._id && ctx_r12.deleteEquipe(equipe_r8._id));\n    });\n    i0.ɵɵelement(31, \"i\", 69);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r8 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r8.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r8.description || \"Aucune description disponible\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Admin: \", equipe_r8.admin || \"Non d\\u00E9fini\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", (equipe_r8.members == null ? null : equipe_r8.members.length) || 0, \" membre\", ((equipe_r8.members == null ? null : equipe_r8.members.length) || 0) !== 1 ? \"s\" : \"\", \"\");\n  }\n}\nfunction EquipeComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, EquipeComponent_div_44_div_1_Template, 32, 5, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes)(\"ngForTrackBy\", ctx_r3.trackByEquipeId);\n  }\n}\nfunction EquipeComponent_div_45_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 17)(2, \"div\", 104);\n    i0.ɵɵelement(3, \"i\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"p\", 106);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 107);\n    i0.ɵɵtext(8, \"Membre de l'\\u00E9quipe\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_45_div_23_div_1_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const membreId_r18 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.removeMembreFromEquipe(ctx_r19.selectedEquipe._id, ctx_r19.getMemberId(membreId_r18)));\n    });\n    i0.ɵɵelement(10, \"i\", 109);\n    i0.ɵɵtext(11, \" Retirer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const membreId_r18 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(membreId_r18);\n  }\n}\nfunction EquipeComponent_div_45_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtemplate(1, EquipeComponent_div_45_div_23_div_1_Template, 12, 1, \"div\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.selectedEquipe.members)(\"ngForTrackBy\", ctx_r13.trackByMemberId);\n  }\n}\nfunction EquipeComponent_div_45_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"div\", 111);\n    i0.ɵɵelement(2, \"i\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 113);\n    i0.ɵɵtext(4, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 114);\n    i0.ɵɵtext(6, \"Ajoutez des membres pour commencer \\u00E0 collaborer\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_45_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.closeMemberModal($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 71);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_45_Template_div_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵelement(3, \"div\", 50);\n    i0.ɵɵelementStart(4, \"div\", 51)(5, \"div\", 72)(6, \"div\", 17)(7, \"div\", 73);\n    i0.ɵɵelement(8, \"i\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\")(10, \"h3\", 75);\n    i0.ɵɵtext(11, \" Gestion des Membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 76);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_45_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.closeMemberModal());\n    });\n    i0.ɵɵelement(15, \"i\", 78);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 79)(17, \"div\", 8)(18, \"h4\", 80);\n    i0.ɵɵelement(19, \"i\", 61);\n    i0.ɵɵtext(20, \" Membres Actuels \");\n    i0.ɵɵelementStart(21, \"span\", 81);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, EquipeComponent_div_45_div_23_Template, 2, 2, \"div\", 82);\n    i0.ɵɵtemplate(24, EquipeComponent_div_45_ng_template_24_Template, 7, 0, \"ng-template\", null, 83, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 28)(27, \"h4\", 80);\n    i0.ɵɵelement(28, \"i\", 84);\n    i0.ɵɵtext(29, \" Ajouter un Membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 85)(31, \"div\", 86)(32, \"div\", 87);\n    i0.ɵɵelement(33, \"input\", 88, 89);\n    i0.ɵɵelementStart(35, \"div\", 90);\n    i0.ɵɵelement(36, \"i\", 91);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_45_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const _r16 = i0.ɵɵreference(34);\n      const ctx_r25 = i0.ɵɵnextContext();\n      ctx_r25.addMembreToEquipe(ctx_r25.selectedEquipe._id, _r16.value);\n      return i0.ɵɵresetView(_r16.value = \"\");\n    });\n    i0.ɵɵelement(38, \"i\", 24);\n    i0.ɵɵtext(39, \" Ajouter \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 93)(41, \"div\", 94)(42, \"div\", 95);\n    i0.ɵɵelement(43, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 32)(45, \"h5\", 97);\n    i0.ɵɵtext(46, \" Information importante \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\", 76);\n    i0.ɵɵtext(48, \" Pour ajouter un membre, vous devez d'abord cr\\u00E9er le membre dans la section des membres. Utilisez l'ID exact du membre pour l'ajouter \\u00E0 cette \\u00E9quipe. \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(49, \"div\", 98)(50, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_45_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.closeMemberModal());\n    });\n    i0.ɵɵelement(51, \"i\", 100);\n    i0.ɵɵtext(52, \" Fermer \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r14 = i0.ɵɵreference(25);\n    const _r16 = i0.ɵɵreference(34);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"hidden\", !ctx_r4.showMemberModal);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \\u00C9quipe: \", ctx_r4.selectedEquipe.name, \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r4.selectedEquipe.members == null ? null : ctx_r4.selectedEquipe.members.length) || 0, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedEquipe.members && ctx_r4.selectedEquipe.members.length > 0)(\"ngIfElse\", _r14);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.selectedEquipe || !ctx_r4.selectedEquipe._id || !_r16.value);\n  }\n}\nexport class EquipeComponent {\n  constructor(equipeService, membreService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.equipes = [];\n    this.selectedEquipe = null;\n    this.membres = [];\n    this.loading = false;\n    this.error = '';\n    this.showMemberModal = false;\n  }\n  // Méthode utilitaire pour extraire l'ID d'un membre\n  getMemberId(membre) {\n    if (typeof membre === 'string') {\n      return membre;\n    }\n    return membre._id || membre.id || '';\n  }\n  ngOnInit() {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: data => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: data => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: response => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = {\n          name: '',\n          description: ''\n        }; // Clear input\n        this.loading = false;\n        // Afficher un message de succès temporaire\n        this.showSuccessMessage('Équipe créée avec succès!');\n        this.error = ''; // Effacer les erreurs précédentes\n      },\n\n      error: error => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n  editEquipe(equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: updatedEquipe => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = {\n            name: '',\n            description: ''\n          };\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          this.showSuccessMessage('Équipe mise à jour avec succès!');\n        },\n        error: error => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: response => {\n          console.log('Team deleted successfully:', response);\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n          }\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          this.showSuccessMessage('Équipe supprimée avec succès');\n        },\n        error: error => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  showMembreModal(equipe) {\n    this.selectedEquipe = equipe;\n    this.showMemberModal = true;\n    // Prevent body scroll when modal is open\n    document.body.style.overflow = 'hidden';\n  }\n  closeMemberModal(event) {\n    if (event) {\n      // Only close if clicking on backdrop, not on modal content\n      const target = event.target;\n      if (!target.closest('.bg-white')) {\n        return;\n      }\n    }\n    this.showMemberModal = false;\n    this.selectedEquipe = null;\n    // Restore body scroll\n    document.body.style.overflow = 'auto';\n  }\n  // Scroll to form when \"New Team\" button is clicked\n  scrollToForm() {\n    const formElement = document.getElementById('teamForm');\n    if (formElement) {\n      formElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n      // Focus on the name input after scrolling\n      setTimeout(() => {\n        const nameInput = document.getElementById('teamName');\n        if (nameInput) {\n          nameInput.focus();\n        }\n      }, 500);\n    }\n  }\n  // TrackBy functions for better performance\n  trackByEquipeId(index, equipe) {\n    return equipe._id || index;\n  }\n  trackByMemberId(index, membreId) {\n    if (typeof membreId === 'string') {\n      return membreId;\n    }\n    return membreId._id || membreId.id || index;\n  }\n  // Show success message with modern notification\n  showSuccessMessage(message) {\n    // Create a temporary success notification\n    const notification = document.createElement('div');\n    notification.className = 'fixed top-4 right-4 z-50 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 shadow-lg transform translate-x-full transition-transform duration-300';\n    notification.innerHTML = `\n      <div class=\"flex items-start\">\n        <div class=\"text-green-500 dark:text-green-400 mr-3 mt-0.5\">\n          <i class=\"fas fa-check-circle\"></i>\n        </div>\n        <div class=\"flex-1\">\n          <h3 class=\"font-semibold text-green-800 dark:text-green-300 mb-1\">Succès</h3>\n          <p class=\"text-green-700 dark:text-green-400 text-sm\">${message}</p>\n        </div>\n        <button onclick=\"this.parentElement.parentElement.remove()\" class=\"text-green-500 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 ml-3\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    `;\n    document.body.appendChild(notification);\n    // Animate in\n    setTimeout(() => {\n      notification.style.transform = 'translateX(0)';\n    }, 100);\n    // Auto remove after 5 seconds\n    setTimeout(() => {\n      notification.style.transform = 'translateX(100%)';\n      setTimeout(() => {\n        if (notification.parentElement) {\n          notification.remove();\n        }\n      }, 300);\n    }, 5000);\n  }\n  addMembreToEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      this.error = 'L\\'ID du membre est requis';\n      return;\n    }\n    this.loading = true;\n    // Create a proper Membre object that matches what the API expects\n    const membre = {\n      id: membreId\n    };\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: response => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n        // Afficher un message de succès\n        this.showSuccessMessage('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: error => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      this.error = 'ID du membre non défini';\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: response => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: error => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function EquipeComponent_Factory(t) {\n      return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeComponent,\n      selectors: [[\"app-equipe\"]],\n      decls: 46,\n      vars: 10,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-96\", \"h-96\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#7826b5]/5\", \"to-transparent\", \"dark:from-[#4f5fad]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.02]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"relative\", \"z-10\", \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"py-8\"], [1, \"mb-8\"], [1, \"text-center\", \"mb-6\"], [1, \"text-4xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-3\", \"tracking-wide\"], [1, \"fas\", \"fa-users\", \"mr-3\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-lg\", \"max-w-2xl\", \"mx-auto\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"flex justify-center items-center py-12\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-2xl\", \"shadow-lg\", \"dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)]\", \"border\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\", \"p-6\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"justify-between\", \"items-center\", \"gap-4\"], [1, \"flex\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mr-4\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"flex\", \"items-center\", \"gap-3\"], [1, \"flex\", \"items-center\", \"px-4\", \"py-2\", \"bg-[#6d6870]/10\", \"dark:bg-[#e0e0e0]/10\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"border\", \"border-[#6d6870]/20\", \"dark:border-[#e0e0e0]/20\", \"hover:border-[#4f5fad]/40\", \"dark:hover:border-[#00f7ff]/40\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-2\"], [\"routerLink\", \"/equipes/creation\", 1, \"flex\", \"items-center\", \"px-6\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)]\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\", \"id\", \"membreModal\", 3, \"hidden\", \"click\", 4, \"ngIf\"], [1, \"mb-6\"], [1, \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-xl\", \"p-4\", \"flex\", \"items-start\"], [1, \"text-red-500\", \"dark:text-red-400\", \"mr-3\", \"mt-0.5\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-red-800\", \"dark:text-red-300\", \"mb-1\"], [1, \"text-red-700\", \"dark:text-red-400\", \"text-sm\"], [1, \"text-red-500\", \"dark:text-red-400\", \"hover:text-red-700\", \"dark:hover:text-red-300\", \"ml-3\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-12\"], [1, \"relative\"], [1, \"w-16\", \"h-16\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-full\"], [1, \"absolute\", \"top-0\", \"left-0\", \"w-16\", \"h-16\", \"border-4\", \"border-transparent\", \"border-t-[#4f5fad]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"text-center\", \"py-16\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"mb-6\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-[#7826b5]/20\", \"dark:from-[#00f7ff]/20\", \"dark:to-[#4f5fad]/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-users\", \"text-3xl\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-3\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-6\", \"max-w-md\", \"mx-auto\"], [\"routerLink\", \"/equipes/creation\", 1, \"inline-flex\", \"items-center\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"group bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl dark:hover:shadow-[0_25px_60px_rgba(0,0,0,0.4)]\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"group\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-2xl\", \"shadow-lg\", \"dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)]\", \"border\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:scale-[1.02]\", \"hover:shadow-xl\", \"dark:hover:shadow-[0_25px_60px_rgba(0,0,0,0.4)]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"p-6\", \"pb-4\"], [1, \"flex\", \"items-start\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-colors\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"line-clamp-2\"], [1, \"ml-4\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-[#7826b5]/20\", \"dark:from-[#00f7ff]/20\", \"dark:to-[#4f5fad]/20\", \"rounded-xl\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-users\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\"], [1, \"flex\", \"items-center\", \"justify-between\", \"text-sm\"], [1, \"flex\", \"items-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-user-tie\", \"mr-2\"], [1, \"fas\", \"fa-users\", \"mr-2\"], [1, \"px-6\", \"pb-6\"], [1, \"flex\", \"items-center\", \"gap-2\"], [1, \"flex-1\", \"flex\", \"items-center\", \"justify-center\", \"px-3\", \"py-2\", \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#00f7ff]/20\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-200\", \"text-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"flex-1\", \"flex\", \"items-center\", \"justify-center\", \"px-3\", \"py-2\", \"bg-[#7826b5]/10\", \"dark:bg-[#4f5fad]/10\", \"text-[#7826b5]\", \"dark:text-[#4f5fad]\", \"hover:bg-[#7826b5]/20\", \"dark:hover:bg-[#4f5fad]/20\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-200\", \"text-sm\", 3, \"click\"], [1, \"fas\", \"fa-users-cog\", \"mr-2\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"px-3\", \"py-2\", \"bg-red-50\", \"dark:bg-red-900/20\", \"text-red-600\", \"dark:text-red-400\", \"hover:bg-red-100\", \"dark:hover:bg-red-900/30\", \"rounded-lg\", \"transition-all\", \"duration-200\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"id\", \"membreModal\", 1, \"fixed\", \"inset-0\", \"bg-black/50\", \"backdrop-blur-sm\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", 3, \"click\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-2xl\", \"shadow-2xl\", \"dark:shadow-[0_25px_60px_rgba(0,0,0,0.4)]\", \"border\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\", \"w-full\", \"max-w-2xl\", \"max-h-[90vh]\", \"overflow-hidden\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-[#7826b5]/20\", \"dark:from-[#00f7ff]/20\", \"dark:to-[#4f5fad]/20\", \"rounded-xl\", \"flex\", \"items-center\", \"justify-center\", \"mr-4\"], [1, \"fas\", \"fa-users-cog\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-1\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\"], [\"title\", \"Fermer\", 1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-red-500\", \"dark:hover:text-red-400\", \"transition-colors\", \"p-2\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-lg\"], [1, \"px-6\", \"pb-6\", \"max-h-[calc(90vh-120px)]\", \"overflow-y-auto\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-4\", \"flex\", \"items-center\"], [1, \"ml-2\", \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [\"class\", \"space-y-3\", 4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [1, \"space-y-4\"], [1, \"flex\", \"gap-3\"], [1, \"flex-1\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"ID du membre \\u00E0 ajouter\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-[#f8f9fa]\", \"dark:bg-[#2a2a2a]\", \"border\", \"border-[#e0e0e0]\", \"dark:border-[#404040]\", \"rounded-xl\", \"text-[#2c3e50]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6c757d]\", \"dark:placeholder-[#888]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", \"duration-200\"], [\"membreIdInput\", \"\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-3\"], [1, \"fas\", \"fa-user\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", 3, \"disabled\", \"click\"], [1, \"bg-[#4f5fad]/5\", \"dark:bg-[#00f7ff]/5\", \"rounded-xl\", \"p-4\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"items-start\"], [1, \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mr-3\", \"mt-0.5\"], [1, \"fas\", \"fa-info-circle\"], [1, \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"mb-1\"], [1, \"flex\", \"justify-end\", \"pt-4\", \"border-t\", \"border-[#e0e0e0]\", \"dark:border-[#404040]\"], [1, \"px-6\", \"py-3\", \"bg-[#6d6870]/10\", \"dark:bg-[#e0e0e0]/10\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-200\", \"border\", \"border-[#6d6870]/20\", \"dark:border-[#e0e0e0]/20\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [1, \"space-y-3\"], [\"class\", \"flex items-center justify-between p-4 bg-[#f8f9fa] dark:bg-[#2a2a2a] rounded-xl border border-[#e0e0e0] dark:border-[#404040]\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-4\", \"bg-[#f8f9fa]\", \"dark:bg-[#2a2a2a]\", \"rounded-xl\", \"border\", \"border-[#e0e0e0]\", \"dark:border-[#404040]\"], [1, \"w-10\", \"h-10\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-[#7826b5]/20\", \"dark:from-[#00f7ff]/20\", \"dark:to-[#4f5fad]/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mr-3\"], [1, \"fas\", \"fa-user\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"text-sm\"], [1, \"font-medium\", \"text-[#2c3e50]\", \"dark:text-[#e0e0e0]\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"title\", \"Retirer ce membre\", 1, \"px-3\", \"py-2\", \"bg-red-50\", \"dark:bg-red-900/20\", \"text-red-600\", \"dark:text-red-400\", \"hover:bg-red-100\", \"dark:hover:bg-red-900/30\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-sm\", \"font-medium\", 3, \"click\"], [1, \"fas\", \"fa-user-minus\", \"mr-1\"], [1, \"text-center\", \"py-8\"], [1, \"w-16\", \"h-16\", \"mx-auto\", \"mb-4\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-user-slash\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-xl\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"font-medium\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"mt-1\"]],\n      template: function EquipeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\", 9)(20, \"h1\", 10);\n          i0.ɵɵelement(21, \"i\", 11);\n          i0.ɵɵtext(22, \" Gestion des \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 12);\n          i0.ɵɵtext(24, \" Cr\\u00E9ez, g\\u00E9rez et organisez vos \\u00E9quipes de travail en toute simplicit\\u00E9 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(25, EquipeComponent_div_25_Template, 11, 1, \"div\", 13);\n          i0.ɵɵtemplate(26, EquipeComponent_div_26_Template, 4, 0, \"div\", 14);\n          i0.ɵɵelementStart(27, \"div\", 8)(28, \"div\", 15)(29, \"div\", 16)(30, \"div\", 17)(31, \"h2\", 18);\n          i0.ɵɵtext(32, \" \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\", 19);\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 20)(36, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_36_listener() {\n            return ctx.loadEquipes();\n          });\n          i0.ɵɵelement(37, \"i\", 22);\n          i0.ɵɵtext(38, \" Rafra\\u00EEchir \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 23);\n          i0.ɵɵelement(40, \"i\", 24);\n          i0.ɵɵtext(41, \" Nouvelle \\u00C9quipe \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"div\", 8);\n          i0.ɵɵtemplate(43, EquipeComponent_div_43_Template, 10, 0, \"div\", 25);\n          i0.ɵɵtemplate(44, EquipeComponent_div_44_Template, 2, 2, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, EquipeComponent_div_45_Template, 53, 7, \"div\", 27);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate2(\" \", ctx.equipes.length, \" \\u00E9quipe\", ctx.equipes.length !== 1 ? \"s\" : \"\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"animate-spin\", ctx.loading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.equipes.length === 0 && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.equipes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipe);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.RouterLink],\n      styles: [\"\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 20px rgba(79, 95, 173, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 30px rgba(79, 95, 173, 0.5);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-5px);\\n  }\\n}\\n\\n\\n\\n.team-card[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.5s ease-out;\\n}\\n\\n.team-card[_ngcontent-%COMP%]:hover {\\n  animation: _ngcontent-%COMP%_float 2s ease-in-out infinite;\\n}\\n\\n\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  animation: spin 1s linear infinite;\\n}\\n\\n\\n\\n.line-clamp-2[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n\\n.line-clamp-3[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n\\n.custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(79, 95, 173, 0.3);\\n  border-radius: 3px;\\n}\\n\\n.custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(79, 95, 173, 0.5);\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   .custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(0, 247, 255, 0.3);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.btn-shine[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.btn-shine[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    rgba(255, 255, 255, 0.2),\\n    transparent\\n  );\\n  transition: left 0.5s;\\n}\\n\\n.btn-shine[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n\\n\\n.focus-ring[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #4f5fad;\\n  outline-offset: 2px;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .focus-ring[_ngcontent-%COMP%]:focus {\\n  outline-color: #00f7ff;\\n}\\n\\n\\n\\n@media (max-width: 640px) {\\n  .team-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .team-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(1, 1fr);\\n  }\\n}\\n\\n@media (min-width: 769px) and (max-width: 1024px) {\\n  .team-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n\\n@media (min-width: 1025px) {\\n  .team-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n\\n\\n\\n.team-card[_ngcontent-%COMP%]:nth-child(1) { animation-delay: 0.1s; }\\n.team-card[_ngcontent-%COMP%]:nth-child(2) { animation-delay: 0.2s; }\\n.team-card[_ngcontent-%COMP%]:nth-child(3) { animation-delay: 0.3s; }\\n.team-card[_ngcontent-%COMP%]:nth-child(4) { animation-delay: 0.4s; }\\n.team-card[_ngcontent-%COMP%]:nth-child(5) { animation-delay: 0.5s; }\\n.team-card[_ngcontent-%COMP%]:nth-child(6) { animation-delay: 0.6s; }\\n\\n\\n\\n.modal-backdrop[_ngcontent-%COMP%] {\\n  backdrop-filter: blur(8px);\\n  -webkit-backdrop-filter: blur(8px);\\n}\\n\\n\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.15);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 4px 12px rgba(0, 247, 255, 0.15);\\n}\\n\\n\\n\\n.input-success[_ngcontent-%COMP%] {\\n  border-color: #10b981;\\n  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);\\n}\\n\\n.input-error[_ngcontent-%COMP%] {\\n  border-color: #ef4444;\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n\\n\\n.notification-enter[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.3s ease-out;\\n}\\n\\n.notification-leave[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.3s ease-out reverse;\\n}\\n\\n\\n\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%], *[_ngcontent-%COMP%]::before, *[_ngcontent-%COMP%]::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n\\n\\n\\n@media (prefers-contrast: high) {\\n  .team-card[_ngcontent-%COMP%] {\\n    border: 2px solid;\\n  }\\n\\n  .btn-primary[_ngcontent-%COMP%] {\\n    border: 2px solid;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeComponent_div_25_Template_button_click_9_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "error", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "EquipeComponent_div_44_div_1_Template_button_click_24_listener", "restoredCtx", "_r10", "equipe_r8", "$implicit", "ctx_r9", "editEquipe", "EquipeComponent_div_44_div_1_Template_button_click_27_listener", "ctx_r11", "showMembreModal", "EquipeComponent_div_44_div_1_Template_button_click_30_listener", "ctx_r12", "_id", "deleteEquipe", "ɵɵtextInterpolate1", "name", "description", "admin", "ɵɵtextInterpolate2", "members", "length", "ɵɵtemplate", "EquipeComponent_div_44_div_1_Template", "ɵɵproperty", "ctx_r3", "equipes", "trackByEquipeId", "EquipeComponent_div_45_div_23_div_1_Template_button_click_9_listener", "_r20", "membreId_r18", "ctx_r19", "removeMembreFromEquipe", "selectedEquipe", "getMemberId", "EquipeComponent_div_45_div_23_div_1_Template", "ctx_r13", "trackByMemberId", "EquipeComponent_div_45_Template_div_click_0_listener", "$event", "_r22", "ctx_r21", "closeMemberModal", "EquipeComponent_div_45_Template_div_click_1_listener", "stopPropagation", "EquipeComponent_div_45_Template_button_click_14_listener", "ctx_r24", "EquipeComponent_div_45_div_23_Template", "EquipeComponent_div_45_ng_template_24_Template", "ɵɵtemplateRefExtractor", "EquipeComponent_div_45_Template_button_click_37_listener", "_r16", "ɵɵreference", "ctx_r25", "addMembreToEquipe", "value", "EquipeComponent_div_45_Template_button_click_50_listener", "ctx_r26", "ɵɵclassProp", "ctx_r4", "showMemberModal", "_r14", "EquipeComponent", "constructor", "equipeService", "membreService", "membres", "loading", "membre", "id", "ngOnInit", "loadEquipes", "loadMembres", "getEquipes", "subscribe", "next", "data", "console", "log", "message", "getMembres", "addEquipe", "newEquipe", "response", "showSuccessMessage", "equipe", "isEditing", "cancelEdit", "updateSelectedEquipe", "updateEquipe", "updatedEquipe", "confirm", "document", "body", "style", "overflow", "event", "target", "closest", "scrollToForm", "formElement", "getElementById", "scrollIntoView", "behavior", "block", "setTimeout", "nameInput", "focus", "index", "membreId", "notification", "createElement", "className", "innerHTML", "append<PERSON><PERSON><PERSON>", "transform", "parentElement", "remove", "teamId", "trim", "find", "e", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "selectors", "decls", "vars", "consts", "template", "EquipeComponent_Template", "rf", "ctx", "EquipeComponent_div_25_Template", "EquipeComponent_div_26_Template", "EquipeComponent_Template_button_click_36_listener", "EquipeComponent_div_43_Template", "EquipeComponent_div_44_Template", "EquipeComponent_div_45_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.ts", "C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { EquipeService } from 'src/app/services/equipe.service';\r\nimport { MembreService } from 'src/app/services/membre.service';\r\nimport { Equipe } from 'src/app/models/equipe.model';\r\nimport { Membre } from 'src/app/models/membre.model';\r\nimport { forkJoin } from 'rxjs';\r\n\r\n// Add Bootstrap type declaration\r\ndeclare global {\r\n  interface Window {\r\n    bootstrap: any;\r\n  }\r\n}\r\n\r\n@Component({\r\n  selector: 'app-equipe',\r\n  templateUrl: './equipe.component.html',\r\n  styleUrls: ['./equipe.component.css'],\r\n})\r\nexport class EquipeComponent implements OnInit {\r\n  equipes: Equipe[] = [];\r\n  selectedEquipe: Equipe | null = null;\r\n  membres: Membre[] = [];\r\n  loading = false;\r\n  error = '';\r\n  showMemberModal = false;\r\n\r\n  constructor(\r\n    private equipeService: EquipeService,\r\n    private membreService: MembreService\r\n  ) {}\r\n\r\n  // Méthode utilitaire pour extraire l'ID d'un membre\r\n  getMemberId(membre: string | any): string {\r\n    if (typeof membre === 'string') {\r\n      return membre;\r\n    }\r\n    return membre._id || membre.id || '';\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadEquipes();\r\n    this.loadMembres();\r\n  }\r\n\r\n  loadEquipes() {\r\n    this.loading = true;\r\n    this.equipeService.getEquipes().subscribe({\r\n      next: (data) => {\r\n        console.log('Loaded equipes:', data);\r\n        this.equipes = data;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading equipes:', error);\r\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadMembres() {\r\n    this.loading = true;\r\n    this.membreService.getMembres().subscribe({\r\n      next: (data) => {\r\n        console.log('Loaded membres:', data);\r\n        this.membres = data;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading membres:', error);\r\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  addEquipe() {\r\n    console.log('Adding equipe:', this.newEquipe);\r\n\r\n    if (!this.newEquipe.name) {\r\n      console.error('Team name is required');\r\n      this.error = 'Le nom de l\\'équipe est requis';\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.error = '';\r\n\r\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\r\n      next: (response) => {\r\n        console.log('Equipe added successfully:', response);\r\n        this.loadEquipes();\r\n        this.newEquipe = { name: '', description: '' }; // Clear input\r\n        this.loading = false;\r\n\r\n        // Afficher un message de succès temporaire\r\n        this.showSuccessMessage('Équipe créée avec succès!');\r\n        this.error = ''; // Effacer les erreurs précédentes\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding equipe:', error);\r\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  editEquipe(equipe: Equipe) {\r\n    this.isEditing = true;\r\n    // Créer une copie profonde pour éviter de modifier l'objet original\r\n    this.newEquipe = {\r\n      _id: equipe._id,\r\n      name: equipe.name || '',\r\n      description: equipe.description || '',\r\n      admin: equipe.admin,\r\n      members: equipe.members ? [...equipe.members] : []\r\n    };\r\n  }\r\n\r\n  cancelEdit() {\r\n    this.isEditing = false;\r\n    this.newEquipe = { name: '', description: '' };\r\n    this.error = ''; // Effacer les erreurs\r\n  }\r\n\r\n  updateSelectedEquipe() {\r\n    if (!this.newEquipe.name) {\r\n      console.error('Team name is required');\r\n      this.error = 'Le nom de l\\'équipe est requis';\r\n      return;\r\n    }\r\n\r\n    if (this.newEquipe._id) {\r\n      this.loading = true;\r\n      this.error = '';\r\n\r\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\r\n        next: (updatedEquipe) => {\r\n          console.log('Team updated successfully:', updatedEquipe);\r\n          this.loadEquipes();\r\n          this.isEditing = false;\r\n          this.newEquipe = { name: '', description: '' };\r\n          this.loading = false;\r\n\r\n          // Afficher un message de succès temporaire\r\n          this.showSuccessMessage('Équipe mise à jour avec succès!');\r\n        },\r\n        error: (error) => {\r\n          console.error('Error updating team:', error);\r\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n          this.loading = false;\r\n        }\r\n      });\r\n    } else {\r\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\r\n    }\r\n  }\r\n\r\n  deleteEquipe(id: string) {\r\n    if (!id) {\r\n      console.error('ID is undefined');\r\n      this.error = 'ID de l\\'équipe non défini';\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\r\n      this.loading = true;\r\n      this.error = '';\r\n\r\n      this.equipeService.deleteEquipe(id).subscribe({\r\n        next: (response) => {\r\n          console.log('Team deleted successfully:', response);\r\n\r\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\r\n          if (this.isEditing && this.newEquipe._id === id) {\r\n            this.isEditing = false;\r\n            this.newEquipe = { name: '', description: '' };\r\n          }\r\n\r\n          this.loadEquipes();\r\n          this.loading = false;\r\n\r\n          // Afficher un message de succès\r\n          this.showSuccessMessage('Équipe supprimée avec succès');\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting team:', error);\r\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  showMembreModal(equipe: Equipe) {\r\n    this.selectedEquipe = equipe;\r\n    this.showMemberModal = true;\r\n    // Prevent body scroll when modal is open\r\n    document.body.style.overflow = 'hidden';\r\n  }\r\n\r\n  closeMemberModal(event?: Event) {\r\n    if (event) {\r\n      // Only close if clicking on backdrop, not on modal content\r\n      const target = event.target as HTMLElement;\r\n      if (!target.closest('.bg-white')) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    this.showMemberModal = false;\r\n    this.selectedEquipe = null;\r\n    // Restore body scroll\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // Scroll to form when \"New Team\" button is clicked\r\n  scrollToForm() {\r\n    const formElement = document.getElementById('teamForm');\r\n    if (formElement) {\r\n      formElement.scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start'\r\n      });\r\n\r\n      // Focus on the name input after scrolling\r\n      setTimeout(() => {\r\n        const nameInput = document.getElementById('teamName') as HTMLInputElement;\r\n        if (nameInput) {\r\n          nameInput.focus();\r\n        }\r\n      }, 500);\r\n    }\r\n  }\r\n\r\n  // TrackBy functions for better performance\r\n  trackByEquipeId(index: number, equipe: Equipe): any {\r\n    return equipe._id || index;\r\n  }\r\n\r\n  trackByMemberId(index: number, membreId: string | any): any {\r\n    if (typeof membreId === 'string') {\r\n      return membreId;\r\n    }\r\n    return membreId._id || membreId.id || index;\r\n  }\r\n\r\n  // Show success message with modern notification\r\n  showSuccessMessage(message: string) {\r\n    // Create a temporary success notification\r\n    const notification = document.createElement('div');\r\n    notification.className = 'fixed top-4 right-4 z-50 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 shadow-lg transform translate-x-full transition-transform duration-300';\r\n    notification.innerHTML = `\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-green-500 dark:text-green-400 mr-3 mt-0.5\">\r\n          <i class=\"fas fa-check-circle\"></i>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <h3 class=\"font-semibold text-green-800 dark:text-green-300 mb-1\">Succès</h3>\r\n          <p class=\"text-green-700 dark:text-green-400 text-sm\">${message}</p>\r\n        </div>\r\n        <button onclick=\"this.parentElement.parentElement.remove()\" class=\"text-green-500 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 ml-3\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    `;\r\n\r\n    document.body.appendChild(notification);\r\n\r\n    // Animate in\r\n    setTimeout(() => {\r\n      notification.style.transform = 'translateX(0)';\r\n    }, 100);\r\n\r\n    // Auto remove after 5 seconds\r\n    setTimeout(() => {\r\n      notification.style.transform = 'translateX(100%)';\r\n      setTimeout(() => {\r\n        if (notification.parentElement) {\r\n          notification.remove();\r\n        }\r\n      }, 300);\r\n    }, 5000);\r\n  }\r\n\r\n  addMembreToEquipe(teamId: string | undefined, membreId: string) {\r\n    if (!teamId) {\r\n      console.error('Team ID is undefined');\r\n      this.error = 'ID de l\\'équipe non défini';\r\n      return;\r\n    }\r\n\r\n    if (!membreId || membreId.trim() === '') {\r\n      console.error('Member ID is empty');\r\n      this.error = 'L\\'ID du membre est requis';\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    // Create a proper Membre object that matches what the API expects\r\n    const membre: Membre = { id: membreId };\r\n\r\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\r\n      next: (response) => {\r\n        console.log('Member added successfully:', response);\r\n        this.loadEquipes();\r\n        this.loading = false;\r\n\r\n        // Afficher un message de succès\r\n        this.showSuccessMessage('Membre ajouté avec succès à l\\'équipe');\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding member:', error);\r\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {\r\n    if (!teamId) {\r\n      console.error('Team ID is undefined');\r\n      this.error = 'ID de l\\'équipe non défini';\r\n      return;\r\n    }\r\n\r\n    if (!membreId) {\r\n      console.error('Member ID is undefined');\r\n      this.error = 'ID du membre non défini';\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\r\n      this.loading = true;\r\n\r\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\r\n        next: (response) => {\r\n          console.log('Member removed successfully:', response);\r\n          this.loadEquipes();\r\n          this.loading = false;\r\n\r\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\r\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\r\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\r\n            if (updatedEquipe) {\r\n              this.selectedEquipe = updatedEquipe;\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error removing member:', error);\r\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\">\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div class=\"absolute top-[10%] left-[5%] w-96 h-96 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"></div>\r\n    <div class=\"absolute bottom-[10%] right-[5%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#7826b5]/5 to-transparent dark:from-[#4f5fad]/3 dark:to-transparent blur-3xl\"></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.02]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div class=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n\r\n    <!-- Header Section -->\r\n    <div class=\"mb-8\">\r\n      <div class=\"text-center mb-6\">\r\n        <h1 class=\"text-4xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-3 tracking-wide\">\r\n          <i class=\"fas fa-users mr-3\"></i>\r\n          Gestion des Équipes\r\n        </h1>\r\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-lg max-w-2xl mx-auto\">\r\n          Créez, gérez et organisez vos équipes de travail en toute simplicité\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error Message -->\r\n    <div *ngIf=\"error\" class=\"mb-6\">\r\n      <div class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 flex items-start\">\r\n        <div class=\"text-red-500 dark:text-red-400 mr-3 mt-0.5\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <h3 class=\"font-semibold text-red-800 dark:text-red-300 mb-1\">Erreur</h3>\r\n          <p class=\"text-red-700 dark:text-red-400 text-sm\">{{ error }}</p>\r\n        </div>\r\n        <button\r\n          (click)=\"error = ''\"\r\n          class=\"text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 ml-3\"\r\n        >\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading Indicator -->\r\n    <div *ngIf=\"loading\" class=\"flex justify-center items-center py-12\">\r\n      <div class=\"relative\">\r\n        <div class=\"w-16 h-16 border-4 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-full\"></div>\r\n        <div class=\"absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Action Bar -->\r\n    <div class=\"mb-8\">\r\n      <div class=\"bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 p-6\">\r\n        <div class=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\r\n          <div class=\"flex items-center\">\r\n            <h2 class=\"text-2xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mr-4\">\r\n              Équipes\r\n            </h2>\r\n            <span class=\"bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium\">\r\n              {{ equipes.length }} équipe{{ equipes.length !== 1 ? 's' : '' }}\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"flex items-center gap-3\">\r\n            <button\r\n              (click)=\"loadEquipes()\"\r\n              [disabled]=\"loading\"\r\n              class=\"flex items-center px-4 py-2 bg-[#6d6870]/10 dark:bg-[#e0e0e0]/10 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] rounded-xl font-medium transition-all duration-300 hover:scale-105 border border-[#6d6870]/20 dark:border-[#e0e0e0]/20 hover:border-[#4f5fad]/40 dark:hover:border-[#00f7ff]/40\"\r\n            >\r\n              <i class=\"fas fa-sync-alt mr-2\" [class.animate-spin]=\"loading\"></i>\r\n              Rafraîchir\r\n            </button>\r\n\r\n            <button\r\n              routerLink=\"/equipes/creation\"\r\n              class=\"flex items-center px-6 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)]\"\r\n            >\r\n              <i class=\"fas fa-plus mr-2\"></i>\r\n              Nouvelle Équipe\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Teams Grid -->\r\n    <div class=\"mb-8\">\r\n      <!-- Empty State -->\r\n      <div *ngIf=\"equipes.length === 0 && !loading\" class=\"text-center py-16\">\r\n        <div class=\"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-full flex items-center justify-center\">\r\n          <i class=\"fas fa-users text-3xl text-[#4f5fad] dark:text-[#00f7ff]\"></i>\r\n        </div>\r\n        <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-3\">\r\n          Aucune équipe trouvée\r\n        </h3>\r\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mb-6 max-w-md mx-auto\">\r\n          Commencez par créer votre première équipe pour organiser votre travail collaboratif.\r\n        </p>\r\n        <button\r\n          routerLink=\"/equipes/creation\"\r\n          class=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg\"\r\n        >\r\n          <i class=\"fas fa-plus mr-2\"></i>\r\n          Créer ma première équipe\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Teams Cards -->\r\n      <div *ngIf=\"equipes.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        <div\r\n          *ngFor=\"let equipe of equipes; trackBy: trackByEquipeId\"\r\n          class=\"group bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-lg dark:shadow-[0_20px_50px_rgba(0,0,0,0.3)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl dark:hover:shadow-[0_25px_60px_rgba(0,0,0,0.4)]\"\r\n        >\r\n          <!-- Card Header -->\r\n          <div class=\"relative\">\r\n            <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"></div>\r\n            <div class=\"p-6 pb-4\">\r\n              <div class=\"flex items-start justify-between mb-4\">\r\n                <div class=\"flex-1\">\r\n                  <h3 class=\"text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 group-hover:text-[#7826b5] dark:group-hover:text-[#4f5fad] transition-colors\">\r\n                    {{ equipe.name }}\r\n                  </h3>\r\n                  <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm line-clamp-2\">\r\n                    {{ equipe.description || 'Aucune description disponible' }}\r\n                  </p>\r\n                </div>\r\n                <div class=\"ml-4\">\r\n                  <div class=\"w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-xl flex items-center justify-center\">\r\n                    <i class=\"fas fa-users text-[#4f5fad] dark:text-[#00f7ff]\"></i>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Team Stats -->\r\n              <div class=\"flex items-center justify-between text-sm\">\r\n                <div class=\"flex items-center text-[#6d6870] dark:text-[#a0a0a0]\">\r\n                  <i class=\"fas fa-user-tie mr-2\"></i>\r\n                  <span>Admin: {{ equipe.admin || 'Non défini' }}</span>\r\n                </div>\r\n                <div class=\"flex items-center text-[#6d6870] dark:text-[#a0a0a0]\">\r\n                  <i class=\"fas fa-users mr-2\"></i>\r\n                  <span>{{ equipe.members?.length || 0 }} membre{{ (equipe.members?.length || 0) !== 1 ? 's' : '' }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Card Actions -->\r\n          <div class=\"px-6 pb-6\">\r\n            <div class=\"flex items-center gap-2\">\r\n              <button\r\n                (click)=\"editEquipe(equipe)\"\r\n                class=\"flex-1 flex items-center justify-center px-3 py-2 bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] hover:bg-[#4f5fad]/20 dark:hover:bg-[#00f7ff]/20 rounded-lg font-medium transition-all duration-200 text-sm\"\r\n              >\r\n                <i class=\"fas fa-edit mr-2\"></i>\r\n                Modifier\r\n              </button>\r\n\r\n              <button\r\n                (click)=\"showMembreModal(equipe)\"\r\n                class=\"flex-1 flex items-center justify-center px-3 py-2 bg-[#7826b5]/10 dark:bg-[#4f5fad]/10 text-[#7826b5] dark:text-[#4f5fad] hover:bg-[#7826b5]/20 dark:hover:bg-[#4f5fad]/20 rounded-lg font-medium transition-all duration-200 text-sm\"\r\n              >\r\n                <i class=\"fas fa-users-cog mr-2\"></i>\r\n                Membres\r\n              </button>\r\n\r\n              <button\r\n                (click)=\"equipe._id && deleteEquipe(equipe._id)\"\r\n                class=\"px-3 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200\"\r\n                title=\"Supprimer l'équipe\"\r\n              >\r\n                <i class=\"fas fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Modern Modal for Member Management -->\r\n    <div\r\n      *ngIf=\"selectedEquipe\"\r\n      class=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\r\n      id=\"membreModal\"\r\n      [class.hidden]=\"!showMemberModal\"\r\n      (click)=\"closeMemberModal($event)\"\r\n    >\r\n      <div\r\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-2xl dark:shadow-[0_25px_60px_rgba(0,0,0,0.4)] border border-[#4f5fad]/10 dark:border-[#00f7ff]/10 w-full max-w-2xl max-h-[90vh] overflow-hidden\"\r\n        (click)=\"$event.stopPropagation()\"\r\n      >\r\n\r\n        <!-- Modal Header -->\r\n        <div class=\"relative\">\r\n          <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"></div>\r\n          <div class=\"p-6 pb-4\">\r\n            <div class=\"flex items-center justify-between\">\r\n              <div class=\"flex items-center\">\r\n                <div class=\"w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-xl flex items-center justify-center mr-4\">\r\n                  <i class=\"fas fa-users-cog text-[#4f5fad] dark:text-[#00f7ff]\"></i>\r\n                </div>\r\n                <div>\r\n                  <h3 class=\"text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-1\">\r\n                    Gestion des Membres\r\n                  </h3>\r\n                  <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\r\n                    Équipe: {{ selectedEquipe.name }}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <button\r\n                (click)=\"closeMemberModal()\"\r\n                class=\"text-[#6d6870] dark:text-[#a0a0a0] hover:text-red-500 dark:hover:text-red-400 transition-colors p-2\"\r\n                title=\"Fermer\"\r\n              >\r\n                <i class=\"fas fa-times text-lg\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Modal Content -->\r\n        <div class=\"px-6 pb-6 max-h-[calc(90vh-120px)] overflow-y-auto\">\r\n\r\n          <!-- Current Members Section -->\r\n          <div class=\"mb-8\">\r\n            <h4 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-4 flex items-center\">\r\n              <i class=\"fas fa-users mr-2\"></i>\r\n              Membres Actuels\r\n              <span class=\"ml-2 bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-1 rounded-full text-sm font-medium\">\r\n                {{ selectedEquipe.members?.length || 0 }}\r\n              </span>\r\n            </h4>\r\n\r\n            <!-- Members List -->\r\n            <div *ngIf=\"selectedEquipe.members && selectedEquipe.members.length > 0; else noMembers\" class=\"space-y-3\">\r\n              <div\r\n                *ngFor=\"let membreId of selectedEquipe.members; trackBy: trackByMemberId\"\r\n                class=\"flex items-center justify-between p-4 bg-[#f8f9fa] dark:bg-[#2a2a2a] rounded-xl border border-[#e0e0e0] dark:border-[#404040]\"\r\n              >\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"w-10 h-10 bg-gradient-to-br from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 rounded-full flex items-center justify-center mr-3\">\r\n                    <i class=\"fas fa-user text-[#4f5fad] dark:text-[#00f7ff] text-sm\"></i>\r\n                  </div>\r\n                  <div>\r\n                    <p class=\"font-medium text-[#2c3e50] dark:text-[#e0e0e0]\">{{ membreId }}</p>\r\n                    <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Membre de l'équipe</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <button\r\n                  (click)=\"removeMembreFromEquipe(selectedEquipe._id, getMemberId(membreId))\"\r\n                  class=\"px-3 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200 text-sm font-medium\"\r\n                  title=\"Retirer ce membre\"\r\n                >\r\n                  <i class=\"fas fa-user-minus mr-1\"></i>\r\n                  Retirer\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <ng-template #noMembers>\r\n              <div class=\"text-center py-8\">\r\n                <div class=\"w-16 h-16 mx-auto mb-4 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-full flex items-center justify-center\">\r\n                  <i class=\"fas fa-user-slash text-[#6d6870] dark:text-[#a0a0a0] text-xl\"></i>\r\n                </div>\r\n                <p class=\"text-[#6d6870] dark:text-[#a0a0a0] font-medium\">Aucun membre dans cette équipe</p>\r\n                <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm mt-1\">Ajoutez des membres pour commencer à collaborer</p>\r\n              </div>\r\n            </ng-template>\r\n          </div>\r\n\r\n          <!-- Add Member Section -->\r\n          <div class=\"mb-6\">\r\n            <h4 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-4 flex items-center\">\r\n              <i class=\"fas fa-user-plus mr-2\"></i>\r\n              Ajouter un Membre\r\n            </h4>\r\n\r\n            <div class=\"space-y-4\">\r\n              <div class=\"flex gap-3\">\r\n                <div class=\"flex-1 relative\">\r\n                  <input\r\n                    #membreIdInput\r\n                    type=\"text\"\r\n                    placeholder=\"ID du membre à ajouter\"\r\n                    class=\"w-full px-4 py-3 bg-[#f8f9fa] dark:bg-[#2a2a2a] border border-[#e0e0e0] dark:border-[#404040] rounded-xl text-[#2c3e50] dark:text-[#e0e0e0] placeholder-[#6c757d] dark:placeholder-[#888] focus:outline-none focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all duration-200\"\r\n                  />\r\n                  <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                    <i class=\"fas fa-user text-[#6d6870] dark:text-[#a0a0a0]\"></i>\r\n                  </div>\r\n                </div>\r\n\r\n                <button\r\n                  (click)=\"addMembreToEquipe(selectedEquipe._id, membreIdInput.value); membreIdInput.value = ''\"\r\n                  [disabled]=\"!selectedEquipe || !selectedEquipe._id || !membreIdInput.value\"\r\n                  class=\"px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\r\n                >\r\n                  <i class=\"fas fa-plus mr-2\"></i>\r\n                  Ajouter\r\n                </button>\r\n              </div>\r\n\r\n              <div class=\"bg-[#4f5fad]/5 dark:bg-[#00f7ff]/5 rounded-xl p-4 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\">\r\n                <div class=\"flex items-start\">\r\n                  <div class=\"text-[#4f5fad] dark:text-[#00f7ff] mr-3 mt-0.5\">\r\n                    <i class=\"fas fa-info-circle\"></i>\r\n                  </div>\r\n                  <div class=\"flex-1\">\r\n                    <h5 class=\"font-semibold text-[#4f5fad] dark:text-[#00f7ff] text-sm mb-1\">\r\n                      Information importante\r\n                    </h5>\r\n                    <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\r\n                      Pour ajouter un membre, vous devez d'abord créer le membre dans la section des membres.\r\n                      Utilisez l'ID exact du membre pour l'ajouter à cette équipe.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Modal Actions -->\r\n          <div class=\"flex justify-end pt-4 border-t border-[#e0e0e0] dark:border-[#404040]\">\r\n            <button\r\n              (click)=\"closeMemberModal()\"\r\n              class=\"px-6 py-3 bg-[#6d6870]/10 dark:bg-[#e0e0e0]/10 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] rounded-xl font-medium transition-all duration-200 border border-[#6d6870]/20 dark:border-[#e0e0e0]/20\"\r\n            >\r\n              <i class=\"fas fa-times mr-2\"></i>\r\n              Fermer\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;ICyCIA,EAAA,CAAAC,cAAA,cAAgC;IAG1BD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAC4CD,EAAA,CAAAI,MAAA,aAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,YAAkD;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEnEH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,KAAA,GAAiB,EAAE;IAAA,EAAC;IAGpBZ,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IAP2CH,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAH,KAAA,CAAW;;;;;IAYnEZ,EAAA,CAAAC,cAAA,cAAoE;IAEhED,EAAA,CAAAE,SAAA,cAAgG;IAElGF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyCNH,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAE,SAAA,YAAwE;IAC1EF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAoE;IAClED,EAAA,CAAAI,MAAA,4GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IACCD,EAAA,CAAAE,SAAA,YAAgC;IAChCF,EAAA,CAAAI,MAAA,gDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAKTH,EAAA,CAAAC,cAAA,cAGC;IAGGD,EAAA,CAAAE,SAAA,cAAwI;IACxIF,EAAA,CAAAC,cAAA,cAAsB;IAIdD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAmE;IACjED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAE,SAAA,aAA+D;IACjEF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAuD;IAEnDD,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAyC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAExDH,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAA4F;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAOjHH,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAK,UAAA,mBAAAW,+DAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAU,MAAA,CAAAC,UAAA,CAAAH,SAAA,CAAkB;IAAA,EAAC;IAG5BnB,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAkB,+DAAA;MAAA,MAAAN,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAAxB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,OAAA,CAAAC,eAAA,CAAAN,SAAA,CAAuB;IAAA,EAAC;IAGjCnB,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAqB,+DAAA;MAAA,MAAAT,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAO,OAAA,GAAA3B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAQ,SAAA,CAAAS,GAAA,IAAcD,OAAA,CAAAE,YAAA,CAAAV,SAAA,CAAAS,GAAA,CAAwB;IAAA,EAAC;IAIhD5B,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IApDHH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA8B,kBAAA,MAAAX,SAAA,CAAAY,IAAA,MACF;IAEE/B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA8B,kBAAA,MAAAX,SAAA,CAAAa,WAAA,yCACF;IAaMhC,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAA8B,kBAAA,YAAAX,SAAA,CAAAc,KAAA,0BAAyC;IAIzCjC,EAAA,CAAAa,SAAA,GAA4F;IAA5Fb,EAAA,CAAAkC,kBAAA,MAAAf,SAAA,CAAAgB,OAAA,kBAAAhB,SAAA,CAAAgB,OAAA,CAAAC,MAAA,qBAAAjB,SAAA,CAAAgB,OAAA,kBAAAhB,SAAA,CAAAgB,OAAA,CAAAC,MAAA,6BAA4F;;;;;IAjC9GpC,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAqC,UAAA,IAAAC,qCAAA,mBAkEM;IACRtC,EAAA,CAAAG,YAAA,EAAM;;;;IAlEiBH,EAAA,CAAAa,SAAA,GAAY;IAAZb,EAAA,CAAAuC,UAAA,YAAAC,MAAA,CAAAC,OAAA,CAAY,iBAAAD,MAAA,CAAAE,eAAA;;;;;;IA+H3B1C,EAAA,CAAAC,cAAA,eAGC;IAGKD,EAAA,CAAAE,SAAA,aAAsE;IACxEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IACuDD,EAAA,CAAAI,MAAA,GAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC5EH,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAI,MAAA,8BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIhFH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAsC,qEAAA;MAAA,MAAA1B,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAqC,IAAA;MAAA,MAAAC,YAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAA0B,OAAA,GAAA9C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAmC,OAAA,CAAAC,sBAAA,CAAAD,OAAA,CAAAE,cAAA,CAAApB,GAAA,EAA2CkB,OAAA,CAAAG,WAAA,CAAAJ,YAAA,CAAqB,CAAC;IAAA,EAAC;IAI3E7C,EAAA,CAAAE,SAAA,cAAsC;IACtCF,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAZqDH,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAc,iBAAA,CAAA+B,YAAA,CAAc;;;;;IAVhF7C,EAAA,CAAAC,cAAA,eAA2G;IACzGD,EAAA,CAAAqC,UAAA,IAAAa,4CAAA,oBAsBM;IACRlD,EAAA,CAAAG,YAAA,EAAM;;;;IAtBmBH,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuC,UAAA,YAAAY,OAAA,CAAAH,cAAA,CAAAb,OAAA,CAA2B,iBAAAgB,OAAA,CAAAC,eAAA;;;;;IAyBlDpD,EAAA,CAAAC,cAAA,eAA8B;IAE1BD,EAAA,CAAAE,SAAA,aAA4E;IAC9EF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,0CAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC5FH,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAI,MAAA,2DAA+C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAxF1HH,EAAA,CAAAC,cAAA,cAMC;IADCD,EAAA,CAAAK,UAAA,mBAAAgD,qDAAAC,MAAA;MAAAtD,EAAA,CAAAO,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA6C,OAAA,CAAAC,gBAAA,CAAAH,MAAA,CAAwB;IAAA,EAAC;IAElCtD,EAAA,CAAAC,cAAA,cAGC;IADCD,EAAA,CAAAK,UAAA,mBAAAqD,qDAAAJ,MAAA;MAAA,OAASA,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAIlC3D,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAE,SAAA,cAAwI;IACxIF,EAAA,CAAAC,cAAA,cAAsB;IAIdD,EAAA,CAAAE,SAAA,YAAmE;IACrEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,6BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIRH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAuD,yDAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAAgD,IAAA;MAAA,MAAAM,OAAA,GAAA7D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAkD,OAAA,CAAAJ,gBAAA,EAAkB;IAAA,EAAC;IAI5BzD,EAAA,CAAAE,SAAA,aAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,eAAgE;IAK1DD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,yBACA;IAAAJ,EAAA,CAAAC,cAAA,gBAAsI;IACpID,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAqC,UAAA,KAAAyB,sCAAA,kBAwBM;IAEN9D,EAAA,CAAAqC,UAAA,KAAA0B,8CAAA,iCAAA/D,EAAA,CAAAgE,sBAAA,CAQc;IAChBhE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAE,SAAA,qBAKE;IACFF,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,SAAA,aAA8D;IAChEF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAA4D,yDAAA;MAAAjE,EAAA,CAAAO,aAAA,CAAAgD,IAAA;MAAA,MAAAW,IAAA,GAAAlE,EAAA,CAAAmE,WAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAAU,aAAA;MAAS0D,OAAA,CAAAC,iBAAA,CAAAD,OAAA,CAAApB,cAAA,CAAApB,GAAA,EAAAsC,IAAA,CAAAI,KAAA,CAA0D;MAAA,OAAEtE,EAAA,CAAAW,WAAA,CAAAuD,IAAA,CAAAI,KAAA,GAAsB,EAAE;IAAA,EAAC;IAI9FtE,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAAmH;IAG7GD,EAAA,CAAAE,SAAA,aAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAoB;IAEhBD,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,MAAA,6KAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAQdH,EAAA,CAAAC,cAAA,eAAmF;IAE/ED,EAAA,CAAAK,UAAA,mBAAAkE,yDAAA;MAAAvE,EAAA,CAAAO,aAAA,CAAAgD,IAAA;MAAA,MAAAiB,OAAA,GAAAxE,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA6D,OAAA,CAAAf,gBAAA,EAAkB;IAAA,EAAC;IAG5BzD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAnJfH,EAAA,CAAAyE,WAAA,YAAAC,MAAA,CAAAC,eAAA,CAAiC;IAsBnB3E,EAAA,CAAAa,SAAA,IACF;IADEb,EAAA,CAAA8B,kBAAA,mBAAA4C,MAAA,CAAA1B,cAAA,CAAAjB,IAAA,MACF;IAwBF/B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA8B,kBAAA,OAAA4C,MAAA,CAAA1B,cAAA,CAAAb,OAAA,kBAAAuC,MAAA,CAAA1B,cAAA,CAAAb,OAAA,CAAAC,MAAA,YACF;IAIIpC,EAAA,CAAAa,SAAA,GAAmE;IAAnEb,EAAA,CAAAuC,UAAA,SAAAmC,MAAA,CAAA1B,cAAA,CAAAb,OAAA,IAAAuC,MAAA,CAAA1B,cAAA,CAAAb,OAAA,CAAAC,MAAA,KAAmE,aAAAwC,IAAA;IA4DnE5E,EAAA,CAAAa,SAAA,IAA2E;IAA3Eb,EAAA,CAAAuC,UAAA,cAAAmC,MAAA,CAAA1B,cAAA,KAAA0B,MAAA,CAAA1B,cAAA,CAAApB,GAAA,KAAAsC,IAAA,CAAAI,KAAA,CAA2E;;;ADtS7F,OAAM,MAAOO,eAAe;EAQ1BC,YACUC,aAA4B,EAC5BC,aAA4B;IAD5B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAvC,OAAO,GAAa,EAAE;IACtB,KAAAO,cAAc,GAAkB,IAAI;IACpC,KAAAiC,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAtE,KAAK,GAAG,EAAE;IACV,KAAA+D,eAAe,GAAG,KAAK;EAKpB;EAEH;EACA1B,WAAWA,CAACkC,MAAoB;IAC9B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOA,MAAM;;IAEf,OAAOA,MAAM,CAACvD,GAAG,IAAIuD,MAAM,CAACC,EAAE,IAAI,EAAE;EACtC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,aAAa,CAACS,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAAClD,OAAO,GAAGkD,IAAI;QACnB,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB,CAAC;MACDtE,KAAK,EAAGA,KAAK,IAAI;QACfgF,OAAO,CAAChF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAACkF,OAAO;QACtE,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,aAAa,CAACe,UAAU,EAAE,CAACN,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACV,OAAO,GAAGU,IAAI;QACnB,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB,CAAC;MACDtE,KAAK,EAAGA,KAAK,IAAI;QACfgF,OAAO,CAAChF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAACkF,OAAO;QACtE,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAc,SAASA,CAAA;IACPJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACI,SAAS,CAAC;IAE7C,IAAI,CAAC,IAAI,CAACA,SAAS,CAAClE,IAAI,EAAE;MACxB6D,OAAO,CAAChF,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,CAACsE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACtE,KAAK,GAAG,EAAE;IAEf,IAAI,CAACmE,aAAa,CAACiB,SAAS,CAAC,IAAI,CAACC,SAAS,CAAC,CAACR,SAAS,CAAC;MACrDC,IAAI,EAAGQ,QAAQ,IAAI;QACjBN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEK,QAAQ,CAAC;QACnD,IAAI,CAACZ,WAAW,EAAE;QAClB,IAAI,CAACW,SAAS,GAAG;UAAElE,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE,CAAC,CAAC;QAChD,IAAI,CAACkD,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,CAACiB,kBAAkB,CAAC,2BAA2B,CAAC;QACpD,IAAI,CAACvF,KAAK,GAAG,EAAE,CAAC,CAAC;MACnB,CAAC;;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfgF,OAAO,CAAChF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAEkF,OAAO,IAAIlF,KAAK,CAACkF,OAAO,IAAI,eAAe,CAAC;QACrH,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA5D,UAAUA,CAAC8E,MAAc;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACJ,SAAS,GAAG;MACfrE,GAAG,EAAEwE,MAAM,CAACxE,GAAG;MACfG,IAAI,EAAEqE,MAAM,CAACrE,IAAI,IAAI,EAAE;MACvBC,WAAW,EAAEoE,MAAM,CAACpE,WAAW,IAAI,EAAE;MACrCC,KAAK,EAAEmE,MAAM,CAACnE,KAAK;MACnBE,OAAO,EAAEiE,MAAM,CAACjE,OAAO,GAAG,CAAC,GAAGiE,MAAM,CAACjE,OAAO,CAAC,GAAG;KACjD;EACH;EAEAmE,UAAUA,CAAA;IACR,IAAI,CAACD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACJ,SAAS,GAAG;MAAElE,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IAC9C,IAAI,CAACpB,KAAK,GAAG,EAAE,CAAC,CAAC;EACnB;;EAEA2F,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACN,SAAS,CAAClE,IAAI,EAAE;MACxB6D,OAAO,CAAChF,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACqF,SAAS,CAACrE,GAAG,EAAE;MACtB,IAAI,CAACsD,OAAO,GAAG,IAAI;MACnB,IAAI,CAACtE,KAAK,GAAG,EAAE;MAEf,IAAI,CAACmE,aAAa,CAACyB,YAAY,CAAC,IAAI,CAACP,SAAS,CAACrE,GAAG,EAAE,IAAI,CAACqE,SAAS,CAAC,CAACR,SAAS,CAAC;QAC5EC,IAAI,EAAGe,aAAa,IAAI;UACtBb,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEY,aAAa,CAAC;UACxD,IAAI,CAACnB,WAAW,EAAE;UAClB,IAAI,CAACe,SAAS,GAAG,KAAK;UACtB,IAAI,CAACJ,SAAS,GAAG;YAAElE,IAAI,EAAE,EAAE;YAAEC,WAAW,EAAE;UAAE,CAAE;UAC9C,IAAI,CAACkD,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,CAACiB,kBAAkB,CAAC,iCAAiC,CAAC;QAC5D,CAAC;QACDvF,KAAK,EAAGA,KAAK,IAAI;UACfgF,OAAO,CAAChF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAEkF,OAAO,IAAIlF,KAAK,CAACkF,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACtE,KAAK,GAAG,8CAA8C;;EAE/D;EAEAiB,YAAYA,CAACuD,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPQ,OAAO,CAAChF,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAI8F,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAACxB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACtE,KAAK,GAAG,EAAE;MAEf,IAAI,CAACmE,aAAa,CAAClD,YAAY,CAACuD,EAAE,CAAC,CAACK,SAAS,CAAC;QAC5CC,IAAI,EAAGQ,QAAQ,IAAI;UACjBN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEK,QAAQ,CAAC;UAEnD;UACA,IAAI,IAAI,CAACG,SAAS,IAAI,IAAI,CAACJ,SAAS,CAACrE,GAAG,KAAKwD,EAAE,EAAE;YAC/C,IAAI,CAACiB,SAAS,GAAG,KAAK;YACtB,IAAI,CAACJ,SAAS,GAAG;cAAElE,IAAI,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;;UAGhD,IAAI,CAACsD,WAAW,EAAE;UAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,CAACiB,kBAAkB,CAAC,8BAA8B,CAAC;QACzD,CAAC;QACDvF,KAAK,EAAGA,KAAK,IAAI;UACfgF,OAAO,CAAChF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAEkF,OAAO,IAAIlF,KAAK,CAACkF,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAzD,eAAeA,CAAC2E,MAAc;IAC5B,IAAI,CAACpD,cAAc,GAAGoD,MAAM;IAC5B,IAAI,CAACzB,eAAe,GAAG,IAAI;IAC3B;IACAgC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEArD,gBAAgBA,CAACsD,KAAa;IAC5B,IAAIA,KAAK,EAAE;MACT;MACA,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;MAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;QAChC;;;IAIJ,IAAI,CAACtC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC3B,cAAc,GAAG,IAAI;IAC1B;IACA2D,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAI,YAAYA,CAAA;IACV,MAAMC,WAAW,GAAGR,QAAQ,CAACS,cAAc,CAAC,UAAU,CAAC;IACvD,IAAID,WAAW,EAAE;MACfA,WAAW,CAACE,cAAc,CAAC;QACzBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;OACR,CAAC;MAEF;MACAC,UAAU,CAAC,MAAK;QACd,MAAMC,SAAS,GAAGd,QAAQ,CAACS,cAAc,CAAC,UAAU,CAAqB;QACzE,IAAIK,SAAS,EAAE;UACbA,SAAS,CAACC,KAAK,EAAE;;MAErB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEA;EACAhF,eAAeA,CAACiF,KAAa,EAAEvB,MAAc;IAC3C,OAAOA,MAAM,CAACxE,GAAG,IAAI+F,KAAK;EAC5B;EAEAvE,eAAeA,CAACuE,KAAa,EAAEC,QAAsB;IACnD,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ;;IAEjB,OAAOA,QAAQ,CAAChG,GAAG,IAAIgG,QAAQ,CAACxC,EAAE,IAAIuC,KAAK;EAC7C;EAEA;EACAxB,kBAAkBA,CAACL,OAAe;IAChC;IACA,MAAM+B,YAAY,GAAGlB,QAAQ,CAACmB,aAAa,CAAC,KAAK,CAAC;IAClDD,YAAY,CAACE,SAAS,GAAG,+LAA+L;IACxNF,YAAY,CAACG,SAAS,GAAG;;;;;;;kEAOqClC,OAAO;;;;;;KAMpE;IAEDa,QAAQ,CAACC,IAAI,CAACqB,WAAW,CAACJ,YAAY,CAAC;IAEvC;IACAL,UAAU,CAAC,MAAK;MACdK,YAAY,CAAChB,KAAK,CAACqB,SAAS,GAAG,eAAe;IAChD,CAAC,EAAE,GAAG,CAAC;IAEP;IACAV,UAAU,CAAC,MAAK;MACdK,YAAY,CAAChB,KAAK,CAACqB,SAAS,GAAG,kBAAkB;MACjDV,UAAU,CAAC,MAAK;QACd,IAAIK,YAAY,CAACM,aAAa,EAAE;UAC9BN,YAAY,CAACO,MAAM,EAAE;;MAEzB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,IAAI,CAAC;EACV;EAEA/D,iBAAiBA,CAACgE,MAA0B,EAAET,QAAgB;IAC5D,IAAI,CAACS,MAAM,EAAE;MACXzC,OAAO,CAAChF,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAI,CAACgH,QAAQ,IAAIA,QAAQ,CAACU,IAAI,EAAE,KAAK,EAAE,EAAE;MACvC1C,OAAO,CAAChF,KAAK,CAAC,oBAAoB,CAAC;MACnC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAI,CAACsE,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMC,MAAM,GAAW;MAAEC,EAAE,EAAEwC;IAAQ,CAAE;IAEvC,IAAI,CAAC7C,aAAa,CAACV,iBAAiB,CAACgE,MAAM,EAAElD,MAAM,CAAC,CAACM,SAAS,CAAC;MAC7DC,IAAI,EAAGQ,QAAQ,IAAI;QACjBN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEK,QAAQ,CAAC;QACnD,IAAI,CAACZ,WAAW,EAAE;QAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,CAACiB,kBAAkB,CAAC,uCAAuC,CAAC;MAClE,CAAC;MACDvF,KAAK,EAAGA,KAAK,IAAI;QACfgF,OAAO,CAAChF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,qCAAqC,IAAIA,KAAK,CAACA,KAAK,EAAEkF,OAAO,IAAIlF,KAAK,CAACkF,OAAO,IAAI,eAAe,CAAC;QAC/G,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAnC,sBAAsBA,CAACsF,MAA0B,EAAET,QAAgB;IACjE,IAAI,CAACS,MAAM,EAAE;MACXzC,OAAO,CAAChF,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAI,CAACgH,QAAQ,EAAE;MACbhC,OAAO,CAAChF,KAAK,CAAC,wBAAwB,CAAC;MACvC,IAAI,CAACA,KAAK,GAAG,yBAAyB;MACtC;;IAGF,IAAI8F,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAACxB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACH,aAAa,CAAChC,sBAAsB,CAACsF,MAAM,EAAET,QAAQ,CAAC,CAACnC,SAAS,CAAC;QACpEC,IAAI,EAAGQ,QAAQ,IAAI;UACjBN,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,QAAQ,CAAC;UACrD,IAAI,CAACZ,WAAW,EAAE;UAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,IAAI,CAAClC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACpB,GAAG,KAAKyG,MAAM,EAAE;YAC7D,MAAM5B,aAAa,GAAG,IAAI,CAAChE,OAAO,CAAC8F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,GAAG,KAAKyG,MAAM,CAAC;YAC9D,IAAI5B,aAAa,EAAE;cACjB,IAAI,CAACzD,cAAc,GAAGyD,aAAa;;;QAGzC,CAAC;QACD7F,KAAK,EAAGA,KAAK,IAAI;UACfgF,OAAO,CAAChF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAEkF,OAAO,IAAIlF,KAAK,CAACkF,OAAO,IAAI,eAAe,CAAC;UACrH,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;;;uBAnVWL,eAAe,EAAA7E,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAfhE,eAAe;MAAAiE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB5BpJ,EAAA,CAAAC,cAAA,aAAkF;UAG9ED,EAAA,CAAAE,SAAA,aAA4K;UAI5KF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,cAAuE;UAM/DD,EAAA,CAAAE,SAAA,aAAiC;UACjCF,EAAA,CAAAI,MAAA,kCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAwE;UACtED,EAAA,CAAAI,MAAA,kGACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAqC,UAAA,KAAAiH,+BAAA,mBAgBM;UAGNtJ,EAAA,CAAAqC,UAAA,KAAAkH,+BAAA,kBAKM;UAGNvJ,EAAA,CAAAC,cAAA,cAAkB;UAKRD,EAAA,CAAAI,MAAA,sBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,gBAAiI;UAC/HD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAGTH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAK,UAAA,mBAAAmJ,kDAAA;YAAA,OAASH,GAAA,CAAA/D,WAAA,EAAa;UAAA,EAAC;UAIvBtF,EAAA,CAAAE,SAAA,aAAmE;UACnEF,EAAA,CAAAI,MAAA,yBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAGC;UACCD,EAAA,CAAAE,SAAA,aAAgC;UAChCF,EAAA,CAAAI,MAAA,8BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,cAAkB;UAEhBD,EAAA,CAAAqC,UAAA,KAAAoH,+BAAA,mBAiBM;UAGNzJ,EAAA,CAAAqC,UAAA,KAAAqH,+BAAA,kBAoEM;UACR1J,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAqC,UAAA,KAAAsH,+BAAA,mBA2JM;UACR3J,EAAA,CAAAG,YAAA,EAAM;;;UAxTEH,EAAA,CAAAa,SAAA,IAAW;UAAXb,EAAA,CAAAuC,UAAA,SAAA8G,GAAA,CAAAzI,KAAA,CAAW;UAmBXZ,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAuC,UAAA,SAAA8G,GAAA,CAAAnE,OAAA,CAAa;UAgBTlF,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAkC,kBAAA,MAAAmH,GAAA,CAAA5G,OAAA,CAAAL,MAAA,kBAAAiH,GAAA,CAAA5G,OAAA,CAAAL,MAAA,uBACF;UAMEpC,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAAuC,UAAA,aAAA8G,GAAA,CAAAnE,OAAA,CAAoB;UAGYlF,EAAA,CAAAa,SAAA,GAA8B;UAA9Bb,EAAA,CAAAyE,WAAA,iBAAA4E,GAAA,CAAAnE,OAAA,CAA8B;UAmBhElF,EAAA,CAAAa,SAAA,GAAsC;UAAtCb,EAAA,CAAAuC,UAAA,SAAA8G,GAAA,CAAA5G,OAAA,CAAAL,MAAA,WAAAiH,GAAA,CAAAnE,OAAA,CAAsC;UAoBtClF,EAAA,CAAAa,SAAA,GAAwB;UAAxBb,EAAA,CAAAuC,UAAA,SAAA8G,GAAA,CAAA5G,OAAA,CAAAL,MAAA,KAAwB;UAyE7BpC,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAAuC,UAAA,SAAA8G,GAAA,CAAArG,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}