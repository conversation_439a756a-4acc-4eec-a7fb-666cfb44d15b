{"ast": null, "code": "import { invariant } from \"../../utilities/globals/index.js\";\nexport function toPromise(observable) {\n  var completed = false;\n  return new Promise(function (resolve, reject) {\n    observable.subscribe({\n      next: function (data) {\n        if (completed) {\n          globalThis.__DEV__ !== false && invariant.warn(45);\n        } else {\n          completed = true;\n          resolve(data);\n        }\n      },\n      error: reject\n    });\n  });\n}", "map": {"version": 3, "names": ["invariant", "to<PERSON>romise", "observable", "completed", "Promise", "resolve", "reject", "subscribe", "next", "data", "globalThis", "__DEV__", "warn", "error"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/utils/toPromise.js"], "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nexport function toPromise(observable) {\n    var completed = false;\n    return new Promise(function (resolve, reject) {\n        observable.subscribe({\n            next: function (data) {\n                if (completed) {\n                    globalThis.__DEV__ !== false && invariant.warn(45);\n                }\n                else {\n                    completed = true;\n                    resolve(data);\n                }\n            },\n            error: reject,\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kCAAkC;AAC5D,OAAO,SAASC,SAASA,CAACC,UAAU,EAAE;EAClC,IAAIC,SAAS,GAAG,KAAK;EACrB,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IAC1CJ,UAAU,CAACK,SAAS,CAAC;MACjBC,IAAI,EAAE,SAAAA,CAAUC,IAAI,EAAE;QAClB,IAAIN,SAAS,EAAE;UACXO,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIX,SAAS,CAACY,IAAI,CAAC,EAAE,CAAC;QACtD,CAAC,MACI;UACDT,SAAS,GAAG,IAAI;UAChBE,OAAO,CAACI,IAAI,CAAC;QACjB;MACJ,CAAC;MACDI,KAAK,EAAEP;IACX,CAAC,CAAC;EACN,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}