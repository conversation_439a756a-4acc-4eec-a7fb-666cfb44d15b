import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { EquipeService } from 'src/app/services/equipe.service';
import { NotificationService } from 'src/app/services/notification.service';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-equipe-simple',
  templateUrl: './equipe-simple.component.html',
  styleUrls: ['./equipe-simple.component.css']
})
export class EquipeSimpleComponent implements OnInit {
  creating = false;
  currentUserId: string | null = null;

  constructor(
    private equipeService: EquipeService,
    private authService: AuthService,
    private router: Router,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.getCurrentUserId();
  }

  getCurrentUserId(): void {
    const token = localStorage.getItem('token');
    if (token) {
      this.authService.getProfile(token).subscribe({
        next: (user: any) => {
          this.currentUserId = user._id || user.id;
          console.log('Utilisateur connecté:', user);
        },
        error: (error) => {
          console.error('Erreur lors de la récupération du profil:', error);
          this.notificationService.showError('Erreur d\'authentification. Veuillez vous reconnecter.');
        }
      });
    } else {
      this.notificationService.showError('Vous devez être connecté pour créer une équipe.');
      this.router.navigate(['/login']);
    }
  }

  createTeam(): void {
    if (!this.currentUserId) {
      this.notificationService.showError('Erreur d\'authentification. Veuillez vous reconnecter.');
      return;
    }

    this.creating = true;

    // Générer un nom unique avec timestamp
    const timestamp = new Date().toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/[\/\s:]/g, '');

    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userName = user.firstName || user.name || 'Utilisateur';

    const newTeam = {
      name: `Équipe ${userName} ${timestamp}`,
      description: `Équipe créée automatiquement le ${new Date().toLocaleString('fr-FR')}`,
      admin: this.currentUserId
    };

    console.log('🚀 Création d\'équipe automatique:', newTeam);

    this.equipeService.addEquipe(newTeam).subscribe({
      next: (response) => {
        console.log('✅ Équipe créée avec succès:', response);
        this.creating = false;
        this.notificationService.showSuccess(
          `L'équipe "${response.name}" a été créée avec succès !`
        );

        // Rediriger vers la liste des équipes après 1.5 secondes
        setTimeout(() => {
          this.router.navigate(['/equipes/liste']);
        }, 1500);
      },
      error: (error) => {
        console.error('❌ Erreur lors de la création:', error);
        this.creating = false;

        if (error.status === 400 && error.message?.includes('nom existe déjà')) {
          // Si le nom existe, essayer avec un suffixe aléatoire
          const randomSuffix = Math.floor(Math.random() * 1000);
          const retryTeam = {
            ...newTeam,
            name: `${newTeam.name}-${randomSuffix}`
          };

          console.log('🔄 Retry avec nom modifié:', retryTeam);
          this.retryCreateTeam(retryTeam);
        } else {
          this.notificationService.showError(
            `Erreur lors de la création: ${error.message || 'Erreur inconnue'}`
          );
        }
      }
    });
  }

  private retryCreateTeam(team: any): void {
    this.creating = true;

    this.equipeService.addEquipe(team).subscribe({
      next: (response) => {
        console.log('✅ Équipe créée avec succès (retry):', response);
        this.creating = false;
        this.notificationService.showSuccess(
          `L'équipe "${response.name}" a été créée avec succès !`
        );

        setTimeout(() => {
          this.router.navigate(['/equipes/liste']);
        }, 1500);
      },
      error: (error) => {
        console.error('❌ Erreur lors du retry:', error);
        this.creating = false;
        this.notificationService.showError(
          `Impossible de créer l'équipe: ${error.message || 'Erreur inconnue'}`
        );
      }
    });
  }


}
