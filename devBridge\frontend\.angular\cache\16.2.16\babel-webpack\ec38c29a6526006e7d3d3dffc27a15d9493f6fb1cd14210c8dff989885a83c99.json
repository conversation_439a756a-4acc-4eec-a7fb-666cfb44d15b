{"ast": null, "code": "import { GraphQLError } from './GraphQLError.mjs';\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nexport function syntaxError(source, position, description) {\n  return new GraphQLError(`Syntax Error: ${description}`, {\n    source,\n    positions: [position]\n  });\n}", "map": {"version": 3, "names": ["GraphQLError", "syntaxError", "source", "position", "description", "positions"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/error/syntaxError.mjs"], "sourcesContent": ["import { GraphQLError } from './GraphQLError.mjs';\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nexport function syntaxError(source, position, description) {\n  return new GraphQLError(`Syntax Error: ${description}`, {\n    source,\n    positions: [position],\n  });\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD;AACA;AACA;AACA;;AAEA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EACzD,OAAO,IAAIJ,YAAY,CAAE,iBAAgBI,WAAY,EAAC,EAAE;IACtDF,MAAM;IACNG,SAAS,EAAE,CAACF,QAAQ;EACtB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}