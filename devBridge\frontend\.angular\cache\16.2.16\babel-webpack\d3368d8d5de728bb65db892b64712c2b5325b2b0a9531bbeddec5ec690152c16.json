{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { createHttpLink } from \"./createHttpLink.js\";\nvar HttpLink = /** @class */function (_super) {\n  __extends(HttpLink, _super);\n  function HttpLink(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var _this = _super.call(this, createHttpLink(options).request) || this;\n    _this.options = options;\n    return _this;\n  }\n  return HttpLink;\n}(ApolloLink);\nexport { HttpLink };", "map": {"version": 3, "names": ["__extends", "ApolloLink", "createHttpLink", "HttpLink", "_super", "options", "_this", "call", "request"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/http/HttpLink.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { createHttpLink } from \"./createHttpLink.js\";\nvar HttpLink = /** @class */ (function (_super) {\n    __extends(HttpLink, _super);\n    function HttpLink(options) {\n        if (options === void 0) { options = {}; }\n        var _this = _super.call(this, createHttpLink(options).request) || this;\n        _this.options = options;\n        return _this;\n    }\n    return HttpLink;\n}(ApolloLink));\nexport { HttpLink };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,IAAIC,QAAQ,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC5CJ,SAAS,CAACG,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAACE,OAAO,EAAE;IACvB,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,CAAC,CAAC;IAAE;IACxC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEL,cAAc,CAACG,OAAO,CAAC,CAACG,OAAO,CAAC,IAAI,IAAI;IACtEF,KAAK,CAACD,OAAO,GAAGA,OAAO;IACvB,OAAOC,KAAK;EAChB;EACA,OAAOH,QAAQ;AACnB,CAAC,CAACF,UAAU,CAAE;AACd,SAASE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}