{"ast": null, "code": "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport var serializeFetchParameter = function (p, label) {\n  var serialized;\n  try {\n    serialized = JSON.stringify(p);\n  } catch (e) {\n    var parseError = newInvariantError(42, label, e.message);\n    parseError.parseError = e;\n    throw parseError;\n  }\n  return serialized;\n};", "map": {"version": 3, "names": ["newInvariantError", "serializeFetchParameter", "p", "label", "serialized", "JSON", "stringify", "e", "parseError", "message"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/http/serializeFetchParameter.js"], "sourcesContent": ["import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport var serializeFetchParameter = function (p, label) {\n    var serialized;\n    try {\n        serialized = JSON.stringify(p);\n    }\n    catch (e) {\n        var parseError = newInvariantError(42, label, e.message);\n        parseError.parseError = e;\n        throw parseError;\n    }\n    return serialized;\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,OAAO,IAAIC,uBAAuB,GAAG,SAAAA,CAAUC,CAAC,EAAEC,KAAK,EAAE;EACrD,IAAIC,UAAU;EACd,IAAI;IACAA,UAAU,GAAGC,IAAI,CAACC,SAAS,CAACJ,CAAC,CAAC;EAClC,CAAC,CACD,OAAOK,CAAC,EAAE;IACN,IAAIC,UAAU,GAAGR,iBAAiB,CAAC,EAAE,EAAEG,KAAK,EAAEI,CAAC,CAACE,OAAO,CAAC;IACxDD,UAAU,CAACA,UAAU,GAAGD,CAAC;IACzB,MAAMC,UAAU;EACpB;EACA,OAAOJ,UAAU;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}