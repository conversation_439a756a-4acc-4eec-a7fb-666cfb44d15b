{"ast": null, "code": "export var throwServerError = function (response, result, message) {\n  var error = new Error(message);\n  error.name = \"ServerError\";\n  error.response = response;\n  error.statusCode = response.status;\n  error.result = result;\n  throw error;\n};", "map": {"version": 3, "names": ["throwServerError", "response", "result", "message", "error", "Error", "name", "statusCode", "status"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/utils/throwServerError.js"], "sourcesContent": ["export var throwServerError = function (response, result, message) {\n    var error = new Error(message);\n    error.name = \"ServerError\";\n    error.response = response;\n    error.statusCode = response.status;\n    error.result = result;\n    throw error;\n};\n"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/D,IAAIC,KAAK,GAAG,IAAIC,KAAK,CAACF,OAAO,CAAC;EAC9BC,KAAK,CAACE,IAAI,GAAG,aAAa;EAC1BF,KAAK,CAACH,QAAQ,GAAGA,QAAQ;EACzBG,KAAK,CAACG,UAAU,GAAGN,QAAQ,CAACO,MAAM;EAClCJ,KAAK,CAACF,MAAM,GAAGA,MAAM;EACrB,MAAME,KAAK;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}