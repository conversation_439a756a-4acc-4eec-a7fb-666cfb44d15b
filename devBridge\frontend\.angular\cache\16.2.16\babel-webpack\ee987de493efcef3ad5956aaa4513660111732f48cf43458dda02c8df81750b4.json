{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { isEqualType, isTypeSubTypeOf } from '../utilities/typeComparators.mjs';\nimport { isEnumType, isInputObjectType, isInputType, isInterfaceType, isNamedType, isNonNullType, isObjectType, isOutputType, isRequiredArgument, isRequiredInputField, isUnionType } from './definition.mjs';\nimport { GraphQLDeprecatedDirective, isDirective } from './directives.mjs';\nimport { isIntrospectionType } from './introspection.mjs';\nimport { assertSchema } from './schema.mjs';\n/**\n * Implements the \"Type Validation\" sub-sections of the specification's\n * \"Type System\" section.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the Schema is valid.\n */\n\nexport function validateSchema(schema) {\n  // First check to ensure the provided value is in fact a GraphQLSchema.\n  assertSchema(schema); // If this Schema has already been validated, return the previous results.\n\n  if (schema.__validationErrors) {\n    return schema.__validationErrors;\n  } // Validate the schema, producing a list of errors.\n\n  const context = new SchemaValidationContext(schema);\n  validateRootTypes(context);\n  validateDirectives(context);\n  validateTypes(context); // Persist the results of validation before returning to ensure validation\n  // does not run multiple times for this schema.\n\n  const errors = context.getErrors();\n  schema.__validationErrors = errors;\n  return errors;\n}\n/**\n * Utility function which asserts a schema is valid by throwing an error if\n * it is invalid.\n */\n\nexport function assertValidSchema(schema) {\n  const errors = validateSchema(schema);\n  if (errors.length !== 0) {\n    throw new Error(errors.map(error => error.message).join('\\n\\n'));\n  }\n}\nclass SchemaValidationContext {\n  constructor(schema) {\n    this._errors = [];\n    this.schema = schema;\n  }\n  reportError(message, nodes) {\n    const _nodes = Array.isArray(nodes) ? nodes.filter(Boolean) : nodes;\n    this._errors.push(new GraphQLError(message, {\n      nodes: _nodes\n    }));\n  }\n  getErrors() {\n    return this._errors;\n  }\n}\nfunction validateRootTypes(context) {\n  const schema = context.schema;\n  const queryType = schema.getQueryType();\n  if (!queryType) {\n    context.reportError('Query root type must be provided.', schema.astNode);\n  } else if (!isObjectType(queryType)) {\n    var _getOperationTypeNode;\n    context.reportError(`Query root type must be Object type, it cannot be ${inspect(queryType)}.`, (_getOperationTypeNode = getOperationTypeNode(schema, OperationTypeNode.QUERY)) !== null && _getOperationTypeNode !== void 0 ? _getOperationTypeNode : queryType.astNode);\n  }\n  const mutationType = schema.getMutationType();\n  if (mutationType && !isObjectType(mutationType)) {\n    var _getOperationTypeNode2;\n    context.reportError('Mutation root type must be Object type if provided, it cannot be ' + `${inspect(mutationType)}.`, (_getOperationTypeNode2 = getOperationTypeNode(schema, OperationTypeNode.MUTATION)) !== null && _getOperationTypeNode2 !== void 0 ? _getOperationTypeNode2 : mutationType.astNode);\n  }\n  const subscriptionType = schema.getSubscriptionType();\n  if (subscriptionType && !isObjectType(subscriptionType)) {\n    var _getOperationTypeNode3;\n    context.reportError('Subscription root type must be Object type if provided, it cannot be ' + `${inspect(subscriptionType)}.`, (_getOperationTypeNode3 = getOperationTypeNode(schema, OperationTypeNode.SUBSCRIPTION)) !== null && _getOperationTypeNode3 !== void 0 ? _getOperationTypeNode3 : subscriptionType.astNode);\n  }\n}\nfunction getOperationTypeNode(schema, operation) {\n  var _flatMap$find;\n  return (_flatMap$find = [schema.astNode, ...schema.extensionASTNodes].flatMap(\n  // FIXME: https://github.com/graphql/graphql-js/issues/2203\n  schemaNode => {\n    var _schemaNode$operation;\n    return (/* c8 ignore next */\n      (_schemaNode$operation = schemaNode === null || schemaNode === void 0 ? void 0 : schemaNode.operationTypes) !== null && _schemaNode$operation !== void 0 ? _schemaNode$operation : []\n    );\n  }).find(operationNode => operationNode.operation === operation)) === null || _flatMap$find === void 0 ? void 0 : _flatMap$find.type;\n}\nfunction validateDirectives(context) {\n  for (const directive of context.schema.getDirectives()) {\n    // Ensure all directives are in fact GraphQL directives.\n    if (!isDirective(directive)) {\n      context.reportError(`Expected directive but got: ${inspect(directive)}.`, directive === null || directive === void 0 ? void 0 : directive.astNode);\n      continue;\n    } // Ensure they are named correctly.\n\n    validateName(context, directive); // TODO: Ensure proper locations.\n    // Ensure the arguments are valid.\n\n    for (const arg of directive.args) {\n      // Ensure they are named correctly.\n      validateName(context, arg); // Ensure the type is an input type.\n\n      if (!isInputType(arg.type)) {\n        context.reportError(`The type of @${directive.name}(${arg.name}:) must be Input Type ` + `but got: ${inspect(arg.type)}.`, arg.astNode);\n      }\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode;\n        context.reportError(`Required argument @${directive.name}(${arg.name}:) cannot be deprecated.`, [getDeprecatedDirectiveNode(arg.astNode), (_arg$astNode = arg.astNode) === null || _arg$astNode === void 0 ? void 0 : _arg$astNode.type]);\n      }\n    }\n  }\n}\nfunction validateName(context, node) {\n  // Ensure names are valid, however introspection types opt out.\n  if (node.name.startsWith('__')) {\n    context.reportError(`Name \"${node.name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`, node.astNode);\n  }\n}\nfunction validateTypes(context) {\n  const validateInputObjectCircularRefs = createInputObjectCircularRefsValidator(context);\n  const typeMap = context.schema.getTypeMap();\n  for (const type of Object.values(typeMap)) {\n    // Ensure all provided types are in fact GraphQL type.\n    if (!isNamedType(type)) {\n      context.reportError(`Expected GraphQL named type but got: ${inspect(type)}.`, type.astNode);\n      continue;\n    } // Ensure it is named correctly (excluding introspection types).\n\n    if (!isIntrospectionType(type)) {\n      validateName(context, type);\n    }\n    if (isObjectType(type)) {\n      // Ensure fields are valid\n      validateFields(context, type); // Ensure objects implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isInterfaceType(type)) {\n      // Ensure fields are valid.\n      validateFields(context, type); // Ensure interfaces implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isUnionType(type)) {\n      // Ensure Unions include valid member types.\n      validateUnionMembers(context, type);\n    } else if (isEnumType(type)) {\n      // Ensure Enums have valid values.\n      validateEnumValues(context, type);\n    } else if (isInputObjectType(type)) {\n      // Ensure Input Object fields are valid.\n      validateInputFields(context, type); // Ensure Input Objects do not contain non-nullable circular references\n\n      validateInputObjectCircularRefs(type);\n    }\n  }\n}\nfunction validateFields(context, type) {\n  const fields = Object.values(type.getFields()); // Objects and Interfaces both must define one or more fields.\n\n  if (fields.length === 0) {\n    context.reportError(`Type ${type.name} must define one or more fields.`, [type.astNode, ...type.extensionASTNodes]);\n  }\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an output type\n\n    if (!isOutputType(field.type)) {\n      var _field$astNode;\n      context.reportError(`The type of ${type.name}.${field.name} must be Output Type ` + `but got: ${inspect(field.type)}.`, (_field$astNode = field.astNode) === null || _field$astNode === void 0 ? void 0 : _field$astNode.type);\n    } // Ensure the arguments are valid\n\n    for (const arg of field.args) {\n      const argName = arg.name; // Ensure they are named correctly.\n\n      validateName(context, arg); // Ensure the type is an input type\n\n      if (!isInputType(arg.type)) {\n        var _arg$astNode2;\n        context.reportError(`The type of ${type.name}.${field.name}(${argName}:) must be Input ` + `Type but got: ${inspect(arg.type)}.`, (_arg$astNode2 = arg.astNode) === null || _arg$astNode2 === void 0 ? void 0 : _arg$astNode2.type);\n      }\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode3;\n        context.reportError(`Required argument ${type.name}.${field.name}(${argName}:) cannot be deprecated.`, [getDeprecatedDirectiveNode(arg.astNode), (_arg$astNode3 = arg.astNode) === null || _arg$astNode3 === void 0 ? void 0 : _arg$astNode3.type]);\n      }\n    }\n  }\n}\nfunction validateInterfaces(context, type) {\n  const ifaceTypeNames = Object.create(null);\n  for (const iface of type.getInterfaces()) {\n    if (!isInterfaceType(iface)) {\n      context.reportError(`Type ${inspect(type)} must only implement Interface types, ` + `it cannot implement ${inspect(iface)}.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    if (type === iface) {\n      context.reportError(`Type ${type.name} cannot implement itself because it would create a circular reference.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    if (ifaceTypeNames[iface.name]) {\n      context.reportError(`Type ${type.name} can only implement ${iface.name} once.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    ifaceTypeNames[iface.name] = true;\n    validateTypeImplementsAncestors(context, type, iface);\n    validateTypeImplementsInterface(context, type, iface);\n  }\n}\nfunction validateTypeImplementsInterface(context, type, iface) {\n  const typeFieldMap = type.getFields(); // Assert each interface field is implemented.\n\n  for (const ifaceField of Object.values(iface.getFields())) {\n    const fieldName = ifaceField.name;\n    const typeField = typeFieldMap[fieldName]; // Assert interface field exists on type.\n\n    if (!typeField) {\n      context.reportError(`Interface field ${iface.name}.${fieldName} expected but ${type.name} does not provide it.`, [ifaceField.astNode, type.astNode, ...type.extensionASTNodes]);\n      continue;\n    } // Assert interface field type is satisfied by type field type, by being\n    // a valid subtype. (covariant)\n\n    if (!isTypeSubTypeOf(context.schema, typeField.type, ifaceField.type)) {\n      var _ifaceField$astNode, _typeField$astNode;\n      context.reportError(`Interface field ${iface.name}.${fieldName} expects type ` + `${inspect(ifaceField.type)} but ${type.name}.${fieldName} ` + `is type ${inspect(typeField.type)}.`, [(_ifaceField$astNode = ifaceField.astNode) === null || _ifaceField$astNode === void 0 ? void 0 : _ifaceField$astNode.type, (_typeField$astNode = typeField.astNode) === null || _typeField$astNode === void 0 ? void 0 : _typeField$astNode.type]);\n    } // Assert each interface field arg is implemented.\n\n    for (const ifaceArg of ifaceField.args) {\n      const argName = ifaceArg.name;\n      const typeArg = typeField.args.find(arg => arg.name === argName); // Assert interface field arg exists on object field.\n\n      if (!typeArg) {\n        context.reportError(`Interface field argument ${iface.name}.${fieldName}(${argName}:) expected but ${type.name}.${fieldName} does not provide it.`, [ifaceArg.astNode, typeField.astNode]);\n        continue;\n      } // Assert interface field arg type matches object field arg type.\n      // (invariant)\n      // TODO: change to contravariant?\n\n      if (!isEqualType(ifaceArg.type, typeArg.type)) {\n        var _ifaceArg$astNode, _typeArg$astNode;\n        context.reportError(`Interface field argument ${iface.name}.${fieldName}(${argName}:) ` + `expects type ${inspect(ifaceArg.type)} but ` + `${type.name}.${fieldName}(${argName}:) is type ` + `${inspect(typeArg.type)}.`, [(_ifaceArg$astNode = ifaceArg.astNode) === null || _ifaceArg$astNode === void 0 ? void 0 : _ifaceArg$astNode.type, (_typeArg$astNode = typeArg.astNode) === null || _typeArg$astNode === void 0 ? void 0 : _typeArg$astNode.type]);\n      } // TODO: validate default values?\n    } // Assert additional arguments must not be required.\n\n    for (const typeArg of typeField.args) {\n      const argName = typeArg.name;\n      const ifaceArg = ifaceField.args.find(arg => arg.name === argName);\n      if (!ifaceArg && isRequiredArgument(typeArg)) {\n        context.reportError(`Object field ${type.name}.${fieldName} includes required argument ${argName} that is missing from the Interface field ${iface.name}.${fieldName}.`, [typeArg.astNode, ifaceField.astNode]);\n      }\n    }\n  }\n}\nfunction validateTypeImplementsAncestors(context, type, iface) {\n  const ifaceInterfaces = type.getInterfaces();\n  for (const transitive of iface.getInterfaces()) {\n    if (!ifaceInterfaces.includes(transitive)) {\n      context.reportError(transitive === type ? `Type ${type.name} cannot implement ${iface.name} because it would create a circular reference.` : `Type ${type.name} must implement ${transitive.name} because it is implemented by ${iface.name}.`, [...getAllImplementsInterfaceNodes(iface, transitive), ...getAllImplementsInterfaceNodes(type, iface)]);\n    }\n  }\n}\nfunction validateUnionMembers(context, union) {\n  const memberTypes = union.getTypes();\n  if (memberTypes.length === 0) {\n    context.reportError(`Union type ${union.name} must define one or more member types.`, [union.astNode, ...union.extensionASTNodes]);\n  }\n  const includedTypeNames = Object.create(null);\n  for (const memberType of memberTypes) {\n    if (includedTypeNames[memberType.name]) {\n      context.reportError(`Union type ${union.name} can only include type ${memberType.name} once.`, getUnionMemberTypeNodes(union, memberType.name));\n      continue;\n    }\n    includedTypeNames[memberType.name] = true;\n    if (!isObjectType(memberType)) {\n      context.reportError(`Union type ${union.name} can only include Object types, ` + `it cannot include ${inspect(memberType)}.`, getUnionMemberTypeNodes(union, String(memberType)));\n    }\n  }\n}\nfunction validateEnumValues(context, enumType) {\n  const enumValues = enumType.getValues();\n  if (enumValues.length === 0) {\n    context.reportError(`Enum type ${enumType.name} must define one or more values.`, [enumType.astNode, ...enumType.extensionASTNodes]);\n  }\n  for (const enumValue of enumValues) {\n    // Ensure valid name.\n    validateName(context, enumValue);\n  }\n}\nfunction validateInputFields(context, inputObj) {\n  const fields = Object.values(inputObj.getFields());\n  if (fields.length === 0) {\n    context.reportError(`Input Object type ${inputObj.name} must define one or more fields.`, [inputObj.astNode, ...inputObj.extensionASTNodes]);\n  } // Ensure the arguments are valid\n\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an input type\n\n    if (!isInputType(field.type)) {\n      var _field$astNode2;\n      context.reportError(`The type of ${inputObj.name}.${field.name} must be Input Type ` + `but got: ${inspect(field.type)}.`, (_field$astNode2 = field.astNode) === null || _field$astNode2 === void 0 ? void 0 : _field$astNode2.type);\n    }\n    if (isRequiredInputField(field) && field.deprecationReason != null) {\n      var _field$astNode3;\n      context.reportError(`Required input field ${inputObj.name}.${field.name} cannot be deprecated.`, [getDeprecatedDirectiveNode(field.astNode), (_field$astNode3 = field.astNode) === null || _field$astNode3 === void 0 ? void 0 : _field$astNode3.type]);\n    }\n  }\n}\nfunction createInputObjectCircularRefsValidator(context) {\n  // Modified copy of algorithm from 'src/validation/rules/NoFragmentCycles.js'.\n  // Tracks already visited types to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedTypes = Object.create(null); // Array of types nodes used to produce meaningful errors\n\n  const fieldPath = []; // Position in the type path\n\n  const fieldPathIndexByTypeName = Object.create(null);\n  return detectCycleRecursive; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(inputObj) {\n    if (visitedTypes[inputObj.name]) {\n      return;\n    }\n    visitedTypes[inputObj.name] = true;\n    fieldPathIndexByTypeName[inputObj.name] = fieldPath.length;\n    const fields = Object.values(inputObj.getFields());\n    for (const field of fields) {\n      if (isNonNullType(field.type) && isInputObjectType(field.type.ofType)) {\n        const fieldType = field.type.ofType;\n        const cycleIndex = fieldPathIndexByTypeName[fieldType.name];\n        fieldPath.push(field);\n        if (cycleIndex === undefined) {\n          detectCycleRecursive(fieldType);\n        } else {\n          const cyclePath = fieldPath.slice(cycleIndex);\n          const pathStr = cyclePath.map(fieldObj => fieldObj.name).join('.');\n          context.reportError(`Cannot reference Input Object \"${fieldType.name}\" within itself through a series of non-null fields: \"${pathStr}\".`, cyclePath.map(fieldObj => fieldObj.astNode));\n        }\n        fieldPath.pop();\n      }\n    }\n    fieldPathIndexByTypeName[inputObj.name] = undefined;\n  }\n}\nfunction getAllImplementsInterfaceNodes(type, iface) {\n  const {\n    astNode,\n    extensionASTNodes\n  } = type;\n  const nodes = astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes.flatMap(typeNode => {\n    var _typeNode$interfaces;\n    return (/* c8 ignore next */\n      (_typeNode$interfaces = typeNode.interfaces) !== null && _typeNode$interfaces !== void 0 ? _typeNode$interfaces : []\n    );\n  }).filter(ifaceNode => ifaceNode.name.value === iface.name);\n}\nfunction getUnionMemberTypeNodes(union, typeName) {\n  const {\n    astNode,\n    extensionASTNodes\n  } = union;\n  const nodes = astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes.flatMap(unionNode => {\n    var _unionNode$types;\n    return (/* c8 ignore next */\n      (_unionNode$types = unionNode.types) !== null && _unionNode$types !== void 0 ? _unionNode$types : []\n    );\n  }).filter(typeNode => typeNode.name.value === typeName);\n}\nfunction getDeprecatedDirectiveNode(definitionNode) {\n  var _definitionNode$direc;\n  return definitionNode === null || definitionNode === void 0 ? void 0 : (_definitionNode$direc = definitionNode.directives) === null || _definitionNode$direc === void 0 ? void 0 : _definitionNode$direc.find(node => node.name.value === GraphQLDeprecatedDirective.name);\n}", "map": {"version": 3, "names": ["inspect", "GraphQLError", "OperationTypeNode", "isEqualType", "isTypeSubTypeOf", "isEnumType", "isInputObjectType", "isInputType", "isInterfaceType", "isNamedType", "isNonNullType", "isObjectType", "isOutputType", "isRequiredArgument", "isRequiredInputField", "isUnionType", "GraphQLDeprecatedDirective", "isDirective", "isIntrospectionType", "assertSchema", "validateSchema", "schema", "__validationErrors", "context", "SchemaValidationContext", "validateRootTypes", "validateDirectives", "validateTypes", "errors", "getErrors", "assertValidSchema", "length", "Error", "map", "error", "message", "join", "constructor", "_errors", "reportError", "nodes", "_nodes", "Array", "isArray", "filter", "Boolean", "push", "queryType", "getQueryType", "astNode", "_getOperationTypeNode", "getOperationTypeNode", "QUERY", "mutationType", "getMutationType", "_getOperationTypeNode2", "MUTATION", "subscriptionType", "getSubscriptionType", "_getOperationTypeNode3", "SUBSCRIPTION", "operation", "_flatMap$find", "extensionASTNodes", "flatMap", "schemaNode", "_schemaNode$operation", "operationTypes", "find", "operationNode", "type", "directive", "getDirectives", "validateName", "arg", "args", "name", "deprecationReason", "_arg$astNode", "getDeprecatedDirectiveNode", "node", "startsWith", "validateInputObjectCircularRefs", "createInputObjectCircularRefsValidator", "typeMap", "getTypeMap", "Object", "values", "validateFields", "validateInterfaces", "validateUnionMembers", "validateEnumValues", "validateInputFields", "fields", "getFields", "field", "_field$astNode", "argName", "_arg$astNode2", "_arg$astNode3", "ifaceTypeNames", "create", "iface", "getInterfaces", "getAllImplementsInterfaceNodes", "validateTypeImplementsAncestors", "validateTypeImplementsInterface", "typeFieldMap", "ifaceField", "fieldName", "typeField", "_ifaceField$astNode", "_typeField$astNode", "ifaceArg", "typeArg", "_ifaceArg$astNode", "_typeArg$astNode", "ifaceInterfaces", "transitive", "includes", "union", "memberTypes", "getTypes", "includedTypeNames", "memberType", "getUnionMemberTypeNodes", "String", "enumType", "enum<PERSON><PERSON><PERSON>", "getV<PERSON>ues", "enumValue", "inputObj", "_field$astNode2", "_field$astNode3", "visitedTypes", "fieldPath", "fieldPathIndexByTypeName", "detectCycleRecursive", "ofType", "fieldType", "cycleIndex", "undefined", "cyclePath", "slice", "pathStr", "field<PERSON>bj", "pop", "typeNode", "_typeNode$interfaces", "interfaces", "ifaceNode", "value", "typeName", "unionNode", "_unionNode$types", "types", "definitionNode", "_definitionNode$direc", "directives"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/type/validate.mjs"], "sourcesContent": ["import { inspect } from '../jsutils/inspect.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { isEqualType, isTypeSubTypeOf } from '../utilities/typeComparators.mjs';\nimport {\n  isEnumType,\n  isInputObjectType,\n  isInputType,\n  isInterfaceType,\n  isNamedType,\n  isNonNullType,\n  isObjectType,\n  isOutputType,\n  isRequiredArgument,\n  isRequiredInputField,\n  isUnionType,\n} from './definition.mjs';\nimport { GraphQLDeprecatedDirective, isDirective } from './directives.mjs';\nimport { isIntrospectionType } from './introspection.mjs';\nimport { assertSchema } from './schema.mjs';\n/**\n * Implements the \"Type Validation\" sub-sections of the specification's\n * \"Type System\" section.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the Schema is valid.\n */\n\nexport function validateSchema(schema) {\n  // First check to ensure the provided value is in fact a GraphQLSchema.\n  assertSchema(schema); // If this Schema has already been validated, return the previous results.\n\n  if (schema.__validationErrors) {\n    return schema.__validationErrors;\n  } // Validate the schema, producing a list of errors.\n\n  const context = new SchemaValidationContext(schema);\n  validateRootTypes(context);\n  validateDirectives(context);\n  validateTypes(context); // Persist the results of validation before returning to ensure validation\n  // does not run multiple times for this schema.\n\n  const errors = context.getErrors();\n  schema.__validationErrors = errors;\n  return errors;\n}\n/**\n * Utility function which asserts a schema is valid by throwing an error if\n * it is invalid.\n */\n\nexport function assertValidSchema(schema) {\n  const errors = validateSchema(schema);\n\n  if (errors.length !== 0) {\n    throw new Error(errors.map((error) => error.message).join('\\n\\n'));\n  }\n}\n\nclass SchemaValidationContext {\n  constructor(schema) {\n    this._errors = [];\n    this.schema = schema;\n  }\n\n  reportError(message, nodes) {\n    const _nodes = Array.isArray(nodes) ? nodes.filter(Boolean) : nodes;\n\n    this._errors.push(\n      new GraphQLError(message, {\n        nodes: _nodes,\n      }),\n    );\n  }\n\n  getErrors() {\n    return this._errors;\n  }\n}\n\nfunction validateRootTypes(context) {\n  const schema = context.schema;\n  const queryType = schema.getQueryType();\n\n  if (!queryType) {\n    context.reportError('Query root type must be provided.', schema.astNode);\n  } else if (!isObjectType(queryType)) {\n    var _getOperationTypeNode;\n\n    context.reportError(\n      `Query root type must be Object type, it cannot be ${inspect(\n        queryType,\n      )}.`,\n      (_getOperationTypeNode = getOperationTypeNode(\n        schema,\n        OperationTypeNode.QUERY,\n      )) !== null && _getOperationTypeNode !== void 0\n        ? _getOperationTypeNode\n        : queryType.astNode,\n    );\n  }\n\n  const mutationType = schema.getMutationType();\n\n  if (mutationType && !isObjectType(mutationType)) {\n    var _getOperationTypeNode2;\n\n    context.reportError(\n      'Mutation root type must be Object type if provided, it cannot be ' +\n        `${inspect(mutationType)}.`,\n      (_getOperationTypeNode2 = getOperationTypeNode(\n        schema,\n        OperationTypeNode.MUTATION,\n      )) !== null && _getOperationTypeNode2 !== void 0\n        ? _getOperationTypeNode2\n        : mutationType.astNode,\n    );\n  }\n\n  const subscriptionType = schema.getSubscriptionType();\n\n  if (subscriptionType && !isObjectType(subscriptionType)) {\n    var _getOperationTypeNode3;\n\n    context.reportError(\n      'Subscription root type must be Object type if provided, it cannot be ' +\n        `${inspect(subscriptionType)}.`,\n      (_getOperationTypeNode3 = getOperationTypeNode(\n        schema,\n        OperationTypeNode.SUBSCRIPTION,\n      )) !== null && _getOperationTypeNode3 !== void 0\n        ? _getOperationTypeNode3\n        : subscriptionType.astNode,\n    );\n  }\n}\n\nfunction getOperationTypeNode(schema, operation) {\n  var _flatMap$find;\n\n  return (_flatMap$find = [schema.astNode, ...schema.extensionASTNodes]\n    .flatMap(\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      (schemaNode) => {\n        var _schemaNode$operation;\n\n        return (\n          /* c8 ignore next */\n          (_schemaNode$operation =\n            schemaNode === null || schemaNode === void 0\n              ? void 0\n              : schemaNode.operationTypes) !== null &&\n            _schemaNode$operation !== void 0\n            ? _schemaNode$operation\n            : []\n        );\n      },\n    )\n    .find((operationNode) => operationNode.operation === operation)) === null ||\n    _flatMap$find === void 0\n    ? void 0\n    : _flatMap$find.type;\n}\n\nfunction validateDirectives(context) {\n  for (const directive of context.schema.getDirectives()) {\n    // Ensure all directives are in fact GraphQL directives.\n    if (!isDirective(directive)) {\n      context.reportError(\n        `Expected directive but got: ${inspect(directive)}.`,\n        directive === null || directive === void 0 ? void 0 : directive.astNode,\n      );\n      continue;\n    } // Ensure they are named correctly.\n\n    validateName(context, directive); // TODO: Ensure proper locations.\n    // Ensure the arguments are valid.\n\n    for (const arg of directive.args) {\n      // Ensure they are named correctly.\n      validateName(context, arg); // Ensure the type is an input type.\n\n      if (!isInputType(arg.type)) {\n        context.reportError(\n          `The type of @${directive.name}(${arg.name}:) must be Input Type ` +\n            `but got: ${inspect(arg.type)}.`,\n          arg.astNode,\n        );\n      }\n\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode;\n\n        context.reportError(\n          `Required argument @${directive.name}(${arg.name}:) cannot be deprecated.`,\n          [\n            getDeprecatedDirectiveNode(arg.astNode),\n            (_arg$astNode = arg.astNode) === null || _arg$astNode === void 0\n              ? void 0\n              : _arg$astNode.type,\n          ],\n        );\n      }\n    }\n  }\n}\n\nfunction validateName(context, node) {\n  // Ensure names are valid, however introspection types opt out.\n  if (node.name.startsWith('__')) {\n    context.reportError(\n      `Name \"${node.name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`,\n      node.astNode,\n    );\n  }\n}\n\nfunction validateTypes(context) {\n  const validateInputObjectCircularRefs =\n    createInputObjectCircularRefsValidator(context);\n  const typeMap = context.schema.getTypeMap();\n\n  for (const type of Object.values(typeMap)) {\n    // Ensure all provided types are in fact GraphQL type.\n    if (!isNamedType(type)) {\n      context.reportError(\n        `Expected GraphQL named type but got: ${inspect(type)}.`,\n        type.astNode,\n      );\n      continue;\n    } // Ensure it is named correctly (excluding introspection types).\n\n    if (!isIntrospectionType(type)) {\n      validateName(context, type);\n    }\n\n    if (isObjectType(type)) {\n      // Ensure fields are valid\n      validateFields(context, type); // Ensure objects implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isInterfaceType(type)) {\n      // Ensure fields are valid.\n      validateFields(context, type); // Ensure interfaces implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isUnionType(type)) {\n      // Ensure Unions include valid member types.\n      validateUnionMembers(context, type);\n    } else if (isEnumType(type)) {\n      // Ensure Enums have valid values.\n      validateEnumValues(context, type);\n    } else if (isInputObjectType(type)) {\n      // Ensure Input Object fields are valid.\n      validateInputFields(context, type); // Ensure Input Objects do not contain non-nullable circular references\n\n      validateInputObjectCircularRefs(type);\n    }\n  }\n}\n\nfunction validateFields(context, type) {\n  const fields = Object.values(type.getFields()); // Objects and Interfaces both must define one or more fields.\n\n  if (fields.length === 0) {\n    context.reportError(`Type ${type.name} must define one or more fields.`, [\n      type.astNode,\n      ...type.extensionASTNodes,\n    ]);\n  }\n\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an output type\n\n    if (!isOutputType(field.type)) {\n      var _field$astNode;\n\n      context.reportError(\n        `The type of ${type.name}.${field.name} must be Output Type ` +\n          `but got: ${inspect(field.type)}.`,\n        (_field$astNode = field.astNode) === null || _field$astNode === void 0\n          ? void 0\n          : _field$astNode.type,\n      );\n    } // Ensure the arguments are valid\n\n    for (const arg of field.args) {\n      const argName = arg.name; // Ensure they are named correctly.\n\n      validateName(context, arg); // Ensure the type is an input type\n\n      if (!isInputType(arg.type)) {\n        var _arg$astNode2;\n\n        context.reportError(\n          `The type of ${type.name}.${field.name}(${argName}:) must be Input ` +\n            `Type but got: ${inspect(arg.type)}.`,\n          (_arg$astNode2 = arg.astNode) === null || _arg$astNode2 === void 0\n            ? void 0\n            : _arg$astNode2.type,\n        );\n      }\n\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode3;\n\n        context.reportError(\n          `Required argument ${type.name}.${field.name}(${argName}:) cannot be deprecated.`,\n          [\n            getDeprecatedDirectiveNode(arg.astNode),\n            (_arg$astNode3 = arg.astNode) === null || _arg$astNode3 === void 0\n              ? void 0\n              : _arg$astNode3.type,\n          ],\n        );\n      }\n    }\n  }\n}\n\nfunction validateInterfaces(context, type) {\n  const ifaceTypeNames = Object.create(null);\n\n  for (const iface of type.getInterfaces()) {\n    if (!isInterfaceType(iface)) {\n      context.reportError(\n        `Type ${inspect(type)} must only implement Interface types, ` +\n          `it cannot implement ${inspect(iface)}.`,\n        getAllImplementsInterfaceNodes(type, iface),\n      );\n      continue;\n    }\n\n    if (type === iface) {\n      context.reportError(\n        `Type ${type.name} cannot implement itself because it would create a circular reference.`,\n        getAllImplementsInterfaceNodes(type, iface),\n      );\n      continue;\n    }\n\n    if (ifaceTypeNames[iface.name]) {\n      context.reportError(\n        `Type ${type.name} can only implement ${iface.name} once.`,\n        getAllImplementsInterfaceNodes(type, iface),\n      );\n      continue;\n    }\n\n    ifaceTypeNames[iface.name] = true;\n    validateTypeImplementsAncestors(context, type, iface);\n    validateTypeImplementsInterface(context, type, iface);\n  }\n}\n\nfunction validateTypeImplementsInterface(context, type, iface) {\n  const typeFieldMap = type.getFields(); // Assert each interface field is implemented.\n\n  for (const ifaceField of Object.values(iface.getFields())) {\n    const fieldName = ifaceField.name;\n    const typeField = typeFieldMap[fieldName]; // Assert interface field exists on type.\n\n    if (!typeField) {\n      context.reportError(\n        `Interface field ${iface.name}.${fieldName} expected but ${type.name} does not provide it.`,\n        [ifaceField.astNode, type.astNode, ...type.extensionASTNodes],\n      );\n      continue;\n    } // Assert interface field type is satisfied by type field type, by being\n    // a valid subtype. (covariant)\n\n    if (!isTypeSubTypeOf(context.schema, typeField.type, ifaceField.type)) {\n      var _ifaceField$astNode, _typeField$astNode;\n\n      context.reportError(\n        `Interface field ${iface.name}.${fieldName} expects type ` +\n          `${inspect(ifaceField.type)} but ${type.name}.${fieldName} ` +\n          `is type ${inspect(typeField.type)}.`,\n        [\n          (_ifaceField$astNode = ifaceField.astNode) === null ||\n          _ifaceField$astNode === void 0\n            ? void 0\n            : _ifaceField$astNode.type,\n          (_typeField$astNode = typeField.astNode) === null ||\n          _typeField$astNode === void 0\n            ? void 0\n            : _typeField$astNode.type,\n        ],\n      );\n    } // Assert each interface field arg is implemented.\n\n    for (const ifaceArg of ifaceField.args) {\n      const argName = ifaceArg.name;\n      const typeArg = typeField.args.find((arg) => arg.name === argName); // Assert interface field arg exists on object field.\n\n      if (!typeArg) {\n        context.reportError(\n          `Interface field argument ${iface.name}.${fieldName}(${argName}:) expected but ${type.name}.${fieldName} does not provide it.`,\n          [ifaceArg.astNode, typeField.astNode],\n        );\n        continue;\n      } // Assert interface field arg type matches object field arg type.\n      // (invariant)\n      // TODO: change to contravariant?\n\n      if (!isEqualType(ifaceArg.type, typeArg.type)) {\n        var _ifaceArg$astNode, _typeArg$astNode;\n\n        context.reportError(\n          `Interface field argument ${iface.name}.${fieldName}(${argName}:) ` +\n            `expects type ${inspect(ifaceArg.type)} but ` +\n            `${type.name}.${fieldName}(${argName}:) is type ` +\n            `${inspect(typeArg.type)}.`,\n          [\n            (_ifaceArg$astNode = ifaceArg.astNode) === null ||\n            _ifaceArg$astNode === void 0\n              ? void 0\n              : _ifaceArg$astNode.type,\n            (_typeArg$astNode = typeArg.astNode) === null ||\n            _typeArg$astNode === void 0\n              ? void 0\n              : _typeArg$astNode.type,\n          ],\n        );\n      } // TODO: validate default values?\n    } // Assert additional arguments must not be required.\n\n    for (const typeArg of typeField.args) {\n      const argName = typeArg.name;\n      const ifaceArg = ifaceField.args.find((arg) => arg.name === argName);\n\n      if (!ifaceArg && isRequiredArgument(typeArg)) {\n        context.reportError(\n          `Object field ${type.name}.${fieldName} includes required argument ${argName} that is missing from the Interface field ${iface.name}.${fieldName}.`,\n          [typeArg.astNode, ifaceField.astNode],\n        );\n      }\n    }\n  }\n}\n\nfunction validateTypeImplementsAncestors(context, type, iface) {\n  const ifaceInterfaces = type.getInterfaces();\n\n  for (const transitive of iface.getInterfaces()) {\n    if (!ifaceInterfaces.includes(transitive)) {\n      context.reportError(\n        transitive === type\n          ? `Type ${type.name} cannot implement ${iface.name} because it would create a circular reference.`\n          : `Type ${type.name} must implement ${transitive.name} because it is implemented by ${iface.name}.`,\n        [\n          ...getAllImplementsInterfaceNodes(iface, transitive),\n          ...getAllImplementsInterfaceNodes(type, iface),\n        ],\n      );\n    }\n  }\n}\n\nfunction validateUnionMembers(context, union) {\n  const memberTypes = union.getTypes();\n\n  if (memberTypes.length === 0) {\n    context.reportError(\n      `Union type ${union.name} must define one or more member types.`,\n      [union.astNode, ...union.extensionASTNodes],\n    );\n  }\n\n  const includedTypeNames = Object.create(null);\n\n  for (const memberType of memberTypes) {\n    if (includedTypeNames[memberType.name]) {\n      context.reportError(\n        `Union type ${union.name} can only include type ${memberType.name} once.`,\n        getUnionMemberTypeNodes(union, memberType.name),\n      );\n      continue;\n    }\n\n    includedTypeNames[memberType.name] = true;\n\n    if (!isObjectType(memberType)) {\n      context.reportError(\n        `Union type ${union.name} can only include Object types, ` +\n          `it cannot include ${inspect(memberType)}.`,\n        getUnionMemberTypeNodes(union, String(memberType)),\n      );\n    }\n  }\n}\n\nfunction validateEnumValues(context, enumType) {\n  const enumValues = enumType.getValues();\n\n  if (enumValues.length === 0) {\n    context.reportError(\n      `Enum type ${enumType.name} must define one or more values.`,\n      [enumType.astNode, ...enumType.extensionASTNodes],\n    );\n  }\n\n  for (const enumValue of enumValues) {\n    // Ensure valid name.\n    validateName(context, enumValue);\n  }\n}\n\nfunction validateInputFields(context, inputObj) {\n  const fields = Object.values(inputObj.getFields());\n\n  if (fields.length === 0) {\n    context.reportError(\n      `Input Object type ${inputObj.name} must define one or more fields.`,\n      [inputObj.astNode, ...inputObj.extensionASTNodes],\n    );\n  } // Ensure the arguments are valid\n\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an input type\n\n    if (!isInputType(field.type)) {\n      var _field$astNode2;\n\n      context.reportError(\n        `The type of ${inputObj.name}.${field.name} must be Input Type ` +\n          `but got: ${inspect(field.type)}.`,\n        (_field$astNode2 = field.astNode) === null || _field$astNode2 === void 0\n          ? void 0\n          : _field$astNode2.type,\n      );\n    }\n\n    if (isRequiredInputField(field) && field.deprecationReason != null) {\n      var _field$astNode3;\n\n      context.reportError(\n        `Required input field ${inputObj.name}.${field.name} cannot be deprecated.`,\n        [\n          getDeprecatedDirectiveNode(field.astNode),\n          (_field$astNode3 = field.astNode) === null ||\n          _field$astNode3 === void 0\n            ? void 0\n            : _field$astNode3.type,\n        ],\n      );\n    }\n  }\n}\n\nfunction createInputObjectCircularRefsValidator(context) {\n  // Modified copy of algorithm from 'src/validation/rules/NoFragmentCycles.js'.\n  // Tracks already visited types to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedTypes = Object.create(null); // Array of types nodes used to produce meaningful errors\n\n  const fieldPath = []; // Position in the type path\n\n  const fieldPathIndexByTypeName = Object.create(null);\n  return detectCycleRecursive; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(inputObj) {\n    if (visitedTypes[inputObj.name]) {\n      return;\n    }\n\n    visitedTypes[inputObj.name] = true;\n    fieldPathIndexByTypeName[inputObj.name] = fieldPath.length;\n    const fields = Object.values(inputObj.getFields());\n\n    for (const field of fields) {\n      if (isNonNullType(field.type) && isInputObjectType(field.type.ofType)) {\n        const fieldType = field.type.ofType;\n        const cycleIndex = fieldPathIndexByTypeName[fieldType.name];\n        fieldPath.push(field);\n\n        if (cycleIndex === undefined) {\n          detectCycleRecursive(fieldType);\n        } else {\n          const cyclePath = fieldPath.slice(cycleIndex);\n          const pathStr = cyclePath.map((fieldObj) => fieldObj.name).join('.');\n          context.reportError(\n            `Cannot reference Input Object \"${fieldType.name}\" within itself through a series of non-null fields: \"${pathStr}\".`,\n            cyclePath.map((fieldObj) => fieldObj.astNode),\n          );\n        }\n\n        fieldPath.pop();\n      }\n    }\n\n    fieldPathIndexByTypeName[inputObj.name] = undefined;\n  }\n}\n\nfunction getAllImplementsInterfaceNodes(type, iface) {\n  const { astNode, extensionASTNodes } = type;\n  const nodes =\n    astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes\n    .flatMap((typeNode) => {\n      var _typeNode$interfaces;\n\n      return (\n        /* c8 ignore next */\n        (_typeNode$interfaces = typeNode.interfaces) !== null &&\n          _typeNode$interfaces !== void 0\n          ? _typeNode$interfaces\n          : []\n      );\n    })\n    .filter((ifaceNode) => ifaceNode.name.value === iface.name);\n}\n\nfunction getUnionMemberTypeNodes(union, typeName) {\n  const { astNode, extensionASTNodes } = union;\n  const nodes =\n    astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes\n    .flatMap((unionNode) => {\n      var _unionNode$types;\n\n      return (\n        /* c8 ignore next */\n        (_unionNode$types = unionNode.types) !== null &&\n          _unionNode$types !== void 0\n          ? _unionNode$types\n          : []\n      );\n    })\n    .filter((typeNode) => typeNode.name.value === typeName);\n}\n\nfunction getDeprecatedDirectiveNode(definitionNode) {\n  var _definitionNode$direc;\n\n  return definitionNode === null || definitionNode === void 0\n    ? void 0\n    : (_definitionNode$direc = definitionNode.directives) === null ||\n      _definitionNode$direc === void 0\n    ? void 0\n    : _definitionNode$direc.find(\n        (node) => node.name.value === GraphQLDeprecatedDirective.name,\n      );\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,EAAEC,eAAe,QAAQ,kCAAkC;AAC/E,SACEC,UAAU,EACVC,iBAAiB,EACjBC,WAAW,EACXC,eAAe,EACfC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACpBC,WAAW,QACN,kBAAkB;AACzB,SAASC,0BAA0B,EAAEC,WAAW,QAAQ,kBAAkB;AAC1E,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,YAAY,QAAQ,cAAc;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAE;EACrC;EACAF,YAAY,CAACE,MAAM,CAAC,CAAC,CAAC;;EAEtB,IAAIA,MAAM,CAACC,kBAAkB,EAAE;IAC7B,OAAOD,MAAM,CAACC,kBAAkB;EAClC,CAAC,CAAC;;EAEF,MAAMC,OAAO,GAAG,IAAIC,uBAAuB,CAACH,MAAM,CAAC;EACnDI,iBAAiB,CAACF,OAAO,CAAC;EAC1BG,kBAAkB,CAACH,OAAO,CAAC;EAC3BI,aAAa,CAACJ,OAAO,CAAC,CAAC,CAAC;EACxB;;EAEA,MAAMK,MAAM,GAAGL,OAAO,CAACM,SAAS,CAAC,CAAC;EAClCR,MAAM,CAACC,kBAAkB,GAAGM,MAAM;EAClC,OAAOA,MAAM;AACf;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,iBAAiBA,CAACT,MAAM,EAAE;EACxC,MAAMO,MAAM,GAAGR,cAAc,CAACC,MAAM,CAAC;EAErC,IAAIO,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACvB,MAAM,IAAIC,KAAK,CAACJ,MAAM,CAACK,GAAG,CAAEC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,CAAC;EACpE;AACF;AAEA,MAAMZ,uBAAuB,CAAC;EAC5Ba,WAAWA,CAAChB,MAAM,EAAE;IAClB,IAAI,CAACiB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACjB,MAAM,GAAGA,MAAM;EACtB;EAEAkB,WAAWA,CAACJ,OAAO,EAAEK,KAAK,EAAE;IAC1B,MAAMC,MAAM,GAAGC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACC,OAAO,CAAC,GAAGL,KAAK;IAEnE,IAAI,CAACF,OAAO,CAACQ,IAAI,CACf,IAAI7C,YAAY,CAACkC,OAAO,EAAE;MACxBK,KAAK,EAAEC;IACT,CAAC,CACH,CAAC;EACH;EAEAZ,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACS,OAAO;EACrB;AACF;AAEA,SAASb,iBAAiBA,CAACF,OAAO,EAAE;EAClC,MAAMF,MAAM,GAAGE,OAAO,CAACF,MAAM;EAC7B,MAAM0B,SAAS,GAAG1B,MAAM,CAAC2B,YAAY,CAAC,CAAC;EAEvC,IAAI,CAACD,SAAS,EAAE;IACdxB,OAAO,CAACgB,WAAW,CAAC,mCAAmC,EAAElB,MAAM,CAAC4B,OAAO,CAAC;EAC1E,CAAC,MAAM,IAAI,CAACtC,YAAY,CAACoC,SAAS,CAAC,EAAE;IACnC,IAAIG,qBAAqB;IAEzB3B,OAAO,CAACgB,WAAW,CAChB,qDAAoDvC,OAAO,CAC1D+C,SACF,CAAE,GAAE,EACJ,CAACG,qBAAqB,GAAGC,oBAAoB,CAC3C9B,MAAM,EACNnB,iBAAiB,CAACkD,KACpB,CAAC,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAC3CA,qBAAqB,GACrBH,SAAS,CAACE,OAChB,CAAC;EACH;EAEA,MAAMI,YAAY,GAAGhC,MAAM,CAACiC,eAAe,CAAC,CAAC;EAE7C,IAAID,YAAY,IAAI,CAAC1C,YAAY,CAAC0C,YAAY,CAAC,EAAE;IAC/C,IAAIE,sBAAsB;IAE1BhC,OAAO,CAACgB,WAAW,CACjB,mEAAmE,GAChE,GAAEvC,OAAO,CAACqD,YAAY,CAAE,GAAE,EAC7B,CAACE,sBAAsB,GAAGJ,oBAAoB,CAC5C9B,MAAM,EACNnB,iBAAiB,CAACsD,QACpB,CAAC,MAAM,IAAI,IAAID,sBAAsB,KAAK,KAAK,CAAC,GAC5CA,sBAAsB,GACtBF,YAAY,CAACJ,OACnB,CAAC;EACH;EAEA,MAAMQ,gBAAgB,GAAGpC,MAAM,CAACqC,mBAAmB,CAAC,CAAC;EAErD,IAAID,gBAAgB,IAAI,CAAC9C,YAAY,CAAC8C,gBAAgB,CAAC,EAAE;IACvD,IAAIE,sBAAsB;IAE1BpC,OAAO,CAACgB,WAAW,CACjB,uEAAuE,GACpE,GAAEvC,OAAO,CAACyD,gBAAgB,CAAE,GAAE,EACjC,CAACE,sBAAsB,GAAGR,oBAAoB,CAC5C9B,MAAM,EACNnB,iBAAiB,CAAC0D,YACpB,CAAC,MAAM,IAAI,IAAID,sBAAsB,KAAK,KAAK,CAAC,GAC5CA,sBAAsB,GACtBF,gBAAgB,CAACR,OACvB,CAAC;EACH;AACF;AAEA,SAASE,oBAAoBA,CAAC9B,MAAM,EAAEwC,SAAS,EAAE;EAC/C,IAAIC,aAAa;EAEjB,OAAO,CAACA,aAAa,GAAG,CAACzC,MAAM,CAAC4B,OAAO,EAAE,GAAG5B,MAAM,CAAC0C,iBAAiB,CAAC,CAClEC,OAAO;EACN;EACCC,UAAU,IAAK;IACd,IAAIC,qBAAqB;IAEzB,OACE;MACA,CAACA,qBAAqB,GACpBD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GACxC,KAAK,CAAC,GACNA,UAAU,CAACE,cAAc,MAAM,IAAI,IACvCD,qBAAqB,KAAK,KAAK,CAAC,GAC9BA,qBAAqB,GACrB;IAAE;EAEV,CACF,CAAC,CACAE,IAAI,CAAEC,aAAa,IAAKA,aAAa,CAACR,SAAS,KAAKA,SAAS,CAAC,MAAM,IAAI,IACzEC,aAAa,KAAK,KAAK,CAAC,GACtB,KAAK,CAAC,GACNA,aAAa,CAACQ,IAAI;AACxB;AAEA,SAAS5C,kBAAkBA,CAACH,OAAO,EAAE;EACnC,KAAK,MAAMgD,SAAS,IAAIhD,OAAO,CAACF,MAAM,CAACmD,aAAa,CAAC,CAAC,EAAE;IACtD;IACA,IAAI,CAACvD,WAAW,CAACsD,SAAS,CAAC,EAAE;MAC3BhD,OAAO,CAACgB,WAAW,CAChB,+BAA8BvC,OAAO,CAACuE,SAAS,CAAE,GAAE,EACpDA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtB,OAClE,CAAC;MACD;IACF,CAAC,CAAC;;IAEFwB,YAAY,CAAClD,OAAO,EAAEgD,SAAS,CAAC,CAAC,CAAC;IAClC;;IAEA,KAAK,MAAMG,GAAG,IAAIH,SAAS,CAACI,IAAI,EAAE;MAChC;MACAF,YAAY,CAAClD,OAAO,EAAEmD,GAAG,CAAC,CAAC,CAAC;;MAE5B,IAAI,CAACnE,WAAW,CAACmE,GAAG,CAACJ,IAAI,CAAC,EAAE;QAC1B/C,OAAO,CAACgB,WAAW,CAChB,gBAAegC,SAAS,CAACK,IAAK,IAAGF,GAAG,CAACE,IAAK,wBAAuB,GAC/D,YAAW5E,OAAO,CAAC0E,GAAG,CAACJ,IAAI,CAAE,GAAE,EAClCI,GAAG,CAACzB,OACN,CAAC;MACH;MAEA,IAAIpC,kBAAkB,CAAC6D,GAAG,CAAC,IAAIA,GAAG,CAACG,iBAAiB,IAAI,IAAI,EAAE;QAC5D,IAAIC,YAAY;QAEhBvD,OAAO,CAACgB,WAAW,CAChB,sBAAqBgC,SAAS,CAACK,IAAK,IAAGF,GAAG,CAACE,IAAK,0BAAyB,EAC1E,CACEG,0BAA0B,CAACL,GAAG,CAACzB,OAAO,CAAC,EACvC,CAAC6B,YAAY,GAAGJ,GAAG,CAACzB,OAAO,MAAM,IAAI,IAAI6B,YAAY,KAAK,KAAK,CAAC,GAC5D,KAAK,CAAC,GACNA,YAAY,CAACR,IAAI,CAEzB,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASG,YAAYA,CAAClD,OAAO,EAAEyD,IAAI,EAAE;EACnC;EACA,IAAIA,IAAI,CAACJ,IAAI,CAACK,UAAU,CAAC,IAAI,CAAC,EAAE;IAC9B1D,OAAO,CAACgB,WAAW,CAChB,SAAQyC,IAAI,CAACJ,IAAK,yEAAwE,EAC3FI,IAAI,CAAC/B,OACP,CAAC;EACH;AACF;AAEA,SAAStB,aAAaA,CAACJ,OAAO,EAAE;EAC9B,MAAM2D,+BAA+B,GACnCC,sCAAsC,CAAC5D,OAAO,CAAC;EACjD,MAAM6D,OAAO,GAAG7D,OAAO,CAACF,MAAM,CAACgE,UAAU,CAAC,CAAC;EAE3C,KAAK,MAAMf,IAAI,IAAIgB,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC,EAAE;IACzC;IACA,IAAI,CAAC3E,WAAW,CAAC6D,IAAI,CAAC,EAAE;MACtB/C,OAAO,CAACgB,WAAW,CAChB,wCAAuCvC,OAAO,CAACsE,IAAI,CAAE,GAAE,EACxDA,IAAI,CAACrB,OACP,CAAC;MACD;IACF,CAAC,CAAC;;IAEF,IAAI,CAAC/B,mBAAmB,CAACoD,IAAI,CAAC,EAAE;MAC9BG,YAAY,CAAClD,OAAO,EAAE+C,IAAI,CAAC;IAC7B;IAEA,IAAI3D,YAAY,CAAC2D,IAAI,CAAC,EAAE;MACtB;MACAkB,cAAc,CAACjE,OAAO,EAAE+C,IAAI,CAAC,CAAC,CAAC;;MAE/BmB,kBAAkB,CAAClE,OAAO,EAAE+C,IAAI,CAAC;IACnC,CAAC,MAAM,IAAI9D,eAAe,CAAC8D,IAAI,CAAC,EAAE;MAChC;MACAkB,cAAc,CAACjE,OAAO,EAAE+C,IAAI,CAAC,CAAC,CAAC;;MAE/BmB,kBAAkB,CAAClE,OAAO,EAAE+C,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIvD,WAAW,CAACuD,IAAI,CAAC,EAAE;MAC5B;MACAoB,oBAAoB,CAACnE,OAAO,EAAE+C,IAAI,CAAC;IACrC,CAAC,MAAM,IAAIjE,UAAU,CAACiE,IAAI,CAAC,EAAE;MAC3B;MACAqB,kBAAkB,CAACpE,OAAO,EAAE+C,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIhE,iBAAiB,CAACgE,IAAI,CAAC,EAAE;MAClC;MACAsB,mBAAmB,CAACrE,OAAO,EAAE+C,IAAI,CAAC,CAAC,CAAC;;MAEpCY,+BAA+B,CAACZ,IAAI,CAAC;IACvC;EACF;AACF;AAEA,SAASkB,cAAcA,CAACjE,OAAO,EAAE+C,IAAI,EAAE;EACrC,MAAMuB,MAAM,GAAGP,MAAM,CAACC,MAAM,CAACjB,IAAI,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAID,MAAM,CAAC9D,MAAM,KAAK,CAAC,EAAE;IACvBR,OAAO,CAACgB,WAAW,CAAE,QAAO+B,IAAI,CAACM,IAAK,kCAAiC,EAAE,CACvEN,IAAI,CAACrB,OAAO,EACZ,GAAGqB,IAAI,CAACP,iBAAiB,CAC1B,CAAC;EACJ;EAEA,KAAK,MAAMgC,KAAK,IAAIF,MAAM,EAAE;IAC1B;IACApB,YAAY,CAAClD,OAAO,EAAEwE,KAAK,CAAC,CAAC,CAAC;;IAE9B,IAAI,CAACnF,YAAY,CAACmF,KAAK,CAACzB,IAAI,CAAC,EAAE;MAC7B,IAAI0B,cAAc;MAElBzE,OAAO,CAACgB,WAAW,CAChB,eAAc+B,IAAI,CAACM,IAAK,IAAGmB,KAAK,CAACnB,IAAK,uBAAsB,GAC1D,YAAW5E,OAAO,CAAC+F,KAAK,CAACzB,IAAI,CAAE,GAAE,EACpC,CAAC0B,cAAc,GAAGD,KAAK,CAAC9C,OAAO,MAAM,IAAI,IAAI+C,cAAc,KAAK,KAAK,CAAC,GAClE,KAAK,CAAC,GACNA,cAAc,CAAC1B,IACrB,CAAC;IACH,CAAC,CAAC;;IAEF,KAAK,MAAMI,GAAG,IAAIqB,KAAK,CAACpB,IAAI,EAAE;MAC5B,MAAMsB,OAAO,GAAGvB,GAAG,CAACE,IAAI,CAAC,CAAC;;MAE1BH,YAAY,CAAClD,OAAO,EAAEmD,GAAG,CAAC,CAAC,CAAC;;MAE5B,IAAI,CAACnE,WAAW,CAACmE,GAAG,CAACJ,IAAI,CAAC,EAAE;QAC1B,IAAI4B,aAAa;QAEjB3E,OAAO,CAACgB,WAAW,CAChB,eAAc+B,IAAI,CAACM,IAAK,IAAGmB,KAAK,CAACnB,IAAK,IAAGqB,OAAQ,mBAAkB,GACjE,iBAAgBjG,OAAO,CAAC0E,GAAG,CAACJ,IAAI,CAAE,GAAE,EACvC,CAAC4B,aAAa,GAAGxB,GAAG,CAACzB,OAAO,MAAM,IAAI,IAAIiD,aAAa,KAAK,KAAK,CAAC,GAC9D,KAAK,CAAC,GACNA,aAAa,CAAC5B,IACpB,CAAC;MACH;MAEA,IAAIzD,kBAAkB,CAAC6D,GAAG,CAAC,IAAIA,GAAG,CAACG,iBAAiB,IAAI,IAAI,EAAE;QAC5D,IAAIsB,aAAa;QAEjB5E,OAAO,CAACgB,WAAW,CAChB,qBAAoB+B,IAAI,CAACM,IAAK,IAAGmB,KAAK,CAACnB,IAAK,IAAGqB,OAAQ,0BAAyB,EACjF,CACElB,0BAA0B,CAACL,GAAG,CAACzB,OAAO,CAAC,EACvC,CAACkD,aAAa,GAAGzB,GAAG,CAACzB,OAAO,MAAM,IAAI,IAAIkD,aAAa,KAAK,KAAK,CAAC,GAC9D,KAAK,CAAC,GACNA,aAAa,CAAC7B,IAAI,CAE1B,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASmB,kBAAkBA,CAAClE,OAAO,EAAE+C,IAAI,EAAE;EACzC,MAAM8B,cAAc,GAAGd,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC;EAE1C,KAAK,MAAMC,KAAK,IAAIhC,IAAI,CAACiC,aAAa,CAAC,CAAC,EAAE;IACxC,IAAI,CAAC/F,eAAe,CAAC8F,KAAK,CAAC,EAAE;MAC3B/E,OAAO,CAACgB,WAAW,CAChB,QAAOvC,OAAO,CAACsE,IAAI,CAAE,wCAAuC,GAC1D,uBAAsBtE,OAAO,CAACsG,KAAK,CAAE,GAAE,EAC1CE,8BAA8B,CAAClC,IAAI,EAAEgC,KAAK,CAC5C,CAAC;MACD;IACF;IAEA,IAAIhC,IAAI,KAAKgC,KAAK,EAAE;MAClB/E,OAAO,CAACgB,WAAW,CAChB,QAAO+B,IAAI,CAACM,IAAK,wEAAuE,EACzF4B,8BAA8B,CAAClC,IAAI,EAAEgC,KAAK,CAC5C,CAAC;MACD;IACF;IAEA,IAAIF,cAAc,CAACE,KAAK,CAAC1B,IAAI,CAAC,EAAE;MAC9BrD,OAAO,CAACgB,WAAW,CAChB,QAAO+B,IAAI,CAACM,IAAK,uBAAsB0B,KAAK,CAAC1B,IAAK,QAAO,EAC1D4B,8BAA8B,CAAClC,IAAI,EAAEgC,KAAK,CAC5C,CAAC;MACD;IACF;IAEAF,cAAc,CAACE,KAAK,CAAC1B,IAAI,CAAC,GAAG,IAAI;IACjC6B,+BAA+B,CAAClF,OAAO,EAAE+C,IAAI,EAAEgC,KAAK,CAAC;IACrDI,+BAA+B,CAACnF,OAAO,EAAE+C,IAAI,EAAEgC,KAAK,CAAC;EACvD;AACF;AAEA,SAASI,+BAA+BA,CAACnF,OAAO,EAAE+C,IAAI,EAAEgC,KAAK,EAAE;EAC7D,MAAMK,YAAY,GAAGrC,IAAI,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEvC,KAAK,MAAMc,UAAU,IAAItB,MAAM,CAACC,MAAM,CAACe,KAAK,CAACR,SAAS,CAAC,CAAC,CAAC,EAAE;IACzD,MAAMe,SAAS,GAAGD,UAAU,CAAChC,IAAI;IACjC,MAAMkC,SAAS,GAAGH,YAAY,CAACE,SAAS,CAAC,CAAC,CAAC;;IAE3C,IAAI,CAACC,SAAS,EAAE;MACdvF,OAAO,CAACgB,WAAW,CAChB,mBAAkB+D,KAAK,CAAC1B,IAAK,IAAGiC,SAAU,iBAAgBvC,IAAI,CAACM,IAAK,uBAAsB,EAC3F,CAACgC,UAAU,CAAC3D,OAAO,EAAEqB,IAAI,CAACrB,OAAO,EAAE,GAAGqB,IAAI,CAACP,iBAAiB,CAC9D,CAAC;MACD;IACF,CAAC,CAAC;IACF;;IAEA,IAAI,CAAC3D,eAAe,CAACmB,OAAO,CAACF,MAAM,EAAEyF,SAAS,CAACxC,IAAI,EAAEsC,UAAU,CAACtC,IAAI,CAAC,EAAE;MACrE,IAAIyC,mBAAmB,EAAEC,kBAAkB;MAE3CzF,OAAO,CAACgB,WAAW,CAChB,mBAAkB+D,KAAK,CAAC1B,IAAK,IAAGiC,SAAU,gBAAe,GACvD,GAAE7G,OAAO,CAAC4G,UAAU,CAACtC,IAAI,CAAE,QAAOA,IAAI,CAACM,IAAK,IAAGiC,SAAU,GAAE,GAC3D,WAAU7G,OAAO,CAAC8G,SAAS,CAACxC,IAAI,CAAE,GAAE,EACvC,CACE,CAACyC,mBAAmB,GAAGH,UAAU,CAAC3D,OAAO,MAAM,IAAI,IACnD8D,mBAAmB,KAAK,KAAK,CAAC,GAC1B,KAAK,CAAC,GACNA,mBAAmB,CAACzC,IAAI,EAC5B,CAAC0C,kBAAkB,GAAGF,SAAS,CAAC7D,OAAO,MAAM,IAAI,IACjD+D,kBAAkB,KAAK,KAAK,CAAC,GACzB,KAAK,CAAC,GACNA,kBAAkB,CAAC1C,IAAI,CAE/B,CAAC;IACH,CAAC,CAAC;;IAEF,KAAK,MAAM2C,QAAQ,IAAIL,UAAU,CAACjC,IAAI,EAAE;MACtC,MAAMsB,OAAO,GAAGgB,QAAQ,CAACrC,IAAI;MAC7B,MAAMsC,OAAO,GAAGJ,SAAS,CAACnC,IAAI,CAACP,IAAI,CAAEM,GAAG,IAAKA,GAAG,CAACE,IAAI,KAAKqB,OAAO,CAAC,CAAC,CAAC;;MAEpE,IAAI,CAACiB,OAAO,EAAE;QACZ3F,OAAO,CAACgB,WAAW,CAChB,4BAA2B+D,KAAK,CAAC1B,IAAK,IAAGiC,SAAU,IAAGZ,OAAQ,mBAAkB3B,IAAI,CAACM,IAAK,IAAGiC,SAAU,uBAAsB,EAC9H,CAACI,QAAQ,CAAChE,OAAO,EAAE6D,SAAS,CAAC7D,OAAO,CACtC,CAAC;QACD;MACF,CAAC,CAAC;MACF;MACA;;MAEA,IAAI,CAAC9C,WAAW,CAAC8G,QAAQ,CAAC3C,IAAI,EAAE4C,OAAO,CAAC5C,IAAI,CAAC,EAAE;QAC7C,IAAI6C,iBAAiB,EAAEC,gBAAgB;QAEvC7F,OAAO,CAACgB,WAAW,CAChB,4BAA2B+D,KAAK,CAAC1B,IAAK,IAAGiC,SAAU,IAAGZ,OAAQ,KAAI,GAChE,gBAAejG,OAAO,CAACiH,QAAQ,CAAC3C,IAAI,CAAE,OAAM,GAC5C,GAAEA,IAAI,CAACM,IAAK,IAAGiC,SAAU,IAAGZ,OAAQ,aAAY,GAChD,GAAEjG,OAAO,CAACkH,OAAO,CAAC5C,IAAI,CAAE,GAAE,EAC7B,CACE,CAAC6C,iBAAiB,GAAGF,QAAQ,CAAChE,OAAO,MAAM,IAAI,IAC/CkE,iBAAiB,KAAK,KAAK,CAAC,GACxB,KAAK,CAAC,GACNA,iBAAiB,CAAC7C,IAAI,EAC1B,CAAC8C,gBAAgB,GAAGF,OAAO,CAACjE,OAAO,MAAM,IAAI,IAC7CmE,gBAAgB,KAAK,KAAK,CAAC,GACvB,KAAK,CAAC,GACNA,gBAAgB,CAAC9C,IAAI,CAE7B,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF,KAAK,MAAM4C,OAAO,IAAIJ,SAAS,CAACnC,IAAI,EAAE;MACpC,MAAMsB,OAAO,GAAGiB,OAAO,CAACtC,IAAI;MAC5B,MAAMqC,QAAQ,GAAGL,UAAU,CAACjC,IAAI,CAACP,IAAI,CAAEM,GAAG,IAAKA,GAAG,CAACE,IAAI,KAAKqB,OAAO,CAAC;MAEpE,IAAI,CAACgB,QAAQ,IAAIpG,kBAAkB,CAACqG,OAAO,CAAC,EAAE;QAC5C3F,OAAO,CAACgB,WAAW,CAChB,gBAAe+B,IAAI,CAACM,IAAK,IAAGiC,SAAU,+BAA8BZ,OAAQ,6CAA4CK,KAAK,CAAC1B,IAAK,IAAGiC,SAAU,GAAE,EACnJ,CAACK,OAAO,CAACjE,OAAO,EAAE2D,UAAU,CAAC3D,OAAO,CACtC,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASwD,+BAA+BA,CAAClF,OAAO,EAAE+C,IAAI,EAAEgC,KAAK,EAAE;EAC7D,MAAMe,eAAe,GAAG/C,IAAI,CAACiC,aAAa,CAAC,CAAC;EAE5C,KAAK,MAAMe,UAAU,IAAIhB,KAAK,CAACC,aAAa,CAAC,CAAC,EAAE;IAC9C,IAAI,CAACc,eAAe,CAACE,QAAQ,CAACD,UAAU,CAAC,EAAE;MACzC/F,OAAO,CAACgB,WAAW,CACjB+E,UAAU,KAAKhD,IAAI,GACd,QAAOA,IAAI,CAACM,IAAK,qBAAoB0B,KAAK,CAAC1B,IAAK,gDAA+C,GAC/F,QAAON,IAAI,CAACM,IAAK,mBAAkB0C,UAAU,CAAC1C,IAAK,iCAAgC0B,KAAK,CAAC1B,IAAK,GAAE,EACrG,CACE,GAAG4B,8BAA8B,CAACF,KAAK,EAAEgB,UAAU,CAAC,EACpD,GAAGd,8BAA8B,CAAClC,IAAI,EAAEgC,KAAK,CAAC,CAElD,CAAC;IACH;EACF;AACF;AAEA,SAASZ,oBAAoBA,CAACnE,OAAO,EAAEiG,KAAK,EAAE;EAC5C,MAAMC,WAAW,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;EAEpC,IAAID,WAAW,CAAC1F,MAAM,KAAK,CAAC,EAAE;IAC5BR,OAAO,CAACgB,WAAW,CAChB,cAAaiF,KAAK,CAAC5C,IAAK,wCAAuC,EAChE,CAAC4C,KAAK,CAACvE,OAAO,EAAE,GAAGuE,KAAK,CAACzD,iBAAiB,CAC5C,CAAC;EACH;EAEA,MAAM4D,iBAAiB,GAAGrC,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC;EAE7C,KAAK,MAAMuB,UAAU,IAAIH,WAAW,EAAE;IACpC,IAAIE,iBAAiB,CAACC,UAAU,CAAChD,IAAI,CAAC,EAAE;MACtCrD,OAAO,CAACgB,WAAW,CAChB,cAAaiF,KAAK,CAAC5C,IAAK,0BAAyBgD,UAAU,CAAChD,IAAK,QAAO,EACzEiD,uBAAuB,CAACL,KAAK,EAAEI,UAAU,CAAChD,IAAI,CAChD,CAAC;MACD;IACF;IAEA+C,iBAAiB,CAACC,UAAU,CAAChD,IAAI,CAAC,GAAG,IAAI;IAEzC,IAAI,CAACjE,YAAY,CAACiH,UAAU,CAAC,EAAE;MAC7BrG,OAAO,CAACgB,WAAW,CAChB,cAAaiF,KAAK,CAAC5C,IAAK,kCAAiC,GACvD,qBAAoB5E,OAAO,CAAC4H,UAAU,CAAE,GAAE,EAC7CC,uBAAuB,CAACL,KAAK,EAAEM,MAAM,CAACF,UAAU,CAAC,CACnD,CAAC;IACH;EACF;AACF;AAEA,SAASjC,kBAAkBA,CAACpE,OAAO,EAAEwG,QAAQ,EAAE;EAC7C,MAAMC,UAAU,GAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC;EAEvC,IAAID,UAAU,CAACjG,MAAM,KAAK,CAAC,EAAE;IAC3BR,OAAO,CAACgB,WAAW,CAChB,aAAYwF,QAAQ,CAACnD,IAAK,kCAAiC,EAC5D,CAACmD,QAAQ,CAAC9E,OAAO,EAAE,GAAG8E,QAAQ,CAAChE,iBAAiB,CAClD,CAAC;EACH;EAEA,KAAK,MAAMmE,SAAS,IAAIF,UAAU,EAAE;IAClC;IACAvD,YAAY,CAAClD,OAAO,EAAE2G,SAAS,CAAC;EAClC;AACF;AAEA,SAAStC,mBAAmBA,CAACrE,OAAO,EAAE4G,QAAQ,EAAE;EAC9C,MAAMtC,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAC4C,QAAQ,CAACrC,SAAS,CAAC,CAAC,CAAC;EAElD,IAAID,MAAM,CAAC9D,MAAM,KAAK,CAAC,EAAE;IACvBR,OAAO,CAACgB,WAAW,CAChB,qBAAoB4F,QAAQ,CAACvD,IAAK,kCAAiC,EACpE,CAACuD,QAAQ,CAAClF,OAAO,EAAE,GAAGkF,QAAQ,CAACpE,iBAAiB,CAClD,CAAC;EACH,CAAC,CAAC;;EAEF,KAAK,MAAMgC,KAAK,IAAIF,MAAM,EAAE;IAC1B;IACApB,YAAY,CAAClD,OAAO,EAAEwE,KAAK,CAAC,CAAC,CAAC;;IAE9B,IAAI,CAACxF,WAAW,CAACwF,KAAK,CAACzB,IAAI,CAAC,EAAE;MAC5B,IAAI8D,eAAe;MAEnB7G,OAAO,CAACgB,WAAW,CAChB,eAAc4F,QAAQ,CAACvD,IAAK,IAAGmB,KAAK,CAACnB,IAAK,sBAAqB,GAC7D,YAAW5E,OAAO,CAAC+F,KAAK,CAACzB,IAAI,CAAE,GAAE,EACpC,CAAC8D,eAAe,GAAGrC,KAAK,CAAC9C,OAAO,MAAM,IAAI,IAAImF,eAAe,KAAK,KAAK,CAAC,GACpE,KAAK,CAAC,GACNA,eAAe,CAAC9D,IACtB,CAAC;IACH;IAEA,IAAIxD,oBAAoB,CAACiF,KAAK,CAAC,IAAIA,KAAK,CAAClB,iBAAiB,IAAI,IAAI,EAAE;MAClE,IAAIwD,eAAe;MAEnB9G,OAAO,CAACgB,WAAW,CAChB,wBAAuB4F,QAAQ,CAACvD,IAAK,IAAGmB,KAAK,CAACnB,IAAK,wBAAuB,EAC3E,CACEG,0BAA0B,CAACgB,KAAK,CAAC9C,OAAO,CAAC,EACzC,CAACoF,eAAe,GAAGtC,KAAK,CAAC9C,OAAO,MAAM,IAAI,IAC1CoF,eAAe,KAAK,KAAK,CAAC,GACtB,KAAK,CAAC,GACNA,eAAe,CAAC/D,IAAI,CAE5B,CAAC;IACH;EACF;AACF;AAEA,SAASa,sCAAsCA,CAAC5D,OAAO,EAAE;EACvD;EACA;EACA;EACA,MAAM+G,YAAY,GAAGhD,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE1C,MAAMkC,SAAS,GAAG,EAAE,CAAC,CAAC;;EAEtB,MAAMC,wBAAwB,GAAGlD,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC;EACpD,OAAOoC,oBAAoB,CAAC,CAAC;EAC7B;EACA;;EAEA,SAASA,oBAAoBA,CAACN,QAAQ,EAAE;IACtC,IAAIG,YAAY,CAACH,QAAQ,CAACvD,IAAI,CAAC,EAAE;MAC/B;IACF;IAEA0D,YAAY,CAACH,QAAQ,CAACvD,IAAI,CAAC,GAAG,IAAI;IAClC4D,wBAAwB,CAACL,QAAQ,CAACvD,IAAI,CAAC,GAAG2D,SAAS,CAACxG,MAAM;IAC1D,MAAM8D,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAC4C,QAAQ,CAACrC,SAAS,CAAC,CAAC,CAAC;IAElD,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MAC1B,IAAInF,aAAa,CAACqF,KAAK,CAACzB,IAAI,CAAC,IAAIhE,iBAAiB,CAACyF,KAAK,CAACzB,IAAI,CAACoE,MAAM,CAAC,EAAE;QACrE,MAAMC,SAAS,GAAG5C,KAAK,CAACzB,IAAI,CAACoE,MAAM;QACnC,MAAME,UAAU,GAAGJ,wBAAwB,CAACG,SAAS,CAAC/D,IAAI,CAAC;QAC3D2D,SAAS,CAACzF,IAAI,CAACiD,KAAK,CAAC;QAErB,IAAI6C,UAAU,KAAKC,SAAS,EAAE;UAC5BJ,oBAAoB,CAACE,SAAS,CAAC;QACjC,CAAC,MAAM;UACL,MAAMG,SAAS,GAAGP,SAAS,CAACQ,KAAK,CAACH,UAAU,CAAC;UAC7C,MAAMI,OAAO,GAAGF,SAAS,CAAC7G,GAAG,CAAEgH,QAAQ,IAAKA,QAAQ,CAACrE,IAAI,CAAC,CAACxC,IAAI,CAAC,GAAG,CAAC;UACpEb,OAAO,CAACgB,WAAW,CAChB,kCAAiCoG,SAAS,CAAC/D,IAAK,yDAAwDoE,OAAQ,IAAG,EACpHF,SAAS,CAAC7G,GAAG,CAAEgH,QAAQ,IAAKA,QAAQ,CAAChG,OAAO,CAC9C,CAAC;QACH;QAEAsF,SAAS,CAACW,GAAG,CAAC,CAAC;MACjB;IACF;IAEAV,wBAAwB,CAACL,QAAQ,CAACvD,IAAI,CAAC,GAAGiE,SAAS;EACrD;AACF;AAEA,SAASrC,8BAA8BA,CAAClC,IAAI,EAAEgC,KAAK,EAAE;EACnD,MAAM;IAAErD,OAAO;IAAEc;EAAkB,CAAC,GAAGO,IAAI;EAC3C,MAAM9B,KAAK,GACTS,OAAO,IAAI,IAAI,GAAG,CAACA,OAAO,EAAE,GAAGc,iBAAiB,CAAC,GAAGA,iBAAiB,CAAC,CAAC;;EAEzE,OAAOvB,KAAK,CACTwB,OAAO,CAAEmF,QAAQ,IAAK;IACrB,IAAIC,oBAAoB;IAExB,OACE;MACA,CAACA,oBAAoB,GAAGD,QAAQ,CAACE,UAAU,MAAM,IAAI,IACnDD,oBAAoB,KAAK,KAAK,CAAC,GAC7BA,oBAAoB,GACpB;IAAE;EAEV,CAAC,CAAC,CACDxG,MAAM,CAAE0G,SAAS,IAAKA,SAAS,CAAC1E,IAAI,CAAC2E,KAAK,KAAKjD,KAAK,CAAC1B,IAAI,CAAC;AAC/D;AAEA,SAASiD,uBAAuBA,CAACL,KAAK,EAAEgC,QAAQ,EAAE;EAChD,MAAM;IAAEvG,OAAO;IAAEc;EAAkB,CAAC,GAAGyD,KAAK;EAC5C,MAAMhF,KAAK,GACTS,OAAO,IAAI,IAAI,GAAG,CAACA,OAAO,EAAE,GAAGc,iBAAiB,CAAC,GAAGA,iBAAiB,CAAC,CAAC;;EAEzE,OAAOvB,KAAK,CACTwB,OAAO,CAAEyF,SAAS,IAAK;IACtB,IAAIC,gBAAgB;IAEpB,OACE;MACA,CAACA,gBAAgB,GAAGD,SAAS,CAACE,KAAK,MAAM,IAAI,IAC3CD,gBAAgB,KAAK,KAAK,CAAC,GACzBA,gBAAgB,GAChB;IAAE;EAEV,CAAC,CAAC,CACD9G,MAAM,CAAEuG,QAAQ,IAAKA,QAAQ,CAACvE,IAAI,CAAC2E,KAAK,KAAKC,QAAQ,CAAC;AAC3D;AAEA,SAASzE,0BAA0BA,CAAC6E,cAAc,EAAE;EAClD,IAAIC,qBAAqB;EAEzB,OAAOD,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GACvD,KAAK,CAAC,GACN,CAACC,qBAAqB,GAAGD,cAAc,CAACE,UAAU,MAAM,IAAI,IAC5DD,qBAAqB,KAAK,KAAK,CAAC,GAChC,KAAK,CAAC,GACNA,qBAAqB,CAACzF,IAAI,CACvBY,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAAC2E,KAAK,KAAKvI,0BAA0B,CAAC4D,IAC3D,CAAC;AACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}