{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link endOfYesterday} function options.\n */\n\n/**\n * @name endOfYesterday\n * @category Day Helpers\n * @summary Return the end of yesterday.\n * @pure false\n *\n * @description\n * Return the end of yesterday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @returns The end of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfYesterday()\n * //=> Sun Oct 5 2014 23:59:59.999\n */\nexport function endOfYesterday(options) {\n  const now = constructNow(options?.in);\n  const date = constructFrom(options?.in, 0);\n  date.setFullYear(now.getFullYear(), now.getMonth(), now.getDate() - 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default endOfYesterday;", "map": {"version": 3, "names": ["constructFrom", "constructNow", "endOfYesterday", "options", "now", "in", "date", "setFullYear", "getFullYear", "getMonth", "getDate", "setHours"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/endOfYesterday.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link endOfYesterday} function options.\n */\n\n/**\n * @name endOfYesterday\n * @category Day Helpers\n * @summary Return the end of yesterday.\n * @pure false\n *\n * @description\n * Return the end of yesterday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @returns The end of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfYesterday()\n * //=> Sun Oct 5 2014 23:59:59.999\n */\nexport function endOfYesterday(options) {\n  const now = constructNow(options?.in);\n  const date = constructFrom(options?.in, 0);\n  date.setFullYear(now.getFullYear(), now.getMonth(), now.getDate() - 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default endOfYesterday;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,mBAAmB;;AAEhD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAE;EACtC,MAAMC,GAAG,GAAGH,YAAY,CAACE,OAAO,EAAEE,EAAE,CAAC;EACrC,MAAMC,IAAI,GAAGN,aAAa,CAACG,OAAO,EAAEE,EAAE,EAAE,CAAC,CAAC;EAC1CC,IAAI,CAACC,WAAW,CAACH,GAAG,CAACI,WAAW,CAAC,CAAC,EAAEJ,GAAG,CAACK,QAAQ,CAAC,CAAC,EAAEL,GAAG,CAACM,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EACtEJ,IAAI,CAACK,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC9B,OAAOL,IAAI;AACb;;AAEA;AACA,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}