{"ast": null, "code": "/**\n * Given a Path and a key, return a new Path containing the new key.\n */\nexport function addPath(prev, key, typename) {\n  return {\n    prev,\n    key,\n    typename\n  };\n}\n/**\n * Given a Path, return an Array of the path keys.\n */\n\nexport function pathToArray(path) {\n  const flattened = [];\n  let curr = path;\n  while (curr) {\n    flattened.push(curr.key);\n    curr = curr.prev;\n  }\n  return flattened.reverse();\n}", "map": {"version": 3, "names": ["addPath", "prev", "key", "typename", "pathToArray", "path", "flattened", "curr", "push", "reverse"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/jsutils/Path.mjs"], "sourcesContent": ["/**\n * Given a Path and a key, return a new Path containing the new key.\n */\nexport function addPath(prev, key, typename) {\n  return {\n    prev,\n    key,\n    typename,\n  };\n}\n/**\n * Given a Path, return an Array of the path keys.\n */\n\nexport function pathToArray(path) {\n  const flattened = [];\n  let curr = path;\n\n  while (curr) {\n    flattened.push(curr.key);\n    curr = curr.prev;\n  }\n\n  return flattened.reverse();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EAC3C,OAAO;IACLF,IAAI;IACJC,GAAG;IACHC;EACF,CAAC;AACH;AACA;AACA;AACA;;AAEA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,MAAMC,SAAS,GAAG,EAAE;EACpB,IAAIC,IAAI,GAAGF,IAAI;EAEf,OAAOE,IAAI,EAAE;IACXD,SAAS,CAACE,IAAI,CAACD,IAAI,CAACL,GAAG,CAAC;IACxBK,IAAI,GAAGA,IAAI,CAACN,IAAI;EAClB;EAEA,OAAOK,SAAS,CAACG,OAAO,CAAC,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}