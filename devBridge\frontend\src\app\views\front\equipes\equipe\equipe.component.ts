import { Component, OnInit } from '@angular/core';
import { EquipeService } from 'src/app/services/equipe.service';
import { MembreService } from 'src/app/services/membre.service';
import { Equipe } from 'src/app/models/equipe.model';
import { Membre } from 'src/app/models/membre.model';
import { forkJoin } from 'rxjs';

// Add Bootstrap type declaration
declare global {
  interface Window {
    bootstrap: any;
  }
}

@Component({
  selector: 'app-equipe',
  templateUrl: './equipe.component.html',
  styleUrls: ['./equipe.component.css'],
})
export class EquipeComponent implements OnInit {
  equipes: Equipe[] = [];
  newEquipe: Equipe = { name: '', description: '' };
  selectedEquipe: Equipe | null = null;
  isEditing = false;
  membres: Membre[] = [];
  loading = false;
  error = '';
  showMemberModal = false;

  constructor(
    private equipeService: EquipeService,
    private membreService: MembreService
  ) {}

  // Méthode utilitaire pour extraire l'ID d'un membre
  getMemberId(membre: string | any): string {
    if (typeof membre === 'string') {
      return membre;
    }
    return membre._id || membre.id || '';
  }

  ngOnInit(): void {
    this.loadEquipes();
    this.loadMembres();
  }

  loadEquipes() {
    this.loading = true;
    this.equipeService.getEquipes().subscribe({
      next: (data) => {
        console.log('Loaded equipes:', data);
        this.equipes = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading equipes:', error);
        this.error = 'Erreur lors du chargement des équipes: ' + error.message;
        this.loading = false;
      }
    });
  }

  loadMembres() {
    this.loading = true;
    this.membreService.getMembres().subscribe({
      next: (data) => {
        console.log('Loaded membres:', data);
        this.membres = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading membres:', error);
        this.error = 'Erreur lors du chargement des membres: ' + error.message;
        this.loading = false;
      }
    });
  }

  addEquipe() {
    console.log('Adding equipe:', this.newEquipe);

    if (!this.newEquipe.name) {
      console.error('Team name is required');
      this.error = 'Le nom de l\'équipe est requis';
      return;
    }

    this.loading = true;
    this.error = '';

    this.equipeService.addEquipe(this.newEquipe).subscribe({
      next: (response) => {
        console.log('Equipe added successfully:', response);
        this.loadEquipes();
        this.newEquipe = { name: '', description: '' }; // Clear input
        this.loading = false;

        // Afficher un message de succès temporaire
        this.showSuccessMessage('Équipe créée avec succès!');
        this.error = ''; // Effacer les erreurs précédentes
      },
      error: (error) => {
        console.error('Error adding equipe:', error);
        this.error = 'Erreur lors de la création de l\'équipe: ' + (error.error?.message || error.message || 'Unknown error');
        this.loading = false;
      }
    });
  }

  editEquipe(equipe: Equipe) {
    this.isEditing = true;
    // Créer une copie profonde pour éviter de modifier l'objet original
    this.newEquipe = {
      _id: equipe._id,
      name: equipe.name || '',
      description: equipe.description || '',
      admin: equipe.admin,
      members: equipe.members ? [...equipe.members] : []
    };
  }

  cancelEdit() {
    this.isEditing = false;
    this.newEquipe = { name: '', description: '' };
    this.error = ''; // Effacer les erreurs
  }

  updateSelectedEquipe() {
    if (!this.newEquipe.name) {
      console.error('Team name is required');
      this.error = 'Le nom de l\'équipe est requis';
      return;
    }

    if (this.newEquipe._id) {
      this.loading = true;
      this.error = '';

      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({
        next: (updatedEquipe) => {
          console.log('Team updated successfully:', updatedEquipe);
          this.loadEquipes();
          this.isEditing = false;
          this.newEquipe = { name: '', description: '' };
          this.loading = false;

          // Afficher un message de succès temporaire
          this.showSuccessMessage('Équipe mise à jour avec succès!');
        },
        error: (error) => {
          console.error('Error updating team:', error);
          this.error = 'Erreur lors de la mise à jour de l\'équipe: ' + (error.error?.message || error.message || 'Unknown error');
          this.loading = false;
        }
      });
    } else {
      this.error = 'ID de l\'équipe manquant pour la mise à jour';
    }
  }

  deleteEquipe(id: string) {
    if (!id) {
      console.error('ID is undefined');
      this.error = 'ID de l\'équipe non défini';
      return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {
      this.loading = true;
      this.error = '';

      this.equipeService.deleteEquipe(id).subscribe({
        next: (response) => {
          console.log('Team deleted successfully:', response);

          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire
          if (this.isEditing && this.newEquipe._id === id) {
            this.isEditing = false;
            this.newEquipe = { name: '', description: '' };
          }

          this.loadEquipes();
          this.loading = false;

          // Afficher un message de succès
          this.showSuccessMessage('Équipe supprimée avec succès');
        },
        error: (error) => {
          console.error('Error deleting team:', error);
          this.error = 'Erreur lors de la suppression de l\'équipe: ' + (error.error?.message || error.message || 'Unknown error');
          this.loading = false;
        }
      });
    }
  }

  showMembreModal(equipe: Equipe) {
    this.selectedEquipe = equipe;
    this.showMemberModal = true;
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  }

  closeMemberModal(event?: Event) {
    if (event) {
      // Only close if clicking on backdrop, not on modal content
      const target = event.target as HTMLElement;
      if (!target.closest('.bg-white')) {
        return;
      }
    }

    this.showMemberModal = false;
    this.selectedEquipe = null;
    // Restore body scroll
    document.body.style.overflow = 'auto';
  }

  // Scroll to form when "New Team" button is clicked
  scrollToForm() {
    const formElement = document.getElementById('teamForm');
    if (formElement) {
      formElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // Focus on the name input after scrolling
      setTimeout(() => {
        const nameInput = document.getElementById('teamName') as HTMLInputElement;
        if (nameInput) {
          nameInput.focus();
        }
      }, 500);
    }
  }

  // TrackBy functions for better performance
  trackByEquipeId(index: number, equipe: Equipe): any {
    return equipe._id || index;
  }

  trackByMemberId(index: number, membreId: string | any): any {
    if (typeof membreId === 'string') {
      return membreId;
    }
    return membreId._id || membreId.id || index;
  }

  // Show success message with modern notification
  showSuccessMessage(message: string) {
    // Create a temporary success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 z-50 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 shadow-lg transform translate-x-full transition-transform duration-300';
    notification.innerHTML = `
      <div class="flex items-start">
        <div class="text-green-500 dark:text-green-400 mr-3 mt-0.5">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="flex-1">
          <h3 class="font-semibold text-green-800 dark:text-green-300 mb-1">Succès</h3>
          <p class="text-green-700 dark:text-green-400 text-sm">${message}</p>
        </div>
        <button onclick="this.parentElement.parentElement.remove()" class="text-green-500 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 ml-3">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 300);
    }, 5000);
  }

  addMembreToEquipe(teamId: string | undefined, membreId: string) {
    if (!teamId) {
      console.error('Team ID is undefined');
      this.error = 'ID de l\'équipe non défini';
      return;
    }

    if (!membreId || membreId.trim() === '') {
      console.error('Member ID is empty');
      this.error = 'L\'ID du membre est requis';
      return;
    }

    this.loading = true;

    // Create a proper Membre object that matches what the API expects
    const membre: Membre = { id: membreId };

    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({
      next: (response) => {
        console.log('Member added successfully:', response);
        this.loadEquipes();
        this.loading = false;

        // Afficher un message de succès
        this.showSuccessMessage('Membre ajouté avec succès à l\'équipe');
      },
      error: (error) => {
        console.error('Error adding member:', error);
        this.error = 'Erreur lors de l\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');
        this.loading = false;
      }
    });
  }

  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {
    if (!teamId) {
      console.error('Team ID is undefined');
      this.error = 'ID de l\'équipe non défini';
      return;
    }

    if (!membreId) {
      console.error('Member ID is undefined');
      this.error = 'ID du membre non défini';
      return;
    }

    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\'équipe?')) {
      this.loading = true;

      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({
        next: (response) => {
          console.log('Member removed successfully:', response);
          this.loadEquipes();
          this.loading = false;

          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée
          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {
            const updatedEquipe = this.equipes.find(e => e._id === teamId);
            if (updatedEquipe) {
              this.selectedEquipe = updatedEquipe;
            }
          }
        },
        error: (error) => {
          console.error('Error removing member:', error);
          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');
          this.loading = false;
        }
      });
    }
  }
}
















