<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Création d'Équipe</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.danger { background: #dc3545; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; box-sizing: border-box; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Création d'Équipe - Diagnostic Complet</h1>
        
        <div class="section">
            <h2>🔗 État de la Connexion</h2>
            <div>Backend: <span id="backendStatus" class="status">Vérification...</span></div>
            <div>Frontend: <span id="frontendStatus" class="status online">En ligne</span></div>
            <button class="button" onclick="checkConnection()">Vérifier Connexion</button>
            <div id="connectionResult" class="result"></div>
        </div>

        <div class="section">
            <h2>🔐 Test d'Authentification</h2>
            <input type="text" id="token" placeholder="Token JWT (optionnel - sera pris du localStorage)">
            <button class="button" onclick="testAuth()">Tester Auth</button>
            <div id="authResult" class="result"></div>
        </div>

        <div class="section">
            <h2>📋 Test Récupération des Équipes</h2>
            <button class="button" onclick="getTeams()">Récupérer Équipes</button>
            <div id="teamsResult" class="result"></div>
        </div>

        <div class="section">
            <h2>➕ Test Création d'Équipe</h2>
            <input type="text" id="teamName" placeholder="Nom de l'équipe" value="">
            <textarea id="teamDescription" placeholder="Description de l'équipe" rows="3"></textarea>
            <button class="button success" onclick="createTeam()">Créer Équipe</button>
            <button class="button" onclick="generateTestData()">Générer Données Test</button>
            <div id="createResult" class="result"></div>
        </div>

        <div class="section">
            <h2>🔍 Diagnostic Avancé</h2>
            <button class="button" onclick="fullDiagnostic()">Diagnostic Complet</button>
            <button class="button danger" onclick="clearResults()">Effacer Résultats</button>
            <div id="diagnosticResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        function getToken() {
            return document.getElementById('token').value || localStorage.getItem('token');
        }
        
        function getHeaders() {
            const token = getToken();
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
        }
        
        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }
        
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/teams`, {
                    method: 'HEAD',
                    headers: getHeaders()
                });
                
                const status = response.ok ? 'En ligne' : 'Erreur';
                document.getElementById('backendStatus').textContent = status;
                document.getElementById('backendStatus').className = `status ${response.ok ? 'online' : 'offline'}`;
                
                displayResult('connectionResult', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                }, !response.ok);
            } catch (error) {
                document.getElementById('backendStatus').textContent = 'Hors ligne';
                document.getElementById('backendStatus').className = 'status offline';
                displayResult('connectionResult', { error: error.message }, true);
            }
        }
        
        async function testAuth() {
            const token = getToken();
            if (!token) {
                displayResult('authResult', { error: 'Aucun token fourni' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/profile`, {
                    headers: getHeaders()
                });
                
                const data = await response.json();
                displayResult('authResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                displayResult('authResult', { error: error.message }, true);
            }
        }
        
        async function getTeams() {
            try {
                const response = await fetch(`${API_BASE}/teams`, {
                    headers: getHeaders()
                });
                
                const data = await response.json();
                displayResult('teamsResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                displayResult('teamsResult', { error: error.message }, true);
            }
        }
        
        async function createTeam() {
            const name = document.getElementById('teamName').value;
            const description = document.getElementById('teamDescription').value;
            
            if (!name || !description) {
                displayResult('createResult', { error: 'Nom et description requis' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/teams`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        name: name,
                        description: description
                    })
                });
                
                const data = await response.json();
                displayResult('createResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
                
                if (response.ok) {
                    // Actualiser la liste des équipes
                    setTimeout(getTeams, 1000);
                }
            } catch (error) {
                displayResult('createResult', { error: error.message }, true);
            }
        }
        
        function generateTestData() {
            const timestamp = Date.now();
            document.getElementById('teamName').value = `Test Équipe ${timestamp}`;
            document.getElementById('teamDescription').value = `Description de test générée automatiquement le ${new Date().toLocaleString()}. Cette équipe est créée pour tester le système.`;
        }
        
        async function fullDiagnostic() {
            const results = {
                timestamp: new Date().toISOString(),
                localStorage: {
                    token: localStorage.getItem('token') ? 'Présent' : 'Absent',
                    user: localStorage.getItem('user') ? 'Présent' : 'Absent',
                    allKeys: Object.keys(localStorage)
                },
                environment: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    apiBase: API_BASE
                }
            };
            
            // Test de connectivité
            try {
                const healthResponse = await fetch(`${API_BASE}/teams`, { method: 'HEAD' });
                results.connectivity = {
                    status: healthResponse.status,
                    ok: healthResponse.ok
                };
            } catch (error) {
                results.connectivity = {
                    error: error.message
                };
            }
            
            displayResult('diagnosticResult', results);
        }
        
        function clearResults() {
            ['connectionResult', 'authResult', 'teamsResult', 'createResult', 'diagnosticResult'].forEach(id => {
                document.getElementById(id).textContent = '';
                document.getElementById(id).className = 'result';
            });
        }
        
        // Auto-remplir le token depuis localStorage au chargement
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                document.getElementById('token').value = token;
            }
            checkConnection();
            generateTestData();
        };
    </script>
</body>
</html>
