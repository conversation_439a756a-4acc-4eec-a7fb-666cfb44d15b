{"ast": null, "code": "import { isWhiteSpace } from './characterClasses.mjs';\n/**\n * Produces the value of a block string from its parsed raw value, similar to\n * CoffeeScript's block string, Python's docstring trim or Ruby's strip_heredoc.\n *\n * This implements the GraphQL spec's BlockStringValue() static algorithm.\n *\n * @internal\n */\n\nexport function dedentBlockStringLines(lines) {\n  var _firstNonEmptyLine2;\n  let commonIndent = Number.MAX_SAFE_INTEGER;\n  let firstNonEmptyLine = null;\n  let lastNonEmptyLine = -1;\n  for (let i = 0; i < lines.length; ++i) {\n    var _firstNonEmptyLine;\n    const line = lines[i];\n    const indent = leadingWhitespace(line);\n    if (indent === line.length) {\n      continue; // skip empty lines\n    }\n\n    firstNonEmptyLine = (_firstNonEmptyLine = firstNonEmptyLine) !== null && _firstNonEmptyLine !== void 0 ? _firstNonEmptyLine : i;\n    lastNonEmptyLine = i;\n    if (i !== 0 && indent < commonIndent) {\n      commonIndent = indent;\n    }\n  }\n  return lines // Remove common indentation from all lines but first.\n  .map((line, i) => i === 0 ? line : line.slice(commonIndent)) // Remove leading and trailing blank lines.\n  .slice((_firstNonEmptyLine2 = firstNonEmptyLine) !== null && _firstNonEmptyLine2 !== void 0 ? _firstNonEmptyLine2 : 0, lastNonEmptyLine + 1);\n}\nfunction leadingWhitespace(str) {\n  let i = 0;\n  while (i < str.length && isWhiteSpace(str.charCodeAt(i))) {\n    ++i;\n  }\n  return i;\n}\n/**\n * @internal\n */\n\nexport function isPrintableAsBlockString(value) {\n  if (value === '') {\n    return true; // empty string is printable\n  }\n\n  let isEmptyLine = true;\n  let hasIndent = false;\n  let hasCommonIndent = true;\n  let seenNonEmptyLine = false;\n  for (let i = 0; i < value.length; ++i) {\n    switch (value.codePointAt(i)) {\n      case 0x0000:\n      case 0x0001:\n      case 0x0002:\n      case 0x0003:\n      case 0x0004:\n      case 0x0005:\n      case 0x0006:\n      case 0x0007:\n      case 0x0008:\n      case 0x000b:\n      case 0x000c:\n      case 0x000e:\n      case 0x000f:\n        return false;\n      // Has non-printable characters\n\n      case 0x000d:\n        //  \\r\n        return false;\n      // Has \\r or \\r\\n which will be replaced as \\n\n\n      case 10:\n        //  \\n\n        if (isEmptyLine && !seenNonEmptyLine) {\n          return false; // Has leading new line\n        }\n\n        seenNonEmptyLine = true;\n        isEmptyLine = true;\n        hasIndent = false;\n        break;\n      case 9: //   \\t\n\n      case 32:\n        //  <space>\n        hasIndent || (hasIndent = isEmptyLine);\n        break;\n      default:\n        hasCommonIndent && (hasCommonIndent = hasIndent);\n        isEmptyLine = false;\n    }\n  }\n  if (isEmptyLine) {\n    return false; // Has trailing empty lines\n  }\n\n  if (hasCommonIndent && seenNonEmptyLine) {\n    return false; // Has internal indent\n  }\n\n  return true;\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n *\n * @internal\n */\n\nexport function printBlockString(value, options) {\n  const escapedValue = value.replace(/\"\"\"/g, '\\\\\"\"\"'); // Expand a block string's raw value into independent lines.\n\n  const lines = escapedValue.split(/\\r\\n|[\\n\\r]/g);\n  const isSingleLine = lines.length === 1; // If common indentation is found we can fix some of those cases by adding leading new line\n\n  const forceLeadingNewLine = lines.length > 1 && lines.slice(1).every(line => line.length === 0 || isWhiteSpace(line.charCodeAt(0))); // Trailing triple quotes just looks confusing but doesn't force trailing new line\n\n  const hasTrailingTripleQuotes = escapedValue.endsWith('\\\\\"\"\"'); // Trailing quote (single or double) or slash forces trailing new line\n\n  const hasTrailingQuote = value.endsWith('\"') && !hasTrailingTripleQuotes;\n  const hasTrailingSlash = value.endsWith('\\\\');\n  const forceTrailingNewline = hasTrailingQuote || hasTrailingSlash;\n  const printAsMultipleLines = !(options !== null && options !== void 0 && options.minimize) && (\n  // add leading and trailing new lines only if it improves readability\n  !isSingleLine || value.length > 70 || forceTrailingNewline || forceLeadingNewLine || hasTrailingTripleQuotes);\n  let result = ''; // Format a multi-line block quote to account for leading space.\n\n  const skipLeadingNewLine = isSingleLine && isWhiteSpace(value.charCodeAt(0));\n  if (printAsMultipleLines && !skipLeadingNewLine || forceLeadingNewLine) {\n    result += '\\n';\n  }\n  result += escapedValue;\n  if (printAsMultipleLines || forceTrailingNewline) {\n    result += '\\n';\n  }\n  return '\"\"\"' + result + '\"\"\"';\n}", "map": {"version": 3, "names": ["isWhiteSpace", "dedentBlockStringLines", "lines", "_firstNonEmptyLine2", "commonIndent", "Number", "MAX_SAFE_INTEGER", "firstNonEmptyLine", "lastNonEmptyLine", "i", "length", "_firstNonEmptyLine", "line", "indent", "leadingWhitespace", "map", "slice", "str", "charCodeAt", "isPrintableAsBlockString", "value", "isEmptyLine", "hasIndent", "hasCommonIndent", "seenNonEmptyLine", "codePointAt", "printBlockString", "options", "escapedValue", "replace", "split", "isSingleLine", "forceLeadingNewLine", "every", "hasTrailingTripleQuotes", "endsWith", "hasTrailingQuote", "hasTrailingSlash", "forceTrailingNewline", "printAsMultipleLines", "minimize", "result", "skipLeadingNewLine"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/language/blockString.mjs"], "sourcesContent": ["import { isWhiteSpace } from './characterClasses.mjs';\n/**\n * Produces the value of a block string from its parsed raw value, similar to\n * CoffeeScript's block string, Python's docstring trim or Ruby's strip_heredoc.\n *\n * This implements the GraphQL spec's BlockStringValue() static algorithm.\n *\n * @internal\n */\n\nexport function dedentBlockStringLines(lines) {\n  var _firstNonEmptyLine2;\n\n  let commonIndent = Number.MAX_SAFE_INTEGER;\n  let firstNonEmptyLine = null;\n  let lastNonEmptyLine = -1;\n\n  for (let i = 0; i < lines.length; ++i) {\n    var _firstNonEmptyLine;\n\n    const line = lines[i];\n    const indent = leadingWhitespace(line);\n\n    if (indent === line.length) {\n      continue; // skip empty lines\n    }\n\n    firstNonEmptyLine =\n      (_firstNonEmptyLine = firstNonEmptyLine) !== null &&\n      _firstNonEmptyLine !== void 0\n        ? _firstNonEmptyLine\n        : i;\n    lastNonEmptyLine = i;\n\n    if (i !== 0 && indent < commonIndent) {\n      commonIndent = indent;\n    }\n  }\n\n  return lines // Remove common indentation from all lines but first.\n    .map((line, i) => (i === 0 ? line : line.slice(commonIndent))) // Remove leading and trailing blank lines.\n    .slice(\n      (_firstNonEmptyLine2 = firstNonEmptyLine) !== null &&\n        _firstNonEmptyLine2 !== void 0\n        ? _firstNonEmptyLine2\n        : 0,\n      lastNonEmptyLine + 1,\n    );\n}\n\nfunction leadingWhitespace(str) {\n  let i = 0;\n\n  while (i < str.length && isWhiteSpace(str.charCodeAt(i))) {\n    ++i;\n  }\n\n  return i;\n}\n/**\n * @internal\n */\n\nexport function isPrintableAsBlockString(value) {\n  if (value === '') {\n    return true; // empty string is printable\n  }\n\n  let isEmptyLine = true;\n  let hasIndent = false;\n  let hasCommonIndent = true;\n  let seenNonEmptyLine = false;\n\n  for (let i = 0; i < value.length; ++i) {\n    switch (value.codePointAt(i)) {\n      case 0x0000:\n      case 0x0001:\n      case 0x0002:\n      case 0x0003:\n      case 0x0004:\n      case 0x0005:\n      case 0x0006:\n      case 0x0007:\n      case 0x0008:\n      case 0x000b:\n      case 0x000c:\n      case 0x000e:\n      case 0x000f:\n        return false;\n      // Has non-printable characters\n\n      case 0x000d:\n        //  \\r\n        return false;\n      // Has \\r or \\r\\n which will be replaced as \\n\n\n      case 10:\n        //  \\n\n        if (isEmptyLine && !seenNonEmptyLine) {\n          return false; // Has leading new line\n        }\n\n        seenNonEmptyLine = true;\n        isEmptyLine = true;\n        hasIndent = false;\n        break;\n\n      case 9: //   \\t\n\n      case 32:\n        //  <space>\n        hasIndent || (hasIndent = isEmptyLine);\n        break;\n\n      default:\n        hasCommonIndent && (hasCommonIndent = hasIndent);\n        isEmptyLine = false;\n    }\n  }\n\n  if (isEmptyLine) {\n    return false; // Has trailing empty lines\n  }\n\n  if (hasCommonIndent && seenNonEmptyLine) {\n    return false; // Has internal indent\n  }\n\n  return true;\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n *\n * @internal\n */\n\nexport function printBlockString(value, options) {\n  const escapedValue = value.replace(/\"\"\"/g, '\\\\\"\"\"'); // Expand a block string's raw value into independent lines.\n\n  const lines = escapedValue.split(/\\r\\n|[\\n\\r]/g);\n  const isSingleLine = lines.length === 1; // If common indentation is found we can fix some of those cases by adding leading new line\n\n  const forceLeadingNewLine =\n    lines.length > 1 &&\n    lines\n      .slice(1)\n      .every((line) => line.length === 0 || isWhiteSpace(line.charCodeAt(0))); // Trailing triple quotes just looks confusing but doesn't force trailing new line\n\n  const hasTrailingTripleQuotes = escapedValue.endsWith('\\\\\"\"\"'); // Trailing quote (single or double) or slash forces trailing new line\n\n  const hasTrailingQuote = value.endsWith('\"') && !hasTrailingTripleQuotes;\n  const hasTrailingSlash = value.endsWith('\\\\');\n  const forceTrailingNewline = hasTrailingQuote || hasTrailingSlash;\n  const printAsMultipleLines =\n    !(options !== null && options !== void 0 && options.minimize) && // add leading and trailing new lines only if it improves readability\n    (!isSingleLine ||\n      value.length > 70 ||\n      forceTrailingNewline ||\n      forceLeadingNewLine ||\n      hasTrailingTripleQuotes);\n  let result = ''; // Format a multi-line block quote to account for leading space.\n\n  const skipLeadingNewLine = isSingleLine && isWhiteSpace(value.charCodeAt(0));\n\n  if ((printAsMultipleLines && !skipLeadingNewLine) || forceLeadingNewLine) {\n    result += '\\n';\n  }\n\n  result += escapedValue;\n\n  if (printAsMultipleLines || forceTrailingNewline) {\n    result += '\\n';\n  }\n\n  return '\"\"\"' + result + '\"\"\"';\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,wBAAwB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,IAAIC,mBAAmB;EAEvB,IAAIC,YAAY,GAAGC,MAAM,CAACC,gBAAgB;EAC1C,IAAIC,iBAAiB,GAAG,IAAI;EAC5B,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EAEzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACQ,MAAM,EAAE,EAAED,CAAC,EAAE;IACrC,IAAIE,kBAAkB;IAEtB,MAAMC,IAAI,GAAGV,KAAK,CAACO,CAAC,CAAC;IACrB,MAAMI,MAAM,GAAGC,iBAAiB,CAACF,IAAI,CAAC;IAEtC,IAAIC,MAAM,KAAKD,IAAI,CAACF,MAAM,EAAE;MAC1B,SAAS,CAAC;IACZ;;IAEAH,iBAAiB,GACf,CAACI,kBAAkB,GAAGJ,iBAAiB,MAAM,IAAI,IACjDI,kBAAkB,KAAK,KAAK,CAAC,GACzBA,kBAAkB,GAClBF,CAAC;IACPD,gBAAgB,GAAGC,CAAC;IAEpB,IAAIA,CAAC,KAAK,CAAC,IAAII,MAAM,GAAGT,YAAY,EAAE;MACpCA,YAAY,GAAGS,MAAM;IACvB;EACF;EAEA,OAAOX,KAAK,CAAC;EAAA,CACVa,GAAG,CAAC,CAACH,IAAI,EAAEH,CAAC,KAAMA,CAAC,KAAK,CAAC,GAAGG,IAAI,GAAGA,IAAI,CAACI,KAAK,CAACZ,YAAY,CAAE,CAAC,CAAC;EAAA,CAC9DY,KAAK,CACJ,CAACb,mBAAmB,GAAGI,iBAAiB,MAAM,IAAI,IAChDJ,mBAAmB,KAAK,KAAK,CAAC,GAC5BA,mBAAmB,GACnB,CAAC,EACLK,gBAAgB,GAAG,CACrB,CAAC;AACL;AAEA,SAASM,iBAAiBA,CAACG,GAAG,EAAE;EAC9B,IAAIR,CAAC,GAAG,CAAC;EAET,OAAOA,CAAC,GAAGQ,GAAG,CAACP,MAAM,IAAIV,YAAY,CAACiB,GAAG,CAACC,UAAU,CAACT,CAAC,CAAC,CAAC,EAAE;IACxD,EAAEA,CAAC;EACL;EAEA,OAAOA,CAAC;AACV;AACA;AACA;AACA;;AAEA,OAAO,SAASU,wBAAwBA,CAACC,KAAK,EAAE;EAC9C,IAAIA,KAAK,KAAK,EAAE,EAAE;IAChB,OAAO,IAAI,CAAC,CAAC;EACf;;EAEA,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIC,gBAAgB,GAAG,KAAK;EAE5B,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,KAAK,CAACV,MAAM,EAAE,EAAED,CAAC,EAAE;IACrC,QAAQW,KAAK,CAACK,WAAW,CAAChB,CAAC,CAAC;MAC1B,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;MACX,KAAK,MAAM;QACT,OAAO,KAAK;MACd;;MAEA,KAAK,MAAM;QACT;QACA,OAAO,KAAK;MACd;;MAEA,KAAK,EAAE;QACL;QACA,IAAIY,WAAW,IAAI,CAACG,gBAAgB,EAAE;UACpC,OAAO,KAAK,CAAC,CAAC;QAChB;;QAEAA,gBAAgB,GAAG,IAAI;QACvBH,WAAW,GAAG,IAAI;QAClBC,SAAS,GAAG,KAAK;QACjB;MAEF,KAAK,CAAC,CAAC,CAAC;;MAER,KAAK,EAAE;QACL;QACAA,SAAS,KAAKA,SAAS,GAAGD,WAAW,CAAC;QACtC;MAEF;QACEE,eAAe,KAAKA,eAAe,GAAGD,SAAS,CAAC;QAChDD,WAAW,GAAG,KAAK;IACvB;EACF;EAEA,IAAIA,WAAW,EAAE;IACf,OAAO,KAAK,CAAC,CAAC;EAChB;;EAEA,IAAIE,eAAe,IAAIC,gBAAgB,EAAE;IACvC,OAAO,KAAK,CAAC,CAAC;EAChB;;EAEA,OAAO,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,gBAAgBA,CAACN,KAAK,EAAEO,OAAO,EAAE;EAC/C,MAAMC,YAAY,GAAGR,KAAK,CAACS,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;;EAErD,MAAM3B,KAAK,GAAG0B,YAAY,CAACE,KAAK,CAAC,cAAc,CAAC;EAChD,MAAMC,YAAY,GAAG7B,KAAK,CAACQ,MAAM,KAAK,CAAC,CAAC,CAAC;;EAEzC,MAAMsB,mBAAmB,GACvB9B,KAAK,CAACQ,MAAM,GAAG,CAAC,IAChBR,KAAK,CACFc,KAAK,CAAC,CAAC,CAAC,CACRiB,KAAK,CAAErB,IAAI,IAAKA,IAAI,CAACF,MAAM,KAAK,CAAC,IAAIV,YAAY,CAACY,IAAI,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE7E,MAAMgB,uBAAuB,GAAGN,YAAY,CAACO,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEhE,MAAMC,gBAAgB,GAAGhB,KAAK,CAACe,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACD,uBAAuB;EACxE,MAAMG,gBAAgB,GAAGjB,KAAK,CAACe,QAAQ,CAAC,IAAI,CAAC;EAC7C,MAAMG,oBAAoB,GAAGF,gBAAgB,IAAIC,gBAAgB;EACjE,MAAME,oBAAoB,GACxB,EAAEZ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACa,QAAQ,CAAC;EAAI;EAChE,CAACT,YAAY,IACZX,KAAK,CAACV,MAAM,GAAG,EAAE,IACjB4B,oBAAoB,IACpBN,mBAAmB,IACnBE,uBAAuB,CAAC;EAC5B,IAAIO,MAAM,GAAG,EAAE,CAAC,CAAC;;EAEjB,MAAMC,kBAAkB,GAAGX,YAAY,IAAI/B,YAAY,CAACoB,KAAK,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAE5E,IAAKqB,oBAAoB,IAAI,CAACG,kBAAkB,IAAKV,mBAAmB,EAAE;IACxES,MAAM,IAAI,IAAI;EAChB;EAEAA,MAAM,IAAIb,YAAY;EAEtB,IAAIW,oBAAoB,IAAID,oBAAoB,EAAE;IAChDG,MAAM,IAAI,IAAI;EAChB;EAEA,OAAO,KAAK,GAAGA,MAAM,GAAG,KAAK;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}