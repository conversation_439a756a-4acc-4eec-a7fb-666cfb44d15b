var autoScroll=function(){"use strict";function e(e,t){return void 0===e?void 0===t?e:t:e}function t(t,n){return t=e(t,n),"function"==typeof t?function(){for(var e=arguments,n=arguments.length,o=Array(n),r=0;r<n;r++)o[r]=e[r];return!!t.apply(this,o)}:t?function(){return!0}:function(){return!1}}function n(e,t){if(t=a(t,!0),!y(t))return-1;for(var n=0;n<e.length;n++)if(e[n]===t)return n;return-1}function o(e,t){return-1!==n(e,t)}function r(e,t){for(var n=0;n<t.length;n++)o(e,t[n])||e.push(t[n]);return t}function i(e){for(var t=arguments,n=[],o=arguments.length-1;o-- >0;)n[o]=t[o+1];return n=n.map(a),r(e,n)}function u(e){for(var t=arguments,o=[],r=arguments.length-1;r-- >0;)o[r]=t[r+1];return o.map(a).reduce(function(t,o){var r=n(e,o);return-1!==r?t.concat(e.splice(r,1)):t},[])}function a(e,t){if("string"==typeof e)try{return document.querySelector(e)}catch(e){throw e}if(!y(e)&&!t)throw new TypeError(e+" is not a DOM element.");return e}function c(e,n){n=n||{};var o=t(n.allowUpdate,!0);return function(t){if(t=t||window.event,e.target=t.target||t.srcElement||t.originalTarget,e.element=this,e.type=t.type,o(t)){if(t.targetTouches)e.x=t.targetTouches[0].clientX,e.y=t.targetTouches[0].clientY,e.pageX=t.targetTouches[0].pageX,e.pageY=t.targetTouches[0].pageY,e.screenX=t.targetTouches[0].screenX,e.screenY=t.targetTouches[0].screenY;else{if(null===t.pageX&&null!==t.clientX){var n=t.target&&t.target.ownerDocument||document,r=n.documentElement,i=n.body;e.pageX=t.clientX+(r&&r.scrollLeft||i&&i.scrollLeft||0)-(r&&r.clientLeft||i&&i.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||i&&i.scrollTop||0)-(r&&r.clientTop||i&&i.clientTop||0)}else e.pageX=t.pageX,e.pageY=t.pageY;e.x=t.clientX,e.y=t.clientY,e.screenX=t.screenX,e.screenY=t.screenY}e.clientX=e.x,e.clientY=e.y}}}function l(){var e={top:{value:0,enumerable:!0},left:{value:0,enumerable:!0},right:{value:window.innerWidth,enumerable:!0},bottom:{value:window.innerHeight,enumerable:!0},width:{value:window.innerWidth,enumerable:!0},height:{value:window.innerHeight,enumerable:!0},x:{value:0,enumerable:!0},y:{value:0,enumerable:!0}};if(Object.create)return Object.create({},e);var t={};return Object.defineProperties(t,e),t}function f(e){if(e===window)return l();try{var t=e.getBoundingClientRect();return void 0===t.x&&(t.x=t.left,t.y=t.top),t}catch(t){throw new TypeError("Can't call getBoundingClientRect on "+e)}}function d(e,t){var n=f(t);return e.y>n.top&&e.y<n.bottom&&e.x>n.left&&e.x<n.right}function s(e){function t(e){for(var t=0;t<L.length;t++)o[L[t]]=e[L[t]]}function n(){e&&e.removeEventListener("mousemove",t,!1),o=null}var o={screenX:0,screenY:0,clientX:0,clientY:0,ctrlKey:!1,shiftKey:!1,altKey:!1,metaKey:!1,button:0,buttons:1,relatedTarget:null,region:null};return void 0!==e&&e.addEventListener("mousemove",t),{destroy:n,dispatch:function(){return MouseEvent?function(e,t,n){var r=new MouseEvent("mousemove",m(o,t));return v(r,n),e.dispatchEvent(r)}:"function"==typeof document.createEvent?function(e,t,n){var r=m(o,t),i=document.createEvent("MouseEvents");return i.initMouseEvent("mousemove",!0,!0,window,0,r.screenX,r.screenY,r.clientX,r.clientY,r.ctrlKey,r.altKey,r.shiftKey,r.metaKey,r.button,r.relatedTarget),v(i,n),e.dispatchEvent(i)}:"function"==typeof document.createEventObject?function(e,t,n){var r=document.createEventObject(),i=m(o,t);for(var u in i)r[u]=i[u];return v(r,n),e.dispatchEvent(r)}:void 0}()}}function m(e,t){t=t||{};for(var n=E(e),o=0;o<L.length;o++)void 0!==t[L[o]]&&(n[L[o]]=t[L[o]]);return n}function v(e,t){console.log("data ",t),e.data=t||{},e.dispatched="mousemove"}function w(e,n){function r(t){for(var n=0;n<e.length;n++)if(e[n]===t.target){O=!0;break}O&&Y(function(){return O=!1})}function a(){F=!0}function l(){F=!1,d()}function d(){x(C),x(K)}function m(){F=!1}function v(t){if(!t)return null;if(W===t)return t;if(o(e,t))return t;for(;t=t.parentNode;)if(o(e,t))return t;return null}function w(){for(var t=null,n=0;n<e.length;n++)g(M,e[n])&&(t=e[n]);return t}function p(e){if(X.autoScroll()&&!e.dispatched){var t=e.target,n=document.body;W&&!g(M,W)&&(X.scrollWhenOutside||(W=null)),t&&t.parentNode===n?t=w():(t=v(t))||(t=w()),t&&t!==W&&(W=t),q&&(x(K),K=Y(h)),W&&(x(C),C=Y(y))}}function h(){b(q),x(K),K=Y(h)}function y(){W&&(b(W),x(C),C=Y(y))}function b(e){var t,n,o=f(e);t=M.x<o.left+X.margin.left?Math.floor(Math.max(-1,(M.x-o.left)/X.margin.left-1)*X.maxSpeed.left):M.x>o.right-X.margin.right?Math.ceil(Math.min(1,(M.x-o.right)/X.margin.right+1)*X.maxSpeed.right):0,n=M.y<o.top+X.margin.top?Math.floor(Math.max(-1,(M.y-o.top)/X.margin.top-1)*X.maxSpeed.top):M.y>o.bottom-X.margin.bottom?Math.ceil(Math.min(1,(M.y-o.bottom)/X.margin.bottom+1)*X.maxSpeed.bottom):0,X.syncMove()&&A.dispatch(e,{pageX:M.pageX+t,pageY:M.pageY+n,clientX:M.x+t,clientY:M.y+n}),setTimeout(function(){n&&E(e,n),t&&L(e,t)})}function E(e,t){e===window?window.scrollTo(e.pageXOffset,e.pageYOffset+t):e.scrollTop+=t}function L(e,t){e===window?window.scrollTo(e.pageXOffset+t,e.pageYOffset):e.scrollLeft+=t}void 0===n&&(n={});var X=this,T=4,O=!1;if("object"!=typeof n.margin){var j=n.margin||-1;this.margin={left:j,right:j,top:j,bottom:j}}else this.margin=n.margin;this.scrollWhenOutside=n.scrollWhenOutside||!1;var M={},S=c(M),A=s(),F=!1;window.addEventListener("mousemove",S,!1),window.addEventListener("touchmove",S,!1),isNaN(n.maxSpeed)||(T=n.maxSpeed),"object"!=typeof T&&(T={left:T,right:T,top:T,bottom:T}),this.autoScroll=t(n.autoScroll),this.syncMove=t(n.syncMove,!1),this.destroy=function(t){window.removeEventListener("mousemove",S,!1),window.removeEventListener("touchmove",S,!1),window.removeEventListener("mousedown",a,!1),window.removeEventListener("touchstart",a,!1),window.removeEventListener("mouseup",l,!1),window.removeEventListener("touchend",l,!1),window.removeEventListener("pointerup",l,!1),window.removeEventListener("mouseleave",m,!1),window.removeEventListener("mousemove",p,!1),window.removeEventListener("touchmove",p,!1),window.removeEventListener("scroll",r,!0),e=[],t&&d()},this.add=function(){for(var t=[],n=arguments.length;n--;)t[n]=arguments[n];return i.apply(void 0,[e].concat(t)),this},this.remove=function(){for(var t=[],n=arguments.length;n--;)t[n]=arguments[n];return u.apply(void 0,[e].concat(t))};var K,q=null;"[object Array]"!==Object.prototype.toString.call(e)&&(e=[e]),function(t){e=[],t.forEach(function(e){e===window?q=window:X.add(e)})}(e),Object.defineProperties(this,{down:{get:function(){return F}},maxSpeed:{get:function(){return T}},point:{get:function(){return M}},scrolling:{get:function(){return O}}});var C,W=null;window.addEventListener("mousedown",a,!1),window.addEventListener("touchstart",a,!1),window.addEventListener("mouseup",l,!1),window.addEventListener("touchend",l,!1),window.addEventListener("pointerup",l,!1),window.addEventListener("mousemove",p,!1),window.addEventListener("touchmove",p,!1),window.addEventListener("mouseleave",m,!1),window.addEventListener("scroll",r,!0)}function p(e,t){return new w(e,t)}function g(e,t,n){return n?e.y>n.top&&e.y<n.bottom&&e.x>n.left&&e.x<n.right:d(e,t)}var h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},y=function(e){return null!=e&&"object"===(void 0===e?"undefined":h(e))&&1===e.nodeType&&"object"===h(e.style)&&"object"===h(e.ownerDocument)},b=void 0;b="function"!=typeof Object.create?function(e){var t=function(){};return function(e,n){if(e!==Object(e)&&null!==e)throw TypeError("Argument must be an object, or null");t.prototype=e||{};var o=new t;return t.prototype=null,void 0!==n&&Object.defineProperties(o,n),null===e&&(o.__proto__=null),o}}():Object.create;var E=b,L=["altKey","button","buttons","clientX","clientY","ctrlKey","metaKey","movementX","movementY","offsetX","offsetY","pageX","pageY","region","relatedTarget","screenX","screenY","shiftKey","which","x","y"],X=["webkit","moz","ms","o"],Y=function(){if("undefined"==typeof window)return function(){};for(var e=0,t=X.length;e<t&&!window.requestAnimationFrame;++e)window.requestAnimationFrame=window[X[e]+"RequestAnimationFrame"];if(!window.requestAnimationFrame){var n=0;window.requestAnimationFrame=function(e){var t=(new Date).getTime(),o=Math.max(0,16-t-n),r=window.setTimeout(function(){return e(t+o)},o);return n=t+o,r}}return window.requestAnimationFrame.bind(window)}(),x=function(){if("undefined"==typeof window)return function(){};for(var e=0,t=X.length;e<t&&!window.cancelAnimationFrame;++e)window.cancelAnimationFrame=window[X[e]+"CancelAnimationFrame"]||window[X[e]+"CancelRequestAnimationFrame"];return window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){window.clearTimeout(e)}),window.cancelAnimationFrame.bind(window)}();return p}();