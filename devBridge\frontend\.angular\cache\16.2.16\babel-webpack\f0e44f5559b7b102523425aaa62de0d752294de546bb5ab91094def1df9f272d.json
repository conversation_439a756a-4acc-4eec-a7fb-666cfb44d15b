{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\nfunction isNodeResponse(value) {\n  return !!value.body;\n}\nfunction isReadableStream(value) {\n  return !!value.getReader;\n}\nfunction isAsyncIterableIterator(value) {\n  return !!(canUseAsyncIteratorSymbol && value[Symbol.asyncIterator]);\n}\nfunction isStreamableBlob(value) {\n  return !!value.stream;\n}\nfunction isBlob(value) {\n  return !!value.arrayBuffer;\n}\nfunction isNodeReadableStream(value) {\n  return !!value.pipe;\n}\nexport function responseIterator(response) {\n  var body = response;\n  if (isNodeResponse(response)) body = response.body;\n  if (isAsyncIterableIterator(body)) return asyncIterator(body);\n  if (isReadableStream(body)) return readerIterator(body.getReader());\n  // this errors without casting to ReadableStream<T>\n  // because Blob.stream() returns a NodeJS ReadableStream\n  if (isStreamableBlob(body)) {\n    return readerIterator(body.stream().getReader());\n  }\n  if (isBlob(body)) return promiseIterator(body.arrayBuffer());\n  if (isNodeReadableStream(body)) return nodeStreamIterator(body);\n  throw new Error(\"Unknown body type for responseIterator. Please pass a streamable response.\");\n}", "map": {"version": 3, "names": ["canUseAsyncIteratorSymbol", "asyncIterator", "nodeStreamIterator", "promiseIterator", "readerIterator", "isNodeResponse", "value", "body", "isReadableStream", "<PERSON><PERSON><PERSON><PERSON>", "isAsyncIterableIterator", "Symbol", "isStreamableBlob", "stream", "isBlob", "arrayBuffer", "isNodeReadableStream", "pipe", "responseIterator", "response", "Error"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/http/responseIterator.js"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\nfunction isNodeResponse(value) {\n    return !!value.body;\n}\nfunction isReadableStream(value) {\n    return !!value.getReader;\n}\nfunction isAsyncIterableIterator(value) {\n    return !!(canUseAsyncIteratorSymbol &&\n        value[Symbol.asyncIterator]);\n}\nfunction isStreamableBlob(value) {\n    return !!value.stream;\n}\nfunction isBlob(value) {\n    return !!value.arrayBuffer;\n}\nfunction isNodeReadableStream(value) {\n    return !!value.pipe;\n}\nexport function responseIterator(response) {\n    var body = response;\n    if (isNodeResponse(response))\n        body = response.body;\n    if (isAsyncIterableIterator(body))\n        return asyncIterator(body);\n    if (isReadableStream(body))\n        return readerIterator(body.getReader());\n    // this errors without casting to ReadableStream<T>\n    // because Blob.stream() returns a NodeJS ReadableStream\n    if (isStreamableBlob(body)) {\n        return readerIterator(body.stream().getReader());\n    }\n    if (isBlob(body))\n        return promiseIterator(body.arrayBuffer());\n    if (isNodeReadableStream(body))\n        return nodeStreamIterator(body);\n    throw new Error(\"Unknown body type for responseIterator. Please pass a streamable response.\");\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,QAAQ,0BAA0B;AACpE,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,eAAe,MAAM,wBAAwB;AACpD,OAAOC,cAAc,MAAM,uBAAuB;AAClD,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC3B,OAAO,CAAC,CAACA,KAAK,CAACC,IAAI;AACvB;AACA,SAASC,gBAAgBA,CAACF,KAAK,EAAE;EAC7B,OAAO,CAAC,CAACA,KAAK,CAACG,SAAS;AAC5B;AACA,SAASC,uBAAuBA,CAACJ,KAAK,EAAE;EACpC,OAAO,CAAC,EAAEN,yBAAyB,IAC/BM,KAAK,CAACK,MAAM,CAACV,aAAa,CAAC,CAAC;AACpC;AACA,SAASW,gBAAgBA,CAACN,KAAK,EAAE;EAC7B,OAAO,CAAC,CAACA,KAAK,CAACO,MAAM;AACzB;AACA,SAASC,MAAMA,CAACR,KAAK,EAAE;EACnB,OAAO,CAAC,CAACA,KAAK,CAACS,WAAW;AAC9B;AACA,SAASC,oBAAoBA,CAACV,KAAK,EAAE;EACjC,OAAO,CAAC,CAACA,KAAK,CAACW,IAAI;AACvB;AACA,OAAO,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EACvC,IAAIZ,IAAI,GAAGY,QAAQ;EACnB,IAAId,cAAc,CAACc,QAAQ,CAAC,EACxBZ,IAAI,GAAGY,QAAQ,CAACZ,IAAI;EACxB,IAAIG,uBAAuB,CAACH,IAAI,CAAC,EAC7B,OAAON,aAAa,CAACM,IAAI,CAAC;EAC9B,IAAIC,gBAAgB,CAACD,IAAI,CAAC,EACtB,OAAOH,cAAc,CAACG,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;EAC3C;EACA;EACA,IAAIG,gBAAgB,CAACL,IAAI,CAAC,EAAE;IACxB,OAAOH,cAAc,CAACG,IAAI,CAACM,MAAM,CAAC,CAAC,CAACJ,SAAS,CAAC,CAAC,CAAC;EACpD;EACA,IAAIK,MAAM,CAACP,IAAI,CAAC,EACZ,OAAOJ,eAAe,CAACI,IAAI,CAACQ,WAAW,CAAC,CAAC,CAAC;EAC9C,IAAIC,oBAAoB,CAACT,IAAI,CAAC,EAC1B,OAAOL,kBAAkB,CAACK,IAAI,CAAC;EACnC,MAAM,IAAIa,KAAK,CAAC,4EAA4E,CAAC;AACjG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}