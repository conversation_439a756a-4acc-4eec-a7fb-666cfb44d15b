{"version": 3, "sources": ["webpack:///./packages/mdc-elevation/mdc-elevation.scss", "webpack:///./packages/material-components-web/node_modules/@material/theme/_css.scss", "webpack:///./packages/mdc-elevation/_elevation-theme.scss"], "names": [], "mappings": ";;;;;;;AAqCI,mBCyCA,wHDzCA,mBCyCA,yHDzCA,mBCyCA,yHDzCA,mBCyCA,yHDzCA,mBCyCA,0HDzCA,mBCyCA,0HDzCA,mBCyCA,2HDzCA,mBCyCA,2HDzCA,mBCyCA,2HDzCA,mBCyCA,2HDzCA,oBCyCA,4HDzCA,oBCyCA,4HDzCA,oBCyCA,4HDzCA,oBCyCA,4HDzCA,oBCyCA,4HDzCA,oBCyCA,4HDzCA,oBCyCA,6HDzCA,oBCyCA,6HDzCA,oBCyCA,6HDzCA,oBCyCA,6HDzCA,oBCyCA,8HDzCA,oBCyCA,8HDzCA,oBCyCA,8HDzCA,oBCyCA,8HDzCA,oBCyCA,8HDpCF,0BAEI,yDAIA,sBEkFK,C", "file": "mdc.elevation.min.css", "sourcesContent": ["//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/theme/custom-properties';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/theme/theme';\n@use '@material/theme/theme-color';\n@use './elevation-theme';\n\n@mixin core-styles($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @for $z-value from 0 through 24 {\n    .mdc-elevation--z#{$z-value} {\n      @include elevation-theme.elevation($z-value, $query: $query);\n    }\n  }\n\n  .mdc-elevation-transition {\n    @include feature-targeting.targets($feat-animation) {\n      transition: elevation-theme.transition-value();\n    }\n\n    @include feature-targeting.targets($feat-structure) {\n      will-change: elevation-theme.$property;\n    }\n  }\n}\n\n///\n/// Called once per application to set up the global default elevation styles.\n///\n@mixin overlay-common($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-structure) {\n      @include base-mixins.emit-once('mdc-elevation/common/structure') {\n        position: absolute;\n        border-radius: inherit;\n        pointer-events: none;\n\n        @include theme.property(\n          opacity,\n          custom-properties.create(--mdc-elevation-overlay-opacity, 0)\n        );\n      }\n    }\n\n    @include feature-targeting.targets($feat-animation) {\n      @include base-mixins.emit-once('mdc-elevation/common/animation') {\n        transition: elevation-theme.overlay-transition-value();\n      }\n    }\n\n    @include base-mixins.emit-once('mdc-elevation/common/color') {\n      $fill-color: custom-properties.create(\n        --mdc-elevation-overlay-color,\n        elevation-theme.$overlay-color\n      );\n      @include elevation-theme.overlay-fill-color($fill-color, $query: $query);\n    }\n  }\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n@use './gss';\n\n/// When true, add an additional property/value declaration before declarations\n/// that use advanced features such as custom properties or CSS functions. This\n/// adds fallback support for older browsers such as IE11 that do not support\n/// these features at the cost of additional CSS. Set this variable to false to\n/// disable generating fallback declarations.\n$enable-fallback-declarations: true !default;\n\n/// Writes a CSS property/value declaration. This mixin is used throughout the\n/// theme package for consistency for dynamically setting CSS property values.\n///\n/// This mixin may optionally take a fallback value. For advanced features such\n/// as custom properties or CSS functions like min and max, a fallback value is\n/// recommended to support older browsers.\n///\n/// @param {String} $property - The CSS property of the declaration.\n/// @param {*} $value - The value of the CSS declaration. The value should be\n///     resolved by other theme functions first (i.e. custom property Maps and\n///     Material theme keys are not supported in this mixin). If the value is\n///     null, no declarations will be emitted.\n/// @param {*} $fallback - An optional fallback value for older browsers. If\n///     provided, a second property/value declaration will be added before the\n///     main property/value declaration.\n/// @param {Map} $gss - An optional Map of GSS annotations to add.\n/// @param {Bool} $important - If true, add `!important` to the declaration.\n@mixin declaration(\n  $property,\n  $value,\n  $fallback-value: null,\n  $gss: (),\n  $important: false\n) {\n  // Normally setting a null value to a property will not emit CSS, so mixins\n  // wouldn't need to check this. However, Sass will throw an error if the\n  // interpolated property is a custom property.\n  @if $value != null {\n    $important-rule: if($important, ' !important', '');\n\n    @if $fallback-value and $enable-fallback-declarations {\n      @include gss.annotate($gss);\n      #{$property}: #{$fallback-value} #{$important-rule};\n\n      // Add @alternate to annotations.\n      $gss: map.merge(\n        $gss,\n        (\n          alternate: true,\n        )\n      );\n    }\n\n    @include gss.annotate($gss);\n    #{$property}: #{$value}#{$important-rule};\n  }\n}\n\n/// Unpacks shorthand values for CSS properties (i.e. lists of 1-3 values).\n/// If a list of 4 values is given, it is returned as-is.\n///\n/// Examples:\n///\n/// unpack-value(4px) => 4px 4px 4px 4px\n/// unpack-value(4px 2px) => 4px 2px 4px 2px\n/// unpack-value(4px 2px 2px) => 4px 2px 2px 2px\n/// unpack-value(4px 2px 0 2px) => 4px 2px 0 2px\n///\n/// @param {Number | Map | List} $value - List of 1 to 4 value numbers.\n/// @return {List} a List of 4 value numbers.\n@function unpack-value($value) {\n  @if meta.type-of($value) == 'map' or list.length($value) == 1 {\n    @return $value $value $value $value;\n  } @else if list.length($value) == 4 {\n    @return $value;\n  } @else if list.length($value) == 3 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 3)\n      list.nth($value, 2);\n  } @else if list.length($value) == 2 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 1)\n      list.nth($value, 2);\n  }\n\n  @error \"Invalid CSS property value: '#{$value}' is more than 4 values\";\n}\n", "//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:map';\n@use 'sass:math';\n@use 'sass:meta';\n@use '@material/animation/variables' as animation-variables;\n@use '@material/theme/custom-properties';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n@use '@material/theme/css';\n@use '@material/theme/theme';\n@use '@material/theme/validate';\n@use '@material/theme/theme-color';\n\n$baseline-color: black !default;\n$umbra-opacity: 0.2 !default;\n$penumbra-opacity: 0.14 !default;\n$ambient-opacity: 0.12 !default;\n\n$umbra-map: (\n  0: '0px 0px 0px 0px',\n  1: '0px 2px 1px -1px',\n  2: '0px 3px 1px -2px',\n  3: '0px 3px 3px -2px',\n  4: '0px 2px 4px -1px',\n  5: '0px 3px 5px -1px',\n  6: '0px 3px 5px -1px',\n  7: '0px 4px 5px -2px',\n  8: '0px 5px 5px -3px',\n  9: '0px 5px 6px -3px',\n  10: '0px 6px 6px -3px',\n  11: '0px 6px 7px -4px',\n  12: '0px 7px 8px -4px',\n  13: '0px 7px 8px -4px',\n  14: '0px 7px 9px -4px',\n  15: '0px 8px 9px -5px',\n  16: '0px 8px 10px -5px',\n  17: '0px 8px 11px -5px',\n  18: '0px 9px 11px -5px',\n  19: '0px 9px 12px -6px',\n  20: '0px 10px 13px -6px',\n  21: '0px 10px 13px -6px',\n  22: '0px 10px 14px -6px',\n  23: '0px 11px 14px -7px',\n  24: '0px 11px 15px -7px',\n) !default;\n\n$penumbra-map: (\n  0: '0px 0px 0px 0px',\n  1: '0px 1px 1px 0px',\n  2: '0px 2px 2px 0px',\n  3: '0px 3px 4px 0px',\n  4: '0px 4px 5px 0px',\n  5: '0px 5px 8px 0px',\n  6: '0px 6px 10px 0px',\n  7: '0px 7px 10px 1px',\n  8: '0px 8px 10px 1px',\n  9: '0px 9px 12px 1px',\n  10: '0px 10px 14px 1px',\n  11: '0px 11px 15px 1px',\n  12: '0px 12px 17px 2px',\n  13: '0px 13px 19px 2px',\n  14: '0px 14px 21px 2px',\n  15: '0px 15px 22px 2px',\n  16: '0px 16px 24px 2px',\n  17: '0px 17px 26px 2px',\n  18: '0px 18px 28px 2px',\n  19: '0px 19px 29px 2px',\n  20: '0px 20px 31px 3px',\n  21: '0px 21px 33px 3px',\n  22: '0px 22px 35px 3px',\n  23: '0px 23px 36px 3px',\n  24: '0px 24px 38px 3px',\n) !default;\n\n$ambient-map: (\n  0: '0px 0px 0px 0px',\n  1: '0px 1px 3px 0px',\n  2: '0px 1px 5px 0px',\n  3: '0px 1px 8px 0px',\n  4: '0px 1px 10px 0px',\n  5: '0px 1px 14px 0px',\n  6: '0px 1px 18px 0px',\n  7: '0px 2px 16px 1px',\n  8: '0px 3px 14px 2px',\n  9: '0px 3px 16px 2px',\n  10: '0px 4px 18px 3px',\n  11: '0px 4px 20px 3px',\n  12: '0px 5px 22px 4px',\n  13: '0px 5px 24px 4px',\n  14: '0px 5px 26px 4px',\n  15: '0px 6px 28px 5px',\n  16: '0px 6px 30px 5px',\n  17: '0px 6px 32px 5px',\n  18: '0px 7px 34px 6px',\n  19: '0px 7px 36px 6px',\n  20: '0px 8px 38px 7px',\n  21: '0px 8px 40px 7px',\n  22: '0px 8px 42px 7px',\n  23: '0px 9px 44px 8px',\n  24: '0px 9px 46px 8px',\n) !default;\n\n// The css property used for elevation. In most cases this should not be changed. It is exposed\n// as a variable for abstraction / easy use when needing to reference the property directly, for\n// example in a `will-change` rule.\n$property: box-shadow !default;\n\n// The default color for the elevation overlay.\n$overlay-color: #fff;\n\n// The css property used for elevation overlay transitions. In most cases this should not be changed. It is exposed\n// as a variable for abstraction / easy use when needing to reference the property directly, for\n// example in a `will-change` rule.\n$overlay-property: opacity !default;\n\n// The default duration value for elevation transitions.\n$transition-duration: 280ms !default;\n\n// The default easing value for elevation transitions.\n$transition-timing-function: animation-variables.$standard-curve-timing-function !default;\n\n///\n/// Sets the elevation transition value.\n///\n/// @param {String} $duration - The duration of the transition.\n/// @param {String} $easing - The easing function for the transition.\n/// @return {String}\n///\n@function transition-value(\n  $duration: $transition-duration,\n  $easing: $transition-timing-function\n) {\n  @return #{$property} #{$duration} #{$easing};\n}\n\n///\n/// Sets the elevation overlay transition value.\n///\n/// @param {String} $duration - The duration of the transition.\n/// @param {String} $easing - The easing function for the transition.\n/// @return {String}\n///\n@function overlay-transition-value(\n  $duration: $transition-duration,\n  $easing: $transition-timing-function\n) {\n  @return #{$overlay-property} #{$duration} #{$easing};\n}\n\n///\n/// Creates a box-shadow from the Material elevation system.\n/// @param {Number} $level - the level of the Material elevation system.\n/// @param {String} $color - the color of the shadow.\n/// @param {Number} $opacity-boost [0] - optional opacity boost for the shadow.\n/// @return {List} the complete box shadow.\n///\n@function _box-shadow($level, $color, $opacity-boost: 0) {\n  $color: theme-color.prop-value($color);\n  $umbra-z-value: map.get($umbra-map, $level);\n  $penumbra-z-value: map.get($penumbra-map, $level);\n  $ambient-z-value: map.get($ambient-map, $level);\n\n  $umbra-color: rgba($color, $umbra-opacity + $opacity-boost);\n  $penumbra-color: rgba($color, $penumbra-opacity + $opacity-boost);\n  $ambient-color: rgba($color, $ambient-opacity + $opacity-boost);\n\n  @return (\n    #{'#{$umbra-z-value} #{$umbra-color}'},\n    #{'#{$penumbra-z-value} #{$penumbra-color}'},\n    #{$ambient-z-value} $ambient-color\n  );\n}\n\n// Returns the correct box-shadow specified by $z-value.\n// The $z-value must be between 0 and 24.\n// If $color has an alpha channel, it will be ignored and overridden. To increase the opacity of the shadow, use\n// $opacity-boost.\n@function elevation-box-shadow(\n  $z-value,\n  $color: $baseline-color,\n  $opacity-boost: 0\n) {\n  @if $z-value == null {\n    @return null;\n  }\n\n  @if meta.type-of($z-value) != number or not math.is-unitless($z-value) {\n    @error \"$z-value must be a unitless number, but received '#{$z-value}'\";\n  }\n\n  @if $z-value < 0 or $z-value > 24 {\n    @error \"$z-value must be between 0 and 24, but received '#{$z-value}'\";\n  }\n\n  @return _box-shadow($z-value, $color, $opacity-boost);\n}\n\n///\n/// Returns a shadow or null if params are invalid.\n/// @param {Number} $level - the level of the Material elevation system.\n/// @param {String} $color - the color of the shadow.\n/// @return {List|null} the complete box shadow or null.\n///\n@function _shadow($level, $color) {\n  @if $level == null and $color == null {\n    // Do not emit a warning if both are null, which means the user did not\n    // provide tokens.\n    @return null;\n  }\n\n  @if $level == null or $color == null {\n    // If one of the tokens is null, emit a warning: the user may not realize\n    // that both are required.\n    @warn \"both $level and $color are required; received $level: '#{$level}', $color: '#{$color}'\";\n    @return null;\n  }\n\n  @if $level < 0 or $level > 24 {\n    @warn \"$level must be between 0 and 24; received '#{$level}'\";\n    @return null;\n  }\n\n  @return _box-shadow($level, $color);\n}\n\n@function get-elevation($level) {\n  @return (box-shadow: elevation-box-shadow($level));\n}\n\n///\n/// Sets the shadow of the element.\n///\n/// @param {String} $box-shadow - The shadow to apply to the element.\n///\n@mixin shadow($box-shadow, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @include feature-targeting.targets($feat-color) {\n    @include theme.property(box-shadow, $box-shadow);\n  }\n}\n\n///\n/// Sets the elevation overlay surface required positioning.\n///\n@mixin overlay-surface-position($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    /* @alternate */\n    position: relative;\n  }\n}\n\n///\n/// Sets the dimensions of the elevation overlay, including positioning and sizing.\n///\n/// @param {Number} $width - The width of the elevation overlay\n/// @param {Number} [$height] - The height of the elevation overlay\n/// @param {Boolean} [$has-content-sizing] - Set to false if the container has no content sizing\n///\n@mixin overlay-dimensions(\n  $width,\n  $height: $width,\n  $has-content-sizing: true,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-structure) {\n      @include theme.property(width, $width);\n      @include theme.property(height, $height);\n\n      @if $has-content-sizing {\n        top: 0;\n        @include rtl.ignore-next-line();\n        left: 0;\n      } @else {\n        top: 50%;\n        @include rtl.ignore-next-line();\n        left: 50%;\n        @include rtl.ignore-next-line();\n        transform: translate(-50%, -50%);\n      }\n    }\n  }\n}\n\n///\n/// Sets the elevation overlay fill color.\n/// Expected to be called directly on the elevation overlay element.\n///\n/// @param {Color} $color - The color of the elevation overlay.\n///\n@mixin overlay-fill-color($color, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @include feature-targeting.targets($feat-color) {\n    @include theme.property(background-color, $color);\n  }\n}\n\n///\n/// Applies the given color to the container of the overlay.\n/// @param {color} $color - the color of the overlay container\n///\n@mixin overlay-container-color($color, $query: feature-targeting.all()) {\n  .mdc-elevation-overlay {\n    @include overlay-fill-color($color, $query: $query);\n  }\n}\n\n///\n/// Sets the elevation overlay opacity.\n/// Expected to be called from a parent element.\n///\n/// @param {Number} $opacity - The opacity of the elevation overlay.\n///\n@mixin overlay-opacity($opacity, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-color) {\n      @include theme.property(opacity, $opacity);\n    }\n  }\n}\n\n// Applies the correct CSS rules to an element to give it the elevation specified by $z-value.\n// The $z-value must be between 0 and 24.\n// If $color has an alpha channel, it will be ignored and overridden. To increase the opacity of the shadow, use\n// $opacity-boost.\n@mixin elevation(\n  $z-value,\n  $color: $baseline-color,\n  $opacity-boost: 0,\n  $query: feature-targeting.all()\n) {\n  $box-shadow: elevation-box-shadow(\n    $z-value,\n    $color: $color,\n    $opacity-boost: $opacity-boost\n  );\n\n  @include shadow($box-shadow, $query: $query);\n}\n\n///\n/// Represents the configurable values of the elevation theme.\n///\n$_theme-values: (\n  shadow: null,\n  overlay-opacity: null,\n  overlay-color: null,\n);\n\n///\n/// Applies the shadow theme with the given $resolver function.\n/// @param {Function} $resolver - a function that returns a valid theme config.\n///   @see resolver for an example and expected arguments and return value.\n/// Accepts the following optional keyword args:\n/// @param {Number} $elevation - the level in the elevation system.\n/// @param {String} $shadow-color - the color used for the shadow.\n///\n@mixin with-resolver($resolver, $query: feature-targeting.all(), $args...) {\n  @if $resolver {\n    @include _theme(meta.call($resolver, $args...), $query: $query);\n  }\n}\n\n///\n/// Applies the given theme with validation.\n/// @param {Map} $theme - @see $_theme-values for accepted theme properties.\n///\n@mixin theme-styles($theme: (), $query: feature-targeting.all()) {\n  $theme: validate.theme-styles($_theme-values, $theme, $require-all: false);\n  @include _theme($theme, $query: $query);\n}\n\n///\n/// Applies the given theme.\n/// @param {Map} $theme - @see $_theme-values for accepted theme properties.\n///\n@mixin _theme($theme: (), $query: feature-targeting.all()) {\n  @include shadow(map.get($theme, shadow), $query: $query);\n  @include overlay-opacity(map.get($theme, overlay-opacity), $query: $query);\n  @include overlay-container-color(\n    map.get($theme, overlay-color),\n    $query: $query\n  );\n}\n\n///\n/// Transforms the following optional parameters into a theme config.\n/// @param {Number} $elevation - the level of the elevation system in Material.\n/// @param {String} $shadow-color - the color to be used by the shadow.\n/// @return {Map} @see $_theme-values for accepted theme properties.\n///\n@function resolver($args...) {\n  $opts: meta.keywords($args);\n  $elevation: map.get($opts, elevation);\n  $shadow-color: map.get($opts, shadow-color);\n  @if custom-properties.is-custom-prop($elevation) {\n    @return _resolve-custom-props($elevation, $shadow-color);\n  }\n\n  @return (shadow: _shadow($elevation, $shadow-color));\n}\n\n@function _resolve-custom-props($elevation, $shadow-color) {\n  $fallback-dp: custom-properties.get-fallback($elevation);\n  $fallback-shadow-color: custom-properties.get-fallback($shadow-color);\n  $shadow: custom-properties.set-fallback(\n    $elevation,\n    _shadow($fallback-dp, $fallback-shadow-color)\n  );\n  @return (shadow: $shadow);\n}\n"], "sourceRoot": ""}