{"ast": null, "code": "import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('16.2.12');\nexport { VERSION };", "map": {"version": 3, "names": ["Version", "VERSION"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@angular/cdk/fesm2022/cdk.mjs"], "sourcesContent": ["import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('16.2.12');\n\nexport { VERSION };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;;AAEvC;AACA,MAAMC,OAAO,GAAG,IAAID,OAAO,CAAC,SAAS,CAAC;AAEtC,SAASC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}