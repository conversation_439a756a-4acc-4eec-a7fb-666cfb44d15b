{"ast": null, "code": "import { naturalCompare } from './naturalCompare.mjs';\n/**\n * Given an invalid input string and a list of valid options, returns a filtered\n * list of valid options sorted based on their similarity with the input.\n */\n\nexport function suggestionList(input, options) {\n  const optionsByDistance = Object.create(null);\n  const lexicalDistance = new LexicalDistance(input);\n  const threshold = Math.floor(input.length * 0.4) + 1;\n  for (const option of options) {\n    const distance = lexicalDistance.measure(option, threshold);\n    if (distance !== undefined) {\n      optionsByDistance[option] = distance;\n    }\n  }\n  return Object.keys(optionsByDistance).sort((a, b) => {\n    const distanceDiff = optionsByDistance[a] - optionsByDistance[b];\n    return distanceDiff !== 0 ? distanceDiff : naturalCompare(a, b);\n  });\n}\n/**\n * Computes the lexical distance between strings A and B.\n *\n * The \"distance\" between two strings is given by counting the minimum number\n * of edits needed to transform string A into string B. An edit can be an\n * insertion, deletion, or substitution of a single character, or a swap of two\n * adjacent characters.\n *\n * Includes a custom alteration from <PERSON>rau-<PERSON>enshtein to treat case changes\n * as a single edit which helps identify mis-cased values with an edit distance\n * of 1.\n *\n * This distance can be useful for detecting typos in input or sorting\n */\n\nclass LexicalDistance {\n  constructor(input) {\n    this._input = input;\n    this._inputLowerCase = input.toLowerCase();\n    this._inputArray = stringToArray(this._inputLowerCase);\n    this._rows = [new Array(input.length + 1).fill(0), new Array(input.length + 1).fill(0), new Array(input.length + 1).fill(0)];\n  }\n  measure(option, threshold) {\n    if (this._input === option) {\n      return 0;\n    }\n    const optionLowerCase = option.toLowerCase(); // Any case change counts as a single edit\n\n    if (this._inputLowerCase === optionLowerCase) {\n      return 1;\n    }\n    let a = stringToArray(optionLowerCase);\n    let b = this._inputArray;\n    if (a.length < b.length) {\n      const tmp = a;\n      a = b;\n      b = tmp;\n    }\n    const aLength = a.length;\n    const bLength = b.length;\n    if (aLength - bLength > threshold) {\n      return undefined;\n    }\n    const rows = this._rows;\n    for (let j = 0; j <= bLength; j++) {\n      rows[0][j] = j;\n    }\n    for (let i = 1; i <= aLength; i++) {\n      const upRow = rows[(i - 1) % 3];\n      const currentRow = rows[i % 3];\n      let smallestCell = currentRow[0] = i;\n      for (let j = 1; j <= bLength; j++) {\n        const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n        let currentCell = Math.min(upRow[j] + 1,\n        // delete\n        currentRow[j - 1] + 1,\n        // insert\n        upRow[j - 1] + cost // substitute\n        );\n\n        if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n          // transposition\n          const doubleDiagonalCell = rows[(i - 2) % 3][j - 2];\n          currentCell = Math.min(currentCell, doubleDiagonalCell + 1);\n        }\n        if (currentCell < smallestCell) {\n          smallestCell = currentCell;\n        }\n        currentRow[j] = currentCell;\n      } // Early exit, since distance can't go smaller than smallest element of the previous row.\n\n      if (smallestCell > threshold) {\n        return undefined;\n      }\n    }\n    const distance = rows[aLength % 3][bLength];\n    return distance <= threshold ? distance : undefined;\n  }\n}\nfunction stringToArray(str) {\n  const strLength = str.length;\n  const array = new Array(strLength);\n  for (let i = 0; i < strLength; ++i) {\n    array[i] = str.charCodeAt(i);\n  }\n  return array;\n}", "map": {"version": 3, "names": ["naturalCompare", "suggestionList", "input", "options", "optionsByDistance", "Object", "create", "lexicalDistance", "LexicalDistance", "threshold", "Math", "floor", "length", "option", "distance", "measure", "undefined", "keys", "sort", "a", "b", "distanceDiff", "constructor", "_input", "_inputLowerCase", "toLowerCase", "_inputArray", "stringToArray", "_rows", "Array", "fill", "optionLowerCase", "tmp", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "rows", "j", "i", "upRow", "currentRow", "smallestCell", "cost", "currentCell", "min", "doubleDiagonalCell", "str", "str<PERSON><PERSON><PERSON>", "array", "charCodeAt"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/jsutils/suggestionList.mjs"], "sourcesContent": ["import { naturalCompare } from './naturalCompare.mjs';\n/**\n * Given an invalid input string and a list of valid options, returns a filtered\n * list of valid options sorted based on their similarity with the input.\n */\n\nexport function suggestionList(input, options) {\n  const optionsByDistance = Object.create(null);\n  const lexicalDistance = new LexicalDistance(input);\n  const threshold = Math.floor(input.length * 0.4) + 1;\n\n  for (const option of options) {\n    const distance = lexicalDistance.measure(option, threshold);\n\n    if (distance !== undefined) {\n      optionsByDistance[option] = distance;\n    }\n  }\n\n  return Object.keys(optionsByDistance).sort((a, b) => {\n    const distanceDiff = optionsByDistance[a] - optionsByDistance[b];\n    return distanceDiff !== 0 ? distanceDiff : naturalCompare(a, b);\n  });\n}\n/**\n * Computes the lexical distance between strings A and B.\n *\n * The \"distance\" between two strings is given by counting the minimum number\n * of edits needed to transform string A into string B. An edit can be an\n * insertion, deletion, or substitution of a single character, or a swap of two\n * adjacent characters.\n *\n * Includes a custom alteration from <PERSON>rau-<PERSON>enshtein to treat case changes\n * as a single edit which helps identify mis-cased values with an edit distance\n * of 1.\n *\n * This distance can be useful for detecting typos in input or sorting\n */\n\nclass LexicalDistance {\n  constructor(input) {\n    this._input = input;\n    this._inputLowerCase = input.toLowerCase();\n    this._inputArray = stringToArray(this._inputLowerCase);\n    this._rows = [\n      new Array(input.length + 1).fill(0),\n      new Array(input.length + 1).fill(0),\n      new Array(input.length + 1).fill(0),\n    ];\n  }\n\n  measure(option, threshold) {\n    if (this._input === option) {\n      return 0;\n    }\n\n    const optionLowerCase = option.toLowerCase(); // Any case change counts as a single edit\n\n    if (this._inputLowerCase === optionLowerCase) {\n      return 1;\n    }\n\n    let a = stringToArray(optionLowerCase);\n    let b = this._inputArray;\n\n    if (a.length < b.length) {\n      const tmp = a;\n      a = b;\n      b = tmp;\n    }\n\n    const aLength = a.length;\n    const bLength = b.length;\n\n    if (aLength - bLength > threshold) {\n      return undefined;\n    }\n\n    const rows = this._rows;\n\n    for (let j = 0; j <= bLength; j++) {\n      rows[0][j] = j;\n    }\n\n    for (let i = 1; i <= aLength; i++) {\n      const upRow = rows[(i - 1) % 3];\n      const currentRow = rows[i % 3];\n      let smallestCell = (currentRow[0] = i);\n\n      for (let j = 1; j <= bLength; j++) {\n        const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n        let currentCell = Math.min(\n          upRow[j] + 1, // delete\n          currentRow[j - 1] + 1, // insert\n          upRow[j - 1] + cost, // substitute\n        );\n\n        if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n          // transposition\n          const doubleDiagonalCell = rows[(i - 2) % 3][j - 2];\n          currentCell = Math.min(currentCell, doubleDiagonalCell + 1);\n        }\n\n        if (currentCell < smallestCell) {\n          smallestCell = currentCell;\n        }\n\n        currentRow[j] = currentCell;\n      } // Early exit, since distance can't go smaller than smallest element of the previous row.\n\n      if (smallestCell > threshold) {\n        return undefined;\n      }\n    }\n\n    const distance = rows[aLength % 3][bLength];\n    return distance <= threshold ? distance : undefined;\n  }\n}\n\nfunction stringToArray(str) {\n  const strLength = str.length;\n  const array = new Array(strLength);\n\n  for (let i = 0; i < strLength; ++i) {\n    array[i] = str.charCodeAt(i);\n  }\n\n  return array;\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;AACrD;AACA;AACA;AACA;;AAEA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC7C,MAAMC,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7C,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAACN,KAAK,CAAC;EAClD,MAAMO,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACT,KAAK,CAACU,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;EAEpD,KAAK,MAAMC,MAAM,IAAIV,OAAO,EAAE;IAC5B,MAAMW,QAAQ,GAAGP,eAAe,CAACQ,OAAO,CAACF,MAAM,EAAEJ,SAAS,CAAC;IAE3D,IAAIK,QAAQ,KAAKE,SAAS,EAAE;MAC1BZ,iBAAiB,CAACS,MAAM,CAAC,GAAGC,QAAQ;IACtC;EACF;EAEA,OAAOT,MAAM,CAACY,IAAI,CAACb,iBAAiB,CAAC,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACnD,MAAMC,YAAY,GAAGjB,iBAAiB,CAACe,CAAC,CAAC,GAAGf,iBAAiB,CAACgB,CAAC,CAAC;IAChE,OAAOC,YAAY,KAAK,CAAC,GAAGA,YAAY,GAAGrB,cAAc,CAACmB,CAAC,EAAEC,CAAC,CAAC;EACjE,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMZ,eAAe,CAAC;EACpBc,WAAWA,CAACpB,KAAK,EAAE;IACjB,IAAI,CAACqB,MAAM,GAAGrB,KAAK;IACnB,IAAI,CAACsB,eAAe,GAAGtB,KAAK,CAACuB,WAAW,CAAC,CAAC;IAC1C,IAAI,CAACC,WAAW,GAAGC,aAAa,CAAC,IAAI,CAACH,eAAe,CAAC;IACtD,IAAI,CAACI,KAAK,GAAG,CACX,IAAIC,KAAK,CAAC3B,KAAK,CAACU,MAAM,GAAG,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,EACnC,IAAID,KAAK,CAAC3B,KAAK,CAACU,MAAM,GAAG,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,EACnC,IAAID,KAAK,CAAC3B,KAAK,CAACU,MAAM,GAAG,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,CACpC;EACH;EAEAf,OAAOA,CAACF,MAAM,EAAEJ,SAAS,EAAE;IACzB,IAAI,IAAI,CAACc,MAAM,KAAKV,MAAM,EAAE;MAC1B,OAAO,CAAC;IACV;IAEA,MAAMkB,eAAe,GAAGlB,MAAM,CAACY,WAAW,CAAC,CAAC,CAAC,CAAC;;IAE9C,IAAI,IAAI,CAACD,eAAe,KAAKO,eAAe,EAAE;MAC5C,OAAO,CAAC;IACV;IAEA,IAAIZ,CAAC,GAAGQ,aAAa,CAACI,eAAe,CAAC;IACtC,IAAIX,CAAC,GAAG,IAAI,CAACM,WAAW;IAExB,IAAIP,CAAC,CAACP,MAAM,GAAGQ,CAAC,CAACR,MAAM,EAAE;MACvB,MAAMoB,GAAG,GAAGb,CAAC;MACbA,CAAC,GAAGC,CAAC;MACLA,CAAC,GAAGY,GAAG;IACT;IAEA,MAAMC,OAAO,GAAGd,CAAC,CAACP,MAAM;IACxB,MAAMsB,OAAO,GAAGd,CAAC,CAACR,MAAM;IAExB,IAAIqB,OAAO,GAAGC,OAAO,GAAGzB,SAAS,EAAE;MACjC,OAAOO,SAAS;IAClB;IAEA,MAAMmB,IAAI,GAAG,IAAI,CAACP,KAAK;IAEvB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACjCD,IAAI,CAAC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGA,CAAC;IAChB;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,OAAO,EAAEI,CAAC,EAAE,EAAE;MACjC,MAAMC,KAAK,GAAGH,IAAI,CAAC,CAACE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MAC/B,MAAME,UAAU,GAAGJ,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC;MAC9B,IAAIG,YAAY,GAAID,UAAU,CAAC,CAAC,CAAC,GAAGF,CAAE;MAEtC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;QACjC,MAAMK,IAAI,GAAGtB,CAAC,CAACkB,CAAC,GAAG,CAAC,CAAC,KAAKjB,CAAC,CAACgB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAC1C,IAAIM,WAAW,GAAGhC,IAAI,CAACiC,GAAG,CACxBL,KAAK,CAACF,CAAC,CAAC,GAAG,CAAC;QAAE;QACdG,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE;QACvBE,KAAK,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGK,IAAI,CAAE;QACvB,CAAC;;QAED,IAAIJ,CAAC,GAAG,CAAC,IAAID,CAAC,GAAG,CAAC,IAAIjB,CAAC,CAACkB,CAAC,GAAG,CAAC,CAAC,KAAKjB,CAAC,CAACgB,CAAC,GAAG,CAAC,CAAC,IAAIjB,CAAC,CAACkB,CAAC,GAAG,CAAC,CAAC,KAAKjB,CAAC,CAACgB,CAAC,GAAG,CAAC,CAAC,EAAE;UACpE;UACA,MAAMQ,kBAAkB,GAAGT,IAAI,CAAC,CAACE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC;UACnDM,WAAW,GAAGhC,IAAI,CAACiC,GAAG,CAACD,WAAW,EAAEE,kBAAkB,GAAG,CAAC,CAAC;QAC7D;QAEA,IAAIF,WAAW,GAAGF,YAAY,EAAE;UAC9BA,YAAY,GAAGE,WAAW;QAC5B;QAEAH,UAAU,CAACH,CAAC,CAAC,GAAGM,WAAW;MAC7B,CAAC,CAAC;;MAEF,IAAIF,YAAY,GAAG/B,SAAS,EAAE;QAC5B,OAAOO,SAAS;MAClB;IACF;IAEA,MAAMF,QAAQ,GAAGqB,IAAI,CAACF,OAAO,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC;IAC3C,OAAOpB,QAAQ,IAAIL,SAAS,GAAGK,QAAQ,GAAGE,SAAS;EACrD;AACF;AAEA,SAASW,aAAaA,CAACkB,GAAG,EAAE;EAC1B,MAAMC,SAAS,GAAGD,GAAG,CAACjC,MAAM;EAC5B,MAAMmC,KAAK,GAAG,IAAIlB,KAAK,CAACiB,SAAS,CAAC;EAElC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,SAAS,EAAE,EAAET,CAAC,EAAE;IAClCU,KAAK,CAACV,CAAC,CAAC,GAAGQ,GAAG,CAACG,UAAU,CAACX,CAAC,CAAC;EAC9B;EAEA,OAAOU,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}