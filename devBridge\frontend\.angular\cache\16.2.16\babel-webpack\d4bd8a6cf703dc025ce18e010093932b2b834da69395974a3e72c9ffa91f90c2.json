{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isInputType } from '../../type/definition.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables are input types\n *\n * A GraphQL operation is only valid if all the variables it defines are of\n * input types (scalar, enum, or input object).\n *\n * See https://spec.graphql.org/draft/#sec-Variables-Are-Input-Types\n */\nexport function VariablesAreInputTypesRule(context) {\n  return {\n    VariableDefinition(node) {\n      const type = typeFromAST(context.getSchema(), node.type);\n      if (type !== undefined && !isInputType(type)) {\n        const variableName = node.variable.name.value;\n        const typeName = print(node.type);\n        context.reportError(new GraphQLError(`Variable \"$${variableName}\" cannot be non-input type \"${typeName}\".`, {\n          nodes: node.type\n        }));\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "print", "isInputType", "typeFromAST", "VariablesAreInputTypesRule", "context", "VariableDefinition", "node", "type", "getSchema", "undefined", "variableName", "variable", "name", "value", "typeName", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/VariablesAreInputTypesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isInputType } from '../../type/definition.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables are input types\n *\n * A GraphQL operation is only valid if all the variables it defines are of\n * input types (scalar, enum, or input object).\n *\n * See https://spec.graphql.org/draft/#sec-Variables-Are-Input-Types\n */\nexport function VariablesAreInputTypesRule(context) {\n  return {\n    VariableDefinition(node) {\n      const type = typeFromAST(context.getSchema(), node.type);\n\n      if (type !== undefined && !isInputType(type)) {\n        const variableName = node.variable.name.value;\n        const typeName = print(node.type);\n        context.reportError(\n          new GraphQLError(\n            `Variable \"$${variableName}\" cannot be non-input type \"${typeName}\".`,\n            {\n              nodes: node.type,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,WAAW,QAAQ,iCAAiC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACC,OAAO,EAAE;EAClD,OAAO;IACLC,kBAAkBA,CAACC,IAAI,EAAE;MACvB,MAAMC,IAAI,GAAGL,WAAW,CAACE,OAAO,CAACI,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACC,IAAI,CAAC;MAExD,IAAIA,IAAI,KAAKE,SAAS,IAAI,CAACR,WAAW,CAACM,IAAI,CAAC,EAAE;QAC5C,MAAMG,YAAY,GAAGJ,IAAI,CAACK,QAAQ,CAACC,IAAI,CAACC,KAAK;QAC7C,MAAMC,QAAQ,GAAGd,KAAK,CAACM,IAAI,CAACC,IAAI,CAAC;QACjCH,OAAO,CAACW,WAAW,CACjB,IAAIhB,YAAY,CACb,cAAaW,YAAa,+BAA8BI,QAAS,IAAG,EACrE;UACEE,KAAK,EAAEV,IAAI,CAACC;QACd,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}