{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EquipeService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlBackend}teams`;\n    console.log('API URL:', this.apiUrl);\n  }\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    console.log('🔍 Debug Auth Headers:');\n    console.log('- Token exists:', !!token);\n    console.log('- Token preview:', token ? token.substring(0, 20) + '...' : 'null');\n    console.log('- User exists:', !!user);\n    console.log('- User data:', user ? JSON.parse(user) : 'null');\n    if (!token) {\n      console.error('❌ No authentication token found in localStorage');\n      console.log('📋 Available localStorage keys:', Object.keys(localStorage));\n    }\n    const headers = {\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    };\n    console.log('📤 Headers being sent:', headers);\n    return headers;\n  }\n  getEquipes() {\n    console.log('Fetching teams from:', this.apiUrl);\n    return this.http.get(this.apiUrl, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Teams received:', data)), catchError(this.handleError));\n  }\n  getEquipe(id) {\n    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\n    return this.http.get(`${this.apiUrl}/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team received:', data)), catchError(this.handleError));\n  }\n  addEquipe(equipe) {\n    console.log('🚀 Starting team creation...');\n    console.log('📝 Team data:', equipe);\n    console.log('🌐 API URL:', this.apiUrl);\n    const headers = this.getAuthHeaders();\n    return this.http.post(this.apiUrl, equipe, {\n      headers\n    }).pipe(tap(data => {\n      console.log('✅ Team created successfully:', data);\n    }), catchError(error => {\n      console.error('❌ Error creating team:', error);\n      console.error('📊 Error details:', {\n        status: error.status,\n        statusText: error.statusText,\n        message: error.error?.message,\n        url: error.url,\n        headers: error.headers\n      });\n      return this.handleError(error);\n    }));\n  }\n  updateEquipe(id, equipe) {\n    console.log(`Updating team with id ${id}:`, equipe);\n    return this.http.put(`${this.apiUrl}/${id}`, equipe, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team updated, response:', data)), catchError(this.handleError));\n  }\n  deleteEquipe(id) {\n    console.log(`Deleting team with id ${id}`);\n    console.log(`API URL: ${this.apiUrl}/${id}`);\n    return this.http.delete(`${this.apiUrl}/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team deleted, response:', data)), catchError(error => {\n      console.error('Error deleting team:', error);\n      console.error('Request URL:', `${this.apiUrl}/${id}`);\n      return this.handleError(error);\n    }));\n  }\n  addMembreToEquipe(teamId, membre) {\n    console.log(`Adding member to team ${teamId}:`, membre);\n    // Créer l'objet attendu par le backend\n    const memberData = {\n      userId: membre.id,\n      role: membre.role || 'membre' // Utiliser le rôle spécifié ou \"membre\" par défaut\n    };\n\n    console.log('Sending to backend:', memberData);\n    console.log('Team ID type:', typeof teamId, 'value:', teamId);\n    console.log('User ID type:', typeof membre.id, 'value:', membre.id);\n    // Utiliser la route directe pour ajouter un membre à une équipe\n    return this.http.post(`${this.apiUrl}/${teamId}/members`, memberData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Member added, response:', data)), catchError(this.handleError));\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    console.log(`Removing member ${membreId} from team ${teamId}`);\n    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\n    // Utiliser la route directe pour supprimer un membre d'une équipe\n    return this.http.delete(`${this.apiUrl}/${teamId}/members/${membreId}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Member removed, response:', data)), catchError(error => {\n      console.error('Error removing member:', error);\n      console.error('Request URL:', `${this.apiUrl}/${teamId}/members/${membreId}`);\n      return this.handleError(error);\n    }));\n  }\n  /**\n   * Récupère les détails des membres d'une équipe\n   * @param teamId ID de l'équipe\n   * @returns Observable contenant la liste des membres avec leurs détails\n   */\n  getTeamMembers(teamId) {\n    console.log(`Fetching team members for team ${teamId}`);\n    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\n    return this.http.get(`${this.apiUrl}/${teamId}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(map(team => {\n      console.log('Team data received:', team);\n      // Transformer les IDs des membres en objets avec l'ID et le rôle\n      if (team && team.members) {\n        return team.members.map(memberId => ({\n          user: memberId,\n          role: 'membre',\n          _id: memberId // Utiliser l'ID du membre comme ID du TeamMember\n        }));\n      }\n\n      return [];\n    }), tap(data => console.log('Team members processed:', data)), catchError(this.handleError));\n  }\n  // Nouvelles méthodes améliorées\n  /**\n   * Récupère toutes les équipes avec filtres et pagination\n   */\n  getEquipesWithFilters(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      if (filters.status) params = params.set('status', filters.status);\n      if (filters.isPublic !== undefined) params = params.set('isPublic', filters.isPublic.toString());\n      if (filters.search) params = params.set('search', filters.search);\n      if (filters.page) params = params.set('page', filters.page.toString());\n      if (filters.limit) params = params.set('limit', filters.limit.toString());\n    }\n    return this.http.get(this.apiUrl, {\n      params,\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Teams with filters received:', data)), catchError(this.handleError));\n  }\n  /**\n   * Crée une nouvelle équipe avec les nouvelles fonctionnalités\n   */\n  createEquipe(teamData) {\n    return this.http.post(this.apiUrl, teamData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team created:', data)), catchError(this.handleError));\n  }\n  /**\n   * Met à jour une équipe avec les nouvelles fonctionnalités\n   */\n  updateEquipeAdvanced(id, teamData) {\n    return this.http.put(`${this.apiUrl}/${id}`, teamData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Team updated:', data)), catchError(this.handleError));\n  }\n  /**\n   * Ajoute un membre à une équipe (nouvelle version)\n   */\n  addMemberToTeam(teamId, memberData) {\n    return this.http.post(`${this.apiUrl}/${teamId}/members`, memberData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('Member added to team:', data)), catchError(this.handleError));\n  }\n  /**\n   * Retire un membre d'une équipe (nouvelle version)\n   */\n  removeMemberFromTeam(teamId, memberData) {\n    return this.http.delete(`${this.apiUrl}/${teamId}/members`, {\n      headers: this.getAuthHeaders(),\n      body: memberData\n    }).pipe(tap(data => console.log('Member removed from team:', data)), catchError(this.handleError));\n  }\n  /**\n   * Récupère les équipes d'un utilisateur\n   */\n  getUserTeams(userId) {\n    const url = userId ? `${this.apiUrl}/user/${userId}` : `${this.apiUrl}/my-teams`;\n    return this.http.get(url, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(data => console.log('User teams received:', data)), catchError(this.handleError));\n  }\n  /**\n   * Archive une équipe\n   */\n  archiveTeam(teamId) {\n    return this.updateEquipeAdvanced(teamId, {\n      status: 'archived'\n    });\n  }\n  /**\n   * Active une équipe\n   */\n  activateTeam(teamId) {\n    return this.updateEquipeAdvanced(teamId, {\n      status: 'active'\n    });\n  }\n  /**\n   * Vérifie si un utilisateur peut rejoindre une équipe\n   */\n  canJoinTeam(team) {\n    return team.status === 'active' && team.isPublic === true && !team.isFullTeam;\n  }\n  /**\n   * Vérifie si un utilisateur est admin d'une équipe\n   */\n  isTeamAdmin(team, userId) {\n    if (typeof team.admin === 'string') {\n      return team.admin === userId;\n    } else if (team.admin && typeof team.admin === 'object') {\n      return team.admin._id === userId || team.admin.id === userId;\n    }\n    return false;\n  }\n  /**\n   * Vérifie si un utilisateur est membre d'une équipe\n   */\n  isTeamMember(team, userId) {\n    if (!team.members) return false;\n    return team.members.some(member => {\n      if (typeof member === 'string') {\n        return member === userId;\n      } else if (member && typeof member === 'object') {\n        return member._id === userId || member.id === userId;\n      }\n      return false;\n    });\n  }\n  handleError(error) {\n    let errorMessage = '';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Erreur client: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      const status = error.status;\n      const message = error.error?.message || error.statusText;\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\n      // Log des détails supplémentaires pour le débogage\n      console.error('Error details:', {\n        status: error.status,\n        statusText: error.statusText,\n        url: error.url,\n        error: error.error\n      });\n      if (status === 0) {\n        console.error(\"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\");\n      }\n    }\n    console.error('API Error:', errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function EquipeService_Factory(t) {\n      return new (t || EquipeService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EquipeService,\n      factory: EquipeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "throwError", "catchError", "tap", "map", "environment", "EquipeService", "constructor", "http", "apiUrl", "urlBackend", "console", "log", "getAuthHeaders", "token", "localStorage", "getItem", "user", "substring", "JSON", "parse", "error", "Object", "keys", "headers", "getEquipes", "get", "pipe", "data", "handleError", "getEquipe", "id", "addEquipe", "equipe", "post", "status", "statusText", "message", "url", "updateEquipe", "put", "deleteEquipe", "delete", "addMembreToEquipe", "teamId", "membre", "memberData", "userId", "role", "removeMembreFromEquipe", "membreId", "getTeamMembers", "team", "members", "memberId", "_id", "getEquipesWithFilters", "filters", "params", "set", "isPublic", "undefined", "toString", "search", "page", "limit", "createEquipe", "teamData", "updateEquipeAdvanced", "addMemberToTeam", "removeMemberFromTeam", "body", "getUserTeams", "archiveTeam", "activateTeam", "canJoinTeam", "isFullTeam", "isTeamAdmin", "admin", "isTeamMember", "some", "member", "errorMessage", "ErrorEvent", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\services\\equipe.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap, map } from 'rxjs/operators';\r\nimport {\r\n  Equipe,\r\n  CreateTeamRequest,\r\n  UpdateTeamRequest,\r\n  AddMemberRequest,\r\n  RemoveMemberRequest,\r\n  TeamSearchFilters,\r\n  TeamListResponse\r\n} from '../models/equipe.model';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class EquipeService {\r\n  private apiUrl = `${environment.urlBackend}teams`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('API URL:', this.apiUrl);\r\n  }\r\n\r\n  private getAuthHeaders() {\r\n    const token = localStorage.getItem('token');\r\n    const user = localStorage.getItem('user');\r\n\r\n    console.log('🔍 Debug Auth Headers:');\r\n    console.log('- Token exists:', !!token);\r\n    console.log('- Token preview:', token ? token.substring(0, 20) + '...' : 'null');\r\n    console.log('- User exists:', !!user);\r\n    console.log('- User data:', user ? JSON.parse(user) : 'null');\r\n\r\n    if (!token) {\r\n      console.error('❌ No authentication token found in localStorage');\r\n      console.log('📋 Available localStorage keys:', Object.keys(localStorage));\r\n    }\r\n\r\n    const headers = {\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    };\r\n\r\n    console.log('📤 Headers being sent:', headers);\r\n    return headers;\r\n  }\r\n\r\n  getEquipes(): Observable<Equipe[]> {\r\n    console.log('Fetching teams from:', this.apiUrl);\r\n    return this.http.get<Equipe[]>(this.apiUrl, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Teams received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  getEquipe(id: string): Observable<Equipe> {\r\n    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\r\n    return this.http.get<Equipe>(`${this.apiUrl}/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  addEquipe(equipe: Equipe): Observable<Equipe> {\r\n    console.log('🚀 Starting team creation...');\r\n    console.log('📝 Team data:', equipe);\r\n    console.log('🌐 API URL:', this.apiUrl);\r\n\r\n    const headers = this.getAuthHeaders();\r\n\r\n    return this.http.post<Equipe>(this.apiUrl, equipe, { headers }).pipe(\r\n      tap((data) => {\r\n        console.log('✅ Team created successfully:', data);\r\n      }),\r\n      catchError((error) => {\r\n        console.error('❌ Error creating team:', error);\r\n        console.error('📊 Error details:', {\r\n          status: error.status,\r\n          statusText: error.statusText,\r\n          message: error.error?.message,\r\n          url: error.url,\r\n          headers: error.headers\r\n        });\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  updateEquipe(id: string, equipe: Equipe): Observable<Equipe> {\r\n    console.log(`Updating team with id ${id}:`, equipe);\r\n    return this.http.put<Equipe>(`${this.apiUrl}/${id}`, equipe, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team updated, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  deleteEquipe(id: string): Observable<any> {\r\n    console.log(`Deleting team with id ${id}`);\r\n    console.log(`API URL: ${this.apiUrl}/${id}`);\r\n\r\n    return this.http.delete(`${this.apiUrl}/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team deleted, response:', data)),\r\n      catchError((error) => {\r\n        console.error('Error deleting team:', error);\r\n        console.error('Request URL:', `${this.apiUrl}/${id}`);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  addMembreToEquipe(teamId: string, membre: any): Observable<any> {\r\n    console.log(`Adding member to team ${teamId}:`, membre);\r\n\r\n    // Créer l'objet attendu par le backend\r\n    const memberData = {\r\n      userId: membre.id,\r\n      role: membre.role || 'membre', // Utiliser le rôle spécifié ou \"membre\" par défaut\r\n    };\r\n\r\n    console.log('Sending to backend:', memberData);\r\n    console.log('Team ID type:', typeof teamId, 'value:', teamId);\r\n    console.log('User ID type:', typeof membre.id, 'value:', membre.id);\r\n\r\n    // Utiliser la route directe pour ajouter un membre à une équipe\r\n    return this.http\r\n      .post<any>(`${this.apiUrl}/${teamId}/members`, memberData, {\r\n        headers: this.getAuthHeaders()\r\n      })\r\n      .pipe(\r\n        tap((data) => console.log('Member added, response:', data)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  removeMembreFromEquipe(teamId: string, membreId: string): Observable<any> {\r\n    console.log(`Removing member ${membreId} from team ${teamId}`);\r\n    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\r\n\r\n    // Utiliser la route directe pour supprimer un membre d'une équipe\r\n    return this.http\r\n      .delete<any>(`${this.apiUrl}/${teamId}/members/${membreId}`, {\r\n        headers: this.getAuthHeaders()\r\n      })\r\n      .pipe(\r\n        tap((data) => console.log('Member removed, response:', data)),\r\n        catchError((error) => {\r\n          console.error('Error removing member:', error);\r\n          console.error(\r\n            'Request URL:',\r\n            `${this.apiUrl}/${teamId}/members/${membreId}`\r\n          );\r\n          return this.handleError(error);\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Récupère les détails des membres d'une équipe\r\n   * @param teamId ID de l'équipe\r\n   * @returns Observable contenant la liste des membres avec leurs détails\r\n   */\r\n  getTeamMembers(teamId: string): Observable<any[]> {\r\n    console.log(`Fetching team members for team ${teamId}`);\r\n    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\r\n    return this.http.get<any>(`${this.apiUrl}/${teamId}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      map((team) => {\r\n        console.log('Team data received:', team);\r\n        // Transformer les IDs des membres en objets avec l'ID et le rôle\r\n        if (team && team.members) {\r\n          return team.members.map((memberId: string) => ({\r\n            user: memberId,\r\n            role: 'membre', // Par défaut, tous les membres ont le rôle \"membre\"\r\n            _id: memberId, // Utiliser l'ID du membre comme ID du TeamMember\r\n          }));\r\n        }\r\n        return [];\r\n      }),\r\n      tap((data) => console.log('Team members processed:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Nouvelles méthodes améliorées\r\n\r\n  /**\r\n   * Récupère toutes les équipes avec filtres et pagination\r\n   */\r\n  getEquipesWithFilters(filters?: TeamSearchFilters): Observable<TeamListResponse> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      if (filters.status) params = params.set('status', filters.status);\r\n      if (filters.isPublic !== undefined) params = params.set('isPublic', filters.isPublic.toString());\r\n      if (filters.search) params = params.set('search', filters.search);\r\n      if (filters.page) params = params.set('page', filters.page.toString());\r\n      if (filters.limit) params = params.set('limit', filters.limit.toString());\r\n    }\r\n\r\n    return this.http.get<TeamListResponse>(this.apiUrl, {\r\n      params,\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Teams with filters received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Crée une nouvelle équipe avec les nouvelles fonctionnalités\r\n   */\r\n  createEquipe(teamData: CreateTeamRequest): Observable<{ message: string; team: Equipe }> {\r\n    return this.http.post<{ message: string; team: Equipe }>(this.apiUrl, teamData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team created:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Met à jour une équipe avec les nouvelles fonctionnalités\r\n   */\r\n  updateEquipeAdvanced(id: string, teamData: UpdateTeamRequest): Observable<{ message: string; team: Equipe }> {\r\n    return this.http.put<{ message: string; team: Equipe }>(`${this.apiUrl}/${id}`, teamData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Team updated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Ajoute un membre à une équipe (nouvelle version)\r\n   */\r\n  addMemberToTeam(teamId: string, memberData: AddMemberRequest): Observable<{ message: string; team: Equipe; newMember: any }> {\r\n    return this.http.post<{ message: string; team: Equipe; newMember: any }>(`${this.apiUrl}/${teamId}/members`, memberData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('Member added to team:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Retire un membre d'une équipe (nouvelle version)\r\n   */\r\n  removeMemberFromTeam(teamId: string, memberData: RemoveMemberRequest): Observable<{ message: string; team: Equipe }> {\r\n    return this.http.delete<{ message: string; team: Equipe }>(`${this.apiUrl}/${teamId}/members`, {\r\n      headers: this.getAuthHeaders(),\r\n      body: memberData\r\n    }).pipe(\r\n      tap((data) => console.log('Member removed from team:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupère les équipes d'un utilisateur\r\n   */\r\n  getUserTeams(userId?: string): Observable<{ teams: Equipe[]; count: number }> {\r\n    const url = userId ? `${this.apiUrl}/user/${userId}` : `${this.apiUrl}/my-teams`;\r\n\r\n    return this.http.get<{ teams: Equipe[]; count: number }>(url, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap((data) => console.log('User teams received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Archive une équipe\r\n   */\r\n  archiveTeam(teamId: string): Observable<{ message: string; team: Equipe }> {\r\n    return this.updateEquipeAdvanced(teamId, { status: 'archived' });\r\n  }\r\n\r\n  /**\r\n   * Active une équipe\r\n   */\r\n  activateTeam(teamId: string): Observable<{ message: string; team: Equipe }> {\r\n    return this.updateEquipeAdvanced(teamId, { status: 'active' });\r\n  }\r\n\r\n  /**\r\n   * Vérifie si un utilisateur peut rejoindre une équipe\r\n   */\r\n  canJoinTeam(team: Equipe): boolean {\r\n    return team.status === 'active' &&\r\n           team.isPublic === true &&\r\n           !team.isFullTeam;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si un utilisateur est admin d'une équipe\r\n   */\r\n  isTeamAdmin(team: Equipe, userId: string): boolean {\r\n    if (typeof team.admin === 'string') {\r\n      return team.admin === userId;\r\n    } else if (team.admin && typeof team.admin === 'object') {\r\n      return team.admin._id === userId || (team.admin as any).id === userId;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si un utilisateur est membre d'une équipe\r\n   */\r\n  isTeamMember(team: Equipe, userId: string): boolean {\r\n    if (!team.members) return false;\r\n\r\n    return team.members.some(member => {\r\n      if (typeof member === 'string') {\r\n        return member === userId;\r\n      } else if (member && typeof member === 'object') {\r\n        return member._id === userId || (member as any).id === userId;\r\n      }\r\n      return false;\r\n    });\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Erreur client: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      const status = error.status;\r\n      const message = error.error?.message || error.statusText;\r\n\r\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\r\n\r\n      // Log des détails supplémentaires pour le débogage\r\n      console.error('Error details:', {\r\n        status: error.status,\r\n        statusText: error.statusText,\r\n        url: error.url,\r\n        error: error.error,\r\n      });\r\n\r\n      if (status === 0) {\r\n        console.error(\r\n          \"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\"\r\n        );\r\n      }\r\n    }\r\n\r\n    console.error('API Error:', errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAwCA,UAAU,QAAQ,sBAAsB;AAChF,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,EAAEC,GAAG,QAAQ,gBAAgB;AAUrD,SAASC,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,OAAO;IAG/CC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACH,MAAM,CAAC;EACtC;EAEQI,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAEzCL,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACE,KAAK,CAAC;IACvCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,KAAK,GAAGA,KAAK,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;IAChFP,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAACK,IAAI,CAAC;IACrCN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEK,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,GAAG,MAAM,CAAC;IAE7D,IAAI,CAACH,KAAK,EAAE;MACVH,OAAO,CAACU,KAAK,CAAC,iDAAiD,CAAC;MAChEV,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEU,MAAM,CAACC,IAAI,CAACR,YAAY,CAAC,CAAC;;IAG3E,MAAMS,OAAO,GAAG;MACd,eAAe,EAAE,UAAUV,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB;IAEDH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEY,OAAO,CAAC;IAC9C,OAAOA,OAAO;EAChB;EAEAC,UAAUA,CAAA;IACRd,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACH,MAAM,CAAC;IAChD,OAAO,IAAI,CAACD,IAAI,CAACkB,GAAG,CAAW,IAAI,CAACjB,MAAM,EAAE;MAC1Ce,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgB,IAAI,CAAC,CAAC,EACnD1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEAC,SAASA,CAACC,EAAU;IAClBpB,OAAO,CAACC,GAAG,CAAC,yBAAyBmB,EAAE,UAAU,IAAI,CAACtB,MAAM,IAAIsB,EAAE,EAAE,CAAC;IACrE,OAAO,IAAI,CAACvB,IAAI,CAACkB,GAAG,CAAS,GAAG,IAAI,CAACjB,MAAM,IAAIsB,EAAE,EAAE,EAAE;MACnDP,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgB,IAAI,CAAC,CAAC,EAClD1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEAG,SAASA,CAACC,MAAc;IACtBtB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqB,MAAM,CAAC;IACpCtB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACH,MAAM,CAAC;IAEvC,MAAMe,OAAO,GAAG,IAAI,CAACX,cAAc,EAAE;IAErC,OAAO,IAAI,CAACL,IAAI,CAAC0B,IAAI,CAAS,IAAI,CAACzB,MAAM,EAAEwB,MAAM,EAAE;MAAET;IAAO,CAAE,CAAC,CAACG,IAAI,CAClExB,GAAG,CAAEyB,IAAI,IAAI;MACXjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,IAAI,CAAC;IACnD,CAAC,CAAC,EACF1B,UAAU,CAAEmB,KAAK,IAAI;MACnBV,OAAO,CAACU,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CV,OAAO,CAACU,KAAK,CAAC,mBAAmB,EAAE;QACjCc,MAAM,EAAEd,KAAK,CAACc,MAAM;QACpBC,UAAU,EAAEf,KAAK,CAACe,UAAU;QAC5BC,OAAO,EAAEhB,KAAK,CAACA,KAAK,EAAEgB,OAAO;QAC7BC,GAAG,EAAEjB,KAAK,CAACiB,GAAG;QACdd,OAAO,EAAEH,KAAK,CAACG;OAChB,CAAC;MACF,OAAO,IAAI,CAACK,WAAW,CAACR,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAkB,YAAYA,CAACR,EAAU,EAAEE,MAAc;IACrCtB,OAAO,CAACC,GAAG,CAAC,yBAAyBmB,EAAE,GAAG,EAAEE,MAAM,CAAC;IACnD,OAAO,IAAI,CAACzB,IAAI,CAACgC,GAAG,CAAS,GAAG,IAAI,CAAC/B,MAAM,IAAIsB,EAAE,EAAE,EAAEE,MAAM,EAAE;MAC3DT,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,IAAI,CAAC,CAAC,EAC3D1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEAY,YAAYA,CAACV,EAAU;IACrBpB,OAAO,CAACC,GAAG,CAAC,yBAAyBmB,EAAE,EAAE,CAAC;IAC1CpB,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACH,MAAM,IAAIsB,EAAE,EAAE,CAAC;IAE5C,OAAO,IAAI,CAACvB,IAAI,CAACkC,MAAM,CAAC,GAAG,IAAI,CAACjC,MAAM,IAAIsB,EAAE,EAAE,EAAE;MAC9CP,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,IAAI,CAAC,CAAC,EAC3D1B,UAAU,CAAEmB,KAAK,IAAI;MACnBV,OAAO,CAACU,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CV,OAAO,CAACU,KAAK,CAAC,cAAc,EAAE,GAAG,IAAI,CAACZ,MAAM,IAAIsB,EAAE,EAAE,CAAC;MACrD,OAAO,IAAI,CAACF,WAAW,CAACR,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAsB,iBAAiBA,CAACC,MAAc,EAAEC,MAAW;IAC3ClC,OAAO,CAACC,GAAG,CAAC,yBAAyBgC,MAAM,GAAG,EAAEC,MAAM,CAAC;IAEvD;IACA,MAAMC,UAAU,GAAG;MACjBC,MAAM,EAAEF,MAAM,CAACd,EAAE;MACjBiB,IAAI,EAAEH,MAAM,CAACG,IAAI,IAAI,QAAQ,CAAE;KAChC;;IAEDrC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkC,UAAU,CAAC;IAC9CnC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOgC,MAAM,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAC7DjC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOiC,MAAM,CAACd,EAAE,EAAE,QAAQ,EAAEc,MAAM,CAACd,EAAE,CAAC;IAEnE;IACA,OAAO,IAAI,CAACvB,IAAI,CACb0B,IAAI,CAAM,GAAG,IAAI,CAACzB,MAAM,IAAImC,MAAM,UAAU,EAAEE,UAAU,EAAE;MACzDtB,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CACDc,IAAI,CACHxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,IAAI,CAAC,CAAC,EAC3D1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAoB,sBAAsBA,CAACL,MAAc,EAAEM,QAAgB;IACrDvC,OAAO,CAACC,GAAG,CAAC,mBAAmBsC,QAAQ,cAAcN,MAAM,EAAE,CAAC;IAC9DjC,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACH,MAAM,IAAImC,MAAM,YAAYM,QAAQ,EAAE,CAAC;IAEpE;IACA,OAAO,IAAI,CAAC1C,IAAI,CACbkC,MAAM,CAAM,GAAG,IAAI,CAACjC,MAAM,IAAImC,MAAM,YAAYM,QAAQ,EAAE,EAAE;MAC3D1B,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CACDc,IAAI,CACHxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgB,IAAI,CAAC,CAAC,EAC7D1B,UAAU,CAAEmB,KAAK,IAAI;MACnBV,OAAO,CAACU,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CV,OAAO,CAACU,KAAK,CACX,cAAc,EACd,GAAG,IAAI,CAACZ,MAAM,IAAImC,MAAM,YAAYM,QAAQ,EAAE,CAC/C;MACD,OAAO,IAAI,CAACrB,WAAW,CAACR,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKA8B,cAAcA,CAACP,MAAc;IAC3BjC,OAAO,CAACC,GAAG,CAAC,kCAAkCgC,MAAM,EAAE,CAAC;IACvD;IACA,OAAO,IAAI,CAACpC,IAAI,CAACkB,GAAG,CAAM,GAAG,IAAI,CAACjB,MAAM,IAAImC,MAAM,EAAE,EAAE;MACpDpB,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLvB,GAAG,CAAEgD,IAAI,IAAI;MACXzC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwC,IAAI,CAAC;MACxC;MACA,IAAIA,IAAI,IAAIA,IAAI,CAACC,OAAO,EAAE;QACxB,OAAOD,IAAI,CAACC,OAAO,CAACjD,GAAG,CAAEkD,QAAgB,KAAM;UAC7CrC,IAAI,EAAEqC,QAAQ;UACdN,IAAI,EAAE,QAAQ;UACdO,GAAG,EAAED,QAAQ,CAAE;SAChB,CAAC,CAAC;;;MAEL,OAAO,EAAE;IACX,CAAC,CAAC,EACFnD,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,IAAI,CAAC,CAAC,EAC3D1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EAEA;;;EAGA2B,qBAAqBA,CAACC,OAA2B;IAC/C,IAAIC,MAAM,GAAG,IAAI1D,UAAU,EAAE;IAE7B,IAAIyD,OAAO,EAAE;MACX,IAAIA,OAAO,CAACtB,MAAM,EAAEuB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAEF,OAAO,CAACtB,MAAM,CAAC;MACjE,IAAIsB,OAAO,CAACG,QAAQ,KAAKC,SAAS,EAAEH,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,UAAU,EAAEF,OAAO,CAACG,QAAQ,CAACE,QAAQ,EAAE,CAAC;MAChG,IAAIL,OAAO,CAACM,MAAM,EAAEL,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAEF,OAAO,CAACM,MAAM,CAAC;MACjE,IAAIN,OAAO,CAACO,IAAI,EAAEN,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAEF,OAAO,CAACO,IAAI,CAACF,QAAQ,EAAE,CAAC;MACtE,IAAIL,OAAO,CAACQ,KAAK,EAAEP,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,OAAO,EAAEF,OAAO,CAACQ,KAAK,CAACH,QAAQ,EAAE,CAAC;;IAG3E,OAAO,IAAI,CAACtD,IAAI,CAACkB,GAAG,CAAmB,IAAI,CAACjB,MAAM,EAAE;MAClDiD,MAAM;MACNlC,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,IAAI,CAAC,CAAC,EAChE1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAqC,YAAYA,CAACC,QAA2B;IACtC,OAAO,IAAI,CAAC3D,IAAI,CAAC0B,IAAI,CAAoC,IAAI,CAACzB,MAAM,EAAE0D,QAAQ,EAAE;MAC9E3C,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgB,IAAI,CAAC,CAAC,EACjD1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAuC,oBAAoBA,CAACrC,EAAU,EAAEoC,QAA2B;IAC1D,OAAO,IAAI,CAAC3D,IAAI,CAACgC,GAAG,CAAoC,GAAG,IAAI,CAAC/B,MAAM,IAAIsB,EAAE,EAAE,EAAEoC,QAAQ,EAAE;MACxF3C,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgB,IAAI,CAAC,CAAC,EACjD1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAwC,eAAeA,CAACzB,MAAc,EAAEE,UAA4B;IAC1D,OAAO,IAAI,CAACtC,IAAI,CAAC0B,IAAI,CAAoD,GAAG,IAAI,CAACzB,MAAM,IAAImC,MAAM,UAAU,EAAEE,UAAU,EAAE;MACvHtB,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,IAAI,CAAC,CAAC,EACzD1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAyC,oBAAoBA,CAAC1B,MAAc,EAAEE,UAA+B;IAClE,OAAO,IAAI,CAACtC,IAAI,CAACkC,MAAM,CAAoC,GAAG,IAAI,CAACjC,MAAM,IAAImC,MAAM,UAAU,EAAE;MAC7FpB,OAAO,EAAE,IAAI,CAACX,cAAc,EAAE;MAC9B0D,IAAI,EAAEzB;KACP,CAAC,CAACnB,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgB,IAAI,CAAC,CAAC,EAC7D1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGA2C,YAAYA,CAACzB,MAAe;IAC1B,MAAMT,GAAG,GAAGS,MAAM,GAAG,GAAG,IAAI,CAACtC,MAAM,SAASsC,MAAM,EAAE,GAAG,GAAG,IAAI,CAACtC,MAAM,WAAW;IAEhF,OAAO,IAAI,CAACD,IAAI,CAACkB,GAAG,CAAqCY,GAAG,EAAE;MAC5Dd,OAAO,EAAE,IAAI,CAACX,cAAc;KAC7B,CAAC,CAACc,IAAI,CACLxB,GAAG,CAAEyB,IAAI,IAAKjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgB,IAAI,CAAC,CAAC,EACxD1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGA4C,WAAWA,CAAC7B,MAAc;IACxB,OAAO,IAAI,CAACwB,oBAAoB,CAACxB,MAAM,EAAE;MAAET,MAAM,EAAE;IAAU,CAAE,CAAC;EAClE;EAEA;;;EAGAuC,YAAYA,CAAC9B,MAAc;IACzB,OAAO,IAAI,CAACwB,oBAAoB,CAACxB,MAAM,EAAE;MAAET,MAAM,EAAE;IAAQ,CAAE,CAAC;EAChE;EAEA;;;EAGAwC,WAAWA,CAACvB,IAAY;IACtB,OAAOA,IAAI,CAACjB,MAAM,KAAK,QAAQ,IACxBiB,IAAI,CAACQ,QAAQ,KAAK,IAAI,IACtB,CAACR,IAAI,CAACwB,UAAU;EACzB;EAEA;;;EAGAC,WAAWA,CAACzB,IAAY,EAAEL,MAAc;IACtC,IAAI,OAAOK,IAAI,CAAC0B,KAAK,KAAK,QAAQ,EAAE;MAClC,OAAO1B,IAAI,CAAC0B,KAAK,KAAK/B,MAAM;KAC7B,MAAM,IAAIK,IAAI,CAAC0B,KAAK,IAAI,OAAO1B,IAAI,CAAC0B,KAAK,KAAK,QAAQ,EAAE;MACvD,OAAO1B,IAAI,CAAC0B,KAAK,CAACvB,GAAG,KAAKR,MAAM,IAAKK,IAAI,CAAC0B,KAAa,CAAC/C,EAAE,KAAKgB,MAAM;;IAEvE,OAAO,KAAK;EACd;EAEA;;;EAGAgC,YAAYA,CAAC3B,IAAY,EAAEL,MAAc;IACvC,IAAI,CAACK,IAAI,CAACC,OAAO,EAAE,OAAO,KAAK;IAE/B,OAAOD,IAAI,CAACC,OAAO,CAAC2B,IAAI,CAACC,MAAM,IAAG;MAChC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAOA,MAAM,KAAKlC,MAAM;OACzB,MAAM,IAAIkC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC/C,OAAOA,MAAM,CAAC1B,GAAG,KAAKR,MAAM,IAAKkC,MAAc,CAAClD,EAAE,KAAKgB,MAAM;;MAE/D,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEQlB,WAAWA,CAACR,KAAwB;IAC1C,IAAI6D,YAAY,GAAG,EAAE;IAErB,IAAI7D,KAAK,CAACA,KAAK,YAAY8D,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,kBAAkB7D,KAAK,CAACA,KAAK,CAACgB,OAAO,EAAE;KACvD,MAAM;MACL;MACA,MAAMF,MAAM,GAAGd,KAAK,CAACc,MAAM;MAC3B,MAAME,OAAO,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,OAAO,IAAIhB,KAAK,CAACe,UAAU;MAExD8C,YAAY,GAAG,wBAAwB/C,MAAM,cAAcE,OAAO,EAAE;MAEpE;MACA1B,OAAO,CAACU,KAAK,CAAC,gBAAgB,EAAE;QAC9Bc,MAAM,EAAEd,KAAK,CAACc,MAAM;QACpBC,UAAU,EAAEf,KAAK,CAACe,UAAU;QAC5BE,GAAG,EAAEjB,KAAK,CAACiB,GAAG;QACdjB,KAAK,EAAEA,KAAK,CAACA;OACd,CAAC;MAEF,IAAIc,MAAM,KAAK,CAAC,EAAE;QAChBxB,OAAO,CAACU,KAAK,CACX,uEAAuE,CACxE;;;IAILV,OAAO,CAACU,KAAK,CAAC,YAAY,EAAE6D,YAAY,CAAC;IACzC,OAAOjF,UAAU,CAAC,MAAM,IAAImF,KAAK,CAACF,YAAY,CAAC,CAAC;EAClD;;;uBAzVW5E,aAAa,EAAA+E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAblF,aAAa;MAAAmF,OAAA,EAAbnF,aAAa,CAAAoF,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}