{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { print } from '../language/printer.mjs';\nimport { isEnumType, isInputObjectType, isInterfaceType, isListType, isNamedType, isNonNullType, isObjectType, isRequiredArgument, isRequiredInputField, isScalarType, isUnionType } from '../type/definition.mjs';\nimport { isSpecifiedScalarType } from '../type/scalars.mjs';\nimport { astFromValue } from './astFromValue.mjs';\nimport { sortValueNode } from './sortValueNode.mjs';\nvar BreakingChangeType;\n(function (BreakingChangeType) {\n  BreakingChangeType['TYPE_REMOVED'] = 'TYPE_REMOVED';\n  BreakingChangeType['TYPE_CHANGED_KIND'] = 'TYPE_CHANGED_KIND';\n  BreakingChangeType['TYPE_REMOVED_FROM_UNION'] = 'TYPE_REMOVED_FROM_UNION';\n  BreakingChangeType['VALUE_REMOVED_FROM_ENUM'] = 'VALUE_REMOVED_FROM_ENUM';\n  BreakingChangeType['REQUIRED_INPUT_FIELD_ADDED'] = 'REQUIRED_INPUT_FIELD_ADDED';\n  BreakingChangeType['IMPLEMENTED_INTERFACE_REMOVED'] = 'IMPLEMENTED_INTERFACE_REMOVED';\n  BreakingChangeType['FIELD_REMOVED'] = 'FIELD_REMOVED';\n  BreakingChangeType['FIELD_CHANGED_KIND'] = 'FIELD_CHANGED_KIND';\n  BreakingChangeType['REQUIRED_ARG_ADDED'] = 'REQUIRED_ARG_ADDED';\n  BreakingChangeType['ARG_REMOVED'] = 'ARG_REMOVED';\n  BreakingChangeType['ARG_CHANGED_KIND'] = 'ARG_CHANGED_KIND';\n  BreakingChangeType['DIRECTIVE_REMOVED'] = 'DIRECTIVE_REMOVED';\n  BreakingChangeType['DIRECTIVE_ARG_REMOVED'] = 'DIRECTIVE_ARG_REMOVED';\n  BreakingChangeType['REQUIRED_DIRECTIVE_ARG_ADDED'] = 'REQUIRED_DIRECTIVE_ARG_ADDED';\n  BreakingChangeType['DIRECTIVE_REPEATABLE_REMOVED'] = 'DIRECTIVE_REPEATABLE_REMOVED';\n  BreakingChangeType['DIRECTIVE_LOCATION_REMOVED'] = 'DIRECTIVE_LOCATION_REMOVED';\n})(BreakingChangeType || (BreakingChangeType = {}));\nexport { BreakingChangeType };\nvar DangerousChangeType;\n(function (DangerousChangeType) {\n  DangerousChangeType['VALUE_ADDED_TO_ENUM'] = 'VALUE_ADDED_TO_ENUM';\n  DangerousChangeType['TYPE_ADDED_TO_UNION'] = 'TYPE_ADDED_TO_UNION';\n  DangerousChangeType['OPTIONAL_INPUT_FIELD_ADDED'] = 'OPTIONAL_INPUT_FIELD_ADDED';\n  DangerousChangeType['OPTIONAL_ARG_ADDED'] = 'OPTIONAL_ARG_ADDED';\n  DangerousChangeType['IMPLEMENTED_INTERFACE_ADDED'] = 'IMPLEMENTED_INTERFACE_ADDED';\n  DangerousChangeType['ARG_DEFAULT_VALUE_CHANGE'] = 'ARG_DEFAULT_VALUE_CHANGE';\n})(DangerousChangeType || (DangerousChangeType = {}));\nexport { DangerousChangeType };\n\n/**\n * Given two schemas, returns an Array containing descriptions of all the types\n * of breaking changes covered by the other functions down below.\n */\nexport function findBreakingChanges(oldSchema, newSchema) {\n  // @ts-expect-error\n  return findSchemaChanges(oldSchema, newSchema).filter(change => change.type in BreakingChangeType);\n}\n/**\n * Given two schemas, returns an Array containing descriptions of all the types\n * of potentially dangerous changes covered by the other functions down below.\n */\n\nexport function findDangerousChanges(oldSchema, newSchema) {\n  // @ts-expect-error\n  return findSchemaChanges(oldSchema, newSchema).filter(change => change.type in DangerousChangeType);\n}\nfunction findSchemaChanges(oldSchema, newSchema) {\n  return [...findTypeChanges(oldSchema, newSchema), ...findDirectiveChanges(oldSchema, newSchema)];\n}\nfunction findDirectiveChanges(oldSchema, newSchema) {\n  const schemaChanges = [];\n  const directivesDiff = diff(oldSchema.getDirectives(), newSchema.getDirectives());\n  for (const oldDirective of directivesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.DIRECTIVE_REMOVED,\n      description: `${oldDirective.name} was removed.`\n    });\n  }\n  for (const [oldDirective, newDirective] of directivesDiff.persisted) {\n    const argsDiff = diff(oldDirective.args, newDirective.args);\n    for (const newArg of argsDiff.added) {\n      if (isRequiredArgument(newArg)) {\n        schemaChanges.push({\n          type: BreakingChangeType.REQUIRED_DIRECTIVE_ARG_ADDED,\n          description: `A required arg ${newArg.name} on directive ${oldDirective.name} was added.`\n        });\n      }\n    }\n    for (const oldArg of argsDiff.removed) {\n      schemaChanges.push({\n        type: BreakingChangeType.DIRECTIVE_ARG_REMOVED,\n        description: `${oldArg.name} was removed from ${oldDirective.name}.`\n      });\n    }\n    if (oldDirective.isRepeatable && !newDirective.isRepeatable) {\n      schemaChanges.push({\n        type: BreakingChangeType.DIRECTIVE_REPEATABLE_REMOVED,\n        description: `Repeatable flag was removed from ${oldDirective.name}.`\n      });\n    }\n    for (const location of oldDirective.locations) {\n      if (!newDirective.locations.includes(location)) {\n        schemaChanges.push({\n          type: BreakingChangeType.DIRECTIVE_LOCATION_REMOVED,\n          description: `${location} was removed from ${oldDirective.name}.`\n        });\n      }\n    }\n  }\n  return schemaChanges;\n}\nfunction findTypeChanges(oldSchema, newSchema) {\n  const schemaChanges = [];\n  const typesDiff = diff(Object.values(oldSchema.getTypeMap()), Object.values(newSchema.getTypeMap()));\n  for (const oldType of typesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.TYPE_REMOVED,\n      description: isSpecifiedScalarType(oldType) ? `Standard scalar ${oldType.name} was removed because it is not referenced anymore.` : `${oldType.name} was removed.`\n    });\n  }\n  for (const [oldType, newType] of typesDiff.persisted) {\n    if (isEnumType(oldType) && isEnumType(newType)) {\n      schemaChanges.push(...findEnumTypeChanges(oldType, newType));\n    } else if (isUnionType(oldType) && isUnionType(newType)) {\n      schemaChanges.push(...findUnionTypeChanges(oldType, newType));\n    } else if (isInputObjectType(oldType) && isInputObjectType(newType)) {\n      schemaChanges.push(...findInputObjectTypeChanges(oldType, newType));\n    } else if (isObjectType(oldType) && isObjectType(newType)) {\n      schemaChanges.push(...findFieldChanges(oldType, newType), ...findImplementedInterfacesChanges(oldType, newType));\n    } else if (isInterfaceType(oldType) && isInterfaceType(newType)) {\n      schemaChanges.push(...findFieldChanges(oldType, newType), ...findImplementedInterfacesChanges(oldType, newType));\n    } else if (oldType.constructor !== newType.constructor) {\n      schemaChanges.push({\n        type: BreakingChangeType.TYPE_CHANGED_KIND,\n        description: `${oldType.name} changed from ` + `${typeKindName(oldType)} to ${typeKindName(newType)}.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction findInputObjectTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const fieldsDiff = diff(Object.values(oldType.getFields()), Object.values(newType.getFields()));\n  for (const newField of fieldsDiff.added) {\n    if (isRequiredInputField(newField)) {\n      schemaChanges.push({\n        type: BreakingChangeType.REQUIRED_INPUT_FIELD_ADDED,\n        description: `A required field ${newField.name} on input type ${oldType.name} was added.`\n      });\n    } else {\n      schemaChanges.push({\n        type: DangerousChangeType.OPTIONAL_INPUT_FIELD_ADDED,\n        description: `An optional field ${newField.name} on input type ${oldType.name} was added.`\n      });\n    }\n  }\n  for (const oldField of fieldsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.FIELD_REMOVED,\n      description: `${oldType.name}.${oldField.name} was removed.`\n    });\n  }\n  for (const [oldField, newField] of fieldsDiff.persisted) {\n    const isSafe = isChangeSafeForInputObjectFieldOrFieldArg(oldField.type, newField.type);\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.FIELD_CHANGED_KIND,\n        description: `${oldType.name}.${oldField.name} changed type from ` + `${String(oldField.type)} to ${String(newField.type)}.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction findUnionTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const possibleTypesDiff = diff(oldType.getTypes(), newType.getTypes());\n  for (const newPossibleType of possibleTypesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.TYPE_ADDED_TO_UNION,\n      description: `${newPossibleType.name} was added to union type ${oldType.name}.`\n    });\n  }\n  for (const oldPossibleType of possibleTypesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.TYPE_REMOVED_FROM_UNION,\n      description: `${oldPossibleType.name} was removed from union type ${oldType.name}.`\n    });\n  }\n  return schemaChanges;\n}\nfunction findEnumTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const valuesDiff = diff(oldType.getValues(), newType.getValues());\n  for (const newValue of valuesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.VALUE_ADDED_TO_ENUM,\n      description: `${newValue.name} was added to enum type ${oldType.name}.`\n    });\n  }\n  for (const oldValue of valuesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.VALUE_REMOVED_FROM_ENUM,\n      description: `${oldValue.name} was removed from enum type ${oldType.name}.`\n    });\n  }\n  return schemaChanges;\n}\nfunction findImplementedInterfacesChanges(oldType, newType) {\n  const schemaChanges = [];\n  const interfacesDiff = diff(oldType.getInterfaces(), newType.getInterfaces());\n  for (const newInterface of interfacesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.IMPLEMENTED_INTERFACE_ADDED,\n      description: `${newInterface.name} added to interfaces implemented by ${oldType.name}.`\n    });\n  }\n  for (const oldInterface of interfacesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.IMPLEMENTED_INTERFACE_REMOVED,\n      description: `${oldType.name} no longer implements interface ${oldInterface.name}.`\n    });\n  }\n  return schemaChanges;\n}\nfunction findFieldChanges(oldType, newType) {\n  const schemaChanges = [];\n  const fieldsDiff = diff(Object.values(oldType.getFields()), Object.values(newType.getFields()));\n  for (const oldField of fieldsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.FIELD_REMOVED,\n      description: `${oldType.name}.${oldField.name} was removed.`\n    });\n  }\n  for (const [oldField, newField] of fieldsDiff.persisted) {\n    schemaChanges.push(...findArgChanges(oldType, oldField, newField));\n    const isSafe = isChangeSafeForObjectOrInterfaceField(oldField.type, newField.type);\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.FIELD_CHANGED_KIND,\n        description: `${oldType.name}.${oldField.name} changed type from ` + `${String(oldField.type)} to ${String(newField.type)}.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction findArgChanges(oldType, oldField, newField) {\n  const schemaChanges = [];\n  const argsDiff = diff(oldField.args, newField.args);\n  for (const oldArg of argsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.ARG_REMOVED,\n      description: `${oldType.name}.${oldField.name} arg ${oldArg.name} was removed.`\n    });\n  }\n  for (const [oldArg, newArg] of argsDiff.persisted) {\n    const isSafe = isChangeSafeForInputObjectFieldOrFieldArg(oldArg.type, newArg.type);\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.ARG_CHANGED_KIND,\n        description: `${oldType.name}.${oldField.name} arg ${oldArg.name} has changed type from ` + `${String(oldArg.type)} to ${String(newArg.type)}.`\n      });\n    } else if (oldArg.defaultValue !== undefined) {\n      if (newArg.defaultValue === undefined) {\n        schemaChanges.push({\n          type: DangerousChangeType.ARG_DEFAULT_VALUE_CHANGE,\n          description: `${oldType.name}.${oldField.name} arg ${oldArg.name} defaultValue was removed.`\n        });\n      } else {\n        // Since we looking only for client's observable changes we should\n        // compare default values in the same representation as they are\n        // represented inside introspection.\n        const oldValueStr = stringifyValue(oldArg.defaultValue, oldArg.type);\n        const newValueStr = stringifyValue(newArg.defaultValue, newArg.type);\n        if (oldValueStr !== newValueStr) {\n          schemaChanges.push({\n            type: DangerousChangeType.ARG_DEFAULT_VALUE_CHANGE,\n            description: `${oldType.name}.${oldField.name} arg ${oldArg.name} has changed defaultValue from ${oldValueStr} to ${newValueStr}.`\n          });\n        }\n      }\n    }\n  }\n  for (const newArg of argsDiff.added) {\n    if (isRequiredArgument(newArg)) {\n      schemaChanges.push({\n        type: BreakingChangeType.REQUIRED_ARG_ADDED,\n        description: `A required arg ${newArg.name} on ${oldType.name}.${oldField.name} was added.`\n      });\n    } else {\n      schemaChanges.push({\n        type: DangerousChangeType.OPTIONAL_ARG_ADDED,\n        description: `An optional arg ${newArg.name} on ${oldType.name}.${oldField.name} was added.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction isChangeSafeForObjectOrInterfaceField(oldType, newType) {\n  if (isListType(oldType)) {\n    return (\n      // if they're both lists, make sure the underlying types are compatible\n      isListType(newType) && isChangeSafeForObjectOrInterfaceField(oldType.ofType, newType.ofType) ||\n      // moving from nullable to non-null of the same underlying type is safe\n      isNonNullType(newType) && isChangeSafeForObjectOrInterfaceField(oldType, newType.ofType)\n    );\n  }\n  if (isNonNullType(oldType)) {\n    // if they're both non-null, make sure the underlying types are compatible\n    return isNonNullType(newType) && isChangeSafeForObjectOrInterfaceField(oldType.ofType, newType.ofType);\n  }\n  return (\n    // if they're both named types, see if their names are equivalent\n    isNamedType(newType) && oldType.name === newType.name ||\n    // moving from nullable to non-null of the same underlying type is safe\n    isNonNullType(newType) && isChangeSafeForObjectOrInterfaceField(oldType, newType.ofType)\n  );\n}\nfunction isChangeSafeForInputObjectFieldOrFieldArg(oldType, newType) {\n  if (isListType(oldType)) {\n    // if they're both lists, make sure the underlying types are compatible\n    return isListType(newType) && isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType.ofType);\n  }\n  if (isNonNullType(oldType)) {\n    return (\n      // if they're both non-null, make sure the underlying types are\n      // compatible\n      isNonNullType(newType) && isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType.ofType) ||\n      // moving from non-null to nullable of the same underlying type is safe\n      !isNonNullType(newType) && isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType)\n    );\n  } // if they're both named types, see if their names are equivalent\n\n  return isNamedType(newType) && oldType.name === newType.name;\n}\nfunction typeKindName(type) {\n  if (isScalarType(type)) {\n    return 'a Scalar type';\n  }\n  if (isObjectType(type)) {\n    return 'an Object type';\n  }\n  if (isInterfaceType(type)) {\n    return 'an Interface type';\n  }\n  if (isUnionType(type)) {\n    return 'a Union type';\n  }\n  if (isEnumType(type)) {\n    return 'an Enum type';\n  }\n  if (isInputObjectType(type)) {\n    return 'an Input type';\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected type: ' + inspect(type));\n}\nfunction stringifyValue(value, type) {\n  const ast = astFromValue(value, type);\n  ast != null || invariant(false);\n  return print(sortValueNode(ast));\n}\nfunction diff(oldArray, newArray) {\n  const added = [];\n  const removed = [];\n  const persisted = [];\n  const oldMap = keyMap(oldArray, ({\n    name\n  }) => name);\n  const newMap = keyMap(newArray, ({\n    name\n  }) => name);\n  for (const oldItem of oldArray) {\n    const newItem = newMap[oldItem.name];\n    if (newItem === undefined) {\n      removed.push(oldItem);\n    } else {\n      persisted.push([oldItem, newItem]);\n    }\n  }\n  for (const newItem of newArray) {\n    if (oldMap[newItem.name] === undefined) {\n      added.push(newItem);\n    }\n  }\n  return {\n    added,\n    persisted,\n    removed\n  };\n}", "map": {"version": 3, "names": ["inspect", "invariant", "keyMap", "print", "isEnumType", "isInputObjectType", "isInterfaceType", "isListType", "isNamedType", "isNonNullType", "isObjectType", "isRequiredArgument", "isRequiredInputField", "isScalarType", "isUnionType", "isSpecifiedScalarType", "astFromValue", "sortValueNode", "BreakingChangeType", "DangerousChangeType", "findBreakingChanges", "oldSchema", "newSchema", "findSchemaChanges", "filter", "change", "type", "findDangerousChanges", "findTypeChanges", "findDirectiveChanges", "schemaChanges", "directivesDiff", "diff", "getDirectives", "oldDirective", "removed", "push", "DIRECTIVE_REMOVED", "description", "name", "newDirective", "persisted", "argsDiff", "args", "newArg", "added", "REQUIRED_DIRECTIVE_ARG_ADDED", "oldArg", "DIRECTIVE_ARG_REMOVED", "isRepeatable", "DIRECTIVE_REPEATABLE_REMOVED", "location", "locations", "includes", "DIRECTIVE_LOCATION_REMOVED", "typesDiff", "Object", "values", "getTypeMap", "oldType", "TYPE_REMOVED", "newType", "findEnumTypeChanges", "findUnionTypeChanges", "findInputObjectTypeChanges", "find<PERSON>ieldChanges", "findImplementedInterfacesChanges", "constructor", "TYPE_CHANGED_KIND", "typeKindName", "fieldsDiff", "getFields", "newField", "REQUIRED_INPUT_FIELD_ADDED", "OPTIONAL_INPUT_FIELD_ADDED", "<PERSON><PERSON><PERSON>", "FIELD_REMOVED", "isSafe", "isChangeSafeForInputObjectFieldOrFieldArg", "FIELD_CHANGED_KIND", "String", "possibleTypesDiff", "getTypes", "newPossibleType", "TYPE_ADDED_TO_UNION", "oldPossibleType", "TYPE_REMOVED_FROM_UNION", "valuesDiff", "getV<PERSON>ues", "newValue", "VALUE_ADDED_TO_ENUM", "oldValue", "VALUE_REMOVED_FROM_ENUM", "interfacesDiff", "getInterfaces", "newInterface", "IMPLEMENTED_INTERFACE_ADDED", "oldInterface", "IMPLEMENTED_INTERFACE_REMOVED", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isChangeSafeForObjectOrInterfaceField", "ARG_REMOVED", "ARG_CHANGED_KIND", "defaultValue", "undefined", "ARG_DEFAULT_VALUE_CHANGE", "oldValueStr", "stringifyValue", "newValueStr", "REQUIRED_ARG_ADDED", "OPTIONAL_ARG_ADDED", "ofType", "value", "ast", "oldArray", "newArray", "oldMap", "newMap", "oldItem", "newItem"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/utilities/findBreakingChanges.mjs"], "sourcesContent": ["import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { print } from '../language/printer.mjs';\nimport {\n  isEnumType,\n  isInputObjectType,\n  isInterfaceType,\n  isListType,\n  isNamedType,\n  isNonNullType,\n  isObjectType,\n  isRequiredArgument,\n  isRequiredInputField,\n  isScalarType,\n  isUnionType,\n} from '../type/definition.mjs';\nimport { isSpecifiedScalarType } from '../type/scalars.mjs';\nimport { astFromValue } from './astFromValue.mjs';\nimport { sortValueNode } from './sortValueNode.mjs';\nvar BreakingChangeType;\n\n(function (BreakingChangeType) {\n  BreakingChangeType['TYPE_REMOVED'] = 'TYPE_REMOVED';\n  BreakingChangeType['TYPE_CHANGED_KIND'] = 'TYPE_CHANGED_KIND';\n  BreakingChangeType['TYPE_REMOVED_FROM_UNION'] = 'TYPE_REMOVED_FROM_UNION';\n  BreakingChangeType['VALUE_REMOVED_FROM_ENUM'] = 'VALUE_REMOVED_FROM_ENUM';\n  BreakingChangeType['REQUIRED_INPUT_FIELD_ADDED'] =\n    'REQUIRED_INPUT_FIELD_ADDED';\n  BreakingChangeType['IMPLEMENTED_INTERFACE_REMOVED'] =\n    'IMPLEMENTED_INTERFACE_REMOVED';\n  BreakingChangeType['FIELD_REMOVED'] = 'FIELD_REMOVED';\n  BreakingChangeType['FIELD_CHANGED_KIND'] = 'FIELD_CHANGED_KIND';\n  BreakingChangeType['REQUIRED_ARG_ADDED'] = 'REQUIRED_ARG_ADDED';\n  BreakingChangeType['ARG_REMOVED'] = 'ARG_REMOVED';\n  BreakingChangeType['ARG_CHANGED_KIND'] = 'ARG_CHANGED_KIND';\n  BreakingChangeType['DIRECTIVE_REMOVED'] = 'DIRECTIVE_REMOVED';\n  BreakingChangeType['DIRECTIVE_ARG_REMOVED'] = 'DIRECTIVE_ARG_REMOVED';\n  BreakingChangeType['REQUIRED_DIRECTIVE_ARG_ADDED'] =\n    'REQUIRED_DIRECTIVE_ARG_ADDED';\n  BreakingChangeType['DIRECTIVE_REPEATABLE_REMOVED'] =\n    'DIRECTIVE_REPEATABLE_REMOVED';\n  BreakingChangeType['DIRECTIVE_LOCATION_REMOVED'] =\n    'DIRECTIVE_LOCATION_REMOVED';\n})(BreakingChangeType || (BreakingChangeType = {}));\n\nexport { BreakingChangeType };\nvar DangerousChangeType;\n\n(function (DangerousChangeType) {\n  DangerousChangeType['VALUE_ADDED_TO_ENUM'] = 'VALUE_ADDED_TO_ENUM';\n  DangerousChangeType['TYPE_ADDED_TO_UNION'] = 'TYPE_ADDED_TO_UNION';\n  DangerousChangeType['OPTIONAL_INPUT_FIELD_ADDED'] =\n    'OPTIONAL_INPUT_FIELD_ADDED';\n  DangerousChangeType['OPTIONAL_ARG_ADDED'] = 'OPTIONAL_ARG_ADDED';\n  DangerousChangeType['IMPLEMENTED_INTERFACE_ADDED'] =\n    'IMPLEMENTED_INTERFACE_ADDED';\n  DangerousChangeType['ARG_DEFAULT_VALUE_CHANGE'] = 'ARG_DEFAULT_VALUE_CHANGE';\n})(DangerousChangeType || (DangerousChangeType = {}));\n\nexport { DangerousChangeType };\n\n/**\n * Given two schemas, returns an Array containing descriptions of all the types\n * of breaking changes covered by the other functions down below.\n */\nexport function findBreakingChanges(oldSchema, newSchema) {\n  // @ts-expect-error\n  return findSchemaChanges(oldSchema, newSchema).filter(\n    (change) => change.type in BreakingChangeType,\n  );\n}\n/**\n * Given two schemas, returns an Array containing descriptions of all the types\n * of potentially dangerous changes covered by the other functions down below.\n */\n\nexport function findDangerousChanges(oldSchema, newSchema) {\n  // @ts-expect-error\n  return findSchemaChanges(oldSchema, newSchema).filter(\n    (change) => change.type in DangerousChangeType,\n  );\n}\n\nfunction findSchemaChanges(oldSchema, newSchema) {\n  return [\n    ...findTypeChanges(oldSchema, newSchema),\n    ...findDirectiveChanges(oldSchema, newSchema),\n  ];\n}\n\nfunction findDirectiveChanges(oldSchema, newSchema) {\n  const schemaChanges = [];\n  const directivesDiff = diff(\n    oldSchema.getDirectives(),\n    newSchema.getDirectives(),\n  );\n\n  for (const oldDirective of directivesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.DIRECTIVE_REMOVED,\n      description: `${oldDirective.name} was removed.`,\n    });\n  }\n\n  for (const [oldDirective, newDirective] of directivesDiff.persisted) {\n    const argsDiff = diff(oldDirective.args, newDirective.args);\n\n    for (const newArg of argsDiff.added) {\n      if (isRequiredArgument(newArg)) {\n        schemaChanges.push({\n          type: BreakingChangeType.REQUIRED_DIRECTIVE_ARG_ADDED,\n          description: `A required arg ${newArg.name} on directive ${oldDirective.name} was added.`,\n        });\n      }\n    }\n\n    for (const oldArg of argsDiff.removed) {\n      schemaChanges.push({\n        type: BreakingChangeType.DIRECTIVE_ARG_REMOVED,\n        description: `${oldArg.name} was removed from ${oldDirective.name}.`,\n      });\n    }\n\n    if (oldDirective.isRepeatable && !newDirective.isRepeatable) {\n      schemaChanges.push({\n        type: BreakingChangeType.DIRECTIVE_REPEATABLE_REMOVED,\n        description: `Repeatable flag was removed from ${oldDirective.name}.`,\n      });\n    }\n\n    for (const location of oldDirective.locations) {\n      if (!newDirective.locations.includes(location)) {\n        schemaChanges.push({\n          type: BreakingChangeType.DIRECTIVE_LOCATION_REMOVED,\n          description: `${location} was removed from ${oldDirective.name}.`,\n        });\n      }\n    }\n  }\n\n  return schemaChanges;\n}\n\nfunction findTypeChanges(oldSchema, newSchema) {\n  const schemaChanges = [];\n  const typesDiff = diff(\n    Object.values(oldSchema.getTypeMap()),\n    Object.values(newSchema.getTypeMap()),\n  );\n\n  for (const oldType of typesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.TYPE_REMOVED,\n      description: isSpecifiedScalarType(oldType)\n        ? `Standard scalar ${oldType.name} was removed because it is not referenced anymore.`\n        : `${oldType.name} was removed.`,\n    });\n  }\n\n  for (const [oldType, newType] of typesDiff.persisted) {\n    if (isEnumType(oldType) && isEnumType(newType)) {\n      schemaChanges.push(...findEnumTypeChanges(oldType, newType));\n    } else if (isUnionType(oldType) && isUnionType(newType)) {\n      schemaChanges.push(...findUnionTypeChanges(oldType, newType));\n    } else if (isInputObjectType(oldType) && isInputObjectType(newType)) {\n      schemaChanges.push(...findInputObjectTypeChanges(oldType, newType));\n    } else if (isObjectType(oldType) && isObjectType(newType)) {\n      schemaChanges.push(\n        ...findFieldChanges(oldType, newType),\n        ...findImplementedInterfacesChanges(oldType, newType),\n      );\n    } else if (isInterfaceType(oldType) && isInterfaceType(newType)) {\n      schemaChanges.push(\n        ...findFieldChanges(oldType, newType),\n        ...findImplementedInterfacesChanges(oldType, newType),\n      );\n    } else if (oldType.constructor !== newType.constructor) {\n      schemaChanges.push({\n        type: BreakingChangeType.TYPE_CHANGED_KIND,\n        description:\n          `${oldType.name} changed from ` +\n          `${typeKindName(oldType)} to ${typeKindName(newType)}.`,\n      });\n    }\n  }\n\n  return schemaChanges;\n}\n\nfunction findInputObjectTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const fieldsDiff = diff(\n    Object.values(oldType.getFields()),\n    Object.values(newType.getFields()),\n  );\n\n  for (const newField of fieldsDiff.added) {\n    if (isRequiredInputField(newField)) {\n      schemaChanges.push({\n        type: BreakingChangeType.REQUIRED_INPUT_FIELD_ADDED,\n        description: `A required field ${newField.name} on input type ${oldType.name} was added.`,\n      });\n    } else {\n      schemaChanges.push({\n        type: DangerousChangeType.OPTIONAL_INPUT_FIELD_ADDED,\n        description: `An optional field ${newField.name} on input type ${oldType.name} was added.`,\n      });\n    }\n  }\n\n  for (const oldField of fieldsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.FIELD_REMOVED,\n      description: `${oldType.name}.${oldField.name} was removed.`,\n    });\n  }\n\n  for (const [oldField, newField] of fieldsDiff.persisted) {\n    const isSafe = isChangeSafeForInputObjectFieldOrFieldArg(\n      oldField.type,\n      newField.type,\n    );\n\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.FIELD_CHANGED_KIND,\n        description:\n          `${oldType.name}.${oldField.name} changed type from ` +\n          `${String(oldField.type)} to ${String(newField.type)}.`,\n      });\n    }\n  }\n\n  return schemaChanges;\n}\n\nfunction findUnionTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const possibleTypesDiff = diff(oldType.getTypes(), newType.getTypes());\n\n  for (const newPossibleType of possibleTypesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.TYPE_ADDED_TO_UNION,\n      description: `${newPossibleType.name} was added to union type ${oldType.name}.`,\n    });\n  }\n\n  for (const oldPossibleType of possibleTypesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.TYPE_REMOVED_FROM_UNION,\n      description: `${oldPossibleType.name} was removed from union type ${oldType.name}.`,\n    });\n  }\n\n  return schemaChanges;\n}\n\nfunction findEnumTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const valuesDiff = diff(oldType.getValues(), newType.getValues());\n\n  for (const newValue of valuesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.VALUE_ADDED_TO_ENUM,\n      description: `${newValue.name} was added to enum type ${oldType.name}.`,\n    });\n  }\n\n  for (const oldValue of valuesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.VALUE_REMOVED_FROM_ENUM,\n      description: `${oldValue.name} was removed from enum type ${oldType.name}.`,\n    });\n  }\n\n  return schemaChanges;\n}\n\nfunction findImplementedInterfacesChanges(oldType, newType) {\n  const schemaChanges = [];\n  const interfacesDiff = diff(oldType.getInterfaces(), newType.getInterfaces());\n\n  for (const newInterface of interfacesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.IMPLEMENTED_INTERFACE_ADDED,\n      description: `${newInterface.name} added to interfaces implemented by ${oldType.name}.`,\n    });\n  }\n\n  for (const oldInterface of interfacesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.IMPLEMENTED_INTERFACE_REMOVED,\n      description: `${oldType.name} no longer implements interface ${oldInterface.name}.`,\n    });\n  }\n\n  return schemaChanges;\n}\n\nfunction findFieldChanges(oldType, newType) {\n  const schemaChanges = [];\n  const fieldsDiff = diff(\n    Object.values(oldType.getFields()),\n    Object.values(newType.getFields()),\n  );\n\n  for (const oldField of fieldsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.FIELD_REMOVED,\n      description: `${oldType.name}.${oldField.name} was removed.`,\n    });\n  }\n\n  for (const [oldField, newField] of fieldsDiff.persisted) {\n    schemaChanges.push(...findArgChanges(oldType, oldField, newField));\n    const isSafe = isChangeSafeForObjectOrInterfaceField(\n      oldField.type,\n      newField.type,\n    );\n\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.FIELD_CHANGED_KIND,\n        description:\n          `${oldType.name}.${oldField.name} changed type from ` +\n          `${String(oldField.type)} to ${String(newField.type)}.`,\n      });\n    }\n  }\n\n  return schemaChanges;\n}\n\nfunction findArgChanges(oldType, oldField, newField) {\n  const schemaChanges = [];\n  const argsDiff = diff(oldField.args, newField.args);\n\n  for (const oldArg of argsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.ARG_REMOVED,\n      description: `${oldType.name}.${oldField.name} arg ${oldArg.name} was removed.`,\n    });\n  }\n\n  for (const [oldArg, newArg] of argsDiff.persisted) {\n    const isSafe = isChangeSafeForInputObjectFieldOrFieldArg(\n      oldArg.type,\n      newArg.type,\n    );\n\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.ARG_CHANGED_KIND,\n        description:\n          `${oldType.name}.${oldField.name} arg ${oldArg.name} has changed type from ` +\n          `${String(oldArg.type)} to ${String(newArg.type)}.`,\n      });\n    } else if (oldArg.defaultValue !== undefined) {\n      if (newArg.defaultValue === undefined) {\n        schemaChanges.push({\n          type: DangerousChangeType.ARG_DEFAULT_VALUE_CHANGE,\n          description: `${oldType.name}.${oldField.name} arg ${oldArg.name} defaultValue was removed.`,\n        });\n      } else {\n        // Since we looking only for client's observable changes we should\n        // compare default values in the same representation as they are\n        // represented inside introspection.\n        const oldValueStr = stringifyValue(oldArg.defaultValue, oldArg.type);\n        const newValueStr = stringifyValue(newArg.defaultValue, newArg.type);\n\n        if (oldValueStr !== newValueStr) {\n          schemaChanges.push({\n            type: DangerousChangeType.ARG_DEFAULT_VALUE_CHANGE,\n            description: `${oldType.name}.${oldField.name} arg ${oldArg.name} has changed defaultValue from ${oldValueStr} to ${newValueStr}.`,\n          });\n        }\n      }\n    }\n  }\n\n  for (const newArg of argsDiff.added) {\n    if (isRequiredArgument(newArg)) {\n      schemaChanges.push({\n        type: BreakingChangeType.REQUIRED_ARG_ADDED,\n        description: `A required arg ${newArg.name} on ${oldType.name}.${oldField.name} was added.`,\n      });\n    } else {\n      schemaChanges.push({\n        type: DangerousChangeType.OPTIONAL_ARG_ADDED,\n        description: `An optional arg ${newArg.name} on ${oldType.name}.${oldField.name} was added.`,\n      });\n    }\n  }\n\n  return schemaChanges;\n}\n\nfunction isChangeSafeForObjectOrInterfaceField(oldType, newType) {\n  if (isListType(oldType)) {\n    return (\n      // if they're both lists, make sure the underlying types are compatible\n      (isListType(newType) &&\n        isChangeSafeForObjectOrInterfaceField(\n          oldType.ofType,\n          newType.ofType,\n        )) || // moving from nullable to non-null of the same underlying type is safe\n      (isNonNullType(newType) &&\n        isChangeSafeForObjectOrInterfaceField(oldType, newType.ofType))\n    );\n  }\n\n  if (isNonNullType(oldType)) {\n    // if they're both non-null, make sure the underlying types are compatible\n    return (\n      isNonNullType(newType) &&\n      isChangeSafeForObjectOrInterfaceField(oldType.ofType, newType.ofType)\n    );\n  }\n\n  return (\n    // if they're both named types, see if their names are equivalent\n    (isNamedType(newType) && oldType.name === newType.name) || // moving from nullable to non-null of the same underlying type is safe\n    (isNonNullType(newType) &&\n      isChangeSafeForObjectOrInterfaceField(oldType, newType.ofType))\n  );\n}\n\nfunction isChangeSafeForInputObjectFieldOrFieldArg(oldType, newType) {\n  if (isListType(oldType)) {\n    // if they're both lists, make sure the underlying types are compatible\n    return (\n      isListType(newType) &&\n      isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType.ofType)\n    );\n  }\n\n  if (isNonNullType(oldType)) {\n    return (\n      // if they're both non-null, make sure the underlying types are\n      // compatible\n      (isNonNullType(newType) &&\n        isChangeSafeForInputObjectFieldOrFieldArg(\n          oldType.ofType,\n          newType.ofType,\n        )) || // moving from non-null to nullable of the same underlying type is safe\n      (!isNonNullType(newType) &&\n        isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType))\n    );\n  } // if they're both named types, see if their names are equivalent\n\n  return isNamedType(newType) && oldType.name === newType.name;\n}\n\nfunction typeKindName(type) {\n  if (isScalarType(type)) {\n    return 'a Scalar type';\n  }\n\n  if (isObjectType(type)) {\n    return 'an Object type';\n  }\n\n  if (isInterfaceType(type)) {\n    return 'an Interface type';\n  }\n\n  if (isUnionType(type)) {\n    return 'a Union type';\n  }\n\n  if (isEnumType(type)) {\n    return 'an Enum type';\n  }\n\n  if (isInputObjectType(type)) {\n    return 'an Input type';\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected type: ' + inspect(type));\n}\n\nfunction stringifyValue(value, type) {\n  const ast = astFromValue(value, type);\n  ast != null || invariant(false);\n  return print(sortValueNode(ast));\n}\n\nfunction diff(oldArray, newArray) {\n  const added = [];\n  const removed = [];\n  const persisted = [];\n  const oldMap = keyMap(oldArray, ({ name }) => name);\n  const newMap = keyMap(newArray, ({ name }) => name);\n\n  for (const oldItem of oldArray) {\n    const newItem = newMap[oldItem.name];\n\n    if (newItem === undefined) {\n      removed.push(oldItem);\n    } else {\n      persisted.push([oldItem, newItem]);\n    }\n  }\n\n  for (const newItem of newArray) {\n    if (oldMap[newItem.name] === undefined) {\n      added.push(newItem);\n    }\n  }\n\n  return {\n    added,\n    persisted,\n    removed,\n  };\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SACEC,UAAU,EACVC,iBAAiB,EACjBC,eAAe,EACfC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACpBC,YAAY,EACZC,WAAW,QACN,wBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,IAAIC,kBAAkB;AAEtB,CAAC,UAAUA,kBAAkB,EAAE;EAC7BA,kBAAkB,CAAC,cAAc,CAAC,GAAG,cAAc;EACnDA,kBAAkB,CAAC,mBAAmB,CAAC,GAAG,mBAAmB;EAC7DA,kBAAkB,CAAC,yBAAyB,CAAC,GAAG,yBAAyB;EACzEA,kBAAkB,CAAC,yBAAyB,CAAC,GAAG,yBAAyB;EACzEA,kBAAkB,CAAC,4BAA4B,CAAC,GAC9C,4BAA4B;EAC9BA,kBAAkB,CAAC,+BAA+B,CAAC,GACjD,+BAA+B;EACjCA,kBAAkB,CAAC,eAAe,CAAC,GAAG,eAAe;EACrDA,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC/DA,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC/DA,kBAAkB,CAAC,aAAa,CAAC,GAAG,aAAa;EACjDA,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC3DA,kBAAkB,CAAC,mBAAmB,CAAC,GAAG,mBAAmB;EAC7DA,kBAAkB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EACrEA,kBAAkB,CAAC,8BAA8B,CAAC,GAChD,8BAA8B;EAChCA,kBAAkB,CAAC,8BAA8B,CAAC,GAChD,8BAA8B;EAChCA,kBAAkB,CAAC,4BAA4B,CAAC,GAC9C,4BAA4B;AAChC,CAAC,EAAEA,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AAEnD,SAASA,kBAAkB;AAC3B,IAAIC,mBAAmB;AAEvB,CAAC,UAAUA,mBAAmB,EAAE;EAC9BA,mBAAmB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EAClEA,mBAAmB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EAClEA,mBAAmB,CAAC,4BAA4B,CAAC,GAC/C,4BAA4B;EAC9BA,mBAAmB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAChEA,mBAAmB,CAAC,6BAA6B,CAAC,GAChD,6BAA6B;EAC/BA,mBAAmB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;AAC9E,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AAErD,SAASA,mBAAmB;;AAE5B;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACxD;EACA,OAAOC,iBAAiB,CAACF,SAAS,EAAEC,SAAS,CAAC,CAACE,MAAM,CAClDC,MAAM,IAAKA,MAAM,CAACC,IAAI,IAAIR,kBAC7B,CAAC;AACH;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASS,oBAAoBA,CAACN,SAAS,EAAEC,SAAS,EAAE;EACzD;EACA,OAAOC,iBAAiB,CAACF,SAAS,EAAEC,SAAS,CAAC,CAACE,MAAM,CAClDC,MAAM,IAAKA,MAAM,CAACC,IAAI,IAAIP,mBAC7B,CAAC;AACH;AAEA,SAASI,iBAAiBA,CAACF,SAAS,EAAEC,SAAS,EAAE;EAC/C,OAAO,CACL,GAAGM,eAAe,CAACP,SAAS,EAAEC,SAAS,CAAC,EACxC,GAAGO,oBAAoB,CAACR,SAAS,EAAEC,SAAS,CAAC,CAC9C;AACH;AAEA,SAASO,oBAAoBA,CAACR,SAAS,EAAEC,SAAS,EAAE;EAClD,MAAMQ,aAAa,GAAG,EAAE;EACxB,MAAMC,cAAc,GAAGC,IAAI,CACzBX,SAAS,CAACY,aAAa,CAAC,CAAC,EACzBX,SAAS,CAACW,aAAa,CAAC,CAC1B,CAAC;EAED,KAAK,MAAMC,YAAY,IAAIH,cAAc,CAACI,OAAO,EAAE;IACjDL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAACmB,iBAAiB;MAC1CC,WAAW,EAAG,GAAEJ,YAAY,CAACK,IAAK;IACpC,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM,CAACL,YAAY,EAAEM,YAAY,CAAC,IAAIT,cAAc,CAACU,SAAS,EAAE;IACnE,MAAMC,QAAQ,GAAGV,IAAI,CAACE,YAAY,CAACS,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC;IAE3D,KAAK,MAAMC,MAAM,IAAIF,QAAQ,CAACG,KAAK,EAAE;MACnC,IAAIlC,kBAAkB,CAACiC,MAAM,CAAC,EAAE;QAC9Bd,aAAa,CAACM,IAAI,CAAC;UACjBV,IAAI,EAAER,kBAAkB,CAAC4B,4BAA4B;UACrDR,WAAW,EAAG,kBAAiBM,MAAM,CAACL,IAAK,iBAAgBL,YAAY,CAACK,IAAK;QAC/E,CAAC,CAAC;MACJ;IACF;IAEA,KAAK,MAAMQ,MAAM,IAAIL,QAAQ,CAACP,OAAO,EAAE;MACrCL,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAAC8B,qBAAqB;QAC9CV,WAAW,EAAG,GAAES,MAAM,CAACR,IAAK,qBAAoBL,YAAY,CAACK,IAAK;MACpE,CAAC,CAAC;IACJ;IAEA,IAAIL,YAAY,CAACe,YAAY,IAAI,CAACT,YAAY,CAACS,YAAY,EAAE;MAC3DnB,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAACgC,4BAA4B;QACrDZ,WAAW,EAAG,oCAAmCJ,YAAY,CAACK,IAAK;MACrE,CAAC,CAAC;IACJ;IAEA,KAAK,MAAMY,QAAQ,IAAIjB,YAAY,CAACkB,SAAS,EAAE;MAC7C,IAAI,CAACZ,YAAY,CAACY,SAAS,CAACC,QAAQ,CAACF,QAAQ,CAAC,EAAE;QAC9CrB,aAAa,CAACM,IAAI,CAAC;UACjBV,IAAI,EAAER,kBAAkB,CAACoC,0BAA0B;UACnDhB,WAAW,EAAG,GAAEa,QAAS,qBAAoBjB,YAAY,CAACK,IAAK;QACjE,CAAC,CAAC;MACJ;IACF;EACF;EAEA,OAAOT,aAAa;AACtB;AAEA,SAASF,eAAeA,CAACP,SAAS,EAAEC,SAAS,EAAE;EAC7C,MAAMQ,aAAa,GAAG,EAAE;EACxB,MAAMyB,SAAS,GAAGvB,IAAI,CACpBwB,MAAM,CAACC,MAAM,CAACpC,SAAS,CAACqC,UAAU,CAAC,CAAC,CAAC,EACrCF,MAAM,CAACC,MAAM,CAACnC,SAAS,CAACoC,UAAU,CAAC,CAAC,CACtC,CAAC;EAED,KAAK,MAAMC,OAAO,IAAIJ,SAAS,CAACpB,OAAO,EAAE;IACvCL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAAC0C,YAAY;MACrCtB,WAAW,EAAEvB,qBAAqB,CAAC4C,OAAO,CAAC,GACtC,mBAAkBA,OAAO,CAACpB,IAAK,oDAAmD,GAClF,GAAEoB,OAAO,CAACpB,IAAK;IACtB,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM,CAACoB,OAAO,EAAEE,OAAO,CAAC,IAAIN,SAAS,CAACd,SAAS,EAAE;IACpD,IAAIrC,UAAU,CAACuD,OAAO,CAAC,IAAIvD,UAAU,CAACyD,OAAO,CAAC,EAAE;MAC9C/B,aAAa,CAACM,IAAI,CAAC,GAAG0B,mBAAmB,CAACH,OAAO,EAAEE,OAAO,CAAC,CAAC;IAC9D,CAAC,MAAM,IAAI/C,WAAW,CAAC6C,OAAO,CAAC,IAAI7C,WAAW,CAAC+C,OAAO,CAAC,EAAE;MACvD/B,aAAa,CAACM,IAAI,CAAC,GAAG2B,oBAAoB,CAACJ,OAAO,EAAEE,OAAO,CAAC,CAAC;IAC/D,CAAC,MAAM,IAAIxD,iBAAiB,CAACsD,OAAO,CAAC,IAAItD,iBAAiB,CAACwD,OAAO,CAAC,EAAE;MACnE/B,aAAa,CAACM,IAAI,CAAC,GAAG4B,0BAA0B,CAACL,OAAO,EAAEE,OAAO,CAAC,CAAC;IACrE,CAAC,MAAM,IAAInD,YAAY,CAACiD,OAAO,CAAC,IAAIjD,YAAY,CAACmD,OAAO,CAAC,EAAE;MACzD/B,aAAa,CAACM,IAAI,CAChB,GAAG6B,gBAAgB,CAACN,OAAO,EAAEE,OAAO,CAAC,EACrC,GAAGK,gCAAgC,CAACP,OAAO,EAAEE,OAAO,CACtD,CAAC;IACH,CAAC,MAAM,IAAIvD,eAAe,CAACqD,OAAO,CAAC,IAAIrD,eAAe,CAACuD,OAAO,CAAC,EAAE;MAC/D/B,aAAa,CAACM,IAAI,CAChB,GAAG6B,gBAAgB,CAACN,OAAO,EAAEE,OAAO,CAAC,EACrC,GAAGK,gCAAgC,CAACP,OAAO,EAAEE,OAAO,CACtD,CAAC;IACH,CAAC,MAAM,IAAIF,OAAO,CAACQ,WAAW,KAAKN,OAAO,CAACM,WAAW,EAAE;MACtDrC,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAACkD,iBAAiB;QAC1C9B,WAAW,EACR,GAAEqB,OAAO,CAACpB,IAAK,gBAAe,GAC9B,GAAE8B,YAAY,CAACV,OAAO,CAAE,OAAMU,YAAY,CAACR,OAAO,CAAE;MACzD,CAAC,CAAC;IACJ;EACF;EAEA,OAAO/B,aAAa;AACtB;AAEA,SAASkC,0BAA0BA,CAACL,OAAO,EAAEE,OAAO,EAAE;EACpD,MAAM/B,aAAa,GAAG,EAAE;EACxB,MAAMwC,UAAU,GAAGtC,IAAI,CACrBwB,MAAM,CAACC,MAAM,CAACE,OAAO,CAACY,SAAS,CAAC,CAAC,CAAC,EAClCf,MAAM,CAACC,MAAM,CAACI,OAAO,CAACU,SAAS,CAAC,CAAC,CACnC,CAAC;EAED,KAAK,MAAMC,QAAQ,IAAIF,UAAU,CAACzB,KAAK,EAAE;IACvC,IAAIjC,oBAAoB,CAAC4D,QAAQ,CAAC,EAAE;MAClC1C,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAACuD,0BAA0B;QACnDnC,WAAW,EAAG,oBAAmBkC,QAAQ,CAACjC,IAAK,kBAAiBoB,OAAO,CAACpB,IAAK;MAC/E,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAEP,mBAAmB,CAACuD,0BAA0B;QACpDpC,WAAW,EAAG,qBAAoBkC,QAAQ,CAACjC,IAAK,kBAAiBoB,OAAO,CAACpB,IAAK;MAChF,CAAC,CAAC;IACJ;EACF;EAEA,KAAK,MAAMoC,QAAQ,IAAIL,UAAU,CAACnC,OAAO,EAAE;IACzCL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAAC0D,aAAa;MACtCtC,WAAW,EAAG,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK;IAChD,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM,CAACoC,QAAQ,EAAEH,QAAQ,CAAC,IAAIF,UAAU,CAAC7B,SAAS,EAAE;IACvD,MAAMoC,MAAM,GAAGC,yCAAyC,CACtDH,QAAQ,CAACjD,IAAI,EACb8C,QAAQ,CAAC9C,IACX,CAAC;IAED,IAAI,CAACmD,MAAM,EAAE;MACX/C,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAAC6D,kBAAkB;QAC3CzC,WAAW,EACR,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK,qBAAoB,GACpD,GAAEyC,MAAM,CAACL,QAAQ,CAACjD,IAAI,CAAE,OAAMsD,MAAM,CAACR,QAAQ,CAAC9C,IAAI,CAAE;MACzD,CAAC,CAAC;IACJ;EACF;EAEA,OAAOI,aAAa;AACtB;AAEA,SAASiC,oBAAoBA,CAACJ,OAAO,EAAEE,OAAO,EAAE;EAC9C,MAAM/B,aAAa,GAAG,EAAE;EACxB,MAAMmD,iBAAiB,GAAGjD,IAAI,CAAC2B,OAAO,CAACuB,QAAQ,CAAC,CAAC,EAAErB,OAAO,CAACqB,QAAQ,CAAC,CAAC,CAAC;EAEtE,KAAK,MAAMC,eAAe,IAAIF,iBAAiB,CAACpC,KAAK,EAAE;IACrDf,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAEP,mBAAmB,CAACiE,mBAAmB;MAC7C9C,WAAW,EAAG,GAAE6C,eAAe,CAAC5C,IAAK,4BAA2BoB,OAAO,CAACpB,IAAK;IAC/E,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM8C,eAAe,IAAIJ,iBAAiB,CAAC9C,OAAO,EAAE;IACvDL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAACoE,uBAAuB;MAChDhD,WAAW,EAAG,GAAE+C,eAAe,CAAC9C,IAAK,gCAA+BoB,OAAO,CAACpB,IAAK;IACnF,CAAC,CAAC;EACJ;EAEA,OAAOT,aAAa;AACtB;AAEA,SAASgC,mBAAmBA,CAACH,OAAO,EAAEE,OAAO,EAAE;EAC7C,MAAM/B,aAAa,GAAG,EAAE;EACxB,MAAMyD,UAAU,GAAGvD,IAAI,CAAC2B,OAAO,CAAC6B,SAAS,CAAC,CAAC,EAAE3B,OAAO,CAAC2B,SAAS,CAAC,CAAC,CAAC;EAEjE,KAAK,MAAMC,QAAQ,IAAIF,UAAU,CAAC1C,KAAK,EAAE;IACvCf,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAEP,mBAAmB,CAACuE,mBAAmB;MAC7CpD,WAAW,EAAG,GAAEmD,QAAQ,CAAClD,IAAK,2BAA0BoB,OAAO,CAACpB,IAAK;IACvE,CAAC,CAAC;EACJ;EAEA,KAAK,MAAMoD,QAAQ,IAAIJ,UAAU,CAACpD,OAAO,EAAE;IACzCL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAAC0E,uBAAuB;MAChDtD,WAAW,EAAG,GAAEqD,QAAQ,CAACpD,IAAK,+BAA8BoB,OAAO,CAACpB,IAAK;IAC3E,CAAC,CAAC;EACJ;EAEA,OAAOT,aAAa;AACtB;AAEA,SAASoC,gCAAgCA,CAACP,OAAO,EAAEE,OAAO,EAAE;EAC1D,MAAM/B,aAAa,GAAG,EAAE;EACxB,MAAM+D,cAAc,GAAG7D,IAAI,CAAC2B,OAAO,CAACmC,aAAa,CAAC,CAAC,EAAEjC,OAAO,CAACiC,aAAa,CAAC,CAAC,CAAC;EAE7E,KAAK,MAAMC,YAAY,IAAIF,cAAc,CAAChD,KAAK,EAAE;IAC/Cf,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAEP,mBAAmB,CAAC6E,2BAA2B;MACrD1D,WAAW,EAAG,GAAEyD,YAAY,CAACxD,IAAK,uCAAsCoB,OAAO,CAACpB,IAAK;IACvF,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM0D,YAAY,IAAIJ,cAAc,CAAC1D,OAAO,EAAE;IACjDL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAACgF,6BAA6B;MACtD5D,WAAW,EAAG,GAAEqB,OAAO,CAACpB,IAAK,mCAAkC0D,YAAY,CAAC1D,IAAK;IACnF,CAAC,CAAC;EACJ;EAEA,OAAOT,aAAa;AACtB;AAEA,SAASmC,gBAAgBA,CAACN,OAAO,EAAEE,OAAO,EAAE;EAC1C,MAAM/B,aAAa,GAAG,EAAE;EACxB,MAAMwC,UAAU,GAAGtC,IAAI,CACrBwB,MAAM,CAACC,MAAM,CAACE,OAAO,CAACY,SAAS,CAAC,CAAC,CAAC,EAClCf,MAAM,CAACC,MAAM,CAACI,OAAO,CAACU,SAAS,CAAC,CAAC,CACnC,CAAC;EAED,KAAK,MAAMI,QAAQ,IAAIL,UAAU,CAACnC,OAAO,EAAE;IACzCL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAAC0D,aAAa;MACtCtC,WAAW,EAAG,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK;IAChD,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM,CAACoC,QAAQ,EAAEH,QAAQ,CAAC,IAAIF,UAAU,CAAC7B,SAAS,EAAE;IACvDX,aAAa,CAACM,IAAI,CAAC,GAAG+D,cAAc,CAACxC,OAAO,EAAEgB,QAAQ,EAAEH,QAAQ,CAAC,CAAC;IAClE,MAAMK,MAAM,GAAGuB,qCAAqC,CAClDzB,QAAQ,CAACjD,IAAI,EACb8C,QAAQ,CAAC9C,IACX,CAAC;IAED,IAAI,CAACmD,MAAM,EAAE;MACX/C,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAAC6D,kBAAkB;QAC3CzC,WAAW,EACR,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK,qBAAoB,GACpD,GAAEyC,MAAM,CAACL,QAAQ,CAACjD,IAAI,CAAE,OAAMsD,MAAM,CAACR,QAAQ,CAAC9C,IAAI,CAAE;MACzD,CAAC,CAAC;IACJ;EACF;EAEA,OAAOI,aAAa;AACtB;AAEA,SAASqE,cAAcA,CAACxC,OAAO,EAAEgB,QAAQ,EAAEH,QAAQ,EAAE;EACnD,MAAM1C,aAAa,GAAG,EAAE;EACxB,MAAMY,QAAQ,GAAGV,IAAI,CAAC2C,QAAQ,CAAChC,IAAI,EAAE6B,QAAQ,CAAC7B,IAAI,CAAC;EAEnD,KAAK,MAAMI,MAAM,IAAIL,QAAQ,CAACP,OAAO,EAAE;IACrCL,aAAa,CAACM,IAAI,CAAC;MACjBV,IAAI,EAAER,kBAAkB,CAACmF,WAAW;MACpC/D,WAAW,EAAG,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK,QAAOQ,MAAM,CAACR,IAAK;IACnE,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM,CAACQ,MAAM,EAAEH,MAAM,CAAC,IAAIF,QAAQ,CAACD,SAAS,EAAE;IACjD,MAAMoC,MAAM,GAAGC,yCAAyC,CACtD/B,MAAM,CAACrB,IAAI,EACXkB,MAAM,CAAClB,IACT,CAAC;IAED,IAAI,CAACmD,MAAM,EAAE;MACX/C,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAACoF,gBAAgB;QACzChE,WAAW,EACR,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK,QAAOQ,MAAM,CAACR,IAAK,yBAAwB,GAC3E,GAAEyC,MAAM,CAACjC,MAAM,CAACrB,IAAI,CAAE,OAAMsD,MAAM,CAACpC,MAAM,CAAClB,IAAI,CAAE;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIqB,MAAM,CAACwD,YAAY,KAAKC,SAAS,EAAE;MAC5C,IAAI5D,MAAM,CAAC2D,YAAY,KAAKC,SAAS,EAAE;QACrC1E,aAAa,CAACM,IAAI,CAAC;UACjBV,IAAI,EAAEP,mBAAmB,CAACsF,wBAAwB;UAClDnE,WAAW,EAAG,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK,QAAOQ,MAAM,CAACR,IAAK;QACnE,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;QACA;QACA,MAAMmE,WAAW,GAAGC,cAAc,CAAC5D,MAAM,CAACwD,YAAY,EAAExD,MAAM,CAACrB,IAAI,CAAC;QACpE,MAAMkF,WAAW,GAAGD,cAAc,CAAC/D,MAAM,CAAC2D,YAAY,EAAE3D,MAAM,CAAClB,IAAI,CAAC;QAEpE,IAAIgF,WAAW,KAAKE,WAAW,EAAE;UAC/B9E,aAAa,CAACM,IAAI,CAAC;YACjBV,IAAI,EAAEP,mBAAmB,CAACsF,wBAAwB;YAClDnE,WAAW,EAAG,GAAEqB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK,QAAOQ,MAAM,CAACR,IAAK,kCAAiCmE,WAAY,OAAME,WAAY;UAClI,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEA,KAAK,MAAMhE,MAAM,IAAIF,QAAQ,CAACG,KAAK,EAAE;IACnC,IAAIlC,kBAAkB,CAACiC,MAAM,CAAC,EAAE;MAC9Bd,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAER,kBAAkB,CAAC2F,kBAAkB;QAC3CvE,WAAW,EAAG,kBAAiBM,MAAM,CAACL,IAAK,OAAMoB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK;MACjF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,aAAa,CAACM,IAAI,CAAC;QACjBV,IAAI,EAAEP,mBAAmB,CAAC2F,kBAAkB;QAC5CxE,WAAW,EAAG,mBAAkBM,MAAM,CAACL,IAAK,OAAMoB,OAAO,CAACpB,IAAK,IAAGoC,QAAQ,CAACpC,IAAK;MAClF,CAAC,CAAC;IACJ;EACF;EAEA,OAAOT,aAAa;AACtB;AAEA,SAASsE,qCAAqCA,CAACzC,OAAO,EAAEE,OAAO,EAAE;EAC/D,IAAItD,UAAU,CAACoD,OAAO,CAAC,EAAE;IACvB;MACE;MACCpD,UAAU,CAACsD,OAAO,CAAC,IAClBuC,qCAAqC,CACnCzC,OAAO,CAACoD,MAAM,EACdlD,OAAO,CAACkD,MACV,CAAC;MAAK;MACPtG,aAAa,CAACoD,OAAO,CAAC,IACrBuC,qCAAqC,CAACzC,OAAO,EAAEE,OAAO,CAACkD,MAAM;IAAE;EAErE;EAEA,IAAItG,aAAa,CAACkD,OAAO,CAAC,EAAE;IAC1B;IACA,OACElD,aAAa,CAACoD,OAAO,CAAC,IACtBuC,qCAAqC,CAACzC,OAAO,CAACoD,MAAM,EAAElD,OAAO,CAACkD,MAAM,CAAC;EAEzE;EAEA;IACE;IACCvG,WAAW,CAACqD,OAAO,CAAC,IAAIF,OAAO,CAACpB,IAAI,KAAKsB,OAAO,CAACtB,IAAI;IAAK;IAC1D9B,aAAa,CAACoD,OAAO,CAAC,IACrBuC,qCAAqC,CAACzC,OAAO,EAAEE,OAAO,CAACkD,MAAM;EAAE;AAErE;AAEA,SAASjC,yCAAyCA,CAACnB,OAAO,EAAEE,OAAO,EAAE;EACnE,IAAItD,UAAU,CAACoD,OAAO,CAAC,EAAE;IACvB;IACA,OACEpD,UAAU,CAACsD,OAAO,CAAC,IACnBiB,yCAAyC,CAACnB,OAAO,CAACoD,MAAM,EAAElD,OAAO,CAACkD,MAAM,CAAC;EAE7E;EAEA,IAAItG,aAAa,CAACkD,OAAO,CAAC,EAAE;IAC1B;MACE;MACA;MACClD,aAAa,CAACoD,OAAO,CAAC,IACrBiB,yCAAyC,CACvCnB,OAAO,CAACoD,MAAM,EACdlD,OAAO,CAACkD,MACV,CAAC;MAAK;MACP,CAACtG,aAAa,CAACoD,OAAO,CAAC,IACtBiB,yCAAyC,CAACnB,OAAO,CAACoD,MAAM,EAAElD,OAAO;IAAE;EAEzE,CAAC,CAAC;;EAEF,OAAOrD,WAAW,CAACqD,OAAO,CAAC,IAAIF,OAAO,CAACpB,IAAI,KAAKsB,OAAO,CAACtB,IAAI;AAC9D;AAEA,SAAS8B,YAAYA,CAAC3C,IAAI,EAAE;EAC1B,IAAIb,YAAY,CAACa,IAAI,CAAC,EAAE;IACtB,OAAO,eAAe;EACxB;EAEA,IAAIhB,YAAY,CAACgB,IAAI,CAAC,EAAE;IACtB,OAAO,gBAAgB;EACzB;EAEA,IAAIpB,eAAe,CAACoB,IAAI,CAAC,EAAE;IACzB,OAAO,mBAAmB;EAC5B;EAEA,IAAIZ,WAAW,CAACY,IAAI,CAAC,EAAE;IACrB,OAAO,cAAc;EACvB;EAEA,IAAItB,UAAU,CAACsB,IAAI,CAAC,EAAE;IACpB,OAAO,cAAc;EACvB;EAEA,IAAIrB,iBAAiB,CAACqB,IAAI,CAAC,EAAE;IAC3B,OAAO,eAAe;EACxB;EACA;EACA;;EAEA,KAAK,IAAIzB,SAAS,CAAC,KAAK,EAAE,mBAAmB,GAAGD,OAAO,CAAC0B,IAAI,CAAC,CAAC;AAChE;AAEA,SAASiF,cAAcA,CAACK,KAAK,EAAEtF,IAAI,EAAE;EACnC,MAAMuF,GAAG,GAAGjG,YAAY,CAACgG,KAAK,EAAEtF,IAAI,CAAC;EACrCuF,GAAG,IAAI,IAAI,IAAIhH,SAAS,CAAC,KAAK,CAAC;EAC/B,OAAOE,KAAK,CAACc,aAAa,CAACgG,GAAG,CAAC,CAAC;AAClC;AAEA,SAASjF,IAAIA,CAACkF,QAAQ,EAAEC,QAAQ,EAAE;EAChC,MAAMtE,KAAK,GAAG,EAAE;EAChB,MAAMV,OAAO,GAAG,EAAE;EAClB,MAAMM,SAAS,GAAG,EAAE;EACpB,MAAM2E,MAAM,GAAGlH,MAAM,CAACgH,QAAQ,EAAE,CAAC;IAAE3E;EAAK,CAAC,KAAKA,IAAI,CAAC;EACnD,MAAM8E,MAAM,GAAGnH,MAAM,CAACiH,QAAQ,EAAE,CAAC;IAAE5E;EAAK,CAAC,KAAKA,IAAI,CAAC;EAEnD,KAAK,MAAM+E,OAAO,IAAIJ,QAAQ,EAAE;IAC9B,MAAMK,OAAO,GAAGF,MAAM,CAACC,OAAO,CAAC/E,IAAI,CAAC;IAEpC,IAAIgF,OAAO,KAAKf,SAAS,EAAE;MACzBrE,OAAO,CAACC,IAAI,CAACkF,OAAO,CAAC;IACvB,CAAC,MAAM;MACL7E,SAAS,CAACL,IAAI,CAAC,CAACkF,OAAO,EAAEC,OAAO,CAAC,CAAC;IACpC;EACF;EAEA,KAAK,MAAMA,OAAO,IAAIJ,QAAQ,EAAE;IAC9B,IAAIC,MAAM,CAACG,OAAO,CAAChF,IAAI,CAAC,KAAKiE,SAAS,EAAE;MACtC3D,KAAK,CAACT,IAAI,CAACmF,OAAO,CAAC;IACrB;EACF;EAEA,OAAO;IACL1E,KAAK;IACLJ,SAAS;IACTN;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}