{"ast": null, "code": "export function toObjMap(obj) {\n  if (obj == null) {\n    return Object.create(null);\n  }\n  if (Object.getPrototypeOf(obj) === null) {\n    return obj;\n  }\n  const map = Object.create(null);\n  for (const [key, value] of Object.entries(obj)) {\n    map[key] = value;\n  }\n  return map;\n}", "map": {"version": 3, "names": ["toObjMap", "obj", "Object", "create", "getPrototypeOf", "map", "key", "value", "entries"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/jsutils/toObjMap.mjs"], "sourcesContent": ["export function toObjMap(obj) {\n  if (obj == null) {\n    return Object.create(null);\n  }\n\n  if (Object.getPrototypeOf(obj) === null) {\n    return obj;\n  }\n\n  const map = Object.create(null);\n\n  for (const [key, value] of Object.entries(obj)) {\n    map[key] = value;\n  }\n\n  return map;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAIA,GAAG,IAAI,IAAI,EAAE;IACf,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC5B;EAEA,IAAID,MAAM,CAACE,cAAc,CAACH,GAAG,CAAC,KAAK,IAAI,EAAE;IACvC,OAAOA,GAAG;EACZ;EAEA,MAAMI,GAAG,GAAGH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAE/B,KAAK,MAAM,CAACG,GAAG,EAAEC,KAAK,CAAC,IAAIL,MAAM,CAACM,OAAO,CAACP,GAAG,CAAC,EAAE;IAC9CI,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAClB;EAEA,OAAOF,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}