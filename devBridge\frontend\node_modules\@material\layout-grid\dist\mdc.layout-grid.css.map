{"version": 3, "sources": ["webpack:///./packages/mdc-layout-grid/mdc-layout-grid.scss", "webpack:///mdc-layout-grid.scss", "webpack:///./packages/mdc-layout-grid/_mixins.scss"], "names": [], "mappings": ";;;;;;;AA4BA;EAEI;EAIA;EAIA;EARA;EAIA;EAIA;EARA;EAIA;EAIA;AC5BJ;;AC0DI;EFtBJ;IE2DE;IACA;IACA,aF1Da;IE2Db;ED1FA;AACF;AC4CI;EFjBJ;IE2DE;IACA;IACA,aF1Da;IE2Db;EDlFA;AACF;AC+BI;EFZJ;IE2DE;IACA;IACA,aF1Da;IE2Db;ED1EA;AACF;;ACiCI;EFZJ;IEgEE;IACA;IACA;IACA;IACA;EDhFA;ECkFA;IFtEF;MEuEI;MACA;MACA,cFrEW;MEsEX;MACA;ID/EA;EACF;AACF;ACSI;EFPJ;IEgEE;IACA;IACA;IACA;IACA;ED9DA;ECgEA;IFtEF;MEuEI;MACA;MACA,cFrEW;MEsEX;MACA;ID7DA;EACF;AACF;ACdI;EFFJ;IEgEE;IACA;IACA;IACA;IACA;ED5CA;EC8CA;IFtEF;MEuEI;MACA;MACA,cFrEW;MEsEX;MACA;ID3CA;EACF;AACF;;ACtBI;EFDJ;IEuBE;IACA;IAsDA;IACA;IACA;EDhDA;ECNA;IF1BF;ME2BI;MACA;IDSA;EACF;EC4CA;IFlFF;MEmFI;IDzCA;EACF;ED/Be;;IEWf;IACA;EDwBA;ECtBA;IFde;;MEeb;MACA;ID0BA;EACF;;ED3Ce;;IEWf;IACA;EDqCA;ECnCA;IFde;;MEeb;MACA;IDuCA;EACF;;EDxDe;;IEWf;IACA;EDkDA;EChDA;IFde;;MEeb;MACA;IDoDA;EACF;;EDrEe;;IEWf;IACA;ED+DA;EC7DA;IFde;;MEeb;MACA;IDiEA;EACF;;EDlFe;;IEWf;IACA;ED4EA;EC1EA;IFde;;MEeb;MACA;ID8EA;EACF;;ED/Fe;;IEWf;IACA;EDyFA;ECvFA;IFde;;MEeb;MACA;ID2FA;EACF;;ED5Ge;;IEWf;IACA;EDsGA;ECpGA;IFde;;MEeb;MACA;IDwGA;EACF;;EDzHe;;IEWf;IACA;EDmHA;ECjHA;IFde;;MEeb;MACA;IDqHA;EACF;;EDtIe;;IEWf;IACA;EDgIA;EC9HA;IFde;;MEeb;MACA;IDkIA;EACF;;EDnJe;;IEWf;IACA;ED6IA;EC3IA;IFde;;MEeb;MACA;ID+IA;EACF;;EDhKe;;IEWf;IACA;ED0JA;ECxJA;IFde;;MEeb;MACA;ID4JA;EACF;;ED7Ke;;IEWf;IACA;EDuKA;ECrKA;IFde;;MEeb;MACA;IDyKA;EACF;AACF;AC3MI;EFIJ;IEuBE;IACA;IAsDA;IACA;IACA;ED+HA;ECrLA;IF1BF;ME2BI;MACA;IDwLA;EACF;ECnIA;IFlFF;MEmFI;IDsIA;EACF;ED9Me;;IEWf;IACA;EDuMA;ECrMA;IFde;;MEeb;MACA;IDyMA;EACF;;ED1Ne;;IEWf;IACA;EDoNA;EClNA;IFde;;MEeb;MACA;IDsNA;EACF;;EDvOe;;IEWf;IACA;EDiOA;EC/NA;IFde;;MEeb;MACA;IDmOA;EACF;;EDpPe;;IEWf;IACA;ED8OA;EC5OA;IFde;;MEeb;MACA;IDgPA;EACF;;EDjQe;;IEWf;IACA;ED2PA;ECzPA;IFde;;MEeb;MACA;ID6PA;EACF;;ED9Qe;;IEWf;IACA;EDwQA;ECtQA;IFde;;MEeb;MACA;ID0QA;EACF;;ED3Re;;IEWf;IACA;EDqRA;ECnRA;IFde;;MEeb;MACA;IDuRA;EACF;;EDxSe;;IEWf;IACA;EDkSA;EChSA;IFde;;MEeb;MACA;IDoSA;EACF;;EDrTe;;IEWf;IACA;ED+SA;EC7SA;IFde;;MEeb;MACA;IDiTA;EACF;;EDlUe;;IEWf;IACA;ED4TA;EC1TA;IFde;;MEeb;MACA;ID8TA;EACF;;ED/Ue;;IEWf;IACA;EDyUA;ECvUA;IFde;;MEeb;MACA;ID2UA;EACF;;ED5Ve;;IEWf;IACA;EDsVA;ECpVA;IFde;;MEeb;MACA;IDwVA;EACF;AACF;AC/XI;EFSJ;IEuBE;IACA;IAsDA;IACA;IACA;ED8SA;ECpWA;IF1BF;ME2BI;MACA;IDuWA;EACF;EClTA;IFlFF;MEmFI;IDqTA;EACF;ED7Xe;;IEWf;IACA;EDsXA;ECpXA;IFde;;MEeb;MACA;IDwXA;EACF;;EDzYe;;IEWf;IACA;EDmYA;ECjYA;IFde;;MEeb;MACA;IDqYA;EACF;;EDtZe;;IEWf;IACA;EDgZA;EC9YA;IFde;;MEeb;MACA;IDkZA;EACF;;EDnae;;IEWf;IACA;ED6ZA;EC3ZA;IFde;;MEeb;MACA;ID+ZA;EACF;;EDhbe;;IEWf;IACA;ED0aA;ECxaA;IFde;;MEeb;MACA;ID4aA;EACF;;ED7be;;IEWf;IACA;EDubA;ECrbA;IFde;;MEeb;MACA;IDybA;EACF;;ED1ce;;IEWf;IACA;EDocA;EClcA;IFde;;MEeb;MACA;IDscA;EACF;;EDvde;;IEWf;IACA;EDidA;EC/cA;IFde;;MEeb;MACA;IDmdA;EACF;;EDpee;;IEWf;IACA;ED8dA;EC5dA;IFde;;MEeb;MACA;IDgeA;EACF;;EDjfe;;IEWf;IACA;ED2eA;ECzeA;IFde;;MEeb;MACA;ID6eA;EACF;;ED9fe;;IEWf;IACA;EDwfA;ECtfA;IFde;;MEeb;MACA;ID0fA;EACF;;ED3gBe;;IEWf;IACA;EDqgBA;ECngBA;IFde;;MEeb;MACA;IDugBA;EACF;AACF;AD/gBI;EEkEF,QFnEa;ACmhBf;ADlhBI;EEkEF,QFnEa;ACshBf;ADrhBI;EEkEF,QFnEa;ACyhBf;ADxhBI;EEkEF,QFnEa;AC4hBf;AD3hBI;EEkEF,QFnEa;AC+hBf;AD9hBI;EEkEF,QFnEa;ACkiBf;ADjiBI;EEkEF,QFnEa;ACqiBf;ADpiBI;EEkEF,QFnEa;ACwiBf;ADviBI;EEkEF,QFnEa;AC2iBf;AD1iBI;EEkEF,SFnEa;AC8iBf;AD7iBI;EEkEF,SFnEa;ACijBf;ADhjBI;EEkEF,SFnEa;ACojBf;AD7iBE;EEiEE;AD+eJ;AC7eI;EFnEF;IEoEI;EDgfJ;AACF;ADjjBE;EEqEE;AD+eJ;ADhjBE;EEqEE;AD8eJ;AC5eI;EFvEF;IEwEI;ED+eJ;AACF;;AC3lBI;EFwCJ;IEqFE;IAEA;;;;GAAA;EDseA;AACF;AC3mBI;EF6CJ;IEqFE;IAEA;;;;GAAA;EDgfA;AACF;AC1nBI;EFkDJ;IEqFE;IAEA;;;;GAAA;ED0fA;AACF;;ADjkBA;EACE;EACA;ACokBF;;ADjkBA;EACE;EACA;ACokBF,C", "file": "mdc.layout-grid.css", "sourcesContent": ["// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:list';\n@use 'sass:map';\n@use './variables';\n@use './mixins';\n\n:root {\n  @each $size in map.keys(variables.$columns) {\n    --mdc-layout-grid-margin-#{$size}: #{map.get(\n        variables.$default-margin,\n        $size\n      )};\n    --mdc-layout-grid-gutter-#{$size}: #{map.get(\n        variables.$default-gutter,\n        $size\n      )};\n    --mdc-layout-grid-column-width-#{$size}: #{map.get(\n        variables.$column-width,\n        $size\n      )};\n  }\n}\n\n// postcss-bem-linter: define layout-grid\n.mdc-layout-grid {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n\n      @include mixins.layout-grid($size, $margin, variables.$max-width);\n    }\n  }\n}\n\n.mdc-layout-grid__inner {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n      $gutter: map.get(variables.$default-gutter, $size);\n\n      @include mixins.inner($size, $margin, $gutter);\n    }\n  }\n}\n\n.mdc-layout-grid__cell {\n  // select the upper breakpoint\n  $upper-breakpoint: list.nth(map.keys(variables.$columns), 1);\n\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $gutter: map.get(variables.$default-gutter, $size);\n\n      @include mixins.cell($size, variables.$default-column-span, $gutter);\n\n      @for $span from 1 through map.get(variables.$columns, $upper-breakpoint) {\n        // Span classes.\n        @at-root .mdc-layout-grid__cell--span-#{$span},\n          .mdc-layout-grid__cell--span-#{$span}-#{$size} {\n          @include mixins.cell-span_($size, $span, $gutter);\n        }\n      }\n    }\n  }\n\n  // Order override classes.\n  @for $i from 1 through map.get(variables.$columns, $upper-breakpoint) {\n    &--order-#{$i} {\n      @include mixins.cell-order($i);\n    }\n  }\n\n  // Alignment classes.\n  &--align-top {\n    @include mixins.cell-align(top);\n  }\n\n  &--align-middle {\n    @include mixins.cell-align(middle);\n  }\n\n  &--align-bottom {\n    @include mixins.cell-align(bottom);\n  }\n}\n\n.mdc-layout-grid--fixed-column-width {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n      $gutter: map.get(variables.$default-gutter, $size);\n      $column-width: map.get(variables.$column-width, $size);\n\n      @include mixins.fixed-column-width(\n        $size,\n        $margin,\n        $gutter,\n        $column-width\n      );\n    }\n  }\n}\n\n.mdc-layout-grid--align-left {\n  margin-right: auto;\n  margin-left: 0;\n}\n\n.mdc-layout-grid--align-right {\n  margin-right: 0;\n  margin-left: auto;\n}\n// postcss-bem-linter: end\n", ":root {\n  --mdc-layout-grid-margin-desktop: 24px;\n  --mdc-layout-grid-gutter-desktop: 24px;\n  --mdc-layout-grid-column-width-desktop: 72px;\n  --mdc-layout-grid-margin-tablet: 16px;\n  --mdc-layout-grid-gutter-tablet: 16px;\n  --mdc-layout-grid-column-width-tablet: 72px;\n  --mdc-layout-grid-margin-phone: 16px;\n  --mdc-layout-grid-gutter-phone: 16px;\n  --mdc-layout-grid-column-width-phone: 72px;\n}\n\n@media (min-width: 840px) {\n  .mdc-layout-grid {\n    box-sizing: border-box;\n    margin: 0 auto;\n    padding: 24px;\n    padding: var(--mdc-layout-grid-margin-desktop, 24px);\n  }\n}\n@media (min-width: 600px) and (max-width: 839px) {\n  .mdc-layout-grid {\n    box-sizing: border-box;\n    margin: 0 auto;\n    padding: 16px;\n    padding: var(--mdc-layout-grid-margin-tablet, 16px);\n  }\n}\n@media (max-width: 599px) {\n  .mdc-layout-grid {\n    box-sizing: border-box;\n    margin: 0 auto;\n    padding: 16px;\n    padding: var(--mdc-layout-grid-margin-phone, 16px);\n  }\n}\n\n@media (min-width: 840px) {\n  .mdc-layout-grid__inner {\n    display: flex;\n    flex-flow: row wrap;\n    align-items: stretch;\n    margin: -12px;\n    margin: calc(var(--mdc-layout-grid-gutter-desktop, 24px) / 2 * -1);\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__inner {\n      display: grid;\n      margin: 0;\n      grid-gap: 24px;\n      grid-gap: var(--mdc-layout-grid-gutter-desktop, 24px);\n      grid-template-columns: repeat(12, minmax(0, 1fr));\n    }\n  }\n}\n@media (min-width: 600px) and (max-width: 839px) {\n  .mdc-layout-grid__inner {\n    display: flex;\n    flex-flow: row wrap;\n    align-items: stretch;\n    margin: -8px;\n    margin: calc(var(--mdc-layout-grid-gutter-tablet, 16px) / 2 * -1);\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__inner {\n      display: grid;\n      margin: 0;\n      grid-gap: 16px;\n      grid-gap: var(--mdc-layout-grid-gutter-tablet, 16px);\n      grid-template-columns: repeat(8, minmax(0, 1fr));\n    }\n  }\n}\n@media (max-width: 599px) {\n  .mdc-layout-grid__inner {\n    display: flex;\n    flex-flow: row wrap;\n    align-items: stretch;\n    margin: -8px;\n    margin: calc(var(--mdc-layout-grid-gutter-phone, 16px) / 2 * -1);\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__inner {\n      display: grid;\n      margin: 0;\n      grid-gap: 16px;\n      grid-gap: var(--mdc-layout-grid-gutter-phone, 16px);\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n}\n\n@media (min-width: 840px) {\n  .mdc-layout-grid__cell {\n    width: calc(33.3333333333% - 24px);\n    width: calc(33.3333333333% - var(--mdc-layout-grid-gutter-desktop, 24px));\n    box-sizing: border-box;\n    margin: 12px;\n    margin: calc(var(--mdc-layout-grid-gutter-desktop, 24px) / 2);\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell {\n      margin: 0;\n    }\n  }\n  .mdc-layout-grid__cell--span-1,\n.mdc-layout-grid__cell--span-1-desktop {\n    width: calc(8.3333333333% - 24px);\n    width: calc(8.3333333333% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-1,\n.mdc-layout-grid__cell--span-1-desktop {\n      width: auto;\n      grid-column-end: span 1;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-2,\n.mdc-layout-grid__cell--span-2-desktop {\n    width: calc(16.6666666667% - 24px);\n    width: calc(16.6666666667% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-2,\n.mdc-layout-grid__cell--span-2-desktop {\n      width: auto;\n      grid-column-end: span 2;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-3,\n.mdc-layout-grid__cell--span-3-desktop {\n    width: calc(25% - 24px);\n    width: calc(25% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-3,\n.mdc-layout-grid__cell--span-3-desktop {\n      width: auto;\n      grid-column-end: span 3;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-4,\n.mdc-layout-grid__cell--span-4-desktop {\n    width: calc(33.3333333333% - 24px);\n    width: calc(33.3333333333% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-4,\n.mdc-layout-grid__cell--span-4-desktop {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-5,\n.mdc-layout-grid__cell--span-5-desktop {\n    width: calc(41.6666666667% - 24px);\n    width: calc(41.6666666667% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-5,\n.mdc-layout-grid__cell--span-5-desktop {\n      width: auto;\n      grid-column-end: span 5;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-6,\n.mdc-layout-grid__cell--span-6-desktop {\n    width: calc(50% - 24px);\n    width: calc(50% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-6,\n.mdc-layout-grid__cell--span-6-desktop {\n      width: auto;\n      grid-column-end: span 6;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-7,\n.mdc-layout-grid__cell--span-7-desktop {\n    width: calc(58.3333333333% - 24px);\n    width: calc(58.3333333333% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-7,\n.mdc-layout-grid__cell--span-7-desktop {\n      width: auto;\n      grid-column-end: span 7;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-8,\n.mdc-layout-grid__cell--span-8-desktop {\n    width: calc(66.6666666667% - 24px);\n    width: calc(66.6666666667% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-8,\n.mdc-layout-grid__cell--span-8-desktop {\n      width: auto;\n      grid-column-end: span 8;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-9,\n.mdc-layout-grid__cell--span-9-desktop {\n    width: calc(75% - 24px);\n    width: calc(75% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-9,\n.mdc-layout-grid__cell--span-9-desktop {\n      width: auto;\n      grid-column-end: span 9;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-10,\n.mdc-layout-grid__cell--span-10-desktop {\n    width: calc(83.3333333333% - 24px);\n    width: calc(83.3333333333% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-10,\n.mdc-layout-grid__cell--span-10-desktop {\n      width: auto;\n      grid-column-end: span 10;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-11,\n.mdc-layout-grid__cell--span-11-desktop {\n    width: calc(91.6666666667% - 24px);\n    width: calc(91.6666666667% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-11,\n.mdc-layout-grid__cell--span-11-desktop {\n      width: auto;\n      grid-column-end: span 11;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-12,\n.mdc-layout-grid__cell--span-12-desktop {\n    width: calc(100% - 24px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-desktop, 24px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-12,\n.mdc-layout-grid__cell--span-12-desktop {\n      width: auto;\n      grid-column-end: span 12;\n    }\n  }\n}\n@media (min-width: 600px) and (max-width: 839px) {\n  .mdc-layout-grid__cell {\n    width: calc(50% - 16px);\n    width: calc(50% - var(--mdc-layout-grid-gutter-tablet, 16px));\n    box-sizing: border-box;\n    margin: 8px;\n    margin: calc(var(--mdc-layout-grid-gutter-tablet, 16px) / 2);\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell {\n      margin: 0;\n    }\n  }\n  .mdc-layout-grid__cell--span-1,\n.mdc-layout-grid__cell--span-1-tablet {\n    width: calc(12.5% - 16px);\n    width: calc(12.5% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-1,\n.mdc-layout-grid__cell--span-1-tablet {\n      width: auto;\n      grid-column-end: span 1;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-2,\n.mdc-layout-grid__cell--span-2-tablet {\n    width: calc(25% - 16px);\n    width: calc(25% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-2,\n.mdc-layout-grid__cell--span-2-tablet {\n      width: auto;\n      grid-column-end: span 2;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-3,\n.mdc-layout-grid__cell--span-3-tablet {\n    width: calc(37.5% - 16px);\n    width: calc(37.5% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-3,\n.mdc-layout-grid__cell--span-3-tablet {\n      width: auto;\n      grid-column-end: span 3;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-4,\n.mdc-layout-grid__cell--span-4-tablet {\n    width: calc(50% - 16px);\n    width: calc(50% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-4,\n.mdc-layout-grid__cell--span-4-tablet {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-5,\n.mdc-layout-grid__cell--span-5-tablet {\n    width: calc(62.5% - 16px);\n    width: calc(62.5% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-5,\n.mdc-layout-grid__cell--span-5-tablet {\n      width: auto;\n      grid-column-end: span 5;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-6,\n.mdc-layout-grid__cell--span-6-tablet {\n    width: calc(75% - 16px);\n    width: calc(75% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-6,\n.mdc-layout-grid__cell--span-6-tablet {\n      width: auto;\n      grid-column-end: span 6;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-7,\n.mdc-layout-grid__cell--span-7-tablet {\n    width: calc(87.5% - 16px);\n    width: calc(87.5% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-7,\n.mdc-layout-grid__cell--span-7-tablet {\n      width: auto;\n      grid-column-end: span 7;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-8,\n.mdc-layout-grid__cell--span-8-tablet {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-8,\n.mdc-layout-grid__cell--span-8-tablet {\n      width: auto;\n      grid-column-end: span 8;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-9,\n.mdc-layout-grid__cell--span-9-tablet {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-9,\n.mdc-layout-grid__cell--span-9-tablet {\n      width: auto;\n      grid-column-end: span 8;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-10,\n.mdc-layout-grid__cell--span-10-tablet {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-10,\n.mdc-layout-grid__cell--span-10-tablet {\n      width: auto;\n      grid-column-end: span 8;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-11,\n.mdc-layout-grid__cell--span-11-tablet {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-11,\n.mdc-layout-grid__cell--span-11-tablet {\n      width: auto;\n      grid-column-end: span 8;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-12,\n.mdc-layout-grid__cell--span-12-tablet {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-tablet, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-12,\n.mdc-layout-grid__cell--span-12-tablet {\n      width: auto;\n      grid-column-end: span 8;\n    }\n  }\n}\n@media (max-width: 599px) {\n  .mdc-layout-grid__cell {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n    box-sizing: border-box;\n    margin: 8px;\n    margin: calc(var(--mdc-layout-grid-gutter-phone, 16px) / 2);\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell {\n      margin: 0;\n    }\n  }\n  .mdc-layout-grid__cell--span-1,\n.mdc-layout-grid__cell--span-1-phone {\n    width: calc(25% - 16px);\n    width: calc(25% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-1,\n.mdc-layout-grid__cell--span-1-phone {\n      width: auto;\n      grid-column-end: span 1;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-2,\n.mdc-layout-grid__cell--span-2-phone {\n    width: calc(50% - 16px);\n    width: calc(50% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-2,\n.mdc-layout-grid__cell--span-2-phone {\n      width: auto;\n      grid-column-end: span 2;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-3,\n.mdc-layout-grid__cell--span-3-phone {\n    width: calc(75% - 16px);\n    width: calc(75% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-3,\n.mdc-layout-grid__cell--span-3-phone {\n      width: auto;\n      grid-column-end: span 3;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-4,\n.mdc-layout-grid__cell--span-4-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-4,\n.mdc-layout-grid__cell--span-4-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-5,\n.mdc-layout-grid__cell--span-5-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-5,\n.mdc-layout-grid__cell--span-5-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-6,\n.mdc-layout-grid__cell--span-6-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-6,\n.mdc-layout-grid__cell--span-6-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-7,\n.mdc-layout-grid__cell--span-7-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-7,\n.mdc-layout-grid__cell--span-7-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-8,\n.mdc-layout-grid__cell--span-8-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-8,\n.mdc-layout-grid__cell--span-8-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-9,\n.mdc-layout-grid__cell--span-9-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-9,\n.mdc-layout-grid__cell--span-9-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-10,\n.mdc-layout-grid__cell--span-10-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-10,\n.mdc-layout-grid__cell--span-10-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-11,\n.mdc-layout-grid__cell--span-11-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-11,\n.mdc-layout-grid__cell--span-11-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n\n  .mdc-layout-grid__cell--span-12,\n.mdc-layout-grid__cell--span-12-phone {\n    width: calc(100% - 16px);\n    width: calc(100% - var(--mdc-layout-grid-gutter-phone, 16px));\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__cell--span-12,\n.mdc-layout-grid__cell--span-12-phone {\n      width: auto;\n      grid-column-end: span 4;\n    }\n  }\n}\n.mdc-layout-grid__cell--order-1 {\n  order: 1;\n}\n.mdc-layout-grid__cell--order-2 {\n  order: 2;\n}\n.mdc-layout-grid__cell--order-3 {\n  order: 3;\n}\n.mdc-layout-grid__cell--order-4 {\n  order: 4;\n}\n.mdc-layout-grid__cell--order-5 {\n  order: 5;\n}\n.mdc-layout-grid__cell--order-6 {\n  order: 6;\n}\n.mdc-layout-grid__cell--order-7 {\n  order: 7;\n}\n.mdc-layout-grid__cell--order-8 {\n  order: 8;\n}\n.mdc-layout-grid__cell--order-9 {\n  order: 9;\n}\n.mdc-layout-grid__cell--order-10 {\n  order: 10;\n}\n.mdc-layout-grid__cell--order-11 {\n  order: 11;\n}\n.mdc-layout-grid__cell--order-12 {\n  order: 12;\n}\n.mdc-layout-grid__cell--align-top {\n  align-self: flex-start;\n}\n@supports (display: grid) {\n  .mdc-layout-grid__cell--align-top {\n    align-self: start;\n  }\n}\n.mdc-layout-grid__cell--align-middle {\n  align-self: center;\n}\n.mdc-layout-grid__cell--align-bottom {\n  align-self: flex-end;\n}\n@supports (display: grid) {\n  .mdc-layout-grid__cell--align-bottom {\n    align-self: end;\n  }\n}\n\n@media (min-width: 840px) {\n  .mdc-layout-grid--fixed-column-width {\n    width: 1176px;\n    width: calc(\n    var(--mdc-layout-grid-column-width-desktop, 72px) * 12 +\n      var(--mdc-layout-grid-gutter-desktop, 24px) * 11 +\n      var(--mdc-layout-grid-margin-desktop, 24px) * 2\n  );\n  }\n}\n@media (min-width: 600px) and (max-width: 839px) {\n  .mdc-layout-grid--fixed-column-width {\n    width: 720px;\n    width: calc(\n    var(--mdc-layout-grid-column-width-tablet, 72px) * 8 +\n      var(--mdc-layout-grid-gutter-tablet, 16px) * 7 +\n      var(--mdc-layout-grid-margin-tablet, 16px) * 2\n  );\n  }\n}\n@media (max-width: 599px) {\n  .mdc-layout-grid--fixed-column-width {\n    width: 368px;\n    width: calc(\n    var(--mdc-layout-grid-column-width-phone, 72px) * 4 +\n      var(--mdc-layout-grid-gutter-phone, 16px) * 3 +\n      var(--mdc-layout-grid-margin-phone, 16px) * 2\n  );\n  }\n}\n\n.mdc-layout-grid--align-left {\n  margin-right: auto;\n  margin-left: 0;\n}\n\n.mdc-layout-grid--align-right {\n  margin-right: 0;\n  margin-left: auto;\n}", "// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:math';\n@use './variables';\n\n// returns the lower grid boundary or null if the smallest grid is selected\n@function breakpoint-min($size) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n  $min: map.get(variables.$breakpoints, $size);\n\n  @return if($min > 0, $min, null);\n}\n\n// returns the upper grid boundary or null if the largest grid is selected\n@function breakpoint-max($size) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n  $names: map.keys(variables.$columns);\n  $n: list.index($names, $size);\n  $prev: if($n > 1, list.nth($names, $n - 1), null);\n\n  @return if($prev, (breakpoint-min($prev) - 1px), null);\n}\n\n// Private mixins, meant for internal use.\n@mixin media-query_($size) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  $min: breakpoint-min($size);\n  $max: breakpoint-max($size);\n\n  @if $min == null and $max != null {\n    // Phone\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else if $min != null and $max != null {\n    // Tablet\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $min != null and $max == null {\n    // Desktop\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    // Fallback - no breakpoints defined\n    @content;\n  }\n}\n\n@mixin cell-span_($size, $span, $gutter) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  $percent: math.percentage(\n    math.div($span, map.get(variables.$columns, $size))\n  );\n\n  @if $percent > 100% {\n    $percent: 100%;\n  }\n\n  width: calc(#{$percent} - #{$gutter});\n  width: calc(#{$percent} - var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}));\n\n  @supports (display: grid) {\n    width: auto;\n    grid-column-end: span math.min($span, map.get(variables.$columns, $size));\n  }\n}\n\n// Public mixins, meant for developer usage.\n@mixin layout-grid($size, $margin, $max-width: null) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  box-sizing: border-box;\n  margin: 0 auto;\n  padding: $margin;\n  padding: var(--mdc-layout-grid-margin-#{$size}, #{$margin});\n\n  @if $max-width {\n    max-width: $max-width;\n  }\n}\n\n@mixin inner($size, $margin, $gutter) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  display: flex;\n  flex-flow: row wrap;\n  align-items: stretch;\n  margin: math.div(-$gutter, 2);\n  margin: calc(var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}) / 2 * -1);\n\n  @supports (display: grid) {\n    display: grid;\n    margin: 0;\n    grid-gap: $gutter;\n    grid-gap: var(--mdc-layout-grid-gutter-#{$size}, $gutter);\n    grid-template-columns: repeat(\n      map.get(variables.$columns, $size),\n      minmax(0, 1fr)\n    );\n  }\n}\n\n@mixin cell($size, $default-span, $gutter) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  @include cell-span_($size, $default-span, $gutter);\n\n  box-sizing: border-box;\n  margin: math.div($gutter, 2);\n  margin: calc(var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}) / 2);\n\n  @supports (display: grid) {\n    margin: 0;\n  }\n}\n\n@mixin cell-order($order) {\n  order: $order;\n}\n\n@mixin cell-align($position) {\n  @if $position == 'top' {\n    align-self: flex-start;\n\n    @supports (display: grid) {\n      align-self: start;\n    }\n  }\n\n  @if $position == 'middle' {\n    align-self: center;\n  }\n\n  @if $position == 'bottom' {\n    align-self: flex-end;\n\n    @supports (display: grid) {\n      align-self: end;\n    }\n  }\n\n  @if $position == 'stretch' {\n    align-self: stretch;\n  }\n}\n\n@mixin fixed-column-width($size, $margin, $gutter, $column-width) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  $columnCount: map.get(variables.$columns, $size);\n  $gutter-number: $columnCount - 1;\n  $margin-number: 2;\n\n  width: $column-width * $columnCount + $gutter * $gutter-number + $margin *\n    $margin-number;\n  width: calc(\n    var(--mdc-layout-grid-column-width-#{$size}, #{$column-width}) * #{$columnCount} +\n      var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}) * #{$gutter-number} +\n      var(--mdc-layout-grid-margin-#{$size}, #{$margin}) * #{$margin-number}\n  );\n}\n"], "sourceRoot": ""}