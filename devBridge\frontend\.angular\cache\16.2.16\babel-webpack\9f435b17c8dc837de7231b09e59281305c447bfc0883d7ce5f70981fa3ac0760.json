{"ast": null, "code": "import { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { getLocation } from '../language/location.mjs';\nimport { printLocation, printSourceLocation } from '../language/printLocation.mjs';\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5]\n    };\n  }\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nexport class GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n    const {\n      nodes,\n      source,\n      positions,\n      path,\n      originalError,\n      extensions\n    } = toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError = originalError !== null && originalError !== void 0 ? originalError : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined);\n    const nodeLocations = undefinedIfEmpty((_this$nodes = this.nodes) === null || _this$nodes === void 0 ? void 0 : _this$nodes.map(node => node.loc).filter(loc => loc != null)); // Compute locations in the source for the given nodes/positions.\n\n    this.source = source !== null && source !== void 0 ? source : nodeLocations === null || nodeLocations === void 0 ? void 0 : (_nodeLocations$ = nodeLocations[0]) === null || _nodeLocations$ === void 0 ? void 0 : _nodeLocations$.source;\n    this.positions = positions !== null && positions !== void 0 ? positions : nodeLocations === null || nodeLocations === void 0 ? void 0 : nodeLocations.map(loc => loc.start);\n    this.locations = positions && source ? positions.map(pos => getLocation(source, pos)) : nodeLocations === null || nodeLocations === void 0 ? void 0 : nodeLocations.map(loc => getLocation(loc.source, loc.start));\n    const originalExtensions = isObjectLike(originalError === null || originalError === void 0 ? void 0 : originalError.extensions) ? originalError === null || originalError === void 0 ? void 0 : originalError.extensions : undefined;\n    this.extensions = (_ref = extensions !== null && extensions !== void 0 ? extensions : originalExtensions) !== null && _ref !== void 0 ? _ref : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true\n      },\n      name: {\n        enumerable: false\n      },\n      nodes: {\n        enumerable: false\n      },\n      source: {\n        enumerable: false\n      },\n      positions: {\n        enumerable: false\n      },\n      originalError: {\n        enumerable: false\n      }\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (originalError !== null && originalError !== void 0 && originalError.stack) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n  toString() {\n    let output = this.message;\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + printLocation(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + printSourceLocation(this.source, location);\n      }\n    }\n    return output;\n  }\n  toJSON() {\n    const formattedError = {\n      message: this.message\n    };\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n    return formattedError;\n  }\n}\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nexport function printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nexport function formatError(error) {\n  return error.toJSON();\n}", "map": {"version": 3, "names": ["isObjectLike", "getLocation", "printLocation", "printSourceLocation", "toNormalizedOptions", "args", "firstArg", "nodes", "source", "positions", "path", "originalError", "extensions", "GraphQLError", "Error", "constructor", "message", "rawArgs", "_this$nodes", "_nodeLocations$", "_ref", "name", "undefined", "undefinedIfEmpty", "Array", "isArray", "nodeLocations", "map", "node", "loc", "filter", "start", "locations", "pos", "originalExtensions", "Object", "create", "defineProperties", "writable", "enumerable", "stack", "defineProperty", "value", "configurable", "captureStackTrace", "Symbol", "toStringTag", "toString", "output", "location", "toJSON", "formattedError", "keys", "length", "array", "printError", "error", "formatError"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/error/GraphQLError.mjs"], "sourcesContent": ["import { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { getLocation } from '../language/location.mjs';\nimport {\n  printLocation,\n  printSourceLocation,\n} from '../language/printLocation.mjs';\n\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5],\n    };\n  }\n\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nexport class GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n\n    const { nodes, source, positions, path, originalError, extensions } =\n      toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError =\n      originalError !== null && originalError !== void 0\n        ? originalError\n        : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(\n      Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined,\n    );\n    const nodeLocations = undefinedIfEmpty(\n      (_this$nodes = this.nodes) === null || _this$nodes === void 0\n        ? void 0\n        : _this$nodes.map((node) => node.loc).filter((loc) => loc != null),\n    ); // Compute locations in the source for the given nodes/positions.\n\n    this.source =\n      source !== null && source !== void 0\n        ? source\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : (_nodeLocations$ = nodeLocations[0]) === null ||\n          _nodeLocations$ === void 0\n        ? void 0\n        : _nodeLocations$.source;\n    this.positions =\n      positions !== null && positions !== void 0\n        ? positions\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => loc.start);\n    this.locations =\n      positions && source\n        ? positions.map((pos) => getLocation(source, pos))\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => getLocation(loc.source, loc.start));\n    const originalExtensions = isObjectLike(\n      originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions,\n    )\n      ? originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions\n      : undefined;\n    this.extensions =\n      (_ref =\n        extensions !== null && extensions !== void 0\n          ? extensions\n          : originalExtensions) !== null && _ref !== void 0\n        ? _ref\n        : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true,\n      },\n      name: {\n        enumerable: false,\n      },\n      nodes: {\n        enumerable: false,\n      },\n      source: {\n        enumerable: false,\n      },\n      positions: {\n        enumerable: false,\n      },\n      originalError: {\n        enumerable: false,\n      },\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (\n      originalError !== null &&\n      originalError !== void 0 &&\n      originalError.stack\n    ) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true,\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true,\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n\n  toString() {\n    let output = this.message;\n\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + printLocation(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + printSourceLocation(this.source, location);\n      }\n    }\n\n    return output;\n  }\n\n  toJSON() {\n    const formattedError = {\n      message: this.message,\n    };\n\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n\n    return formattedError;\n  }\n}\n\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nexport function printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nexport function formatError(error) {\n  return error.toJSON();\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SACEC,aAAa,EACbC,mBAAmB,QACd,+BAA+B;AAEtC,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACjC,MAAMC,QAAQ,GAAGD,IAAI,CAAC,CAAC,CAAC;EAExB,IAAIC,QAAQ,IAAI,IAAI,IAAI,MAAM,IAAIA,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,EAAE;IAClE,OAAO;MACLC,KAAK,EAAED,QAAQ;MACfE,MAAM,EAAEH,IAAI,CAAC,CAAC,CAAC;MACfI,SAAS,EAAEJ,IAAI,CAAC,CAAC,CAAC;MAClBK,IAAI,EAAEL,IAAI,CAAC,CAAC,CAAC;MACbM,aAAa,EAAEN,IAAI,CAAC,CAAC,CAAC;MACtBO,UAAU,EAAEP,IAAI,CAAC,CAAC;IACpB,CAAC;EACH;EAEA,OAAOC,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMO,YAAY,SAASC,KAAK,CAAC;EACtC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;EACEC,WAAWA,CAACC,OAAO,EAAE,GAAGC,OAAO,EAAE;IAC/B,IAAIC,WAAW,EAAEC,eAAe,EAAEC,IAAI;IAEtC,MAAM;MAAEb,KAAK;MAAEC,MAAM;MAAEC,SAAS;MAAEC,IAAI;MAAEC,aAAa;MAAEC;IAAW,CAAC,GACjER,mBAAmB,CAACa,OAAO,CAAC;IAC9B,KAAK,CAACD,OAAO,CAAC;IACd,IAAI,CAACK,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACX,IAAI,GAAGA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGY,SAAS;IAC/D,IAAI,CAACX,aAAa,GAChBA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAC9CA,aAAa,GACbW,SAAS,CAAC,CAAC;;IAEjB,IAAI,CAACf,KAAK,GAAGgB,gBAAgB,CAC3BC,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC,GAAGe,SACnD,CAAC;IACD,MAAMI,aAAa,GAAGH,gBAAgB,CACpC,CAACL,WAAW,GAAG,IAAI,CAACX,KAAK,MAAM,IAAI,IAAIW,WAAW,KAAK,KAAK,CAAC,GACzD,KAAK,CAAC,GACNA,WAAW,CAACS,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,GAAG,CAAC,CAACC,MAAM,CAAED,GAAG,IAAKA,GAAG,IAAI,IAAI,CACrE,CAAC,CAAC,CAAC;;IAEH,IAAI,CAACrB,MAAM,GACTA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAChCA,MAAM,GACNkB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAClD,KAAK,CAAC,GACN,CAACP,eAAe,GAAGO,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAC7CP,eAAe,KAAK,KAAK,CAAC,GAC1B,KAAK,CAAC,GACNA,eAAe,CAACX,MAAM;IAC5B,IAAI,CAACC,SAAS,GACZA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GACtCA,SAAS,GACTiB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAClD,KAAK,CAAC,GACNA,aAAa,CAACC,GAAG,CAAEE,GAAG,IAAKA,GAAG,CAACE,KAAK,CAAC;IAC3C,IAAI,CAACC,SAAS,GACZvB,SAAS,IAAID,MAAM,GACfC,SAAS,CAACkB,GAAG,CAAEM,GAAG,IAAKhC,WAAW,CAACO,MAAM,EAAEyB,GAAG,CAAC,CAAC,GAChDP,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAClD,KAAK,CAAC,GACNA,aAAa,CAACC,GAAG,CAAEE,GAAG,IAAK5B,WAAW,CAAC4B,GAAG,CAACrB,MAAM,EAAEqB,GAAG,CAACE,KAAK,CAAC,CAAC;IACpE,MAAMG,kBAAkB,GAAGlC,YAAY,CACrCW,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAC9C,KAAK,CAAC,GACNA,aAAa,CAACC,UACpB,CAAC,GACGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAChD,KAAK,CAAC,GACNA,aAAa,CAACC,UAAU,GAC1BU,SAAS;IACb,IAAI,CAACV,UAAU,GACb,CAACQ,IAAI,GACHR,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GACxCA,UAAU,GACVsB,kBAAkB,MAAM,IAAI,IAAId,IAAI,KAAK,KAAK,CAAC,GACjDA,IAAI,GACJe,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;;IAEAD,MAAM,CAACE,gBAAgB,CAAC,IAAI,EAAE;MAC5BrB,OAAO,EAAE;QACPsB,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MACd,CAAC;MACDlB,IAAI,EAAE;QACJkB,UAAU,EAAE;MACd,CAAC;MACDhC,KAAK,EAAE;QACLgC,UAAU,EAAE;MACd,CAAC;MACD/B,MAAM,EAAE;QACN+B,UAAU,EAAE;MACd,CAAC;MACD9B,SAAS,EAAE;QACT8B,UAAU,EAAE;MACd,CAAC;MACD5B,aAAa,EAAE;QACb4B,UAAU,EAAE;MACd;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ;IACA;;IAEA,IACE5B,aAAa,KAAK,IAAI,IACtBA,aAAa,KAAK,KAAK,CAAC,IACxBA,aAAa,CAAC6B,KAAK,EACnB;MACAL,MAAM,CAACM,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;QACnCC,KAAK,EAAE/B,aAAa,CAAC6B,KAAK;QAC1BF,QAAQ,EAAE,IAAI;QACdK,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI7B,KAAK,CAAC8B,iBAAiB,EAAE;MAClC9B,KAAK,CAAC8B,iBAAiB,CAAC,IAAI,EAAE/B,YAAY,CAAC;IAC7C,CAAC,MAAM;MACLsB,MAAM,CAACM,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;QACnCC,KAAK,EAAE5B,KAAK,CAAC,CAAC,CAAC0B,KAAK;QACpBF,QAAQ,EAAE,IAAI;QACdK,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;IACA;EACF;;EAEA,KAAKE,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,cAAc;EACvB;EAEAC,QAAQA,CAAA,EAAG;IACT,IAAIC,MAAM,GAAG,IAAI,CAAChC,OAAO;IAEzB,IAAI,IAAI,CAACT,KAAK,EAAE;MACd,KAAK,MAAMqB,IAAI,IAAI,IAAI,CAACrB,KAAK,EAAE;QAC7B,IAAIqB,IAAI,CAACC,GAAG,EAAE;UACZmB,MAAM,IAAI,MAAM,GAAG9C,aAAa,CAAC0B,IAAI,CAACC,GAAG,CAAC;QAC5C;MACF;IACF,CAAC,MAAM,IAAI,IAAI,CAACrB,MAAM,IAAI,IAAI,CAACwB,SAAS,EAAE;MACxC,KAAK,MAAMiB,QAAQ,IAAI,IAAI,CAACjB,SAAS,EAAE;QACrCgB,MAAM,IAAI,MAAM,GAAG7C,mBAAmB,CAAC,IAAI,CAACK,MAAM,EAAEyC,QAAQ,CAAC;MAC/D;IACF;IAEA,OAAOD,MAAM;EACf;EAEAE,MAAMA,CAAA,EAAG;IACP,MAAMC,cAAc,GAAG;MACrBnC,OAAO,EAAE,IAAI,CAACA;IAChB,CAAC;IAED,IAAI,IAAI,CAACgB,SAAS,IAAI,IAAI,EAAE;MAC1BmB,cAAc,CAACnB,SAAS,GAAG,IAAI,CAACA,SAAS;IAC3C;IAEA,IAAI,IAAI,CAACtB,IAAI,IAAI,IAAI,EAAE;MACrByC,cAAc,CAACzC,IAAI,GAAG,IAAI,CAACA,IAAI;IACjC;IAEA,IAAI,IAAI,CAACE,UAAU,IAAI,IAAI,IAAIuB,MAAM,CAACiB,IAAI,CAAC,IAAI,CAACxC,UAAU,CAAC,CAACyC,MAAM,GAAG,CAAC,EAAE;MACtEF,cAAc,CAACvC,UAAU,GAAG,IAAI,CAACA,UAAU;IAC7C;IAEA,OAAOuC,cAAc;EACvB;AACF;AAEA,SAAS5B,gBAAgBA,CAAC+B,KAAK,EAAE;EAC/B,OAAOA,KAAK,KAAKhC,SAAS,IAAIgC,KAAK,CAACD,MAAM,KAAK,CAAC,GAAG/B,SAAS,GAAGgC,KAAK;AACtE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOA,KAAK,CAACT,QAAQ,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASU,WAAWA,CAACD,KAAK,EAAE;EACjC,OAAOA,KAAK,CAACN,MAAM,CAAC,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}