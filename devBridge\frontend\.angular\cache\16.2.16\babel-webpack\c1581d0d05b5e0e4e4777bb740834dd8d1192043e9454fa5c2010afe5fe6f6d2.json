{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { naturalCompare } from '../jsutils/naturalCompare.mjs';\nimport { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLUnionType, isEnumType, isInputObjectType, isInterfaceType, isListType, isNonNullType, isObjectType, isScalarType, isUnionType } from '../type/definition.mjs';\nimport { GraphQLDirective } from '../type/directives.mjs';\nimport { isIntrospectionType } from '../type/introspection.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\n/**\n * Sort GraphQLSchema.\n *\n * This function returns a sorted copy of the given GraphQLSchema.\n */\n\nexport function lexicographicSortSchema(schema) {\n  const schemaConfig = schema.toConfig();\n  const typeMap = keyValMap(sortByName(schemaConfig.types), type => type.name, sortNamedType);\n  return new GraphQLSchema({\n    ...schemaConfig,\n    types: Object.values(typeMap),\n    directives: sortByName(schemaConfig.directives).map(sortDirective),\n    query: replaceMaybeType(schemaConfig.query),\n    mutation: replaceMaybeType(schemaConfig.mutation),\n    subscription: replaceMaybeType(schemaConfig.subscription)\n  });\n  function replaceType(type) {\n    if (isListType(type)) {\n      // @ts-expect-error\n      return new GraphQLList(replaceType(type.ofType));\n    } else if (isNonNullType(type)) {\n      // @ts-expect-error\n      return new GraphQLNonNull(replaceType(type.ofType));\n    } // @ts-expect-error FIXME: TS Conversion\n\n    return replaceNamedType(type);\n  }\n  function replaceNamedType(type) {\n    return typeMap[type.name];\n  }\n  function replaceMaybeType(maybeType) {\n    return maybeType && replaceNamedType(maybeType);\n  }\n  function sortDirective(directive) {\n    const config = directive.toConfig();\n    return new GraphQLDirective({\n      ...config,\n      locations: sortBy(config.locations, x => x),\n      args: sortArgs(config.args)\n    });\n  }\n  function sortArgs(args) {\n    return sortObjMap(args, arg => ({\n      ...arg,\n      type: replaceType(arg.type)\n    }));\n  }\n  function sortFields(fieldsMap) {\n    return sortObjMap(fieldsMap, field => ({\n      ...field,\n      type: replaceType(field.type),\n      args: field.args && sortArgs(field.args)\n    }));\n  }\n  function sortInputFields(fieldsMap) {\n    return sortObjMap(fieldsMap, field => ({\n      ...field,\n      type: replaceType(field.type)\n    }));\n  }\n  function sortTypes(array) {\n    return sortByName(array).map(replaceNamedType);\n  }\n  function sortNamedType(type) {\n    if (isScalarType(type) || isIntrospectionType(type)) {\n      return type;\n    }\n    if (isObjectType(type)) {\n      const config = type.toConfig();\n      return new GraphQLObjectType({\n        ...config,\n        interfaces: () => sortTypes(config.interfaces),\n        fields: () => sortFields(config.fields)\n      });\n    }\n    if (isInterfaceType(type)) {\n      const config = type.toConfig();\n      return new GraphQLInterfaceType({\n        ...config,\n        interfaces: () => sortTypes(config.interfaces),\n        fields: () => sortFields(config.fields)\n      });\n    }\n    if (isUnionType(type)) {\n      const config = type.toConfig();\n      return new GraphQLUnionType({\n        ...config,\n        types: () => sortTypes(config.types)\n      });\n    }\n    if (isEnumType(type)) {\n      const config = type.toConfig();\n      return new GraphQLEnumType({\n        ...config,\n        values: sortObjMap(config.values, value => value)\n      });\n    }\n    if (isInputObjectType(type)) {\n      const config = type.toConfig();\n      return new GraphQLInputObjectType({\n        ...config,\n        fields: () => sortInputFields(config.fields)\n      });\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible types have been considered.\n\n    false || invariant(false, 'Unexpected type: ' + inspect(type));\n  }\n}\nfunction sortObjMap(map, sortValueFn) {\n  const sortedMap = Object.create(null);\n  for (const key of Object.keys(map).sort(naturalCompare)) {\n    sortedMap[key] = sortValueFn(map[key]);\n  }\n  return sortedMap;\n}\nfunction sortByName(array) {\n  return sortBy(array, obj => obj.name);\n}\nfunction sortBy(array, mapToKey) {\n  return array.slice().sort((obj1, obj2) => {\n    const key1 = mapToKey(obj1);\n    const key2 = mapToKey(obj2);\n    return naturalCompare(key1, key2);\n  });\n}", "map": {"version": 3, "names": ["inspect", "invariant", "keyValMap", "naturalCompare", "GraphQLEnumType", "GraphQLInputObjectType", "GraphQLInterfaceType", "GraphQLList", "GraphQLNonNull", "GraphQLObjectType", "GraphQLUnionType", "isEnumType", "isInputObjectType", "isInterfaceType", "isListType", "isNonNullType", "isObjectType", "isScalarType", "isUnionType", "GraphQLDirective", "isIntrospectionType", "GraphQLSchema", "lexicographicSortSchema", "schema", "schemaConfig", "toConfig", "typeMap", "sortByName", "types", "type", "name", "sortNamedType", "Object", "values", "directives", "map", "sortDirective", "query", "replaceMaybeType", "mutation", "subscription", "replaceType", "ofType", "replaceNamedType", "maybeType", "directive", "config", "locations", "sortBy", "x", "args", "sortArgs", "sortObjMap", "arg", "sortFields", "fieldsMap", "field", "sortInputFields", "sortTypes", "array", "interfaces", "fields", "value", "sortValueFn", "sortedMap", "create", "key", "keys", "sort", "obj", "mapToKey", "slice", "obj1", "obj2", "key1", "key2"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/utilities/lexicographicSortSchema.mjs"], "sourcesContent": ["import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { naturalCompare } from '../jsutils/naturalCompare.mjs';\nimport {\n  GraphQLEnumType,\n  GraphQLInputObjectType,\n  GraphQLInterfaceType,\n  GraphQLList,\n  GraphQLNonNull,\n  GraphQLObjectType,\n  GraphQLUnionType,\n  isEnumType,\n  isInputObjectType,\n  isInterfaceType,\n  isListType,\n  isNonNullType,\n  isObjectType,\n  isScalarType,\n  isUnionType,\n} from '../type/definition.mjs';\nimport { GraphQLDirective } from '../type/directives.mjs';\nimport { isIntrospectionType } from '../type/introspection.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\n/**\n * Sort GraphQLSchema.\n *\n * This function returns a sorted copy of the given GraphQLSchema.\n */\n\nexport function lexicographicSortSchema(schema) {\n  const schemaConfig = schema.toConfig();\n  const typeMap = keyValMap(\n    sortByName(schemaConfig.types),\n    (type) => type.name,\n    sortNamedType,\n  );\n  return new GraphQLSchema({\n    ...schemaConfig,\n    types: Object.values(typeMap),\n    directives: sortByName(schemaConfig.directives).map(sortDirective),\n    query: replaceMaybeType(schemaConfig.query),\n    mutation: replaceMaybeType(schemaConfig.mutation),\n    subscription: replaceMaybeType(schemaConfig.subscription),\n  });\n\n  function replaceType(type) {\n    if (isListType(type)) {\n      // @ts-expect-error\n      return new GraphQLList(replaceType(type.ofType));\n    } else if (isNonNullType(type)) {\n      // @ts-expect-error\n      return new GraphQLNonNull(replaceType(type.ofType));\n    } // @ts-expect-error FIXME: TS Conversion\n\n    return replaceNamedType(type);\n  }\n\n  function replaceNamedType(type) {\n    return typeMap[type.name];\n  }\n\n  function replaceMaybeType(maybeType) {\n    return maybeType && replaceNamedType(maybeType);\n  }\n\n  function sortDirective(directive) {\n    const config = directive.toConfig();\n    return new GraphQLDirective({\n      ...config,\n      locations: sortBy(config.locations, (x) => x),\n      args: sortArgs(config.args),\n    });\n  }\n\n  function sortArgs(args) {\n    return sortObjMap(args, (arg) => ({ ...arg, type: replaceType(arg.type) }));\n  }\n\n  function sortFields(fieldsMap) {\n    return sortObjMap(fieldsMap, (field) => ({\n      ...field,\n      type: replaceType(field.type),\n      args: field.args && sortArgs(field.args),\n    }));\n  }\n\n  function sortInputFields(fieldsMap) {\n    return sortObjMap(fieldsMap, (field) => ({\n      ...field,\n      type: replaceType(field.type),\n    }));\n  }\n\n  function sortTypes(array) {\n    return sortByName(array).map(replaceNamedType);\n  }\n\n  function sortNamedType(type) {\n    if (isScalarType(type) || isIntrospectionType(type)) {\n      return type;\n    }\n\n    if (isObjectType(type)) {\n      const config = type.toConfig();\n      return new GraphQLObjectType({\n        ...config,\n        interfaces: () => sortTypes(config.interfaces),\n        fields: () => sortFields(config.fields),\n      });\n    }\n\n    if (isInterfaceType(type)) {\n      const config = type.toConfig();\n      return new GraphQLInterfaceType({\n        ...config,\n        interfaces: () => sortTypes(config.interfaces),\n        fields: () => sortFields(config.fields),\n      });\n    }\n\n    if (isUnionType(type)) {\n      const config = type.toConfig();\n      return new GraphQLUnionType({\n        ...config,\n        types: () => sortTypes(config.types),\n      });\n    }\n\n    if (isEnumType(type)) {\n      const config = type.toConfig();\n      return new GraphQLEnumType({\n        ...config,\n        values: sortObjMap(config.values, (value) => value),\n      });\n    }\n\n    if (isInputObjectType(type)) {\n      const config = type.toConfig();\n      return new GraphQLInputObjectType({\n        ...config,\n        fields: () => sortInputFields(config.fields),\n      });\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible types have been considered.\n\n    false || invariant(false, 'Unexpected type: ' + inspect(type));\n  }\n}\n\nfunction sortObjMap(map, sortValueFn) {\n  const sortedMap = Object.create(null);\n\n  for (const key of Object.keys(map).sort(naturalCompare)) {\n    sortedMap[key] = sortValueFn(map[key]);\n  }\n\n  return sortedMap;\n}\n\nfunction sortByName(array) {\n  return sortBy(array, (obj) => obj.name);\n}\n\nfunction sortBy(array, mapToKey) {\n  return array.slice().sort((obj1, obj2) => {\n    const key1 = mapToKey(obj1);\n    const key2 = mapToKey(obj2);\n    return naturalCompare(key1, key2);\n  });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SACEC,eAAe,EACfC,sBAAsB,EACtBC,oBAAoB,EACpBC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EACjBC,gBAAgB,EAChBC,UAAU,EACVC,iBAAiB,EACjBC,eAAe,EACfC,UAAU,EACVC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,WAAW,QACN,wBAAwB;AAC/B,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;AAClD;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,uBAAuBA,CAACC,MAAM,EAAE;EAC9C,MAAMC,YAAY,GAAGD,MAAM,CAACE,QAAQ,CAAC,CAAC;EACtC,MAAMC,OAAO,GAAGxB,SAAS,CACvByB,UAAU,CAACH,YAAY,CAACI,KAAK,CAAC,EAC7BC,IAAI,IAAKA,IAAI,CAACC,IAAI,EACnBC,aACF,CAAC;EACD,OAAO,IAAIV,aAAa,CAAC;IACvB,GAAGG,YAAY;IACfI,KAAK,EAAEI,MAAM,CAACC,MAAM,CAACP,OAAO,CAAC;IAC7BQ,UAAU,EAAEP,UAAU,CAACH,YAAY,CAACU,UAAU,CAAC,CAACC,GAAG,CAACC,aAAa,CAAC;IAClEC,KAAK,EAAEC,gBAAgB,CAACd,YAAY,CAACa,KAAK,CAAC;IAC3CE,QAAQ,EAAED,gBAAgB,CAACd,YAAY,CAACe,QAAQ,CAAC;IACjDC,YAAY,EAAEF,gBAAgB,CAACd,YAAY,CAACgB,YAAY;EAC1D,CAAC,CAAC;EAEF,SAASC,WAAWA,CAACZ,IAAI,EAAE;IACzB,IAAIf,UAAU,CAACe,IAAI,CAAC,EAAE;MACpB;MACA,OAAO,IAAItB,WAAW,CAACkC,WAAW,CAACZ,IAAI,CAACa,MAAM,CAAC,CAAC;IAClD,CAAC,MAAM,IAAI3B,aAAa,CAACc,IAAI,CAAC,EAAE;MAC9B;MACA,OAAO,IAAIrB,cAAc,CAACiC,WAAW,CAACZ,IAAI,CAACa,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC;;IAEF,OAAOC,gBAAgB,CAACd,IAAI,CAAC;EAC/B;EAEA,SAASc,gBAAgBA,CAACd,IAAI,EAAE;IAC9B,OAAOH,OAAO,CAACG,IAAI,CAACC,IAAI,CAAC;EAC3B;EAEA,SAASQ,gBAAgBA,CAACM,SAAS,EAAE;IACnC,OAAOA,SAAS,IAAID,gBAAgB,CAACC,SAAS,CAAC;EACjD;EAEA,SAASR,aAAaA,CAACS,SAAS,EAAE;IAChC,MAAMC,MAAM,GAAGD,SAAS,CAACpB,QAAQ,CAAC,CAAC;IACnC,OAAO,IAAIN,gBAAgB,CAAC;MAC1B,GAAG2B,MAAM;MACTC,SAAS,EAAEC,MAAM,CAACF,MAAM,CAACC,SAAS,EAAGE,CAAC,IAAKA,CAAC,CAAC;MAC7CC,IAAI,EAAEC,QAAQ,CAACL,MAAM,CAACI,IAAI;IAC5B,CAAC,CAAC;EACJ;EAEA,SAASC,QAAQA,CAACD,IAAI,EAAE;IACtB,OAAOE,UAAU,CAACF,IAAI,EAAGG,GAAG,KAAM;MAAE,GAAGA,GAAG;MAAExB,IAAI,EAAEY,WAAW,CAACY,GAAG,CAACxB,IAAI;IAAE,CAAC,CAAC,CAAC;EAC7E;EAEA,SAASyB,UAAUA,CAACC,SAAS,EAAE;IAC7B,OAAOH,UAAU,CAACG,SAAS,EAAGC,KAAK,KAAM;MACvC,GAAGA,KAAK;MACR3B,IAAI,EAAEY,WAAW,CAACe,KAAK,CAAC3B,IAAI,CAAC;MAC7BqB,IAAI,EAAEM,KAAK,CAACN,IAAI,IAAIC,QAAQ,CAACK,KAAK,CAACN,IAAI;IACzC,CAAC,CAAC,CAAC;EACL;EAEA,SAASO,eAAeA,CAACF,SAAS,EAAE;IAClC,OAAOH,UAAU,CAACG,SAAS,EAAGC,KAAK,KAAM;MACvC,GAAGA,KAAK;MACR3B,IAAI,EAAEY,WAAW,CAACe,KAAK,CAAC3B,IAAI;IAC9B,CAAC,CAAC,CAAC;EACL;EAEA,SAAS6B,SAASA,CAACC,KAAK,EAAE;IACxB,OAAOhC,UAAU,CAACgC,KAAK,CAAC,CAACxB,GAAG,CAACQ,gBAAgB,CAAC;EAChD;EAEA,SAASZ,aAAaA,CAACF,IAAI,EAAE;IAC3B,IAAIZ,YAAY,CAACY,IAAI,CAAC,IAAIT,mBAAmB,CAACS,IAAI,CAAC,EAAE;MACnD,OAAOA,IAAI;IACb;IAEA,IAAIb,YAAY,CAACa,IAAI,CAAC,EAAE;MACtB,MAAMiB,MAAM,GAAGjB,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC9B,OAAO,IAAIhB,iBAAiB,CAAC;QAC3B,GAAGqC,MAAM;QACTc,UAAU,EAAEA,CAAA,KAAMF,SAAS,CAACZ,MAAM,CAACc,UAAU,CAAC;QAC9CC,MAAM,EAAEA,CAAA,KAAMP,UAAU,CAACR,MAAM,CAACe,MAAM;MACxC,CAAC,CAAC;IACJ;IAEA,IAAIhD,eAAe,CAACgB,IAAI,CAAC,EAAE;MACzB,MAAMiB,MAAM,GAAGjB,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC9B,OAAO,IAAInB,oBAAoB,CAAC;QAC9B,GAAGwC,MAAM;QACTc,UAAU,EAAEA,CAAA,KAAMF,SAAS,CAACZ,MAAM,CAACc,UAAU,CAAC;QAC9CC,MAAM,EAAEA,CAAA,KAAMP,UAAU,CAACR,MAAM,CAACe,MAAM;MACxC,CAAC,CAAC;IACJ;IAEA,IAAI3C,WAAW,CAACW,IAAI,CAAC,EAAE;MACrB,MAAMiB,MAAM,GAAGjB,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC9B,OAAO,IAAIf,gBAAgB,CAAC;QAC1B,GAAGoC,MAAM;QACTlB,KAAK,EAAEA,CAAA,KAAM8B,SAAS,CAACZ,MAAM,CAAClB,KAAK;MACrC,CAAC,CAAC;IACJ;IAEA,IAAIjB,UAAU,CAACkB,IAAI,CAAC,EAAE;MACpB,MAAMiB,MAAM,GAAGjB,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC9B,OAAO,IAAIrB,eAAe,CAAC;QACzB,GAAG0C,MAAM;QACTb,MAAM,EAAEmB,UAAU,CAACN,MAAM,CAACb,MAAM,EAAG6B,KAAK,IAAKA,KAAK;MACpD,CAAC,CAAC;IACJ;IAEA,IAAIlD,iBAAiB,CAACiB,IAAI,CAAC,EAAE;MAC3B,MAAMiB,MAAM,GAAGjB,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC9B,OAAO,IAAIpB,sBAAsB,CAAC;QAChC,GAAGyC,MAAM;QACTe,MAAM,EAAEA,CAAA,KAAMJ,eAAe,CAACX,MAAM,CAACe,MAAM;MAC7C,CAAC,CAAC;IACJ;IACA;IACA;;IAEA,KAAK,IAAI5D,SAAS,CAAC,KAAK,EAAE,mBAAmB,GAAGD,OAAO,CAAC6B,IAAI,CAAC,CAAC;EAChE;AACF;AAEA,SAASuB,UAAUA,CAACjB,GAAG,EAAE4B,WAAW,EAAE;EACpC,MAAMC,SAAS,GAAGhC,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC;EAErC,KAAK,MAAMC,GAAG,IAAIlC,MAAM,CAACmC,IAAI,CAAChC,GAAG,CAAC,CAACiC,IAAI,CAACjE,cAAc,CAAC,EAAE;IACvD6D,SAAS,CAACE,GAAG,CAAC,GAAGH,WAAW,CAAC5B,GAAG,CAAC+B,GAAG,CAAC,CAAC;EACxC;EAEA,OAAOF,SAAS;AAClB;AAEA,SAASrC,UAAUA,CAACgC,KAAK,EAAE;EACzB,OAAOX,MAAM,CAACW,KAAK,EAAGU,GAAG,IAAKA,GAAG,CAACvC,IAAI,CAAC;AACzC;AAEA,SAASkB,MAAMA,CAACW,KAAK,EAAEW,QAAQ,EAAE;EAC/B,OAAOX,KAAK,CAACY,KAAK,CAAC,CAAC,CAACH,IAAI,CAAC,CAACI,IAAI,EAAEC,IAAI,KAAK;IACxC,MAAMC,IAAI,GAAGJ,QAAQ,CAACE,IAAI,CAAC;IAC3B,MAAMG,IAAI,GAAGL,QAAQ,CAACG,IAAI,CAAC;IAC3B,OAAOtE,cAAc,CAACuE,IAAI,EAAEC,IAAI,CAAC;EACnC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}