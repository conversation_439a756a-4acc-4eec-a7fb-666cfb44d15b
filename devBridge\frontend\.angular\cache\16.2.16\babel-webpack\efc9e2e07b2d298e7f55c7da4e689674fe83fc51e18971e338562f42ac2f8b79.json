{"ast": null, "code": "import { printBlockString } from '../language/blockString.mjs';\nimport { isPunctuatorToken<PERSON><PERSON>, <PERSON><PERSON> } from '../language/lexer.mjs';\nimport { isSource, Source } from '../language/source.mjs';\nimport { TokenKind } from '../language/tokenKind.mjs';\n/**\n * Strips characters that are not significant to the validity or execution\n * of a GraphQL document:\n *   - UnicodeBOM\n *   - WhiteSpace\n *   - LineTerminator\n *   - Comment\n *   - Comma\n *   - BlockString indentation\n *\n * Note: It is required to have a delimiter character between neighboring\n * non-punctuator tokens and this function always uses single space as delimiter.\n *\n * It is guaranteed that both input and output documents if parsed would result\n * in the exact same AST except for nodes location.\n *\n * Warning: It is guaranteed that this function will always produce stable results.\n * However, it's not guaranteed that it will stay the same between different\n * releases due to bugfixes or changes in the GraphQL specification.\n *\n * Query example:\n *\n * ```graphql\n * query SomeQuery($foo: String!, $bar: String) {\n *   someField(foo: $foo, bar: $bar) {\n *     a\n *     b {\n *       c\n *       d\n *     }\n *   }\n * }\n * ```\n *\n * Becomes:\n *\n * ```graphql\n * query SomeQuery($foo:String!$bar:String){someField(foo:$foo bar:$bar){a b{c d}}}\n * ```\n *\n * SDL example:\n *\n * ```graphql\n * \"\"\"\n * Type description\n * \"\"\"\n * type Foo {\n *   \"\"\"\n *   Field description\n *   \"\"\"\n *   bar: String\n * }\n * ```\n *\n * Becomes:\n *\n * ```graphql\n * \"\"\"Type description\"\"\" type Foo{\"\"\"Field description\"\"\" bar:String}\n * ```\n */\n\nexport function stripIgnoredCharacters(source) {\n  const sourceObj = isSource(source) ? source : new Source(source);\n  const body = sourceObj.body;\n  const lexer = new Lexer(sourceObj);\n  let strippedBody = '';\n  let wasLastAddedTokenNonPunctuator = false;\n  while (lexer.advance().kind !== TokenKind.EOF) {\n    const currentToken = lexer.token;\n    const tokenKind = currentToken.kind;\n    /**\n     * Every two non-punctuator tokens should have space between them.\n     * Also prevent case of non-punctuator token following by spread resulting\n     * in invalid token (e.g. `1...` is invalid Float token).\n     */\n\n    const isNonPunctuator = !isPunctuatorTokenKind(currentToken.kind);\n    if (wasLastAddedTokenNonPunctuator) {\n      if (isNonPunctuator || currentToken.kind === TokenKind.SPREAD) {\n        strippedBody += ' ';\n      }\n    }\n    const tokenBody = body.slice(currentToken.start, currentToken.end);\n    if (tokenKind === TokenKind.BLOCK_STRING) {\n      strippedBody += printBlockString(currentToken.value, {\n        minimize: true\n      });\n    } else {\n      strippedBody += tokenBody;\n    }\n    wasLastAddedTokenNonPunctuator = isNonPunctuator;\n  }\n  return strippedBody;\n}", "map": {"version": 3, "names": ["printBlockString", "isPunctuatorTokenKind", "<PERSON><PERSON>", "isSource", "Source", "TokenKind", "stripIgnoredCharacters", "source", "sourceObj", "body", "lexer", "strippedBody", "wasLastAddedTokenNonPunctuator", "advance", "kind", "EOF", "currentToken", "token", "tokenKind", "isNonPunctuator", "SPREAD", "tokenBody", "slice", "start", "end", "BLOCK_STRING", "value", "minimize"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/utilities/stripIgnoredCharacters.mjs"], "sourcesContent": ["import { printBlockString } from '../language/blockString.mjs';\nimport { isPunctuatorToken<PERSON><PERSON>, <PERSON><PERSON> } from '../language/lexer.mjs';\nimport { isSource, Source } from '../language/source.mjs';\nimport { TokenKind } from '../language/tokenKind.mjs';\n/**\n * Strips characters that are not significant to the validity or execution\n * of a GraphQL document:\n *   - UnicodeBOM\n *   - WhiteSpace\n *   - LineTerminator\n *   - Comment\n *   - Comma\n *   - BlockString indentation\n *\n * Note: It is required to have a delimiter character between neighboring\n * non-punctuator tokens and this function always uses single space as delimiter.\n *\n * It is guaranteed that both input and output documents if parsed would result\n * in the exact same AST except for nodes location.\n *\n * Warning: It is guaranteed that this function will always produce stable results.\n * However, it's not guaranteed that it will stay the same between different\n * releases due to bugfixes or changes in the GraphQL specification.\n *\n * Query example:\n *\n * ```graphql\n * query SomeQuery($foo: String!, $bar: String) {\n *   someField(foo: $foo, bar: $bar) {\n *     a\n *     b {\n *       c\n *       d\n *     }\n *   }\n * }\n * ```\n *\n * Becomes:\n *\n * ```graphql\n * query SomeQuery($foo:String!$bar:String){someField(foo:$foo bar:$bar){a b{c d}}}\n * ```\n *\n * SDL example:\n *\n * ```graphql\n * \"\"\"\n * Type description\n * \"\"\"\n * type Foo {\n *   \"\"\"\n *   Field description\n *   \"\"\"\n *   bar: String\n * }\n * ```\n *\n * Becomes:\n *\n * ```graphql\n * \"\"\"Type description\"\"\" type Foo{\"\"\"Field description\"\"\" bar:String}\n * ```\n */\n\nexport function stripIgnoredCharacters(source) {\n  const sourceObj = isSource(source) ? source : new Source(source);\n  const body = sourceObj.body;\n  const lexer = new Lexer(sourceObj);\n  let strippedBody = '';\n  let wasLastAddedTokenNonPunctuator = false;\n\n  while (lexer.advance().kind !== TokenKind.EOF) {\n    const currentToken = lexer.token;\n    const tokenKind = currentToken.kind;\n    /**\n     * Every two non-punctuator tokens should have space between them.\n     * Also prevent case of non-punctuator token following by spread resulting\n     * in invalid token (e.g. `1...` is invalid Float token).\n     */\n\n    const isNonPunctuator = !isPunctuatorTokenKind(currentToken.kind);\n\n    if (wasLastAddedTokenNonPunctuator) {\n      if (isNonPunctuator || currentToken.kind === TokenKind.SPREAD) {\n        strippedBody += ' ';\n      }\n    }\n\n    const tokenBody = body.slice(currentToken.start, currentToken.end);\n\n    if (tokenKind === TokenKind.BLOCK_STRING) {\n      strippedBody += printBlockString(currentToken.value, {\n        minimize: true,\n      });\n    } else {\n      strippedBody += tokenBody;\n    }\n\n    wasLastAddedTokenNonPunctuator = isNonPunctuator;\n  }\n\n  return strippedBody;\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,QAAQ,EAAEC,MAAM,QAAQ,wBAAwB;AACzD,SAASC,SAAS,QAAQ,2BAA2B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,MAAMC,SAAS,GAAGL,QAAQ,CAACI,MAAM,CAAC,GAAGA,MAAM,GAAG,IAAIH,MAAM,CAACG,MAAM,CAAC;EAChE,MAAME,IAAI,GAAGD,SAAS,CAACC,IAAI;EAC3B,MAAMC,KAAK,GAAG,IAAIR,KAAK,CAACM,SAAS,CAAC;EAClC,IAAIG,YAAY,GAAG,EAAE;EACrB,IAAIC,8BAA8B,GAAG,KAAK;EAE1C,OAAOF,KAAK,CAACG,OAAO,CAAC,CAAC,CAACC,IAAI,KAAKT,SAAS,CAACU,GAAG,EAAE;IAC7C,MAAMC,YAAY,GAAGN,KAAK,CAACO,KAAK;IAChC,MAAMC,SAAS,GAAGF,YAAY,CAACF,IAAI;IACnC;AACJ;AACA;AACA;AACA;;IAEI,MAAMK,eAAe,GAAG,CAAClB,qBAAqB,CAACe,YAAY,CAACF,IAAI,CAAC;IAEjE,IAAIF,8BAA8B,EAAE;MAClC,IAAIO,eAAe,IAAIH,YAAY,CAACF,IAAI,KAAKT,SAAS,CAACe,MAAM,EAAE;QAC7DT,YAAY,IAAI,GAAG;MACrB;IACF;IAEA,MAAMU,SAAS,GAAGZ,IAAI,CAACa,KAAK,CAACN,YAAY,CAACO,KAAK,EAAEP,YAAY,CAACQ,GAAG,CAAC;IAElE,IAAIN,SAAS,KAAKb,SAAS,CAACoB,YAAY,EAAE;MACxCd,YAAY,IAAIX,gBAAgB,CAACgB,YAAY,CAACU,KAAK,EAAE;QACnDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLhB,YAAY,IAAIU,SAAS;IAC3B;IAEAT,8BAA8B,GAAGO,eAAe;EAClD;EAEA,OAAOR,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}