"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AngularWebpackLoaderPath = exports.AngularWebpackPlugin = exports.default = void 0;
var loader_1 = require("./loader");
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return loader_1.angularWebpackLoader; } });
var plugin_1 = require("./plugin");
Object.defineProperty(exports, "AngularWebpackPlugin", { enumerable: true, get: function () { return plugin_1.AngularWebpackPlugin; } });
exports.AngularWebpackLoaderPath = __filename;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9uZ3Rvb2xzL3dlYnBhY2svc3JjL2l2eS9pbmRleC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7QUFFSCxtQ0FBMkQ7QUFBbEQsaUdBQUEsb0JBQW9CLE9BQVc7QUFDeEMsbUNBQTZFO0FBQXZDLDhHQUFBLG9CQUFvQixPQUFBO0FBRTdDLFFBQUEsd0JBQXdCLEdBQUcsVUFBVSxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmV4cG9ydCB7IGFuZ3VsYXJXZWJwYWNrTG9hZGVyIGFzIGRlZmF1bHQgfSBmcm9tICcuL2xvYWRlcic7XG5leHBvcnQgeyBBbmd1bGFyV2VicGFja1BsdWdpbk9wdGlvbnMsIEFuZ3VsYXJXZWJwYWNrUGx1Z2luIH0gZnJvbSAnLi9wbHVnaW4nO1xuXG5leHBvcnQgY29uc3QgQW5ndWxhcldlYnBhY2tMb2FkZXJQYXRoID0gX19maWxlbmFtZTtcbiJdfQ==