{"version": 3, "sources": ["webpack:///./packages/mdc-layout-grid/mdc-layout-grid.scss", "webpack:///./packages/mdc-layout-grid/_mixins.scss"], "names": [], "mappings": ";;;;;;;AA4BA,MAEI,uCAIA,uCAIA,6CARA,sCAIA,sCAIA,4CARA,qCAIA,qCAIA,2CC8BA,yBDtBJ,iBC2DE,sBACA,cACA,YD1Da,CC2Db,qDA7CE,+CDjBJ,iBC2DE,sBACA,cACA,YD1Da,CC2Db,oDAlDE,yBDZJ,iBC2DE,sBACA,cACA,YD1Da,CC2Db,mDAxCE,yBDZJ,wBCgEE,aACA,mBACA,oBACA,aACA,kEAEA,yBDtEF,wBCuEI,aACA,SACA,aDrEW,CCsEX,qDACA,mDApEA,+CDPJ,wBCgEE,aACA,mBACA,oBACA,YACA,iEAEA,yBDtEF,wBCuEI,aACA,SACA,aDrEW,CCsEX,oDACA,kDAzEA,yBDFJ,wBCgEE,aACA,mBACA,oBACA,YACA,gEAEA,yBDtEF,wBCuEI,aACA,SACA,aDrEW,CCsEX,mDACA,kDA/DA,yBDDJ,uBCuBE,kCACA,yEAsDA,sBACA,YACA,2DAtDA,yBD1BF,uBC2BI,WACA,wBAsDF,yBDlFF,uBCmFI,UDvEa,sECWf,iCACA,wEAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,kCACA,yEAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,uBACA,8DAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,kCACA,yEAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,kCACA,yEAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,uBACA,8DAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,kCACA,yEAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,kCACA,yEAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,uBACA,8DAEA,yBDde,sECeb,WACA,wBDhBa,wECWf,kCACA,yEAEA,yBDde,wECeb,WACA,yBDhBa,wECWf,kCACA,yEAEA,yBDde,wECeb,WACA,yBDhBa,wECWf,wBACA,+DAEA,yBDde,wECeb,WACA,0BAhCA,+CDIJ,uBCuBE,uBACA,6DAsDA,sBACA,WACA,0DAtDA,yBD1BF,uBC2BI,WACA,wBAsDF,yBDlFF,uBCmFI,UDvEa,qECWf,yBACA,+DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,uBACA,6DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,yBACA,+DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,uBACA,6DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,yBACA,+DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,uBACA,6DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,yBACA,+DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,wBACA,8DAEA,yBDde,qECeb,WACA,wBDhBa,qECWf,wBACA,8DAEA,yBDde,qECeb,WACA,wBDhBa,uECWf,wBACA,8DAEA,yBDde,uECeb,WACA,wBDhBa,uECWf,wBACA,8DAEA,yBDde,uECeb,WACA,wBDhBa,uECWf,wBACA,8DAEA,yBDde,uECeb,WACA,yBArCA,yBDSJ,uBCuBE,wBACA,6DAsDA,sBACA,WACA,yDAtDA,yBD1BF,uBC2BI,WACA,wBAsDF,yBDlFF,uBCmFI,UDvEa,oECWf,uBACA,4DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,uBACA,4DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,uBACA,4DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,wBACA,6DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,wBACA,6DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,wBACA,6DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,wBACA,6DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,wBACA,6DAEA,yBDde,oECeb,WACA,wBDhBa,oECWf,wBACA,6DAEA,yBDde,oECeb,WACA,wBDhBa,sECWf,wBACA,6DAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,wBACA,6DAEA,yBDde,sECeb,WACA,wBDhBa,sECWf,wBACA,6DAEA,yBDde,sECeb,WACA,yBDNA,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,gCCkEF,ODnEa,CACX,iCCkEF,QDnEa,CACX,iCCkEF,QDnEa,CACX,iCCkEF,QDnEa,CAOb,kCCiEE,sBAEA,yBDnEF,kCCoEI,kBDhEJ,qCCqEE,kBDjEF,qCCqEE,oBAEA,yBDvEF,qCCwEI,gBA3GF,yBDwCJ,qCCqFE,aAEA;;;;GAAA,EApIE,+CD6CJ,qCCqFE,YAEA;;;;GAAA,EAzIE,yBDkDJ,qCCqFE,YAEA;;;;GAAA,EDtEF,6BACE,kBACA,cAGF,8BACE,eACA,iB", "file": "mdc.layout-grid.min.css", "sourcesContent": ["// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:list';\n@use 'sass:map';\n@use './variables';\n@use './mixins';\n\n:root {\n  @each $size in map.keys(variables.$columns) {\n    --mdc-layout-grid-margin-#{$size}: #{map.get(\n        variables.$default-margin,\n        $size\n      )};\n    --mdc-layout-grid-gutter-#{$size}: #{map.get(\n        variables.$default-gutter,\n        $size\n      )};\n    --mdc-layout-grid-column-width-#{$size}: #{map.get(\n        variables.$column-width,\n        $size\n      )};\n  }\n}\n\n// postcss-bem-linter: define layout-grid\n.mdc-layout-grid {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n\n      @include mixins.layout-grid($size, $margin, variables.$max-width);\n    }\n  }\n}\n\n.mdc-layout-grid__inner {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n      $gutter: map.get(variables.$default-gutter, $size);\n\n      @include mixins.inner($size, $margin, $gutter);\n    }\n  }\n}\n\n.mdc-layout-grid__cell {\n  // select the upper breakpoint\n  $upper-breakpoint: list.nth(map.keys(variables.$columns), 1);\n\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $gutter: map.get(variables.$default-gutter, $size);\n\n      @include mixins.cell($size, variables.$default-column-span, $gutter);\n\n      @for $span from 1 through map.get(variables.$columns, $upper-breakpoint) {\n        // Span classes.\n        @at-root .mdc-layout-grid__cell--span-#{$span},\n          .mdc-layout-grid__cell--span-#{$span}-#{$size} {\n          @include mixins.cell-span_($size, $span, $gutter);\n        }\n      }\n    }\n  }\n\n  // Order override classes.\n  @for $i from 1 through map.get(variables.$columns, $upper-breakpoint) {\n    &--order-#{$i} {\n      @include mixins.cell-order($i);\n    }\n  }\n\n  // Alignment classes.\n  &--align-top {\n    @include mixins.cell-align(top);\n  }\n\n  &--align-middle {\n    @include mixins.cell-align(middle);\n  }\n\n  &--align-bottom {\n    @include mixins.cell-align(bottom);\n  }\n}\n\n.mdc-layout-grid--fixed-column-width {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n      $gutter: map.get(variables.$default-gutter, $size);\n      $column-width: map.get(variables.$column-width, $size);\n\n      @include mixins.fixed-column-width(\n        $size,\n        $margin,\n        $gutter,\n        $column-width\n      );\n    }\n  }\n}\n\n.mdc-layout-grid--align-left {\n  margin-right: auto;\n  margin-left: 0;\n}\n\n.mdc-layout-grid--align-right {\n  margin-right: 0;\n  margin-left: auto;\n}\n// postcss-bem-linter: end\n", "// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:math';\n@use './variables';\n\n// returns the lower grid boundary or null if the smallest grid is selected\n@function breakpoint-min($size) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n  $min: map.get(variables.$breakpoints, $size);\n\n  @return if($min > 0, $min, null);\n}\n\n// returns the upper grid boundary or null if the largest grid is selected\n@function breakpoint-max($size) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n  $names: map.keys(variables.$columns);\n  $n: list.index($names, $size);\n  $prev: if($n > 1, list.nth($names, $n - 1), null);\n\n  @return if($prev, (breakpoint-min($prev) - 1px), null);\n}\n\n// Private mixins, meant for internal use.\n@mixin media-query_($size) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  $min: breakpoint-min($size);\n  $max: breakpoint-max($size);\n\n  @if $min == null and $max != null {\n    // Phone\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else if $min != null and $max != null {\n    // Tablet\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $min != null and $max == null {\n    // Desktop\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    // Fallback - no breakpoints defined\n    @content;\n  }\n}\n\n@mixin cell-span_($size, $span, $gutter) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  $percent: math.percentage(\n    math.div($span, map.get(variables.$columns, $size))\n  );\n\n  @if $percent > 100% {\n    $percent: 100%;\n  }\n\n  width: calc(#{$percent} - #{$gutter});\n  width: calc(#{$percent} - var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}));\n\n  @supports (display: grid) {\n    width: auto;\n    grid-column-end: span math.min($span, map.get(variables.$columns, $size));\n  }\n}\n\n// Public mixins, meant for developer usage.\n@mixin layout-grid($size, $margin, $max-width: null) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  box-sizing: border-box;\n  margin: 0 auto;\n  padding: $margin;\n  padding: var(--mdc-layout-grid-margin-#{$size}, #{$margin});\n\n  @if $max-width {\n    max-width: $max-width;\n  }\n}\n\n@mixin inner($size, $margin, $gutter) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  display: flex;\n  flex-flow: row wrap;\n  align-items: stretch;\n  margin: math.div(-$gutter, 2);\n  margin: calc(var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}) / 2 * -1);\n\n  @supports (display: grid) {\n    display: grid;\n    margin: 0;\n    grid-gap: $gutter;\n    grid-gap: var(--mdc-layout-grid-gutter-#{$size}, $gutter);\n    grid-template-columns: repeat(\n      map.get(variables.$columns, $size),\n      minmax(0, 1fr)\n    );\n  }\n}\n\n@mixin cell($size, $default-span, $gutter) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  @include cell-span_($size, $default-span, $gutter);\n\n  box-sizing: border-box;\n  margin: math.div($gutter, 2);\n  margin: calc(var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}) / 2);\n\n  @supports (display: grid) {\n    margin: 0;\n  }\n}\n\n@mixin cell-order($order) {\n  order: $order;\n}\n\n@mixin cell-align($position) {\n  @if $position == 'top' {\n    align-self: flex-start;\n\n    @supports (display: grid) {\n      align-self: start;\n    }\n  }\n\n  @if $position == 'middle' {\n    align-self: center;\n  }\n\n  @if $position == 'bottom' {\n    align-self: flex-end;\n\n    @supports (display: grid) {\n      align-self: end;\n    }\n  }\n\n  @if $position == 'stretch' {\n    align-self: stretch;\n  }\n}\n\n@mixin fixed-column-width($size, $margin, $gutter, $column-width) {\n  @if not map.has-key(variables.$columns, $size) {\n    @error \"Invalid style specified! Choose one of #{map.keys(variables.$columns)}\";\n  }\n\n  $columnCount: map.get(variables.$columns, $size);\n  $gutter-number: $columnCount - 1;\n  $margin-number: 2;\n\n  width: $column-width * $columnCount + $gutter * $gutter-number + $margin *\n    $margin-number;\n  width: calc(\n    var(--mdc-layout-grid-column-width-#{$size}, #{$column-width}) * #{$columnCount} +\n      var(--mdc-layout-grid-gutter-#{$size}, #{$gutter}) * #{$gutter-number} +\n      var(--mdc-layout-grid-margin-#{$size}, #{$margin}) * #{$margin-number}\n  );\n}\n"], "sourceRoot": ""}