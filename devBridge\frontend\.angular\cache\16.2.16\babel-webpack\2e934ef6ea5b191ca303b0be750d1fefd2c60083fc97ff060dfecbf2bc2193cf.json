{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique operation types\n *\n * A GraphQL document is only valid if it has only one type per operation.\n */\nexport function UniqueOperationTypesRule(context) {\n  const schema = context.getSchema();\n  const definedOperationTypes = Object.create(null);\n  const existingOperationTypes = schema ? {\n    query: schema.getQueryType(),\n    mutation: schema.getMutationType(),\n    subscription: schema.getSubscriptionType()\n  } : {};\n  return {\n    SchemaDefinition: checkOperationTypes,\n    SchemaExtension: checkOperationTypes\n  };\n  function checkOperationTypes(node) {\n    var _node$operationTypes;\n\n    // See: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n    const operationTypesNodes = (_node$operationTypes = node.operationTypes) !== null && _node$operationTypes !== void 0 ? _node$operationTypes : [];\n    for (const operationType of operationTypesNodes) {\n      const operation = operationType.operation;\n      const alreadyDefinedOperationType = definedOperationTypes[operation];\n      if (existingOperationTypes[operation]) {\n        context.reportError(new GraphQLError(`Type for ${operation} already defined in the schema. It cannot be redefined.`, {\n          nodes: operationType\n        }));\n      } else if (alreadyDefinedOperationType) {\n        context.reportError(new GraphQLError(`There can be only one ${operation} type in schema.`, {\n          nodes: [alreadyDefinedOperationType, operationType]\n        }));\n      } else {\n        definedOperationTypes[operation] = operationType;\n      }\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["GraphQLError", "UniqueOperationTypesRule", "context", "schema", "getSchema", "definedOperationTypes", "Object", "create", "existingOperationTypes", "query", "getQueryType", "mutation", "getMutationType", "subscription", "getSubscriptionType", "SchemaDefinition", "checkOperationTypes", "SchemaExtension", "node", "_node$operationTypes", "operationTypesNodes", "operationTypes", "operationType", "operation", "alreadyDefinedOperationType", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/UniqueOperationTypesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique operation types\n *\n * A GraphQL document is only valid if it has only one type per operation.\n */\nexport function UniqueOperationTypesRule(context) {\n  const schema = context.getSchema();\n  const definedOperationTypes = Object.create(null);\n  const existingOperationTypes = schema\n    ? {\n        query: schema.getQueryType(),\n        mutation: schema.getMutationType(),\n        subscription: schema.getSubscriptionType(),\n      }\n    : {};\n  return {\n    SchemaDefinition: checkOperationTypes,\n    SchemaExtension: checkOperationTypes,\n  };\n\n  function checkOperationTypes(node) {\n    var _node$operationTypes;\n\n    // See: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n    const operationTypesNodes =\n      (_node$operationTypes = node.operationTypes) !== null &&\n      _node$operationTypes !== void 0\n        ? _node$operationTypes\n        : [];\n\n    for (const operationType of operationTypesNodes) {\n      const operation = operationType.operation;\n      const alreadyDefinedOperationType = definedOperationTypes[operation];\n\n      if (existingOperationTypes[operation]) {\n        context.reportError(\n          new GraphQLError(\n            `Type for ${operation} already defined in the schema. It cannot be redefined.`,\n            {\n              nodes: operationType,\n            },\n          ),\n        );\n      } else if (alreadyDefinedOperationType) {\n        context.reportError(\n          new GraphQLError(\n            `There can be only one ${operation} type in schema.`,\n            {\n              nodes: [alreadyDefinedOperationType, operationType],\n            },\n          ),\n        );\n      } else {\n        definedOperationTypes[operation] = operationType;\n      }\n    }\n\n    return false;\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EAChD,MAAMC,MAAM,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC;EAClC,MAAMC,qBAAqB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACjD,MAAMC,sBAAsB,GAAGL,MAAM,GACjC;IACEM,KAAK,EAAEN,MAAM,CAACO,YAAY,CAAC,CAAC;IAC5BC,QAAQ,EAAER,MAAM,CAACS,eAAe,CAAC,CAAC;IAClCC,YAAY,EAAEV,MAAM,CAACW,mBAAmB,CAAC;EAC3C,CAAC,GACD,CAAC,CAAC;EACN,OAAO;IACLC,gBAAgB,EAAEC,mBAAmB;IACrCC,eAAe,EAAED;EACnB,CAAC;EAED,SAASA,mBAAmBA,CAACE,IAAI,EAAE;IACjC,IAAIC,oBAAoB;;IAExB;;IAEA;IACA,MAAMC,mBAAmB,GACvB,CAACD,oBAAoB,GAAGD,IAAI,CAACG,cAAc,MAAM,IAAI,IACrDF,oBAAoB,KAAK,KAAK,CAAC,GAC3BA,oBAAoB,GACpB,EAAE;IAER,KAAK,MAAMG,aAAa,IAAIF,mBAAmB,EAAE;MAC/C,MAAMG,SAAS,GAAGD,aAAa,CAACC,SAAS;MACzC,MAAMC,2BAA2B,GAAGnB,qBAAqB,CAACkB,SAAS,CAAC;MAEpE,IAAIf,sBAAsB,CAACe,SAAS,CAAC,EAAE;QACrCrB,OAAO,CAACuB,WAAW,CACjB,IAAIzB,YAAY,CACb,YAAWuB,SAAU,yDAAwD,EAC9E;UACEG,KAAK,EAAEJ;QACT,CACF,CACF,CAAC;MACH,CAAC,MAAM,IAAIE,2BAA2B,EAAE;QACtCtB,OAAO,CAACuB,WAAW,CACjB,IAAIzB,YAAY,CACb,yBAAwBuB,SAAU,kBAAiB,EACpD;UACEG,KAAK,EAAE,CAACF,2BAA2B,EAAEF,aAAa;QACpD,CACF,CACF,CAAC;MACH,CAAC,MAAM;QACLjB,qBAAqB,CAACkB,SAAS,CAAC,GAAGD,aAAa;MAClD;IACF;IAEA,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}