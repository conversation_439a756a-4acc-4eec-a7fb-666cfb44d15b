{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function readerIterator(reader) {\n  var iterator = {\n    next: function () {\n      return reader.read();\n    }\n  };\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function () {\n      return this;\n    };\n  }\n  return iterator;\n}", "map": {"version": 3, "names": ["canUseAsyncIteratorSymbol", "readerIterator", "reader", "iterator", "next", "read", "Symbol", "asyncIterator"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/http/iterators/reader.js"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function readerIterator(reader) {\n    var iterator = {\n        next: function () {\n            return reader.read();\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,QAAQ,6BAA6B;AACvE,eAAe,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC3C,IAAIC,QAAQ,GAAG;IACXC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,OAAOF,MAAM,CAACG,IAAI,CAAC,CAAC;IACxB;EACJ,CAAC;EACD,IAAIL,yBAAyB,EAAE;IAC3BG,QAAQ,CAACG,MAAM,CAACC,aAAa,CAAC,GAAG,YAAY;MACzC,OAAO,IAAI;IACf,CAAC;EACL;EACA,OAAOJ,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}