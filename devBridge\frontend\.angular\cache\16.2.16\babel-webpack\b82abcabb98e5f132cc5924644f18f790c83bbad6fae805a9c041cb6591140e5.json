{"ast": null, "code": "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextSunday} function options.\n */\n\n/**\n * @name nextSunday\n * @category Weekday Helpers\n * @summary When is the next Sunday?\n *\n * @description\n * When is the next Sunday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned if a context is provided.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Sunday\n *\n * @example\n * // When is the next Sunday after March 22, 2020?\n * const result = nextSunday(new Date(2020, 2, 22))\n * //=> Sun Mar 29 2020 00:00:00\n */\nexport function nextSunday(date, options) {\n  return nextDay(date, 0, options);\n}\n\n// Fallback for modularized imports:\nexport default nextSunday;", "map": {"version": 3, "names": ["nextDay", "nextSunday", "date", "options"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/nextSunday.js"], "sourcesContent": ["import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextSunday} function options.\n */\n\n/**\n * @name nextSunday\n * @category Weekday Helpers\n * @summary When is the next Sunday?\n *\n * @description\n * When is the next Sunday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned if a context is provided.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Sunday\n *\n * @example\n * // When is the next Sunday after March 22, 2020?\n * const result = nextSunday(new Date(2020, 2, 22))\n * //=> Sun Mar 29 2020 00:00:00\n */\nexport function nextSunday(date, options) {\n  return nextDay(date, 0, options);\n}\n\n// Fallback for modularized imports:\nexport default nextSunday;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOH,OAAO,CAACE,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC;AAClC;;AAEA;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}