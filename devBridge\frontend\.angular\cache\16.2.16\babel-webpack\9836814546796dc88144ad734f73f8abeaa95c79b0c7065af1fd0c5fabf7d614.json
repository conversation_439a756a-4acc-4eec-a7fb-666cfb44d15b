{"ast": null, "code": "import { invariant } from \"../utilities/globals/index.js\";\nimport { createFragmentMap, getFragmentDefinitions, getOperationDefinition } from \"../utilities/index.js\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { MapImpl, SetImpl, warnOnImproperCacheImplementation } from \"./utils.js\";\n/** @internal */\nexport function maskOperation(data, document, cache) {\n  var _a;\n  if (!cache.fragmentMatches) {\n    if (globalThis.__DEV__ !== false) {\n      warnOnImproperCacheImplementation();\n    }\n    return data;\n  }\n  var definition = getOperationDefinition(document);\n  invariant(definition, 51);\n  if (data == null) {\n    // Maintain the original `null` or `undefined` value\n    return data;\n  }\n  return maskDefinition(data, definition.selectionSet, {\n    operationType: definition.operation,\n    operationName: (_a = definition.name) === null || _a === void 0 ? void 0 : _a.value,\n    fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n    cache: cache,\n    mutableTargets: new MapImpl(),\n    knownChanged: new SetImpl()\n  });\n}", "map": {"version": 3, "names": ["invariant", "createFragmentMap", "getFragmentDefinitions", "getOperationDefinition", "maskDefinition", "MapImpl", "SetImpl", "warnOnImproperCacheImplementation", "maskOperation", "data", "document", "cache", "_a", "fragmentMatches", "globalThis", "__DEV__", "definition", "selectionSet", "operationType", "operation", "operationName", "name", "value", "fragmentMap", "mutableTargets", "knownChanged"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/masking/maskOperation.js"], "sourcesContent": ["import { invariant } from \"../utilities/globals/index.js\";\nimport { createFragmentMap, getFragmentDefinitions, getOperationDefinition, } from \"../utilities/index.js\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { MapImpl, SetImpl, warnOnImproperCacheImplementation, } from \"./utils.js\";\n/** @internal */\nexport function maskOperation(data, document, cache) {\n    var _a;\n    if (!cache.fragmentMatches) {\n        if (globalThis.__DEV__ !== false) {\n            warnOnImproperCacheImplementation();\n        }\n        return data;\n    }\n    var definition = getOperationDefinition(document);\n    invariant(definition, 51);\n    if (data == null) {\n        // Maintain the original `null` or `undefined` value\n        return data;\n    }\n    return maskDefinition(data, definition.selectionSet, {\n        operationType: definition.operation,\n        operationName: (_a = definition.name) === null || _a === void 0 ? void 0 : _a.value,\n        fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n        cache: cache,\n        mutableTargets: new MapImpl(),\n        knownChanged: new SetImpl(),\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,+BAA+B;AACzD,SAASC,iBAAiB,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAS,uBAAuB;AAC1G,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,OAAO,EAAEC,OAAO,EAAEC,iCAAiC,QAAS,YAAY;AACjF;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EACjD,IAAIC,EAAE;EACN,IAAI,CAACD,KAAK,CAACE,eAAe,EAAE;IACxB,IAAIC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;MAC9BR,iCAAiC,CAAC,CAAC;IACvC;IACA,OAAOE,IAAI;EACf;EACA,IAAIO,UAAU,GAAGb,sBAAsB,CAACO,QAAQ,CAAC;EACjDV,SAAS,CAACgB,UAAU,EAAE,EAAE,CAAC;EACzB,IAAIP,IAAI,IAAI,IAAI,EAAE;IACd;IACA,OAAOA,IAAI;EACf;EACA,OAAOL,cAAc,CAACK,IAAI,EAAEO,UAAU,CAACC,YAAY,EAAE;IACjDC,aAAa,EAAEF,UAAU,CAACG,SAAS;IACnCC,aAAa,EAAE,CAACR,EAAE,GAAGI,UAAU,CAACK,IAAI,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,KAAK;IACnFC,WAAW,EAAEtB,iBAAiB,CAACC,sBAAsB,CAACQ,QAAQ,CAAC,CAAC;IAChEC,KAAK,EAAEA,KAAK;IACZa,cAAc,EAAE,IAAInB,OAAO,CAAC,CAAC;IAC7BoB,YAAY,EAAE,IAAInB,OAAO,CAAC;EAC9B,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}