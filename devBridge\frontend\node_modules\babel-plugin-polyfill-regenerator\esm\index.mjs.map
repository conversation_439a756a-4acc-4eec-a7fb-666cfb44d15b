{"version": 3, "file": "index.mjs", "sources": ["../src/index.ts"], "sourcesContent": ["import defineProvider from \"@babel/helper-define-polyfill-provider\";\nimport type { PluginPass } from \"@babel/core\";\n\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\ntype Options = {\n  \"#__secret_key__@babel/runtime__compatibility\": void | {\n    useBabelRuntime: string;\n  };\n};\n\nexport default defineProvider<Options>(({ debug, targets, babel }, options) => {\n  if (!shallowEqual(targets, babel.targets())) {\n    throw new Error(\n      \"This plugin does not use the targets option. Only preset-env's targets\" +\n        \" or top-level targets need to be configured for this plugin to work.\" +\n        \" See https://github.com/babel/babel-polyfills/issues/36 for more\" +\n        \" details.\",\n    );\n  }\n\n  const { [runtimeCompat]: { useBabelRuntime = false } = {} } = options;\n\n  return {\n    name: \"regenerator\",\n\n    polyfills: [\"regenerator-runtime\"],\n\n    usageGlobal(meta, utils) {\n      if (isRegenerator(meta)) {\n        debug(\"regenerator-runtime\");\n        utils.injectGlobalImport(\"regenerator-runtime/runtime.js\");\n      }\n    },\n    usagePure(meta, utils, path) {\n      if (isRegenerator(meta)) {\n        let pureName = \"regenerator-runtime\";\n        if (useBabelRuntime) {\n          const runtimeName =\n            ((path.hub as any).file as PluginPass).get(\n              \"runtimeHelpersModuleName\",\n            ) ?? \"@babel/runtime\";\n          pureName = `${runtimeName}/regenerator`;\n        }\n\n        path.replaceWith(\n          utils.injectDefaultImport(pureName, \"regenerator-runtime\"),\n        );\n      }\n    },\n  };\n});\n\nconst isRegenerator = meta =>\n  meta.kind === \"global\" && meta.name === \"regeneratorRuntime\";\n\nfunction shallowEqual(obj1: any, obj2: any) {\n  return JSON.stringify(obj1) === JSON.stringify(obj2);\n}\n"], "names": ["runtimeCompat", "define<PERSON>rovider", "debug", "targets", "babel", "options", "shallowEqual", "Error", "useBabelRuntime", "name", "polyfills", "usageGlobal", "meta", "utils", "isRegenerator", "injectGlobalImport", "usagePure", "path", "pureName", "_get", "runtimeName", "hub", "file", "get", "replaceWith", "injectDefaultImport", "kind", "obj1", "obj2", "JSON", "stringify"], "mappings": ";;AAGA,MAAMA,aAAa,GAAG,8CAA8C;AAQpE,YAAeC,cAAc,CAAU,CAAC;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAM,CAAC,EAAEC,OAAO,KAAK;EAC7E,IAAI,CAACC,YAAY,CAACH,OAAO,EAAEC,KAAK,CAACD,OAAO,EAAE,CAAC,EAAE;IAC3C,MAAM,IAAII,KAAK,CACb,wEAAwE,GACtE,sEAAsE,GACtE,kEAAkE,GAClE,WACJ,CAAC;;EAGH,MAAM;IAAE,CAACP,aAAa,GAAG;MAAEQ,eAAe,GAAG;KAAO,GAAG;GAAI,GAAGH,OAAO;EAErE,OAAO;IACLI,IAAI,EAAE,aAAa;IAEnBC,SAAS,EAAE,CAAC,qBAAqB,CAAC;IAElCC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAE;MACvB,IAAIC,aAAa,CAACF,IAAI,CAAC,EAAE;QACvBV,KAAK,CAAC,qBAAqB,CAAC;QAC5BW,KAAK,CAACE,kBAAkB,CAAC,gCAAgC,CAAC;;KAE7D;IACDC,SAASA,CAACJ,IAAI,EAAEC,KAAK,EAAEI,IAAI,EAAE;MAC3B,IAAIH,aAAa,CAACF,IAAI,CAAC,EAAE;QACvB,IAAIM,QAAQ,GAAG,qBAAqB;QACpC,IAAIV,eAAe,EAAE;UAAA,IAAAW,IAAA;UACnB,MAAMC,WAAW,IAAAD,IAAA,GACbF,IAAI,CAACI,GAAG,CAASC,IAAI,CAAgBC,GAAG,CACxC,0BACF,CAAC,YAAAJ,IAAA,GAAI,gBAAgB;UACvBD,QAAQ,GAAI,GAAEE,WAAY,cAAa;;QAGzCH,IAAI,CAACO,WAAW,CACdX,KAAK,CAACY,mBAAmB,CAACP,QAAQ,EAAE,qBAAqB,CAC3D,CAAC;;;GAGN;AACH,CAAC,CAAC;AAEF,MAAMJ,aAAa,GAAGF,IAAI,IACxBA,IAAI,CAACc,IAAI,KAAK,QAAQ,IAAId,IAAI,CAACH,IAAI,KAAK,oBAAoB;AAE9D,SAASH,YAAYA,CAACqB,IAAS,EAAEC,IAAS,EAAE;EAC1C,OAAOC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,KAAKE,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC;AACtD;;;;"}