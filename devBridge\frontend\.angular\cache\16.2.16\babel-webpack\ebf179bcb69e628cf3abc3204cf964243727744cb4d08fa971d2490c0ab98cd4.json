{"ast": null, "code": "import { serializeFetchParameter } from \"./serializeFetchParameter.js\";\n// For GET operations, returns the given URI rewritten with parameters, or a\n// parse error.\nexport function rewriteURIForGET(chosenURI, body) {\n  // Implement the standard HTTP GET serialization, plus 'extensions'. Note\n  // the extra level of JSON serialization!\n  var queryParams = [];\n  var addQueryParam = function (key, value) {\n    queryParams.push(\"\".concat(key, \"=\").concat(encodeURIComponent(value)));\n  };\n  if (\"query\" in body) {\n    addQueryParam(\"query\", body.query);\n  }\n  if (body.operationName) {\n    addQueryParam(\"operationName\", body.operationName);\n  }\n  if (body.variables) {\n    var serializedVariables = void 0;\n    try {\n      serializedVariables = serializeFetchParameter(body.variables, \"Variables map\");\n    } catch (parseError) {\n      return {\n        parseError: parseError\n      };\n    }\n    addQueryParam(\"variables\", serializedVariables);\n  }\n  if (body.extensions) {\n    var serializedExtensions = void 0;\n    try {\n      serializedExtensions = serializeFetchParameter(body.extensions, \"Extensions map\");\n    } catch (parseError) {\n      return {\n        parseError: parseError\n      };\n    }\n    addQueryParam(\"extensions\", serializedExtensions);\n  }\n  // Reconstruct the URI with added query params.\n  // XXX This assumes that the URI is well-formed and that it doesn't\n  //     already contain any of these query params. We could instead use the\n  //     URL API and take a polyfill (whatwg-url@6) for older browsers that\n  //     don't support URLSearchParams. Note that some browsers (and\n  //     versions of whatwg-url) support URL but not URLSearchParams!\n  var fragment = \"\",\n    preFragment = chosenURI;\n  var fragmentStart = chosenURI.indexOf(\"#\");\n  if (fragmentStart !== -1) {\n    fragment = chosenURI.substr(fragmentStart);\n    preFragment = chosenURI.substr(0, fragmentStart);\n  }\n  var queryParamsPrefix = preFragment.indexOf(\"?\") === -1 ? \"?\" : \"&\";\n  var newURI = preFragment + queryParamsPrefix + queryParams.join(\"&\") + fragment;\n  return {\n    newURI: newURI\n  };\n}", "map": {"version": 3, "names": ["serializeFetchParameter", "rewriteURIForGET", "chosenURI", "body", "queryParams", "addQueryParam", "key", "value", "push", "concat", "encodeURIComponent", "query", "operationName", "variables", "serializedVariables", "parseError", "extensions", "serializedExtensions", "fragment", "preFragment", "fragmentStart", "indexOf", "substr", "queryParamsPrefix", "newURI", "join"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/@apollo/client/link/http/rewriteURIForGET.js"], "sourcesContent": ["import { serializeFetchParameter } from \"./serializeFetchParameter.js\";\n// For GET operations, returns the given URI rewritten with parameters, or a\n// parse error.\nexport function rewriteURIForGET(chosenURI, body) {\n    // Implement the standard HTTP GET serialization, plus 'extensions'. Note\n    // the extra level of JSON serialization!\n    var queryParams = [];\n    var addQueryParam = function (key, value) {\n        queryParams.push(\"\".concat(key, \"=\").concat(encodeURIComponent(value)));\n    };\n    if (\"query\" in body) {\n        addQueryParam(\"query\", body.query);\n    }\n    if (body.operationName) {\n        addQueryParam(\"operationName\", body.operationName);\n    }\n    if (body.variables) {\n        var serializedVariables = void 0;\n        try {\n            serializedVariables = serializeFetchParameter(body.variables, \"Variables map\");\n        }\n        catch (parseError) {\n            return { parseError: parseError };\n        }\n        addQueryParam(\"variables\", serializedVariables);\n    }\n    if (body.extensions) {\n        var serializedExtensions = void 0;\n        try {\n            serializedExtensions = serializeFetchParameter(body.extensions, \"Extensions map\");\n        }\n        catch (parseError) {\n            return { parseError: parseError };\n        }\n        addQueryParam(\"extensions\", serializedExtensions);\n    }\n    // Reconstruct the URI with added query params.\n    // XXX This assumes that the URI is well-formed and that it doesn't\n    //     already contain any of these query params. We could instead use the\n    //     URL API and take a polyfill (whatwg-url@6) for older browsers that\n    //     don't support URLSearchParams. Note that some browsers (and\n    //     versions of whatwg-url) support URL but not URLSearchParams!\n    var fragment = \"\", preFragment = chosenURI;\n    var fragmentStart = chosenURI.indexOf(\"#\");\n    if (fragmentStart !== -1) {\n        fragment = chosenURI.substr(fragmentStart);\n        preFragment = chosenURI.substr(0, fragmentStart);\n    }\n    var queryParamsPrefix = preFragment.indexOf(\"?\") === -1 ? \"?\" : \"&\";\n    var newURI = preFragment + queryParamsPrefix + queryParams.join(\"&\") + fragment;\n    return { newURI: newURI };\n}\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EAC9C;EACA;EACA,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,aAAa,GAAG,SAAAA,CAAUC,GAAG,EAAEC,KAAK,EAAE;IACtCH,WAAW,CAACI,IAAI,CAAC,EAAE,CAACC,MAAM,CAACH,GAAG,EAAE,GAAG,CAAC,CAACG,MAAM,CAACC,kBAAkB,CAACH,KAAK,CAAC,CAAC,CAAC;EAC3E,CAAC;EACD,IAAI,OAAO,IAAIJ,IAAI,EAAE;IACjBE,aAAa,CAAC,OAAO,EAAEF,IAAI,CAACQ,KAAK,CAAC;EACtC;EACA,IAAIR,IAAI,CAACS,aAAa,EAAE;IACpBP,aAAa,CAAC,eAAe,EAAEF,IAAI,CAACS,aAAa,CAAC;EACtD;EACA,IAAIT,IAAI,CAACU,SAAS,EAAE;IAChB,IAAIC,mBAAmB,GAAG,KAAK,CAAC;IAChC,IAAI;MACAA,mBAAmB,GAAGd,uBAAuB,CAACG,IAAI,CAACU,SAAS,EAAE,eAAe,CAAC;IAClF,CAAC,CACD,OAAOE,UAAU,EAAE;MACf,OAAO;QAAEA,UAAU,EAAEA;MAAW,CAAC;IACrC;IACAV,aAAa,CAAC,WAAW,EAAES,mBAAmB,CAAC;EACnD;EACA,IAAIX,IAAI,CAACa,UAAU,EAAE;IACjB,IAAIC,oBAAoB,GAAG,KAAK,CAAC;IACjC,IAAI;MACAA,oBAAoB,GAAGjB,uBAAuB,CAACG,IAAI,CAACa,UAAU,EAAE,gBAAgB,CAAC;IACrF,CAAC,CACD,OAAOD,UAAU,EAAE;MACf,OAAO;QAAEA,UAAU,EAAEA;MAAW,CAAC;IACrC;IACAV,aAAa,CAAC,YAAY,EAAEY,oBAAoB,CAAC;EACrD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,QAAQ,GAAG,EAAE;IAAEC,WAAW,GAAGjB,SAAS;EAC1C,IAAIkB,aAAa,GAAGlB,SAAS,CAACmB,OAAO,CAAC,GAAG,CAAC;EAC1C,IAAID,aAAa,KAAK,CAAC,CAAC,EAAE;IACtBF,QAAQ,GAAGhB,SAAS,CAACoB,MAAM,CAACF,aAAa,CAAC;IAC1CD,WAAW,GAAGjB,SAAS,CAACoB,MAAM,CAAC,CAAC,EAAEF,aAAa,CAAC;EACpD;EACA,IAAIG,iBAAiB,GAAGJ,WAAW,CAACE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;EACnE,IAAIG,MAAM,GAAGL,WAAW,GAAGI,iBAAiB,GAAGnB,WAAW,CAACqB,IAAI,CAAC,GAAG,CAAC,GAAGP,QAAQ;EAC/E,OAAO;IAAEM,MAAM,EAAEA;EAAO,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}