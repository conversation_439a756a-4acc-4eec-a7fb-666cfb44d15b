{"name": "@types/express", "version": "4.17.21", "description": "TypeScript definitions for express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "China Medical University Hospital", "githubUsername": "CMUH", "url": "https://github.com/CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/puneetar"}, {"name": "<PERSON>", "githubUsername": "dfrankland", "url": "https://github.com/dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "fa18ce9be07653182e2674f9a13cf8347ffb270031a7a8d22ba0e785bbc16ce4", "typeScriptVersion": "4.5"}