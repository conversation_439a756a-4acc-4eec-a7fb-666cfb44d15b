{"ast": null, "code": "import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.js\";\nimport { endOfMonth } from \"./endOfMonth.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\n\n/**\n * The {@link eachWeekendOfMonth} function options.\n */\n\n/**\n * @name eachWeekendOfMonth\n * @category Month Helpers\n * @summary List all the Saturdays and Sundays in the given month.\n *\n * @description\n * Get all the Saturdays and Sundays in the given month.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The given month\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the given month\n * const result = eachWeekendOfMonth(new Date(2022, 1, 1))\n * //=> [\n * //   Sat Feb 05 2022 00:00:00,\n * //   Sun Feb 06 2022 00:00:00,\n * //   Sat Feb 12 2022 00:00:00,\n * //   Sun Feb 13 2022 00:00:00,\n * //   Sat Feb 19 2022 00:00:00,\n * //   Sun Feb 20 2022 00:00:00,\n * //   Sat Feb 26 2022 00:00:00,\n * //   Sun Feb 27 2022 00:00:00\n * // ]\n */\nexport function eachWeekendOfMonth(date, options) {\n  const start = startOfMonth(date, options);\n  const end = endOfMonth(date, options);\n  return eachWeekendOfInterval({\n    start,\n    end\n  }, options);\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfMonth;", "map": {"version": 3, "names": ["eachWeekendOfInterval", "endOfMonth", "startOfMonth", "eachWeekendOfMonth", "date", "options", "start", "end"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/eachWeekendOfMonth.js"], "sourcesContent": ["import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.js\";\nimport { endOfMonth } from \"./endOfMonth.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\n\n/**\n * The {@link eachWeekendOfMonth} function options.\n */\n\n/**\n * @name eachWeekendOfMonth\n * @category Month Helpers\n * @summary List all the Saturdays and Sundays in the given month.\n *\n * @description\n * Get all the Saturdays and Sundays in the given month.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The given month\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the given month\n * const result = eachWeekendOfMonth(new Date(2022, 1, 1))\n * //=> [\n * //   Sat Feb 05 2022 00:00:00,\n * //   Sun Feb 06 2022 00:00:00,\n * //   Sat Feb 12 2022 00:00:00,\n * //   Sun Feb 13 2022 00:00:00,\n * //   Sat Feb 19 2022 00:00:00,\n * //   Sun Feb 20 2022 00:00:00,\n * //   Sat Feb 26 2022 00:00:00,\n * //   Sun Feb 27 2022 00:00:00\n * // ]\n */\nexport function eachWeekendOfMonth(date, options) {\n  const start = startOfMonth(date, options);\n  const end = endOfMonth(date, options);\n  return eachWeekendOfInterval({ start, end }, options);\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfMonth;\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,YAAY,QAAQ,mBAAmB;;AAEhD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAChD,MAAMC,KAAK,GAAGJ,YAAY,CAACE,IAAI,EAAEC,OAAO,CAAC;EACzC,MAAME,GAAG,GAAGN,UAAU,CAACG,IAAI,EAAEC,OAAO,CAAC;EACrC,OAAOL,qBAAqB,CAAC;IAAEM,KAAK;IAAEC;EAAI,CAAC,EAAEF,OAAO,CAAC;AACvD;;AAEA;AACA,eAAeF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}