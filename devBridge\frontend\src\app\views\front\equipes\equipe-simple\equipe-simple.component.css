/* Animations personnalisées */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(79, 95, 173, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(79, 95, 173, 0.5);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Animation pour l'icône principale */
.main-icon {
  animation: float 3s ease-in-out infinite;
}

/* Effet de brillance sur le bouton */
.btn-shine {
  position: relative;
  overflow: hidden;
}

.btn-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-shine:hover::before {
  left: 100%;
}

/* Effet de pulsation pour le mode sombre */
.dark .pulse-glow {
  animation: pulse-glow-dark 2s ease-in-out infinite;
}

@keyframes pulse-glow-dark {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 247, 255, 0.5);
  }
}

/* Amélioration de la lisibilité en mode sombre */
.dark {
  color-scheme: dark;
}

/* Animation de chargement personnalisée */
@keyframes spin-smooth {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-smooth {
  animation: spin-smooth 1s linear infinite;
}

/* Effet de survol pour les éléments interactifs */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Style pour les notifications */
.notification-enter {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .main-container {
    padding: 1rem;
  }
  
  .card-container {
    margin: 0 1rem;
  }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
  .main-icon,
  .pulse-glow,
  .dark .pulse-glow {
    animation: none;
  }
  
  .hover-lift {
    transition: none;
  }
}

/* Focus states pour l'accessibilité */
button:focus-visible {
  outline: 2px solid #4f5fad;
  outline-offset: 2px;
}

.dark button:focus-visible {
  outline-color: #00f7ff;
}
