{"ast": null, "code": "import { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { setISOWeekYear } from \"./setISOWeekYear.js\";\n\n/**\n * The {@link addISOWeekYears} function options.\n */\n\n/**\n * @name addISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Add the specified number of ISO week-numbering years to the given date.\n *\n * @description\n * Add the specified number of ISO week-numbering years to the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be added.\n * @param options - An object with options\n *\n * @returns The new date with the ISO week-numbering years added\n *\n * @example\n * // Add 5 ISO week-numbering years to 2 July 2010:\n * const result = addISOWeekYears(new Date(2010, 6, 2), 5)\n * //=> Fri Jun 26 2015 00:00:00\n */\nexport function addISOWeekYears(date, amount, options) {\n  return setISOWeekYear(date, getISOWeekYear(date, options) + amount, options);\n}\n\n// Fallback for modularized imports:\nexport default addISOWeekYears;", "map": {"version": 3, "names": ["getISOWeekYear", "setISOWeekYear", "addISOWeekYears", "date", "amount", "options"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/date-fns/addISOWeekYears.js"], "sourcesContent": ["import { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { setISOWeekYear } from \"./setISOWeekYear.js\";\n\n/**\n * The {@link addISOWeekYears} function options.\n */\n\n/**\n * @name addISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Add the specified number of ISO week-numbering years to the given date.\n *\n * @description\n * Add the specified number of ISO week-numbering years to the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be added.\n * @param options - An object with options\n *\n * @returns The new date with the ISO week-numbering years added\n *\n * @example\n * // Add 5 ISO week-numbering years to 2 July 2010:\n * const result = addISOWeekYears(new Date(2010, 6, 2), 5)\n * //=> Fri Jun 26 2015 00:00:00\n */\nexport function addISOWeekYears(date, amount, options) {\n  return setISOWeekYear(date, getISOWeekYear(date, options) + amount, options);\n}\n\n// Fallback for modularized imports:\nexport default addISOWeekYears;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,qBAAqB;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,OAAOJ,cAAc,CAACE,IAAI,EAAEH,cAAc,CAACG,IAAI,EAAEE,OAAO,CAAC,GAAGD,MAAM,EAAEC,OAAO,CAAC;AAC9E;;AAEA;AACA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}