{"ast": null, "code": "import { parentEntrySlot } from \"./context.js\";\nimport { maybeUnsubscribe, arrayFromSet } from \"./helpers.js\";\nconst emptySetPool = [];\nconst POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n  if (!condition) {\n    throw new Error(optionalMessage || \"assertion failure\");\n  }\n}\nfunction valueIs(a, b) {\n  const len = a.length;\n  return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n    // Both values must be ordinary (or both exceptional) to be equal.\n    len === b.length &&\n    // The underlying value or exception must be the same.\n    a[len - 1] === b[len - 1]\n  );\n}\nfunction valueGet(value) {\n  switch (value.length) {\n    case 0:\n      throw new Error(\"unknown value\");\n    case 1:\n      return value[0];\n    case 2:\n      throw value[1];\n  }\n}\nfunction valueCopy(value) {\n  return value.slice(0);\n}\nexport class Entry {\n  constructor(fn) {\n    this.fn = fn;\n    this.parents = new Set();\n    this.childValues = new Map();\n    // When this Entry has children that are dirty, this property becomes\n    // a Set containing other Entry objects, borrowed from emptySetPool.\n    // When the set becomes empty, it gets recycled back to emptySetPool.\n    this.dirtyChildren = null;\n    this.dirty = true;\n    this.recomputing = false;\n    this.value = [];\n    this.deps = null;\n    ++Entry.count;\n  }\n  peek() {\n    if (this.value.length === 1 && !mightBeDirty(this)) {\n      rememberParent(this);\n      return this.value[0];\n    }\n  }\n  // This is the most important method of the Entry API, because it\n  // determines whether the cached this.value can be returned immediately,\n  // or must be recomputed. The overall performance of the caching system\n  // depends on the truth of the following observations: (1) this.dirty is\n  // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n  // (3) valueGet(this.value) is usually returned without recomputation.\n  recompute(args) {\n    assert(!this.recomputing, \"already recomputing\");\n    rememberParent(this);\n    return mightBeDirty(this) ? reallyRecompute(this, args) : valueGet(this.value);\n  }\n  setDirty() {\n    if (this.dirty) return;\n    this.dirty = true;\n    reportDirty(this);\n    // We can go ahead and unsubscribe here, since any further dirty\n    // notifications we receive will be redundant, and unsubscribing may\n    // free up some resources, e.g. file watchers.\n    maybeUnsubscribe(this);\n  }\n  dispose() {\n    this.setDirty();\n    // Sever any dependency relationships with our own children, so those\n    // children don't retain this parent Entry in their child.parents sets,\n    // thereby preventing it from being fully garbage collected.\n    forgetChildren(this);\n    // Because this entry has been kicked out of the cache (in index.js),\n    // we've lost the ability to find out if/when this entry becomes dirty,\n    // whether that happens through a subscription, because of a direct call\n    // to entry.setDirty(), or because one of its children becomes dirty.\n    // Because of this loss of future information, we have to assume the\n    // worst (that this entry might have become dirty very soon), so we must\n    // immediately mark this entry's parents as dirty. Normally we could\n    // just call entry.setDirty() rather than calling parent.setDirty() for\n    // each parent, but that would leave this entry in parent.childValues\n    // and parent.dirtyChildren, which would prevent the child from being\n    // truly forgotten.\n    eachParent(this, (parent, child) => {\n      parent.setDirty();\n      forgetChild(parent, this);\n    });\n  }\n  forget() {\n    // The code that creates Entry objects in index.ts will replace this method\n    // with one that actually removes the Entry from the cache, which will also\n    // trigger the entry.dispose method.\n    this.dispose();\n  }\n  dependOn(dep) {\n    dep.add(this);\n    if (!this.deps) {\n      this.deps = emptySetPool.pop() || new Set();\n    }\n    this.deps.add(dep);\n  }\n  forgetDeps() {\n    if (this.deps) {\n      arrayFromSet(this.deps).forEach(dep => dep.delete(this));\n      this.deps.clear();\n      emptySetPool.push(this.deps);\n      this.deps = null;\n    }\n  }\n}\nEntry.count = 0;\nfunction rememberParent(child) {\n  const parent = parentEntrySlot.getValue();\n  if (parent) {\n    child.parents.add(parent);\n    if (!parent.childValues.has(child)) {\n      parent.childValues.set(child, []);\n    }\n    if (mightBeDirty(child)) {\n      reportDirtyChild(parent, child);\n    } else {\n      reportCleanChild(parent, child);\n    }\n    return parent;\n  }\n}\nfunction reallyRecompute(entry, args) {\n  forgetChildren(entry);\n  // Set entry as the parent entry while calling recomputeNewValue(entry).\n  parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n  if (maybeSubscribe(entry, args)) {\n    // If we successfully recomputed entry.value and did not fail to\n    // (re)subscribe, then this Entry is no longer explicitly dirty.\n    setClean(entry);\n  }\n  return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n  entry.recomputing = true;\n  const {\n    normalizeResult\n  } = entry;\n  let oldValueCopy;\n  if (normalizeResult && entry.value.length === 1) {\n    oldValueCopy = valueCopy(entry.value);\n  }\n  // Make entry.value an empty array, representing an unknown value.\n  entry.value.length = 0;\n  try {\n    // If entry.fn succeeds, entry.value will become a normal Value.\n    entry.value[0] = entry.fn.apply(null, args);\n    // If we have a viable oldValueCopy to compare with the (successfully\n    // recomputed) new entry.value, and they are not already === identical, give\n    // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n    // and/or entry.value[0] to determine the final cached entry.value.\n    if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n      try {\n        entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n      } catch (_a) {\n        // If normalizeResult throws, just use the newer value, rather than\n        // saving the exception as entry.value[1].\n      }\n    }\n  } catch (e) {\n    // If entry.fn throws, entry.value will hold that exception.\n    entry.value[1] = e;\n  }\n  // Either way, this line is always reached.\n  entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n  return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n  entry.dirty = false;\n  if (mightBeDirty(entry)) {\n    // This Entry may still have dirty children, in which case we can't\n    // let our parents know we're clean just yet.\n    return;\n  }\n  reportClean(entry);\n}\nfunction reportDirty(child) {\n  eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n  eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n  const parentCount = child.parents.size;\n  if (parentCount) {\n    const parents = arrayFromSet(child.parents);\n    for (let i = 0; i < parentCount; ++i) {\n      callback(parents[i], child);\n    }\n  }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n  // Must have called rememberParent(child) before calling\n  // reportDirtyChild(parent, child).\n  assert(parent.childValues.has(child));\n  assert(mightBeDirty(child));\n  const parentWasClean = !mightBeDirty(parent);\n  if (!parent.dirtyChildren) {\n    parent.dirtyChildren = emptySetPool.pop() || new Set();\n  } else if (parent.dirtyChildren.has(child)) {\n    // If we already know this child is dirty, then we must have already\n    // informed our own parents that we are dirty, so we can terminate\n    // the recursion early.\n    return;\n  }\n  parent.dirtyChildren.add(child);\n  // If parent was clean before, it just became (possibly) dirty (according to\n  // mightBeDirty), since we just added child to parent.dirtyChildren.\n  if (parentWasClean) {\n    reportDirty(parent);\n  }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n  // Must have called rememberChild(child) before calling\n  // reportCleanChild(parent, child).\n  assert(parent.childValues.has(child));\n  assert(!mightBeDirty(child));\n  const childValue = parent.childValues.get(child);\n  if (childValue.length === 0) {\n    parent.childValues.set(child, valueCopy(child.value));\n  } else if (!valueIs(childValue, child.value)) {\n    parent.setDirty();\n  }\n  removeDirtyChild(parent, child);\n  if (mightBeDirty(parent)) {\n    return;\n  }\n  reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n  const dc = parent.dirtyChildren;\n  if (dc) {\n    dc.delete(child);\n    if (dc.size === 0) {\n      if (emptySetPool.length < POOL_TARGET_SIZE) {\n        emptySetPool.push(dc);\n      }\n      parent.dirtyChildren = null;\n    }\n  }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n  if (parent.childValues.size > 0) {\n    parent.childValues.forEach((_value, child) => {\n      forgetChild(parent, child);\n    });\n  }\n  // Remove this parent Entry from any sets to which it was added by the\n  // addToSet method.\n  parent.forgetDeps();\n  // After we forget all our children, this.dirtyChildren must be empty\n  // and therefore must have been reset to null.\n  assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n  child.parents.delete(parent);\n  parent.childValues.delete(child);\n  removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n  if (typeof entry.subscribe === \"function\") {\n    try {\n      maybeUnsubscribe(entry); // Prevent double subscriptions.\n      entry.unsubscribe = entry.subscribe.apply(null, args);\n    } catch (e) {\n      // If this Entry has a subscribe function and it threw an exception\n      // (or an unsubscribe function it previously returned now throws),\n      // return false to indicate that we were not able to subscribe (or\n      // unsubscribe), and this Entry should remain dirty.\n      entry.setDirty();\n      return false;\n    }\n  }\n  // Returning true indicates either that there was no entry.subscribe\n  // function or that it succeeded.\n  return true;\n}", "map": {"version": 3, "names": ["parentEntrySlot", "maybeUnsubscribe", "arrayFromSet", "emptySetPool", "POOL_TARGET_SIZE", "assert", "condition", "optionalMessage", "Error", "valueIs", "a", "b", "len", "length", "valueGet", "value", "valueCopy", "slice", "Entry", "constructor", "fn", "parents", "Set", "<PERSON><PERSON><PERSON><PERSON>", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dirty", "recomputing", "deps", "count", "peek", "mightBeDirty", "rememberParent", "recompute", "args", "reallyRecompute", "set<PERSON>irty", "reportDirty", "dispose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eachParent", "parent", "child", "<PERSON><PERSON><PERSON><PERSON>", "forget", "dependOn", "dep", "add", "pop", "forgetDeps", "for<PERSON>ach", "delete", "clear", "push", "getValue", "has", "set", "reportDirty<PERSON><PERSON>d", "reportCleanChild", "entry", "with<PERSON><PERSON><PERSON>", "recomputeNewValue", "maybeSubscribe", "setClean", "normalizeResult", "oldValueCopy", "apply", "_a", "e", "size", "reportClean", "callback", "parentCount", "i", "parentWasClean", "childValue", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dc", "_value", "subscribe", "unsubscribe"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/optimism/lib/entry.js"], "sourcesContent": ["import { parentEntrySlot } from \"./context.js\";\nimport { maybeUnsubscribe, arrayFromSet } from \"./helpers.js\";\nconst emptySetPool = [];\nconst POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n    if (!condition) {\n        throw new Error(optionalMessage || \"assertion failure\");\n    }\n}\nfunction valueIs(a, b) {\n    const len = a.length;\n    return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n        // Both values must be ordinary (or both exceptional) to be equal.\n        len === b.length &&\n        // The underlying value or exception must be the same.\n        a[len - 1] === b[len - 1]);\n}\nfunction valueGet(value) {\n    switch (value.length) {\n        case 0: throw new Error(\"unknown value\");\n        case 1: return value[0];\n        case 2: throw value[1];\n    }\n}\nfunction valueCopy(value) {\n    return value.slice(0);\n}\nexport class Entry {\n    constructor(fn) {\n        this.fn = fn;\n        this.parents = new Set();\n        this.childValues = new Map();\n        // When this Entry has children that are dirty, this property becomes\n        // a Set containing other Entry objects, borrowed from emptySetPool.\n        // When the set becomes empty, it gets recycled back to emptySetPool.\n        this.dirtyChildren = null;\n        this.dirty = true;\n        this.recomputing = false;\n        this.value = [];\n        this.deps = null;\n        ++Entry.count;\n    }\n    peek() {\n        if (this.value.length === 1 && !mightBeDirty(this)) {\n            rememberParent(this);\n            return this.value[0];\n        }\n    }\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    recompute(args) {\n        assert(!this.recomputing, \"already recomputing\");\n        rememberParent(this);\n        return mightBeDirty(this)\n            ? reallyRecompute(this, args)\n            : valueGet(this.value);\n    }\n    setDirty() {\n        if (this.dirty)\n            return;\n        this.dirty = true;\n        reportDirty(this);\n        // We can go ahead and unsubscribe here, since any further dirty\n        // notifications we receive will be redundant, and unsubscribing may\n        // free up some resources, e.g. file watchers.\n        maybeUnsubscribe(this);\n    }\n    dispose() {\n        this.setDirty();\n        // Sever any dependency relationships with our own children, so those\n        // children don't retain this parent Entry in their child.parents sets,\n        // thereby preventing it from being fully garbage collected.\n        forgetChildren(this);\n        // Because this entry has been kicked out of the cache (in index.js),\n        // we've lost the ability to find out if/when this entry becomes dirty,\n        // whether that happens through a subscription, because of a direct call\n        // to entry.setDirty(), or because one of its children becomes dirty.\n        // Because of this loss of future information, we have to assume the\n        // worst (that this entry might have become dirty very soon), so we must\n        // immediately mark this entry's parents as dirty. Normally we could\n        // just call entry.setDirty() rather than calling parent.setDirty() for\n        // each parent, but that would leave this entry in parent.childValues\n        // and parent.dirtyChildren, which would prevent the child from being\n        // truly forgotten.\n        eachParent(this, (parent, child) => {\n            parent.setDirty();\n            forgetChild(parent, this);\n        });\n    }\n    forget() {\n        // The code that creates Entry objects in index.ts will replace this method\n        // with one that actually removes the Entry from the cache, which will also\n        // trigger the entry.dispose method.\n        this.dispose();\n    }\n    dependOn(dep) {\n        dep.add(this);\n        if (!this.deps) {\n            this.deps = emptySetPool.pop() || new Set();\n        }\n        this.deps.add(dep);\n    }\n    forgetDeps() {\n        if (this.deps) {\n            arrayFromSet(this.deps).forEach(dep => dep.delete(this));\n            this.deps.clear();\n            emptySetPool.push(this.deps);\n            this.deps = null;\n        }\n    }\n}\nEntry.count = 0;\nfunction rememberParent(child) {\n    const parent = parentEntrySlot.getValue();\n    if (parent) {\n        child.parents.add(parent);\n        if (!parent.childValues.has(child)) {\n            parent.childValues.set(child, []);\n        }\n        if (mightBeDirty(child)) {\n            reportDirtyChild(parent, child);\n        }\n        else {\n            reportCleanChild(parent, child);\n        }\n        return parent;\n    }\n}\nfunction reallyRecompute(entry, args) {\n    forgetChildren(entry);\n    // Set entry as the parent entry while calling recomputeNewValue(entry).\n    parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n    if (maybeSubscribe(entry, args)) {\n        // If we successfully recomputed entry.value and did not fail to\n        // (re)subscribe, then this Entry is no longer explicitly dirty.\n        setClean(entry);\n    }\n    return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n    entry.recomputing = true;\n    const { normalizeResult } = entry;\n    let oldValueCopy;\n    if (normalizeResult && entry.value.length === 1) {\n        oldValueCopy = valueCopy(entry.value);\n    }\n    // Make entry.value an empty array, representing an unknown value.\n    entry.value.length = 0;\n    try {\n        // If entry.fn succeeds, entry.value will become a normal Value.\n        entry.value[0] = entry.fn.apply(null, args);\n        // If we have a viable oldValueCopy to compare with the (successfully\n        // recomputed) new entry.value, and they are not already === identical, give\n        // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n        // and/or entry.value[0] to determine the final cached entry.value.\n        if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n            try {\n                entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n            }\n            catch (_a) {\n                // If normalizeResult throws, just use the newer value, rather than\n                // saving the exception as entry.value[1].\n            }\n        }\n    }\n    catch (e) {\n        // If entry.fn throws, entry.value will hold that exception.\n        entry.value[1] = e;\n    }\n    // Either way, this line is always reached.\n    entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n    return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n    entry.dirty = false;\n    if (mightBeDirty(entry)) {\n        // This Entry may still have dirty children, in which case we can't\n        // let our parents know we're clean just yet.\n        return;\n    }\n    reportClean(entry);\n}\nfunction reportDirty(child) {\n    eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n    eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n    const parentCount = child.parents.size;\n    if (parentCount) {\n        const parents = arrayFromSet(child.parents);\n        for (let i = 0; i < parentCount; ++i) {\n            callback(parents[i], child);\n        }\n    }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n    // Must have called rememberParent(child) before calling\n    // reportDirtyChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(mightBeDirty(child));\n    const parentWasClean = !mightBeDirty(parent);\n    if (!parent.dirtyChildren) {\n        parent.dirtyChildren = emptySetPool.pop() || new Set;\n    }\n    else if (parent.dirtyChildren.has(child)) {\n        // If we already know this child is dirty, then we must have already\n        // informed our own parents that we are dirty, so we can terminate\n        // the recursion early.\n        return;\n    }\n    parent.dirtyChildren.add(child);\n    // If parent was clean before, it just became (possibly) dirty (according to\n    // mightBeDirty), since we just added child to parent.dirtyChildren.\n    if (parentWasClean) {\n        reportDirty(parent);\n    }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n    // Must have called rememberChild(child) before calling\n    // reportCleanChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(!mightBeDirty(child));\n    const childValue = parent.childValues.get(child);\n    if (childValue.length === 0) {\n        parent.childValues.set(child, valueCopy(child.value));\n    }\n    else if (!valueIs(childValue, child.value)) {\n        parent.setDirty();\n    }\n    removeDirtyChild(parent, child);\n    if (mightBeDirty(parent)) {\n        return;\n    }\n    reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n    const dc = parent.dirtyChildren;\n    if (dc) {\n        dc.delete(child);\n        if (dc.size === 0) {\n            if (emptySetPool.length < POOL_TARGET_SIZE) {\n                emptySetPool.push(dc);\n            }\n            parent.dirtyChildren = null;\n        }\n    }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n    if (parent.childValues.size > 0) {\n        parent.childValues.forEach((_value, child) => {\n            forgetChild(parent, child);\n        });\n    }\n    // Remove this parent Entry from any sets to which it was added by the\n    // addToSet method.\n    parent.forgetDeps();\n    // After we forget all our children, this.dirtyChildren must be empty\n    // and therefore must have been reset to null.\n    assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n    child.parents.delete(parent);\n    parent.childValues.delete(child);\n    removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n    if (typeof entry.subscribe === \"function\") {\n        try {\n            maybeUnsubscribe(entry); // Prevent double subscriptions.\n            entry.unsubscribe = entry.subscribe.apply(null, args);\n        }\n        catch (e) {\n            // If this Entry has a subscribe function and it threw an exception\n            // (or an unsubscribe function it previously returned now throws),\n            // return false to indicate that we were not able to subscribe (or\n            // unsubscribe), and this Entry should remain dirty.\n            entry.setDirty();\n            return false;\n        }\n    }\n    // Returning true indicates either that there was no entry.subscribe\n    // function or that it succeeded.\n    return true;\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,cAAc;AAC9C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,cAAc;AAC7D,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,gBAAgB,GAAG,GAAG;AAC5B;AACA;AACA,SAASC,MAAMA,CAACC,SAAS,EAAEC,eAAe,EAAE;EACxC,IAAI,CAACD,SAAS,EAAE;IACZ,MAAM,IAAIE,KAAK,CAACD,eAAe,IAAI,mBAAmB,CAAC;EAC3D;AACJ;AACA,SAASE,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnB,MAAMC,GAAG,GAAGF,CAAC,CAACG,MAAM;EACpB;IACA;IACAD,GAAG,GAAG,CAAC;IACH;IACAA,GAAG,KAAKD,CAAC,CAACE,MAAM;IAChB;IACAH,CAAC,CAACE,GAAG,GAAG,CAAC,CAAC,KAAKD,CAAC,CAACC,GAAG,GAAG,CAAC;EAAC;AACjC;AACA,SAASE,QAAQA,CAACC,KAAK,EAAE;EACrB,QAAQA,KAAK,CAACF,MAAM;IAChB,KAAK,CAAC;MAAE,MAAM,IAAIL,KAAK,CAAC,eAAe,CAAC;IACxC,KAAK,CAAC;MAAE,OAAOO,KAAK,CAAC,CAAC,CAAC;IACvB,KAAK,CAAC;MAAE,MAAMA,KAAK,CAAC,CAAC,CAAC;EAC1B;AACJ;AACA,SAASC,SAASA,CAACD,KAAK,EAAE;EACtB,OAAOA,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;AACzB;AACA,OAAO,MAAMC,KAAK,CAAC;EACfC,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACZ,KAAK,GAAG,EAAE;IACf,IAAI,CAACa,IAAI,GAAG,IAAI;IAChB,EAAEV,KAAK,CAACW,KAAK;EACjB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACf,KAAK,CAACF,MAAM,KAAK,CAAC,IAAI,CAACkB,YAAY,CAAC,IAAI,CAAC,EAAE;MAChDC,cAAc,CAAC,IAAI,CAAC;MACpB,OAAO,IAAI,CAACjB,KAAK,CAAC,CAAC,CAAC;IACxB;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACAkB,SAASA,CAACC,IAAI,EAAE;IACZ7B,MAAM,CAAC,CAAC,IAAI,CAACsB,WAAW,EAAE,qBAAqB,CAAC;IAChDK,cAAc,CAAC,IAAI,CAAC;IACpB,OAAOD,YAAY,CAAC,IAAI,CAAC,GACnBI,eAAe,CAAC,IAAI,EAAED,IAAI,CAAC,GAC3BpB,QAAQ,CAAC,IAAI,CAACC,KAAK,CAAC;EAC9B;EACAqB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,KAAK,EACV;IACJ,IAAI,CAACA,KAAK,GAAG,IAAI;IACjBW,WAAW,CAAC,IAAI,CAAC;IACjB;IACA;IACA;IACApC,gBAAgB,CAAC,IAAI,CAAC;EAC1B;EACAqC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,QAAQ,CAAC,CAAC;IACf;IACA;IACA;IACAG,cAAc,CAAC,IAAI,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,UAAU,CAAC,IAAI,EAAE,CAACC,MAAM,EAAEC,KAAK,KAAK;MAChCD,MAAM,CAACL,QAAQ,CAAC,CAAC;MACjBO,WAAW,CAACF,MAAM,EAAE,IAAI,CAAC;IAC7B,CAAC,CAAC;EACN;EACAG,MAAMA,CAAA,EAAG;IACL;IACA;IACA;IACA,IAAI,CAACN,OAAO,CAAC,CAAC;EAClB;EACAO,QAAQA,CAACC,GAAG,EAAE;IACVA,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC;IACb,IAAI,CAAC,IAAI,CAACnB,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAGzB,YAAY,CAAC6C,GAAG,CAAC,CAAC,IAAI,IAAI1B,GAAG,CAAC,CAAC;IAC/C;IACA,IAAI,CAACM,IAAI,CAACmB,GAAG,CAACD,GAAG,CAAC;EACtB;EACAG,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACrB,IAAI,EAAE;MACX1B,YAAY,CAAC,IAAI,CAAC0B,IAAI,CAAC,CAACsB,OAAO,CAACJ,GAAG,IAAIA,GAAG,CAACK,MAAM,CAAC,IAAI,CAAC,CAAC;MACxD,IAAI,CAACvB,IAAI,CAACwB,KAAK,CAAC,CAAC;MACjBjD,YAAY,CAACkD,IAAI,CAAC,IAAI,CAACzB,IAAI,CAAC;MAC5B,IAAI,CAACA,IAAI,GAAG,IAAI;IACpB;EACJ;AACJ;AACAV,KAAK,CAACW,KAAK,GAAG,CAAC;AACf,SAASG,cAAcA,CAACU,KAAK,EAAE;EAC3B,MAAMD,MAAM,GAAGzC,eAAe,CAACsD,QAAQ,CAAC,CAAC;EACzC,IAAIb,MAAM,EAAE;IACRC,KAAK,CAACrB,OAAO,CAAC0B,GAAG,CAACN,MAAM,CAAC;IACzB,IAAI,CAACA,MAAM,CAAClB,WAAW,CAACgC,GAAG,CAACb,KAAK,CAAC,EAAE;MAChCD,MAAM,CAAClB,WAAW,CAACiC,GAAG,CAACd,KAAK,EAAE,EAAE,CAAC;IACrC;IACA,IAAIX,YAAY,CAACW,KAAK,CAAC,EAAE;MACrBe,gBAAgB,CAAChB,MAAM,EAAEC,KAAK,CAAC;IACnC,CAAC,MACI;MACDgB,gBAAgB,CAACjB,MAAM,EAAEC,KAAK,CAAC;IACnC;IACA,OAAOD,MAAM;EACjB;AACJ;AACA,SAASN,eAAeA,CAACwB,KAAK,EAAEzB,IAAI,EAAE;EAClCK,cAAc,CAACoB,KAAK,CAAC;EACrB;EACA3D,eAAe,CAAC4D,SAAS,CAACD,KAAK,EAAEE,iBAAiB,EAAE,CAACF,KAAK,EAAEzB,IAAI,CAAC,CAAC;EAClE,IAAI4B,cAAc,CAACH,KAAK,EAAEzB,IAAI,CAAC,EAAE;IAC7B;IACA;IACA6B,QAAQ,CAACJ,KAAK,CAAC;EACnB;EACA,OAAO7C,QAAQ,CAAC6C,KAAK,CAAC5C,KAAK,CAAC;AAChC;AACA,SAAS8C,iBAAiBA,CAACF,KAAK,EAAEzB,IAAI,EAAE;EACpCyB,KAAK,CAAChC,WAAW,GAAG,IAAI;EACxB,MAAM;IAAEqC;EAAgB,CAAC,GAAGL,KAAK;EACjC,IAAIM,YAAY;EAChB,IAAID,eAAe,IAAIL,KAAK,CAAC5C,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;IAC7CoD,YAAY,GAAGjD,SAAS,CAAC2C,KAAK,CAAC5C,KAAK,CAAC;EACzC;EACA;EACA4C,KAAK,CAAC5C,KAAK,CAACF,MAAM,GAAG,CAAC;EACtB,IAAI;IACA;IACA8C,KAAK,CAAC5C,KAAK,CAAC,CAAC,CAAC,GAAG4C,KAAK,CAACvC,EAAE,CAAC8C,KAAK,CAAC,IAAI,EAAEhC,IAAI,CAAC;IAC3C;IACA;IACA;IACA;IACA,IAAI8B,eAAe,IAAIC,YAAY,IAAI,CAACxD,OAAO,CAACwD,YAAY,EAAEN,KAAK,CAAC5C,KAAK,CAAC,EAAE;MACxE,IAAI;QACA4C,KAAK,CAAC5C,KAAK,CAAC,CAAC,CAAC,GAAGiD,eAAe,CAACL,KAAK,CAAC5C,KAAK,CAAC,CAAC,CAAC,EAAEkD,YAAY,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC,CACD,OAAOE,EAAE,EAAE;QACP;QACA;MAAA;IAER;EACJ,CAAC,CACD,OAAOC,CAAC,EAAE;IACN;IACAT,KAAK,CAAC5C,KAAK,CAAC,CAAC,CAAC,GAAGqD,CAAC;EACtB;EACA;EACAT,KAAK,CAAChC,WAAW,GAAG,KAAK;AAC7B;AACA,SAASI,YAAYA,CAAC4B,KAAK,EAAE;EACzB,OAAOA,KAAK,CAACjC,KAAK,IAAI,CAAC,EAAEiC,KAAK,CAAClC,aAAa,IAAIkC,KAAK,CAAClC,aAAa,CAAC4C,IAAI,CAAC;AAC7E;AACA,SAASN,QAAQA,CAACJ,KAAK,EAAE;EACrBA,KAAK,CAACjC,KAAK,GAAG,KAAK;EACnB,IAAIK,YAAY,CAAC4B,KAAK,CAAC,EAAE;IACrB;IACA;IACA;EACJ;EACAW,WAAW,CAACX,KAAK,CAAC;AACtB;AACA,SAAStB,WAAWA,CAACK,KAAK,EAAE;EACxBF,UAAU,CAACE,KAAK,EAAEe,gBAAgB,CAAC;AACvC;AACA,SAASa,WAAWA,CAAC5B,KAAK,EAAE;EACxBF,UAAU,CAACE,KAAK,EAAEgB,gBAAgB,CAAC;AACvC;AACA,SAASlB,UAAUA,CAACE,KAAK,EAAE6B,QAAQ,EAAE;EACjC,MAAMC,WAAW,GAAG9B,KAAK,CAACrB,OAAO,CAACgD,IAAI;EACtC,IAAIG,WAAW,EAAE;IACb,MAAMnD,OAAO,GAAGnB,YAAY,CAACwC,KAAK,CAACrB,OAAO,CAAC;IAC3C,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,EAAE,EAAEC,CAAC,EAAE;MAClCF,QAAQ,CAAClD,OAAO,CAACoD,CAAC,CAAC,EAAE/B,KAAK,CAAC;IAC/B;EACJ;AACJ;AACA;AACA,SAASe,gBAAgBA,CAAChB,MAAM,EAAEC,KAAK,EAAE;EACrC;EACA;EACArC,MAAM,CAACoC,MAAM,CAAClB,WAAW,CAACgC,GAAG,CAACb,KAAK,CAAC,CAAC;EACrCrC,MAAM,CAAC0B,YAAY,CAACW,KAAK,CAAC,CAAC;EAC3B,MAAMgC,cAAc,GAAG,CAAC3C,YAAY,CAACU,MAAM,CAAC;EAC5C,IAAI,CAACA,MAAM,CAAChB,aAAa,EAAE;IACvBgB,MAAM,CAAChB,aAAa,GAAGtB,YAAY,CAAC6C,GAAG,CAAC,CAAC,IAAI,IAAI1B,GAAG,CAAD,CAAC;EACxD,CAAC,MACI,IAAImB,MAAM,CAAChB,aAAa,CAAC8B,GAAG,CAACb,KAAK,CAAC,EAAE;IACtC;IACA;IACA;IACA;EACJ;EACAD,MAAM,CAAChB,aAAa,CAACsB,GAAG,CAACL,KAAK,CAAC;EAC/B;EACA;EACA,IAAIgC,cAAc,EAAE;IAChBrC,WAAW,CAACI,MAAM,CAAC;EACvB;AACJ;AACA;AACA,SAASiB,gBAAgBA,CAACjB,MAAM,EAAEC,KAAK,EAAE;EACrC;EACA;EACArC,MAAM,CAACoC,MAAM,CAAClB,WAAW,CAACgC,GAAG,CAACb,KAAK,CAAC,CAAC;EACrCrC,MAAM,CAAC,CAAC0B,YAAY,CAACW,KAAK,CAAC,CAAC;EAC5B,MAAMiC,UAAU,GAAGlC,MAAM,CAAClB,WAAW,CAACqD,GAAG,CAAClC,KAAK,CAAC;EAChD,IAAIiC,UAAU,CAAC9D,MAAM,KAAK,CAAC,EAAE;IACzB4B,MAAM,CAAClB,WAAW,CAACiC,GAAG,CAACd,KAAK,EAAE1B,SAAS,CAAC0B,KAAK,CAAC3B,KAAK,CAAC,CAAC;EACzD,CAAC,MACI,IAAI,CAACN,OAAO,CAACkE,UAAU,EAAEjC,KAAK,CAAC3B,KAAK,CAAC,EAAE;IACxC0B,MAAM,CAACL,QAAQ,CAAC,CAAC;EACrB;EACAyC,gBAAgB,CAACpC,MAAM,EAAEC,KAAK,CAAC;EAC/B,IAAIX,YAAY,CAACU,MAAM,CAAC,EAAE;IACtB;EACJ;EACA6B,WAAW,CAAC7B,MAAM,CAAC;AACvB;AACA,SAASoC,gBAAgBA,CAACpC,MAAM,EAAEC,KAAK,EAAE;EACrC,MAAMoC,EAAE,GAAGrC,MAAM,CAAChB,aAAa;EAC/B,IAAIqD,EAAE,EAAE;IACJA,EAAE,CAAC3B,MAAM,CAACT,KAAK,CAAC;IAChB,IAAIoC,EAAE,CAACT,IAAI,KAAK,CAAC,EAAE;MACf,IAAIlE,YAAY,CAACU,MAAM,GAAGT,gBAAgB,EAAE;QACxCD,YAAY,CAACkD,IAAI,CAACyB,EAAE,CAAC;MACzB;MACArC,MAAM,CAAChB,aAAa,GAAG,IAAI;IAC/B;EACJ;AACJ;AACA;AACA;AACA,SAASc,cAAcA,CAACE,MAAM,EAAE;EAC5B,IAAIA,MAAM,CAAClB,WAAW,CAAC8C,IAAI,GAAG,CAAC,EAAE;IAC7B5B,MAAM,CAAClB,WAAW,CAAC2B,OAAO,CAAC,CAAC6B,MAAM,EAAErC,KAAK,KAAK;MAC1CC,WAAW,CAACF,MAAM,EAAEC,KAAK,CAAC;IAC9B,CAAC,CAAC;EACN;EACA;EACA;EACAD,MAAM,CAACQ,UAAU,CAAC,CAAC;EACnB;EACA;EACA5C,MAAM,CAACoC,MAAM,CAAChB,aAAa,KAAK,IAAI,CAAC;AACzC;AACA,SAASkB,WAAWA,CAACF,MAAM,EAAEC,KAAK,EAAE;EAChCA,KAAK,CAACrB,OAAO,CAAC8B,MAAM,CAACV,MAAM,CAAC;EAC5BA,MAAM,CAAClB,WAAW,CAAC4B,MAAM,CAACT,KAAK,CAAC;EAChCmC,gBAAgB,CAACpC,MAAM,EAAEC,KAAK,CAAC;AACnC;AACA,SAASoB,cAAcA,CAACH,KAAK,EAAEzB,IAAI,EAAE;EACjC,IAAI,OAAOyB,KAAK,CAACqB,SAAS,KAAK,UAAU,EAAE;IACvC,IAAI;MACA/E,gBAAgB,CAAC0D,KAAK,CAAC,CAAC,CAAC;MACzBA,KAAK,CAACsB,WAAW,GAAGtB,KAAK,CAACqB,SAAS,CAACd,KAAK,CAAC,IAAI,EAAEhC,IAAI,CAAC;IACzD,CAAC,CACD,OAAOkC,CAAC,EAAE;MACN;MACA;MACA;MACA;MACAT,KAAK,CAACvB,QAAQ,CAAC,CAAC;MAChB,OAAO,KAAK;IAChB;EACJ;EACA;EACA;EACA,OAAO,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}