{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isEnumType } from '../../type/definition.mjs';\n\n/**\n * Unique enum value names\n *\n * A GraphQL enum type is only valid if all its values are uniquely named.\n */\nexport function UniqueEnumValueNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  const knownValueNames = Object.create(null);\n  return {\n    EnumTypeDefinition: checkValueUniqueness,\n    EnumTypeExtension: checkValueUniqueness\n  };\n  function checkValueUniqueness(node) {\n    var _node$values;\n    const typeName = node.name.value;\n    if (!knownValueNames[typeName]) {\n      knownValueNames[typeName] = Object.create(null);\n    } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const valueNodes = (_node$values = node.values) !== null && _node$values !== void 0 ? _node$values : [];\n    const valueNames = knownValueNames[typeName];\n    for (const valueDef of valueNodes) {\n      const valueName = valueDef.name.value;\n      const existingType = existingTypeMap[typeName];\n      if (isEnumType(existingType) && existingType.getValue(valueName)) {\n        context.reportError(new GraphQLError(`Enum value \"${typeName}.${valueName}\" already exists in the schema. It cannot also be defined in this type extension.`, {\n          nodes: valueDef.name\n        }));\n      } else if (valueNames[valueName]) {\n        context.reportError(new GraphQLError(`Enum value \"${typeName}.${valueName}\" can only be defined once.`, {\n          nodes: [valueNames[valueName], valueDef.name]\n        }));\n      } else {\n        valueNames[valueName] = valueDef.name;\n      }\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["GraphQLError", "isEnumType", "UniqueEnumValueNamesRule", "context", "schema", "getSchema", "existingTypeMap", "getTypeMap", "Object", "create", "knownValueNames", "EnumTypeDefinition", "checkValueUniqueness", "EnumTypeExtension", "node", "_node$values", "typeName", "name", "value", "valueNodes", "values", "valueNames", "valueDef", "valueName", "existingType", "getValue", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/UniqueEnumValueNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isEnumType } from '../../type/definition.mjs';\n\n/**\n * Unique enum value names\n *\n * A GraphQL enum type is only valid if all its values are uniquely named.\n */\nexport function UniqueEnumValueNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  const knownValueNames = Object.create(null);\n  return {\n    EnumTypeDefinition: checkValueUniqueness,\n    EnumTypeExtension: checkValueUniqueness,\n  };\n\n  function checkValueUniqueness(node) {\n    var _node$values;\n\n    const typeName = node.name.value;\n\n    if (!knownValueNames[typeName]) {\n      knownValueNames[typeName] = Object.create(null);\n    } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const valueNodes =\n      (_node$values = node.values) !== null && _node$values !== void 0\n        ? _node$values\n        : [];\n    const valueNames = knownValueNames[typeName];\n\n    for (const valueDef of valueNodes) {\n      const valueName = valueDef.name.value;\n      const existingType = existingTypeMap[typeName];\n\n      if (isEnumType(existingType) && existingType.getValue(valueName)) {\n        context.reportError(\n          new GraphQLError(\n            `Enum value \"${typeName}.${valueName}\" already exists in the schema. It cannot also be defined in this type extension.`,\n            {\n              nodes: valueDef.name,\n            },\n          ),\n        );\n      } else if (valueNames[valueName]) {\n        context.reportError(\n          new GraphQLError(\n            `Enum value \"${typeName}.${valueName}\" can only be defined once.`,\n            {\n              nodes: [valueNames[valueName], valueDef.name],\n            },\n          ),\n        );\n      } else {\n        valueNames[valueName] = valueDef.name;\n      }\n    }\n\n    return false;\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,UAAU,QAAQ,2BAA2B;;AAEtD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EAChD,MAAMC,MAAM,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC;EAClC,MAAMC,eAAe,GAAGF,MAAM,GAAGA,MAAM,CAACG,UAAU,CAAC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC1E,MAAMC,eAAe,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC3C,OAAO;IACLE,kBAAkB,EAAEC,oBAAoB;IACxCC,iBAAiB,EAAED;EACrB,CAAC;EAED,SAASA,oBAAoBA,CAACE,IAAI,EAAE;IAClC,IAAIC,YAAY;IAEhB,MAAMC,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK;IAEhC,IAAI,CAACR,eAAe,CAACM,QAAQ,CAAC,EAAE;MAC9BN,eAAe,CAACM,QAAQ,CAAC,GAAGR,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjD,CAAC,CAAC;;IAEF;;IAEA,MAAMU,UAAU,GACd,CAACJ,YAAY,GAAGD,IAAI,CAACM,MAAM,MAAM,IAAI,IAAIL,YAAY,KAAK,KAAK,CAAC,GAC5DA,YAAY,GACZ,EAAE;IACR,MAAMM,UAAU,GAAGX,eAAe,CAACM,QAAQ,CAAC;IAE5C,KAAK,MAAMM,QAAQ,IAAIH,UAAU,EAAE;MACjC,MAAMI,SAAS,GAAGD,QAAQ,CAACL,IAAI,CAACC,KAAK;MACrC,MAAMM,YAAY,GAAGlB,eAAe,CAACU,QAAQ,CAAC;MAE9C,IAAIf,UAAU,CAACuB,YAAY,CAAC,IAAIA,YAAY,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;QAChEpB,OAAO,CAACuB,WAAW,CACjB,IAAI1B,YAAY,CACb,eAAcgB,QAAS,IAAGO,SAAU,mFAAkF,EACvH;UACEI,KAAK,EAAEL,QAAQ,CAACL;QAClB,CACF,CACF,CAAC;MACH,CAAC,MAAM,IAAII,UAAU,CAACE,SAAS,CAAC,EAAE;QAChCpB,OAAO,CAACuB,WAAW,CACjB,IAAI1B,YAAY,CACb,eAAcgB,QAAS,IAAGO,SAAU,6BAA4B,EACjE;UACEI,KAAK,EAAE,CAACN,UAAU,CAACE,SAAS,CAAC,EAAED,QAAQ,CAACL,IAAI;QAC9C,CACF,CACF,CAAC;MACH,CAAC,MAAM;QACLI,UAAU,CAACE,SAAS,CAAC,GAAGD,QAAQ,CAACL,IAAI;MACvC;IACF;IAEA,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}