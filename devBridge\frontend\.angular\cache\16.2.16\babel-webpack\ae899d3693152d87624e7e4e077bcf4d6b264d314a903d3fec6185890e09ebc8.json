{"ast": null, "code": "import { inspect } from './inspect.mjs';\n/**\n * Sometimes a non-error is thrown, wrap it as an Error instance to ensure a consistent Error interface.\n */\n\nexport function toError(thrownValue) {\n  return thrownValue instanceof Error ? thrownValue : new NonErrorThrown(thrownValue);\n}\nclass NonErrorThrown extends Error {\n  constructor(thrownValue) {\n    super('Unexpected error value: ' + inspect(thrownValue));\n    this.name = 'NonErrorThrown';\n    this.thrownValue = thrownValue;\n  }\n}", "map": {"version": 3, "names": ["inspect", "to<PERSON><PERSON><PERSON>", "thrownValue", "Error", "NonErrorThrown", "constructor", "name"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/jsutils/toError.mjs"], "sourcesContent": ["import { inspect } from './inspect.mjs';\n/**\n * Sometimes a non-error is thrown, wrap it as an Error instance to ensure a consistent Error interface.\n */\n\nexport function toError(thrownValue) {\n  return thrownValue instanceof Error\n    ? thrownValue\n    : new NonErrorThrown(thrownValue);\n}\n\nclass NonErrorThrown extends Error {\n  constructor(thrownValue) {\n    super('Unexpected error value: ' + inspect(thrownValue));\n    this.name = 'NonErrorThrown';\n    this.thrownValue = thrownValue;\n  }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC;AACA;AACA;;AAEA,OAAO,SAASC,OAAOA,CAACC,WAAW,EAAE;EACnC,OAAOA,WAAW,YAAYC,KAAK,GAC/BD,WAAW,GACX,IAAIE,cAAc,CAACF,WAAW,CAAC;AACrC;AAEA,MAAME,cAAc,SAASD,KAAK,CAAC;EACjCE,WAAWA,CAACH,WAAW,EAAE;IACvB,KAAK,CAAC,0BAA0B,GAAGF,OAAO,CAACE,WAAW,CAAC,CAAC;IACxD,IAAI,CAACI,IAAI,GAAG,gBAAgB;IAC5B,IAAI,CAACJ,WAAW,GAAGA,WAAW;EAChC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}