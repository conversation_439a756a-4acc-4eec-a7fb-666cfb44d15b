{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction EquipeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.error = \"\");\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction EquipeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"span\", 42);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EquipeComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtext(1, \" Aucune \\u00E9quipe trouv\\u00E9e. Cr\\u00E9ez votre premi\\u00E8re \\u00E9quipe ci-dessous. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_12_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_12_tr_15_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const equipe_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.editEquipe(equipe_r12));\n    });\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_12_tr_15_Template_button_click_12_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const equipe_r12 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r12._id && ctx_r15.deleteEquipe(equipe_r12._id));\n    });\n    i0.ɵɵtext(13, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_12_tr_15_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const equipe_r12 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.showMembreModal(equipe_r12));\n    });\n    i0.ɵɵtext(15, \" G\\u00E9rer membres \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const equipe_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(equipe_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(equipe_r12.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(equipe_r12.admin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", (equipe_r12.members == null ? null : equipe_r12.members.length) || 0, \" membres\");\n  }\n}\nfunction EquipeComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"table\", 45)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Admin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Membres\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, EquipeComponent_div_12_tr_15_Template, 16, 4, \"tr\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes);\n  }\n}\nfunction EquipeComponent_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 50);\n  }\n}\nfunction EquipeComponent_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.cancelEdit());\n    });\n    i0.ɵɵtext(1, \" Annuler \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_50_div_6_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 61)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_50_div_6_li_2_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const membreId_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.removeMembreFromEquipe(ctx_r25.selectedEquipe._id, ctx_r25.getMemberId(membreId_r24)));\n    });\n    i0.ɵɵtext(4, \" Retirer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const membreId_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(membreId_r24);\n  }\n}\nfunction EquipeComponent_div_50_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 59);\n    i0.ɵɵtemplate(2, EquipeComponent_div_50_div_6_li_2_Template, 5, 1, \"li\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.selectedEquipe.members);\n  }\n}\nfunction EquipeComponent_div_50_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 23);\n    i0.ɵɵtext(1, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"h6\");\n    i0.ɵɵtext(5, \"Membres actuels:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EquipeComponent_div_50_div_6_Template, 3, 1, \"div\", 52);\n    i0.ɵɵtemplate(7, EquipeComponent_div_50_ng_template_7_Template, 2, 0, \"ng-template\", null, 53, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 14)(10, \"h6\");\n    i0.ɵɵtext(11, \"Ajouter un membre:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 54);\n    i0.ɵɵelement(13, \"input\", 55, 56);\n    i0.ɵɵelementStart(15, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_50_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const _r22 = i0.ɵɵreference(14);\n      const ctx_r27 = i0.ɵɵnextContext();\n      ctx_r27.addMembreToEquipe(ctx_r27.selectedEquipe._id, _r22.value);\n      return i0.ɵɵresetView(_r22.value = \"\");\n    });\n    i0.ɵɵtext(16, \" Ajouter \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"small\", 23);\n    i0.ɵɵtext(18, \"Entrez l'ID du membre \\u00E0 ajouter \\u00E0 l'\\u00E9quipe\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 58)(20, \"p\", 12)(21, \"strong\");\n    i0.ɵɵtext(22, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Pour ajouter un membre, vous devez d'abord cr\\u00E9er le membre dans la section des membres. \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(8);\n    const _r22 = i0.ɵɵreference(14);\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00C9quipe: \", ctx_r8.selectedEquipe.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.selectedEquipe.members && ctx_r8.selectedEquipe.members.length > 0)(\"ngIfElse\", _r20);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", !ctx_r8.selectedEquipe || !ctx_r8.selectedEquipe._id || !_r22.value);\n  }\n}\nexport class EquipeComponent {\n  constructor(equipeService, membreService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.equipes = [];\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.selectedEquipe = null;\n    this.isEditing = false;\n    this.membres = [];\n    this.loading = false;\n    this.error = '';\n  }\n  // Méthode utilitaire pour extraire l'ID d'un membre\n  getMemberId(membre) {\n    if (typeof membre === 'string') {\n      return membre;\n    }\n    return membre._id || membre.id || '';\n  }\n  ngOnInit() {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: data => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: data => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: response => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = {\n          name: '',\n          description: ''\n        }; // Clear input\n        this.loading = false;\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: error => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n  editEquipe(equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: updatedEquipe => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = {\n            name: '',\n            description: ''\n          };\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: error => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: response => {\n          console.log('Team deleted successfully:', response);\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n          }\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: error => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  showMembreModal(equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n  addMembreToEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n    this.loading = true;\n    // Create a proper Membre object that matches what the API expects\n    const membre = {\n      id: membreId\n    };\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: response => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: error => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: response => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: error => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function EquipeComponent_Factory(t) {\n      return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeComponent,\n      selectors: [[\"app-equipe\"]],\n      decls: 54,\n      vars: 14,\n      consts: [[1, \"container\", \"mt-4\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mb-4\", 4, \"ngIf\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"card\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"name\", \"required\", \"\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [1, \"invalid-feedback\"], [\"for\", \"description\", 1, \"form-label\"], [\"id\", \"description\", \"rows\", \"3\", \"placeholder\", \"Entrez une description pour cette \\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [1, \"text-muted\"], [1, \"d-flex\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-secondary ms-2\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"membreModal\", \"tabindex\", \"-1\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"d-flex\", \"justify-content-center\", \"mb-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-sm\", \"btn-info\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ms-2\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"input-group\", \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"ID du membre\", 1, \"form-control\"], [\"membreIdInput\", \"\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"alert\", \"alert-info\", \"mt-3\"], [1, \"list-group\"], [\"class\", \"list-group-item d-flex justify-content-between align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"]],\n      template: function EquipeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r29 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, EquipeComponent_div_1_Template, 3, 1, \"div\", 1);\n          i0.ɵɵtemplate(2, EquipeComponent_div_2_Template, 4, 0, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h2\");\n          i0.ɵɵtext(7, \"Liste des \\u00E9quipes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_8_listener() {\n            return ctx.loadEquipes();\n          });\n          i0.ɵɵelement(9, \"i\", 7);\n          i0.ɵɵtext(10, \" Rafra\\u00EEchir \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, EquipeComponent_div_11_Template, 2, 0, \"div\", 8);\n          i0.ɵɵtemplate(12, EquipeComponent_div_12_Template, 16, 1, \"div\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 4)(15, \"div\", 10)(16, \"div\", 11)(17, \"h3\", 12);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 13)(20, \"form\")(21, \"div\", 14)(22, \"label\", 15);\n          i0.ɵɵtext(23, \"Nom de l'\\u00E9quipe \");\n          i0.ɵɵelementStart(24, \"span\", 16);\n          i0.ɵɵtext(25, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"input\", 17, 18);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_input_input_26_listener() {\n            i0.ɵɵrestoreView(_r29);\n            const _r4 = i0.ɵɵreference(27);\n            return i0.ɵɵresetView(ctx.newEquipe.name = _r4.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 19);\n          i0.ɵɵtext(29, \"Le nom de l'\\u00E9quipe est requis\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 14)(31, \"label\", 20);\n          i0.ɵɵtext(32, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"textarea\", 21, 22);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_textarea_input_33_listener() {\n            i0.ɵɵrestoreView(_r29);\n            const _r5 = i0.ɵɵreference(34);\n            return i0.ɵɵresetView(ctx.newEquipe.description = _r5.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"small\", 23);\n          i0.ɵɵtext(36, \"Une br\\u00E8ve description de l'\\u00E9quipe et de son objectif\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 24)(38, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_38_listener() {\n            return ctx.isEditing ? ctx.updateSelectedEquipe() : ctx.addEquipe();\n          });\n          i0.ɵɵtemplate(39, EquipeComponent_span_39_Template, 1, 0, \"span\", 26);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(41, EquipeComponent_button_41_Template, 2, 0, \"button\", 27);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(42, \"div\", 28)(43, \"div\", 29)(44, \"div\", 30)(45, \"div\", 31)(46, \"h5\", 32);\n          i0.ɵɵtext(47, \"G\\u00E9rer les membres de l'\\u00E9quipe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"button\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 34);\n          i0.ɵɵtemplate(50, EquipeComponent_div_50_Template, 24, 4, \"div\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 36)(52, \"button\", 37);\n          i0.ɵɵtext(53, \" Fermer \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.equipes.length === 0 && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.equipes.length > 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Modifier une \\u00E9quipe\" : \"Cr\\u00E9er une \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"is-invalid\", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === \"\"));\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.name);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.description || \"\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.newEquipe.name || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipe);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.NgForm],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-radius: 8px 8px 0 0 !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 6px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n\\n.list-group-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.list-group-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "EquipeComponent_div_1_Template_button_click_2_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "error", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "EquipeComponent_div_12_tr_15_Template_button_click_10_listener", "restoredCtx", "_r14", "equipe_r12", "$implicit", "ctx_r13", "editEquipe", "EquipeComponent_div_12_tr_15_Template_button_click_12_listener", "ctx_r15", "_id", "deleteEquipe", "EquipeComponent_div_12_tr_15_Template_button_click_14_listener", "ctx_r16", "showMembreModal", "ɵɵtextInterpolate", "name", "description", "admin", "members", "length", "ɵɵtemplate", "EquipeComponent_div_12_tr_15_Template", "ɵɵproperty", "ctx_r3", "equipes", "ɵɵelement", "EquipeComponent_button_41_Template_button_click_0_listener", "_r18", "ctx_r17", "cancelEdit", "EquipeComponent_div_50_div_6_li_2_Template_button_click_3_listener", "_r26", "membreId_r24", "ctx_r25", "removeMembreFromEquipe", "selectedEquipe", "getMemberId", "EquipeComponent_div_50_div_6_li_2_Template", "ctx_r19", "EquipeComponent_div_50_div_6_Template", "EquipeComponent_div_50_ng_template_7_Template", "ɵɵtemplateRefExtractor", "EquipeComponent_div_50_Template_button_click_15_listener", "_r28", "_r22", "ɵɵreference", "ctx_r27", "addMembreToEquipe", "value", "ctx_r8", "_r20", "EquipeComponent", "constructor", "equipeService", "membreService", "newEquipe", "isEditing", "membres", "loading", "membre", "id", "ngOnInit", "loadEquipes", "loadMembres", "getEquipes", "subscribe", "next", "data", "console", "log", "message", "getMembres", "addEquipe", "response", "successMessage", "alert", "equipe", "updateSelectedEquipe", "updateEquipe", "updatedEquipe", "confirm", "modalRef", "document", "getElementById", "window", "bootstrap", "modal", "Modal", "show", "teamId", "membreId", "trim", "find", "e", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "selectors", "decls", "vars", "consts", "template", "EquipeComponent_Template", "rf", "ctx", "EquipeComponent_div_1_Template", "EquipeComponent_div_2_Template", "EquipeComponent_Template_button_click_8_listener", "EquipeComponent_div_11_Template", "EquipeComponent_div_12_Template", "EquipeComponent_Template_input_input_26_listener", "_r29", "_r4", "EquipeComponent_Template_textarea_input_33_listener", "_r5", "EquipeComponent_Template_button_click_38_listener", "EquipeComponent_span_39_Template", "EquipeComponent_button_41_Template", "EquipeComponent_div_50_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.ts", "C:\\Users\\<USER>\\Desktop\\version finale pi\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { EquipeService } from 'src/app/services/equipe.service';\r\nimport { MembreService } from 'src/app/services/membre.service';\r\nimport { Equipe } from 'src/app/models/equipe.model';\r\nimport { Membre } from 'src/app/models/membre.model';\r\nimport { forkJoin } from 'rxjs';\r\n\r\n// Add Bootstrap type declaration\r\ndeclare global {\r\n  interface Window {\r\n    bootstrap: any;\r\n  }\r\n}\r\n\r\n@Component({\r\n  selector: 'app-equipe',\r\n  templateUrl: './equipe.component.html',\r\n  styleUrls: ['./equipe.component.css'],\r\n})\r\nexport class EquipeComponent implements OnInit {\r\n  equipes: Equipe[] = [];\r\n  newEquipe: Equipe = { name: '', description: '' };\r\n  selectedEquipe: Equipe | null = null;\r\n  isEditing = false;\r\n  membres: Membre[] = [];\r\n  loading = false;\r\n  error = '';\r\n\r\n  constructor(\r\n    private equipeService: EquipeService,\r\n    private membreService: MembreService\r\n  ) {}\r\n\r\n  // Méthode utilitaire pour extraire l'ID d'un membre\r\n  getMemberId(membre: string | any): string {\r\n    if (typeof membre === 'string') {\r\n      return membre;\r\n    }\r\n    return membre._id || membre.id || '';\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadEquipes();\r\n    this.loadMembres();\r\n  }\r\n\r\n  loadEquipes() {\r\n    this.loading = true;\r\n    this.equipeService.getEquipes().subscribe({\r\n      next: (data) => {\r\n        console.log('Loaded equipes:', data);\r\n        this.equipes = data;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading equipes:', error);\r\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadMembres() {\r\n    this.loading = true;\r\n    this.membreService.getMembres().subscribe({\r\n      next: (data) => {\r\n        console.log('Loaded membres:', data);\r\n        this.membres = data;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading membres:', error);\r\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  addEquipe() {\r\n    console.log('Adding equipe:', this.newEquipe);\r\n\r\n    if (!this.newEquipe.name) {\r\n      console.error('Team name is required');\r\n      this.error = 'Le nom de l\\'équipe est requis';\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.error = '';\r\n\r\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\r\n      next: (response) => {\r\n        console.log('Equipe added successfully:', response);\r\n        this.loadEquipes();\r\n        this.newEquipe = { name: '', description: '' }; // Clear input\r\n        this.loading = false;\r\n\r\n        // Afficher un message de succès temporaire\r\n        const successMessage = 'Équipe créée avec succès!';\r\n        this.error = ''; // Effacer les erreurs précédentes\r\n        alert(successMessage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding equipe:', error);\r\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  editEquipe(equipe: Equipe) {\r\n    this.isEditing = true;\r\n    // Créer une copie profonde pour éviter de modifier l'objet original\r\n    this.newEquipe = {\r\n      _id: equipe._id,\r\n      name: equipe.name || '',\r\n      description: equipe.description || '',\r\n      admin: equipe.admin,\r\n      members: equipe.members ? [...equipe.members] : []\r\n    };\r\n  }\r\n\r\n  cancelEdit() {\r\n    this.isEditing = false;\r\n    this.newEquipe = { name: '', description: '' };\r\n    this.error = ''; // Effacer les erreurs\r\n  }\r\n\r\n  updateSelectedEquipe() {\r\n    if (!this.newEquipe.name) {\r\n      console.error('Team name is required');\r\n      this.error = 'Le nom de l\\'équipe est requis';\r\n      return;\r\n    }\r\n\r\n    if (this.newEquipe._id) {\r\n      this.loading = true;\r\n      this.error = '';\r\n\r\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\r\n        next: (updatedEquipe) => {\r\n          console.log('Team updated successfully:', updatedEquipe);\r\n          this.loadEquipes();\r\n          this.isEditing = false;\r\n          this.newEquipe = { name: '', description: '' };\r\n          this.loading = false;\r\n\r\n          // Afficher un message de succès temporaire\r\n          const successMessage = 'Équipe mise à jour avec succès!';\r\n          alert(successMessage);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error updating team:', error);\r\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n          this.loading = false;\r\n        }\r\n      });\r\n    } else {\r\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\r\n    }\r\n  }\r\n\r\n  deleteEquipe(id: string) {\r\n    if (!id) {\r\n      console.error('ID is undefined');\r\n      this.error = 'ID de l\\'équipe non défini';\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\r\n      this.loading = true;\r\n      this.error = '';\r\n\r\n      this.equipeService.deleteEquipe(id).subscribe({\r\n        next: (response) => {\r\n          console.log('Team deleted successfully:', response);\r\n\r\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\r\n          if (this.isEditing && this.newEquipe._id === id) {\r\n            this.isEditing = false;\r\n            this.newEquipe = { name: '', description: '' };\r\n          }\r\n\r\n          this.loadEquipes();\r\n          this.loading = false;\r\n\r\n          // Afficher un message de succès\r\n          alert('Équipe supprimée avec succès');\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting team:', error);\r\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  showMembreModal(equipe: Equipe) {\r\n    this.selectedEquipe = equipe;\r\n    // Ouvrir le modal avec Bootstrap 5\r\n    const modalRef = document.getElementById('membreModal');\r\n    if (modalRef) {\r\n      try {\r\n        // Ensure Bootstrap is properly loaded\r\n        if (typeof window !== 'undefined' && window.bootstrap) {\r\n          const modal = new window.bootstrap.Modal(modalRef);\r\n          modal.show();\r\n        } else {\r\n          console.error('Bootstrap is not loaded properly');\r\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error showing modal:', error);\r\n      }\r\n    } else {\r\n      console.error('Modal element not found');\r\n    }\r\n  }\r\n\r\n  addMembreToEquipe(teamId: string | undefined, membreId: string) {\r\n    if (!teamId) {\r\n      console.error('Team ID is undefined');\r\n      alert('ID de l\\'équipe non défini');\r\n      return;\r\n    }\r\n\r\n    if (!membreId || membreId.trim() === '') {\r\n      console.error('Member ID is empty');\r\n      alert('L\\'ID du membre est requis');\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    // Create a proper Membre object that matches what the API expects\r\n    const membre: Membre = { id: membreId };\r\n\r\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\r\n      next: (response) => {\r\n        console.log('Member added successfully:', response);\r\n        this.loadEquipes();\r\n        this.loading = false;\r\n\r\n        // Afficher un message de succès\r\n        alert('Membre ajouté avec succès à l\\'équipe');\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding member:', error);\r\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\r\n        alert(this.error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {\r\n    if (!teamId) {\r\n      console.error('Team ID is undefined');\r\n      alert('ID de l\\'équipe non défini');\r\n      return;\r\n    }\r\n\r\n    if (!membreId) {\r\n      console.error('Member ID is undefined');\r\n      alert('ID du membre non défini');\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\r\n      this.loading = true;\r\n\r\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\r\n        next: (response) => {\r\n          console.log('Member removed successfully:', response);\r\n          this.loadEquipes();\r\n          this.loading = false;\r\n\r\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\r\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\r\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\r\n            if (updatedEquipe) {\r\n              this.selectedEquipe = updatedEquipe;\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error removing member:', error);\r\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\r\n          alert(this.error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"container mt-4\">\r\n  <!-- Message d'erreur -->\r\n  <div\r\n    *ngIf=\"error\"\r\n    class=\"alert alert-danger alert-dismissible fade show\"\r\n    role=\"alert\"\r\n  >\r\n    {{ error }}\r\n    <button\r\n      type=\"button\"\r\n      class=\"btn-close\"\r\n      (click)=\"error = ''\"\r\n      aria-label=\"Close\"\r\n    ></button>\r\n  </div>\r\n\r\n  <!-- Indicateur de chargement -->\r\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center mb-4\">\r\n    <div class=\"spinner-border text-primary\" role=\"status\">\r\n      <span class=\"visually-hidden\">Chargement...</span>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Liste des équipes -->\r\n  <div class=\"row mb-4\">\r\n    <div class=\"col-12\">\r\n      <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h2>Liste des équipes</h2>\r\n        <button class=\"btn btn-sm btn-outline-primary\" (click)=\"loadEquipes()\">\r\n          <i class=\"bi bi-arrow-clockwise\"></i> Rafraîchir\r\n        </button>\r\n      </div>\r\n\r\n      <div *ngIf=\"equipes.length === 0 && !loading\" class=\"alert alert-info\">\r\n        Aucune équipe trouvée. Créez votre première équipe ci-dessous.\r\n      </div>\r\n\r\n      <div *ngIf=\"equipes.length > 0\" class=\"table-responsive\">\r\n        <table class=\"table table-striped\">\r\n          <thead>\r\n            <tr>\r\n              <th>Nom</th>\r\n              <th>Description</th>\r\n              <th>Admin</th>\r\n              <th>Membres</th>\r\n              <th>Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let equipe of equipes\">\r\n              <td>{{ equipe.name }}</td>\r\n              <td>{{ equipe.description }}</td>\r\n              <td>{{ equipe.admin }}</td>\r\n              <td>{{ equipe.members?.length || 0 }} membres</td>\r\n              <td>\r\n                <button\r\n                  class=\"btn btn-sm btn-info me-2\"\r\n                  (click)=\"editEquipe(equipe)\"\r\n                >\r\n                  Modifier\r\n                </button>\r\n                <button\r\n                  class=\"btn btn-sm btn-danger me-2\"\r\n                  (click)=\"equipe._id && deleteEquipe(equipe._id)\"\r\n                >\r\n                  Supprimer\r\n                </button>\r\n                <button\r\n                  class=\"btn btn-sm btn-primary\"\r\n                  (click)=\"showMembreModal(equipe)\"\r\n                >\r\n                  Gérer membres\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Formulaire de création d'équipe -->\r\n  <div class=\"row mb-4\">\r\n    <div class=\"col-12\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header bg-primary text-white\">\r\n          <h3 class=\"mb-0\">\r\n            {{ isEditing ? \"Modifier une équipe\" : \"Créer une équipe\" }}\r\n          </h3>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <form>\r\n            <div class=\"mb-3\">\r\n              <label for=\"name\" class=\"form-label\"\r\n                >Nom de l'équipe <span class=\"text-danger\">*</span></label\r\n              >\r\n              <input\r\n                #nameInput\r\n                type=\"text\"\r\n                class=\"form-control\"\r\n                id=\"name\"\r\n                [value]=\"newEquipe.name\"\r\n                (input)=\"newEquipe.name = nameInput.value\"\r\n                required\r\n                [class.is-invalid]=\"\r\n                  !newEquipe.name && (isEditing || newEquipe.name === '')\r\n                \"\r\n                placeholder=\"Entrez le nom de l'équipe\"\r\n              />\r\n              <div class=\"invalid-feedback\">Le nom de l'équipe est requis</div>\r\n            </div>\r\n            <div class=\"mb-3\">\r\n              <label for=\"description\" class=\"form-label\">Description</label>\r\n              <textarea\r\n                #descInput\r\n                class=\"form-control\"\r\n                id=\"description\"\r\n                [value]=\"newEquipe.description || ''\"\r\n                (input)=\"newEquipe.description = descInput.value\"\r\n                rows=\"3\"\r\n                placeholder=\"Entrez une description pour cette équipe\"\r\n              ></textarea>\r\n              <small class=\"text-muted\"\r\n                >Une brève description de l'équipe et de son objectif</small\r\n              >\r\n            </div>\r\n            <div class=\"d-flex\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary\"\r\n                [disabled]=\"!newEquipe.name || loading\"\r\n                (click)=\"isEditing ? updateSelectedEquipe() : addEquipe()\"\r\n              >\r\n                <span\r\n                  *ngIf=\"loading\"\r\n                  class=\"spinner-border spinner-border-sm me-1\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                ></span>\r\n                {{ isEditing ? \"Mettre à jour\" : \"Créer\" }}\r\n              </button>\r\n              <button\r\n                *ngIf=\"isEditing\"\r\n                type=\"button\"\r\n                class=\"btn btn-secondary ms-2\"\r\n                (click)=\"cancelEdit()\"\r\n              >\r\n                Annuler\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Modal pour gérer les membres -->\r\n  <div class=\"modal fade\" id=\"membreModal\" tabindex=\"-1\" aria-hidden=\"true\">\r\n    <div class=\"modal-dialog\">\r\n      <div class=\"modal-content\">\r\n        <div class=\"modal-header\">\r\n          <h5 class=\"modal-title\">Gérer les membres de l'équipe</h5>\r\n          <button\r\n            type=\"button\"\r\n            class=\"btn-close\"\r\n            data-bs-dismiss=\"modal\"\r\n            aria-label=\"Close\"\r\n          ></button>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div *ngIf=\"selectedEquipe\">\r\n            <h6>Équipe: {{ selectedEquipe.name }}</h6>\r\n\r\n            <!-- Liste des membres actuels -->\r\n            <div class=\"mb-3\">\r\n              <h6>Membres actuels:</h6>\r\n              <div\r\n                *ngIf=\"\r\n                  selectedEquipe.members && selectedEquipe.members.length > 0;\r\n                  else noMembers\r\n                \"\r\n              >\r\n                <ul class=\"list-group\">\r\n                  <li\r\n                    class=\"list-group-item d-flex justify-content-between align-items-center\"\r\n                    *ngFor=\"let membreId of selectedEquipe.members\"\r\n                  >\r\n                    <span>{{ membreId }}</span>\r\n                    <button\r\n                      class=\"btn btn-sm btn-danger\"\r\n                      (click)=\"\r\n                        removeMembreFromEquipe(selectedEquipe._id, getMemberId(membreId))\r\n                      \"\r\n                    >\r\n                      Retirer\r\n                    </button>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <ng-template #noMembers>\r\n                <p class=\"text-muted\">Aucun membre dans cette équipe</p>\r\n              </ng-template>\r\n            </div>\r\n\r\n            <!-- Formulaire pour ajouter un membre -->\r\n            <div class=\"mb-3\">\r\n              <h6>Ajouter un membre:</h6>\r\n              <div class=\"input-group mb-2\">\r\n                <input\r\n                  #membreIdInput\r\n                  type=\"text\"\r\n                  class=\"form-control\"\r\n                  placeholder=\"ID du membre\"\r\n                />\r\n                <button\r\n                  class=\"btn btn-primary\"\r\n                  [disabled]=\"\r\n                    !selectedEquipe ||\r\n                    !selectedEquipe._id ||\r\n                    !membreIdInput.value\r\n                  \"\r\n                  (click)=\"\r\n                    addMembreToEquipe(selectedEquipe._id, membreIdInput.value);\r\n                    membreIdInput.value = ''\r\n                  \"\r\n                >\r\n                  Ajouter\r\n                </button>\r\n              </div>\r\n              <small class=\"text-muted\"\r\n                >Entrez l'ID du membre à ajouter à l'équipe</small\r\n              >\r\n            </div>\r\n\r\n            <!-- Informations supplémentaires -->\r\n            <div class=\"alert alert-info mt-3\">\r\n              <p class=\"mb-0\">\r\n                <strong>Note:</strong> Pour ajouter un membre, vous devez\r\n                d'abord créer le membre dans la section des membres.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"modal-footer\">\r\n          <button\r\n            type=\"button\"\r\n            class=\"btn btn-secondary\"\r\n            data-bs-dismiss=\"modal\"\r\n          >\r\n            Fermer\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;ICEEA,EAAA,CAAAC,cAAA,cAIC;IACCD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAG,UAAA,mBAAAC,uDAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,KAAA,GAAiB,EAAE;IAAA,EAAC;IAErBV,EAAA,CAAAW,YAAA,EAAS;;;;IANVX,EAAA,CAAAY,SAAA,GACA;IADAZ,EAAA,CAAAa,kBAAA,MAAAC,MAAA,CAAAJ,KAAA,MACA;;;;;IASFV,EAAA,CAAAC,cAAA,cAAgE;IAE9BD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAW,YAAA,EAAO;;;;;IAclDX,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,MAAA,gGACF;IAAAF,EAAA,CAAAW,YAAA,EAAM;;;;;;IAcAX,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAC1BX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAW,YAAA,EAAK;IACjCX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAC3BX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAClDX,EAAA,CAAAC,cAAA,SAAI;IAGAD,EAAA,CAAAG,UAAA,mBAAAY,+DAAA;MAAA,MAAAC,WAAA,GAAAhB,EAAA,CAAAK,aAAA,CAAAY,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAApB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAW,OAAA,CAAAC,UAAA,CAAAH,UAAA,CAAkB;IAAA,EAAC;IAE5BlB,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAG,UAAA,mBAAAmB,+DAAA;MAAA,MAAAN,WAAA,GAAAhB,EAAA,CAAAK,aAAA,CAAAY,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAAvB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAS,UAAA,CAAAM,GAAA,IAAcD,OAAA,CAAAE,YAAA,CAAAP,UAAA,CAAAM,GAAA,CAAwB;IAAA,EAAC;IAEhDxB,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAG,UAAA,mBAAAuB,+DAAA;MAAA,MAAAV,WAAA,GAAAhB,EAAA,CAAAK,aAAA,CAAAY,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAA3B,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAkB,OAAA,CAAAC,eAAA,CAAAV,UAAA,CAAuB;IAAA,EAAC;IAEjClB,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAW,YAAA,EAAS;;;;IAtBPX,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAA6B,iBAAA,CAAAX,UAAA,CAAAY,IAAA,CAAiB;IACjB9B,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAA6B,iBAAA,CAAAX,UAAA,CAAAa,WAAA,CAAwB;IACxB/B,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAA6B,iBAAA,CAAAX,UAAA,CAAAc,KAAA,CAAkB;IAClBhC,EAAA,CAAAY,SAAA,GAAyC;IAAzCZ,EAAA,CAAAa,kBAAA,MAAAK,UAAA,CAAAe,OAAA,kBAAAf,UAAA,CAAAe,OAAA,CAAAC,MAAA,mBAAyC;;;;;IAhBrDlC,EAAA,CAAAC,cAAA,cAAyD;IAI7CD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAW,YAAA,EAAK;IACZX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAW,YAAA,EAAK;IACpBX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAW,YAAA,EAAK;IACdX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAChBX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAGpBX,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAmC,UAAA,KAAAC,qCAAA,kBAyBK;IACPpC,EAAA,CAAAW,YAAA,EAAQ;;;;IA1BiBX,EAAA,CAAAY,SAAA,IAAU;IAAVZ,EAAA,CAAAqC,UAAA,YAAAC,MAAA,CAAAC,OAAA,CAAU;;;;;IAoF7BvC,EAAA,CAAAwC,SAAA,eAKQ;;;;;;IAGVxC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAG,UAAA,mBAAAsC,2DAAA;MAAAzC,EAAA,CAAAK,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAkC,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtB5C,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAW,YAAA,EAAS;;;;;;IAmCLX,EAAA,CAAAC,cAAA,aAGC;IACOD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAW,YAAA,EAAO;IAC3BX,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAG,UAAA,mBAAA0C,mEAAA;MAAA,MAAA7B,WAAA,GAAAhB,EAAA,CAAAK,aAAA,CAAAyC,IAAA;MAAA,MAAAC,YAAA,GAAA/B,WAAA,CAAAG,SAAA;MAAA,MAAA6B,OAAA,GAAAhD,EAAA,CAAAQ,aAAA;MAAA,OAC2BR,EAAA,CAAAS,WAAA,CAAAuC,OAAA,CAAAC,sBAAA,CAAAD,OAAA,CAAAE,cAAA,CAAA1B,GAAA,EAChDwB,OAAA,CAAAG,WAAA,CAAAJ,YAAA,CAAqB,CAAC;IAAA;IAED/C,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAW,YAAA,EAAS;;;;IARHX,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAA6B,iBAAA,CAAAkB,YAAA,CAAc;;;;;IAX1B/C,EAAA,CAAAC,cAAA,UAKC;IAEGD,EAAA,CAAAmC,UAAA,IAAAiB,0CAAA,iBAaK;IACPpD,EAAA,CAAAW,YAAA,EAAK;;;;IAZoBX,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAqC,UAAA,YAAAgB,OAAA,CAAAH,cAAA,CAAAjB,OAAA,CAAyB;;;;;IAelDjC,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,0CAA8B;IAAAF,EAAA,CAAAW,YAAA,EAAI;;;;;;IA9B9DX,EAAA,CAAAC,cAAA,UAA4B;IACtBD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAG1CX,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAW,YAAA,EAAK;IACzBX,EAAA,CAAAmC,UAAA,IAAAmB,qCAAA,kBAsBM;IACNtD,EAAA,CAAAmC,UAAA,IAAAoB,6CAAA,iCAAAvD,EAAA,CAAAwD,sBAAA,CAEc;IAChBxD,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAC3BX,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAwC,SAAA,qBAKE;IACFxC,EAAA,CAAAC,cAAA,kBAWC;IAJCD,EAAA,CAAAG,UAAA,mBAAAsD,yDAAA;MAAAzD,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAC,IAAA,GAAA3D,EAAA,CAAA4D,WAAA;MAAA,MAAAC,OAAA,GAAA7D,EAAA,CAAAQ,aAAA;MACuBqD,OAAA,CAAAC,iBAAA,CAAAD,OAAA,CAAAX,cAAA,CAAA1B,GAAA,EAAAmC,IAAA,CAAAI,KAAA,CACvB;MAAA,OAAsB/D,EAAA,CAAAS,WAAA,CAAAkD,IAAA,CAAAI,KAAA,GACxB,EAAE;IAAA,EAAC;IAED/D,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAW,YAAA,EAAS;IAEXX,EAAA,CAAAC,cAAA,iBACG;IAAAD,EAAA,CAAAE,MAAA,iEAA0C;IAAAF,EAAA,CAAAW,YAAA,EAC5C;IAIHX,EAAA,CAAAC,cAAA,eAAmC;IAEvBD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAW,YAAA,EAAS;IAACX,EAAA,CAAAE,MAAA,sGAEzB;IAAAF,EAAA,CAAAW,YAAA,EAAI;;;;;;IApEFX,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAa,kBAAA,kBAAAmD,MAAA,CAAAd,cAAA,CAAApB,IAAA,KAAiC;IAMhC9B,EAAA,CAAAY,SAAA,GAGf;IAHeZ,EAAA,CAAAqC,UAAA,SAAA2B,MAAA,CAAAd,cAAA,CAAAjB,OAAA,IAAA+B,MAAA,CAAAd,cAAA,CAAAjB,OAAA,CAAAC,MAAA,KAGf,aAAA+B,IAAA;IAoCgBjE,EAAA,CAAAY,SAAA,GAIC;IAJDZ,EAAA,CAAAqC,UAAA,cAAA2B,MAAA,CAAAd,cAAA,KAAAc,MAAA,CAAAd,cAAA,CAAA1B,GAAA,KAAAmC,IAAA,CAAAI,KAAA,CAIC;;;ADzMnB,OAAM,MAAOG,eAAe;EAS1BC,YACUC,aAA4B,EAC5BC,aAA4B;IAD5B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAVvB,KAAA9B,OAAO,GAAa,EAAE;IACtB,KAAA+B,SAAS,GAAW;MAAExC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IACjD,KAAAmB,cAAc,GAAkB,IAAI;IACpC,KAAAqB,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAA/D,KAAK,GAAG,EAAE;EAKP;EAEH;EACAyC,WAAWA,CAACuB,MAAoB;IAC9B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOA,MAAM;;IAEf,OAAOA,MAAM,CAAClD,GAAG,IAAIkD,MAAM,CAACC,EAAE,IAAI,EAAE;EACtC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,aAAa,CAACW,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAAC3C,OAAO,GAAG2C,IAAI;QACnB,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/D,KAAK,EAAGA,KAAK,IAAI;QACfyE,OAAO,CAACzE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC2E,OAAO;QACtE,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,aAAa,CAACiB,UAAU,EAAE,CAACN,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACV,OAAO,GAAGU,IAAI;QACnB,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/D,KAAK,EAAGA,KAAK,IAAI;QACfyE,OAAO,CAACzE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC2E,OAAO;QACtE,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAc,SAASA,CAAA;IACPJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACd,SAAS,CAAC;IAE7C,IAAI,CAAC,IAAI,CAACA,SAAS,CAACxC,IAAI,EAAE;MACxBqD,OAAO,CAACzE,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,CAAC+D,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC/D,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC0D,aAAa,CAACmB,SAAS,CAAC,IAAI,CAACjB,SAAS,CAAC,CAACU,SAAS,CAAC;MACrDC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACX,WAAW,EAAE;QAClB,IAAI,CAACP,SAAS,GAAG;UAAExC,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE,CAAC,CAAC;QAChD,IAAI,CAAC0C,OAAO,GAAG,KAAK;QAEpB;QACA,MAAMgB,cAAc,GAAG,2BAA2B;QAClD,IAAI,CAAC/E,KAAK,GAAG,EAAE,CAAC,CAAC;QACjBgF,KAAK,CAACD,cAAc,CAAC;MACvB,CAAC;MACD/E,KAAK,EAAGA,KAAK,IAAI;QACfyE,OAAO,CAACzE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE2E,OAAO,IAAI3E,KAAK,CAAC2E,OAAO,IAAI,eAAe,CAAC;QACrH,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEApD,UAAUA,CAACsE,MAAc;IACvB,IAAI,CAACpB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACD,SAAS,GAAG;MACf9C,GAAG,EAAEmE,MAAM,CAACnE,GAAG;MACfM,IAAI,EAAE6D,MAAM,CAAC7D,IAAI,IAAI,EAAE;MACvBC,WAAW,EAAE4D,MAAM,CAAC5D,WAAW,IAAI,EAAE;MACrCC,KAAK,EAAE2D,MAAM,CAAC3D,KAAK;MACnBC,OAAO,EAAE0D,MAAM,CAAC1D,OAAO,GAAG,CAAC,GAAG0D,MAAM,CAAC1D,OAAO,CAAC,GAAG;KACjD;EACH;EAEAW,UAAUA,CAAA;IACR,IAAI,CAAC2B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG;MAAExC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IAC9C,IAAI,CAACrB,KAAK,GAAG,EAAE,CAAC,CAAC;EACnB;;EAEAkF,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACtB,SAAS,CAACxC,IAAI,EAAE;MACxBqD,OAAO,CAACzE,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAAC4D,SAAS,CAAC9C,GAAG,EAAE;MACtB,IAAI,CAACiD,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC/D,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC0D,aAAa,CAACyB,YAAY,CAAC,IAAI,CAACvB,SAAS,CAAC9C,GAAG,EAAE,IAAI,CAAC8C,SAAS,CAAC,CAACU,SAAS,CAAC;QAC5EC,IAAI,EAAGa,aAAa,IAAI;UACtBX,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,aAAa,CAAC;UACxD,IAAI,CAACjB,WAAW,EAAE;UAClB,IAAI,CAACN,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,SAAS,GAAG;YAAExC,IAAI,EAAE,EAAE;YAAEC,WAAW,EAAE;UAAE,CAAE;UAC9C,IAAI,CAAC0C,OAAO,GAAG,KAAK;UAEpB;UACA,MAAMgB,cAAc,GAAG,iCAAiC;UACxDC,KAAK,CAACD,cAAc,CAAC;QACvB,CAAC;QACD/E,KAAK,EAAGA,KAAK,IAAI;UACfyE,OAAO,CAACzE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE2E,OAAO,IAAI3E,KAAK,CAAC2E,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC/D,KAAK,GAAG,8CAA8C;;EAE/D;EAEAe,YAAYA,CAACkD,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPQ,OAAO,CAACzE,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAIqF,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAACtB,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC/D,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC0D,aAAa,CAAC3C,YAAY,CAACkD,EAAE,CAAC,CAACK,SAAS,CAAC;QAC5CC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;UAEnD;UACA,IAAI,IAAI,CAACjB,SAAS,IAAI,IAAI,CAACD,SAAS,CAAC9C,GAAG,KAAKmD,EAAE,EAAE;YAC/C,IAAI,CAACJ,SAAS,GAAG,KAAK;YACtB,IAAI,CAACD,SAAS,GAAG;cAAExC,IAAI,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;;UAGhD,IAAI,CAAC8C,WAAW,EAAE;UAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;UAEpB;UACAiB,KAAK,CAAC,8BAA8B,CAAC;QACvC,CAAC;QACDhF,KAAK,EAAGA,KAAK,IAAI;UACfyE,OAAO,CAACzE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE2E,OAAO,IAAI3E,KAAK,CAAC2E,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEA7C,eAAeA,CAAC+D,MAAc;IAC5B,IAAI,CAACzC,cAAc,GAAGyC,MAAM;IAC5B;IACA,MAAMK,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IACvD,IAAIF,QAAQ,EAAE;MACZ,IAAI;QACF;QACA,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;UACrD,MAAMC,KAAK,GAAG,IAAIF,MAAM,CAACC,SAAS,CAACE,KAAK,CAACN,QAAQ,CAAC;UAClDK,KAAK,CAACE,IAAI,EAAE;SACb,MAAM;UACLpB,OAAO,CAACzE,KAAK,CAAC,kCAAkC,CAAC;UACjDgF,KAAK,CAAC,kDAAkD,CAAC;;OAE5D,CAAC,OAAOhF,KAAK,EAAE;QACdyE,OAAO,CAACzE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;KAE/C,MAAM;MACLyE,OAAO,CAACzE,KAAK,CAAC,yBAAyB,CAAC;;EAE5C;EAEAoD,iBAAiBA,CAAC0C,MAA0B,EAAEC,QAAgB;IAC5D,IAAI,CAACD,MAAM,EAAE;MACXrB,OAAO,CAACzE,KAAK,CAAC,sBAAsB,CAAC;MACrCgF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACe,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MACvCvB,OAAO,CAACzE,KAAK,CAAC,oBAAoB,CAAC;MACnCgF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACjB,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMC,MAAM,GAAW;MAAEC,EAAE,EAAE8B;IAAQ,CAAE;IAEvC,IAAI,CAACrC,aAAa,CAACN,iBAAiB,CAAC0C,MAAM,EAAE9B,MAAM,CAAC,CAACM,SAAS,CAAC;MAC7DC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACX,WAAW,EAAE;QAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;QAEpB;QACAiB,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC;MACDhF,KAAK,EAAGA,KAAK,IAAI;QACfyE,OAAO,CAACzE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,qCAAqC,IAAIA,KAAK,CAACA,KAAK,EAAE2E,OAAO,IAAI3E,KAAK,CAAC2E,OAAO,IAAI,eAAe,CAAC;QAC/GK,KAAK,CAAC,IAAI,CAAChF,KAAK,CAAC;QACjB,IAAI,CAAC+D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAxB,sBAAsBA,CAACuD,MAA0B,EAAEC,QAAgB;IACjE,IAAI,CAACD,MAAM,EAAE;MACXrB,OAAO,CAACzE,KAAK,CAAC,sBAAsB,CAAC;MACrCgF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACe,QAAQ,EAAE;MACbtB,OAAO,CAACzE,KAAK,CAAC,wBAAwB,CAAC;MACvCgF,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF,IAAIK,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAACtB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACL,aAAa,CAACnB,sBAAsB,CAACuD,MAAM,EAAEC,QAAQ,CAAC,CAACzB,SAAS,CAAC;QACpEC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,QAAQ,CAAC;UACrD,IAAI,CAACX,WAAW,EAAE;UAClB,IAAI,CAACJ,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,IAAI,CAACvB,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC1B,GAAG,KAAKgF,MAAM,EAAE;YAC7D,MAAMV,aAAa,GAAG,IAAI,CAACvD,OAAO,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpF,GAAG,KAAKgF,MAAM,CAAC;YAC9D,IAAIV,aAAa,EAAE;cACjB,IAAI,CAAC5C,cAAc,GAAG4C,aAAa;;;QAGzC,CAAC;QACDpF,KAAK,EAAGA,KAAK,IAAI;UACfyE,OAAO,CAACzE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE2E,OAAO,IAAI3E,KAAK,CAAC2E,OAAO,IAAI,eAAe,CAAC;UACrHK,KAAK,CAAC,IAAI,CAAChF,KAAK,CAAC;UACjB,IAAI,CAAC+D,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;;;uBAnRWP,eAAe,EAAAlE,EAAA,CAAA6G,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA/G,EAAA,CAAA6G,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAf/C,eAAe;MAAAgD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnB5BxH,EAAA,CAAAC,cAAA,aAA4B;UAE1BD,EAAA,CAAAmC,UAAA,IAAAuF,8BAAA,iBAYM;UAGN1H,EAAA,CAAAmC,UAAA,IAAAwF,8BAAA,iBAIM;UAGN3H,EAAA,CAAAC,cAAA,aAAsB;UAGZD,EAAA,CAAAE,MAAA,6BAAiB;UAAAF,EAAA,CAAAW,YAAA,EAAK;UAC1BX,EAAA,CAAAC,cAAA,gBAAuE;UAAxBD,EAAA,CAAAG,UAAA,mBAAAyH,iDAAA;YAAA,OAASH,GAAA,CAAA5C,WAAA,EAAa;UAAA,EAAC;UACpE7E,EAAA,CAAAwC,SAAA,WAAqC;UAACxC,EAAA,CAAAE,MAAA,yBACxC;UAAAF,EAAA,CAAAW,YAAA,EAAS;UAGXX,EAAA,CAAAmC,UAAA,KAAA0F,+BAAA,iBAEM;UAEN7H,EAAA,CAAAmC,UAAA,KAAA2F,+BAAA,kBAwCM;UACR9H,EAAA,CAAAW,YAAA,EAAM;UAIRX,EAAA,CAAAC,cAAA,cAAsB;UAKZD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAW,YAAA,EAAK;UAEPX,EAAA,CAAAC,cAAA,eAAuB;UAIdD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAW,YAAA,EAAO;UAErDX,EAAA,CAAAC,cAAA,qBAYE;UANAD,EAAA,CAAAG,UAAA,mBAAA4H,iDAAA;YAAA/H,EAAA,CAAAK,aAAA,CAAA2H,IAAA;YAAA,MAAAC,GAAA,GAAAjI,EAAA,CAAA4D,WAAA;YAAA,OAAS5D,EAAA,CAAAS,WAAA,CAAAgH,GAAA,CAAAnD,SAAA,CAAAxC,IAAA,GAAAmG,GAAA,CAAAlE,KAAA,CAAgC;UAAA,EAAC;UAN5C/D,EAAA,CAAAW,YAAA,EAYE;UACFX,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,0CAA6B;UAAAF,EAAA,CAAAW,YAAA,EAAM;UAEnEX,EAAA,CAAAC,cAAA,eAAkB;UAC4BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAW,YAAA,EAAQ;UAC/DX,EAAA,CAAAC,cAAA,wBAQC;UAHCD,EAAA,CAAAG,UAAA,mBAAA+H,oDAAA;YAAAlI,EAAA,CAAAK,aAAA,CAAA2H,IAAA;YAAA,MAAAG,GAAA,GAAAnI,EAAA,CAAA4D,WAAA;YAAA,OAAS5D,EAAA,CAAAS,WAAA,CAAAgH,GAAA,CAAAnD,SAAA,CAAAvC,WAAA,GAAAoG,GAAA,CAAApE,KAAA,CAAuC;UAAA,EAAC;UAGlD/D,EAAA,CAAAW,YAAA,EAAW;UACZX,EAAA,CAAAC,cAAA,iBACG;UAAAD,EAAA,CAAAE,MAAA,sEAAoD;UAAAF,EAAA,CAAAW,YAAA,EACtD;UAEHX,EAAA,CAAAC,cAAA,eAAoB;UAKhBD,EAAA,CAAAG,UAAA,mBAAAiI,kDAAA;YAAA,OAAAX,GAAA,CAAAlD,SAAA,GAAqBkD,GAAA,CAAA7B,oBAAA,EAAsB,GAAG6B,GAAA,CAAAlC,SAAA,EAAW;UAAA,EAAC;UAE1DvF,EAAA,CAAAmC,UAAA,KAAAkG,gCAAA,mBAKQ;UACRrI,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAmC,UAAA,KAAAmG,kCAAA,qBAOS;UACXtI,EAAA,CAAAW,YAAA,EAAM;UAQhBX,EAAA,CAAAC,cAAA,eAA0E;UAI1CD,EAAA,CAAAE,MAAA,+CAA6B;UAAAF,EAAA,CAAAW,YAAA,EAAK;UAC1DX,EAAA,CAAAwC,SAAA,kBAKU;UACZxC,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAmC,UAAA,KAAAoG,+BAAA,mBAuEM;UACRvI,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,eAA0B;UAMtBD,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAW,YAAA,EAAS;;;UAvPdX,EAAA,CAAAY,SAAA,GAAW;UAAXZ,EAAA,CAAAqC,UAAA,SAAAoF,GAAA,CAAA/G,KAAA,CAAW;UAcRV,EAAA,CAAAY,SAAA,GAAa;UAAbZ,EAAA,CAAAqC,UAAA,SAAAoF,GAAA,CAAAhD,OAAA,CAAa;UAgBTzE,EAAA,CAAAY,SAAA,GAAsC;UAAtCZ,EAAA,CAAAqC,UAAA,SAAAoF,GAAA,CAAAlF,OAAA,CAAAL,MAAA,WAAAuF,GAAA,CAAAhD,OAAA,CAAsC;UAItCzE,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAqC,UAAA,SAAAoF,GAAA,CAAAlF,OAAA,CAAAL,MAAA,KAAwB;UAkDxBlC,EAAA,CAAAY,SAAA,GACF;UADEZ,EAAA,CAAAa,kBAAA,MAAA4G,GAAA,CAAAlD,SAAA,kEACF;UAgBMvE,EAAA,CAAAY,SAAA,GAEC;UAFDZ,EAAA,CAAAwI,WAAA,gBAAAf,GAAA,CAAAnD,SAAA,CAAAxC,IAAA,KAAA2F,GAAA,CAAAlD,SAAA,IAAAkD,GAAA,CAAAnD,SAAA,CAAAxC,IAAA,SAEC;UALD9B,EAAA,CAAAqC,UAAA,UAAAoF,GAAA,CAAAnD,SAAA,CAAAxC,IAAA,CAAwB;UAgBxB9B,EAAA,CAAAY,SAAA,GAAqC;UAArCZ,EAAA,CAAAqC,UAAA,UAAAoF,GAAA,CAAAnD,SAAA,CAAAvC,WAAA,OAAqC;UAarC/B,EAAA,CAAAY,SAAA,GAAuC;UAAvCZ,EAAA,CAAAqC,UAAA,cAAAoF,GAAA,CAAAnD,SAAA,CAAAxC,IAAA,IAAA2F,GAAA,CAAAhD,OAAA,CAAuC;UAIpCzE,EAAA,CAAAY,SAAA,GAAa;UAAbZ,EAAA,CAAAqC,UAAA,SAAAoF,GAAA,CAAAhD,OAAA,CAAa;UAKhBzE,EAAA,CAAAY,SAAA,GACF;UADEZ,EAAA,CAAAa,kBAAA,MAAA4G,GAAA,CAAAlD,SAAA,4CACF;UAEGvE,EAAA,CAAAY,SAAA,GAAe;UAAfZ,EAAA,CAAAqC,UAAA,SAAAoF,GAAA,CAAAlD,SAAA,CAAe;UA4BhBvE,EAAA,CAAAY,SAAA,GAAoB;UAApBZ,EAAA,CAAAqC,UAAA,SAAAoF,GAAA,CAAAvE,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}