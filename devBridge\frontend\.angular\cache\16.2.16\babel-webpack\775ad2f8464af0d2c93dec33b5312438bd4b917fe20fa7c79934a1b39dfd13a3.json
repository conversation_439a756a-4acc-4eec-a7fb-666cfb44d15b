{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isExecutableDefinitionNode } from '../../language/predicates.mjs';\n\n/**\n * Executable definitions\n *\n * A GraphQL document is only valid for execution if all definitions are either\n * operation or fragment definitions.\n *\n * See https://spec.graphql.org/draft/#sec-Executable-Definitions\n */\nexport function ExecutableDefinitionsRule(context) {\n  return {\n    Document(node) {\n      for (const definition of node.definitions) {\n        if (!isExecutableDefinitionNode(definition)) {\n          const defName = definition.kind === Kind.SCHEMA_DEFINITION || definition.kind === Kind.SCHEMA_EXTENSION ? 'schema' : '\"' + definition.name.value + '\"';\n          context.reportError(new GraphQLError(`The ${defName} definition is not executable.`, {\n            nodes: definition\n          }));\n        }\n      }\n      return false;\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "Kind", "isExecutableDefinitionNode", "ExecutableDefinitionsRule", "context", "Document", "node", "definition", "definitions", "defName", "kind", "SCHEMA_DEFINITION", "SCHEMA_EXTENSION", "name", "value", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/version finale pi/devBridge/frontend/node_modules/graphql/validation/rules/ExecutableDefinitionsRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isExecutableDefinitionNode } from '../../language/predicates.mjs';\n\n/**\n * Executable definitions\n *\n * A GraphQL document is only valid for execution if all definitions are either\n * operation or fragment definitions.\n *\n * See https://spec.graphql.org/draft/#sec-Executable-Definitions\n */\nexport function ExecutableDefinitionsRule(context) {\n  return {\n    Document(node) {\n      for (const definition of node.definitions) {\n        if (!isExecutableDefinitionNode(definition)) {\n          const defName =\n            definition.kind === Kind.SCHEMA_DEFINITION ||\n            definition.kind === Kind.SCHEMA_EXTENSION\n              ? 'schema'\n              : '\"' + definition.name.value + '\"';\n          context.reportError(\n            new GraphQLError(`The ${defName} definition is not executable.`, {\n              nodes: definition,\n            }),\n          );\n        }\n      }\n\n      return false;\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,0BAA0B,QAAQ,+BAA+B;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAACC,OAAO,EAAE;EACjD,OAAO;IACLC,QAAQA,CAACC,IAAI,EAAE;MACb,KAAK,MAAMC,UAAU,IAAID,IAAI,CAACE,WAAW,EAAE;QACzC,IAAI,CAACN,0BAA0B,CAACK,UAAU,CAAC,EAAE;UAC3C,MAAME,OAAO,GACXF,UAAU,CAACG,IAAI,KAAKT,IAAI,CAACU,iBAAiB,IAC1CJ,UAAU,CAACG,IAAI,KAAKT,IAAI,CAACW,gBAAgB,GACrC,QAAQ,GACR,GAAG,GAAGL,UAAU,CAACM,IAAI,CAACC,KAAK,GAAG,GAAG;UACvCV,OAAO,CAACW,WAAW,CACjB,IAAIf,YAAY,CAAE,OAAMS,OAAQ,gCAA+B,EAAE;YAC/DO,KAAK,EAAET;UACT,CAAC,CACH,CAAC;QACH;MACF;MAEA,OAAO,KAAK;IACd;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}