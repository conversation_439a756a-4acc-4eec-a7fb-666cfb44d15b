<div class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden flex items-center justify-center">
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"></div>
    <div class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 max-w-md w-full mx-auto p-6">
    <!-- Card Container -->
    <div class="bg-white dark:bg-[#1a1a1a] rounded-2xl shadow-2xl dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm">

      <!-- Header with gradient -->
      <div class="relative">
        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]"></div>
        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md"></div>

        <div class="p-8 text-center">
          <!-- Icon -->
          <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-full flex items-center justify-center shadow-lg">
            <i class="fas fa-users text-white text-2xl"></i>
          </div>

          <!-- Title -->
          <h1 class="text-2xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-3 tracking-wide">
            Création Rapide d'Équipe
          </h1>

          <!-- Description -->
          <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm leading-relaxed mb-8">
            Créez instantanément une nouvelle équipe avec des paramètres par défaut.
            Vous pourrez la personnaliser plus tard.
          </p>
        </div>
      </div>

      <!-- Action Section -->
      <div class="px-8 pb-8">
        <!-- Main Create Button -->
        <button
          (click)="createTeam()"
          [disabled]="creating"
          class="w-full relative overflow-hidden group bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-[0_10px_30px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_10px_30px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <!-- Loading Spinner -->
          <span *ngIf="creating" class="absolute inset-0 flex items-center justify-center">
            <div class="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
          </span>

          <!-- Button Content -->
          <span [class.opacity-0]="creating" class="flex items-center justify-center transition-opacity">
            <i class="fas fa-plus-circle mr-3 group-hover:rotate-90 transition-transform duration-300"></i>
            Créer
          </span>

          <!-- Hover Effect -->
          <div class="absolute inset-0 bg-white/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
        </button>

        <!-- Secondary Actions -->
        <div class="mt-4 flex justify-center">
          <button
            (click)="goBack()"
            [disabled]="creating"
            class="text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] text-sm font-medium transition-colors duration-200 disabled:opacity-50"
          >
            <i class="fas fa-arrow-left mr-2"></i>
            Retour à la liste
          </button>
        </div>
      </div>
    </div>

    <!-- Info Card -->
    <div class="mt-6 bg-[#4f5fad]/5 dark:bg-[#00f7ff]/5 rounded-xl p-4 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20">
      <div class="flex items-start">
        <div class="text-[#4f5fad] dark:text-[#00f7ff] mr-3 mt-0.5">
          <i class="fas fa-info-circle"></i>
        </div>
        <div class="flex-1">
          <h3 class="font-semibold text-[#4f5fad] dark:text-[#00f7ff] text-sm mb-1">
            Création Automatique
          </h3>
          <p class="text-[#6d6870] dark:text-[#a0a0a0] text-xs leading-relaxed">
            L'équipe sera créée avec un nom unique basé sur votre profil et l'heure actuelle.
            Vous serez automatiquement défini comme administrateur.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
