const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/authUserMiddleware');
const teamController = require('../controllers/teamController');

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

// Routes principales des équipes
router.get('/', teamController.getAllTeams);
router.post('/', teamController.createTeam);
router.get('/my-teams', teamController.getUserTeams);
router.get('/user/:userId', teamController.getUserTeams);
router.get('/:id', teamController.getTeamById);
router.put('/:id', teamController.updateTeam);
router.delete('/:id', teamController.deleteTeam);

// Routes pour la gestion des membres
router.post('/:id/members', teamController.addMemberToTeam);
router.delete('/:id/members', teamController.removeMemberFromTeam);

module.exports = router;