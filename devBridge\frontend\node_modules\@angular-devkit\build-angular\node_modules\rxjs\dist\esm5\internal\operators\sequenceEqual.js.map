{"version": 3, "file": "sequenceEqual.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/sequenceEqual.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AA2DpD,MAAM,UAAU,aAAa,CAC3B,SAA6B,EAC7B,UAAuD;IAAvD,2BAAA,EAAA,uBAAuC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,KAAK,CAAC,EAAP,CAAO;IAEvD,OAAO,OAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAEhC,IAAM,MAAM,GAAG,WAAW,EAAK,CAAC;QAEhC,IAAM,MAAM,GAAG,WAAW,EAAK,CAAC;QAGhC,IAAM,IAAI,GAAG,UAAC,OAAgB;YAC5B,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,CAAC;QAOF,IAAM,gBAAgB,GAAG,UAAC,SAA2B,EAAE,UAA4B;YACjF,IAAM,uBAAuB,GAAG,wBAAwB,CACtD,UAAU,EACV,UAAC,CAAI;gBACK,IAAA,MAAM,GAAe,UAAU,OAAzB,EAAE,QAAQ,GAAK,UAAU,SAAf,CAAgB;gBACxC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBAOvB,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACnD;qBAAM;oBAIL,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;iBAChD;YACH,CAAC,EACD;gBAEE,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAClB,IAAA,QAAQ,GAAa,UAAU,SAAvB,EAAE,MAAM,GAAK,UAAU,OAAf,CAAgB;gBAKxC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;gBAEtC,uBAAuB,aAAvB,uBAAuB,uBAAvB,uBAAuB,CAAE,WAAW,EAAE,CAAC;YACzC,CAAC,CACF,CAAC;YAEF,OAAO,uBAAuB,CAAC;QACjC,CAAC,CAAC;QAGF,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QACnD,SAAS,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;AACL,CAAC;AAgBD,SAAS,WAAW;IAClB,OAAO;QACL,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,KAAK;KAChB,CAAC;AACJ,CAAC"}